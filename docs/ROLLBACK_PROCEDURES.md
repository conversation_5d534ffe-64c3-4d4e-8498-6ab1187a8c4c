# PIX Transaction Duplication Fix - Rollback and Recovery Procedures

## Overview

This document provides comprehensive rollback and recovery procedures for the PIX transaction duplication fixes. These procedures are designed to ensure rapid recovery in case of issues during or after deployment.

## Emergency Contacts

- **Primary Engineer**: [Your Name] - [Phone] - [Email]
- **Database Administrator**: [<PERSON><PERSON> Name] - [Phone] - [Email]
- **DevOps Lead**: [<PERSON>O<PERSON> Name] - [Phone] - [Email]
- **Product Manager**: [PM Name] - [Phone] - [Email]

## Quick Reference - Emergency Rollback

### 🚨 IMMEDIATE ROLLBACK (< 5 minutes)

If critical issues are detected immediately after deployment:

```bash
# 1. Disable feature flags (immediate effect)
export ENABLE_NEW_TRANSACTION_SERVICE=false
export ROLLOUT_PERCENTAGE=0

# 2. Reset circuit breakers to force fallback
curl -X POST http://localhost:3000/health/transactions/reset-circuit-breakers

# 3. Restart application servers
kubectl rollout restart deployment/api-server
# OR for traditional deployments:
pm2 restart all
```

### 📊 Check System Status

```bash
# Check health status
curl http://localhost:3000/health/transactions

# Check circuit breaker status
curl http://localhost:3000/health/transactions/circuit-breakers

# Check recent transaction metrics
curl http://localhost:3000/health/transactions/metrics
```

## Detailed Rollback Procedures

### Level 1: Feature Flag Rollback (Recommended First Step)

**Impact**: Immediate, no downtime
**Time**: < 1 minute
**Risk**: Very Low

```bash
# Set environment variables or update configuration
export ENABLE_NEW_TRANSACTION_SERVICE=false
export ROLLOUT_PERCENTAGE=0
export ENABLE_CIRCUIT_BREAKER=false

# Restart application to pick up new config
# (or use hot reload if supported)
```

**Verification**:
```bash
# Check that new service is disabled
curl http://localhost:3000/health/transactions | jq '.featureFlags'

# Verify transactions are using legacy service
# Check logs for "usedNewService: false"
```

### Level 2: Database Constraint Rollback

**Impact**: Medium, requires database maintenance window
**Time**: 5-15 minutes
**Risk**: Medium

```bash
# 1. Connect to database
psql -h $DB_HOST -U $DB_USER -d $DB_NAME

# 2. Run rollback script
\i packages/database/migrations/rollback_transaction_constraints.sql

# 3. Verify rollback
SELECT indexname FROM pg_indexes 
WHERE tablename = 'transaction' 
AND indexname IN (
  'unique_send_transaction',
  'unique_reference_code', 
  'unique_external_id'
);
-- Should return no rows
```

**Verification**:
```bash
# Test transaction creation
npm run test:transaction-duplication

# Check for constraint errors in logs
grep -i "constraint" /var/log/application.log
```

### Level 3: Code Rollback

**Impact**: High, requires deployment
**Time**: 10-30 minutes
**Risk**: Medium

```bash
# 1. Revert to previous Git commit
git log --oneline -10  # Find previous stable commit
git revert <commit-hash>

# 2. Deploy previous version
# Use your standard deployment process

# 3. Verify deployment
curl http://localhost:3000/health/transactions
```

### Level 4: Full System Rollback

**Impact**: Very High, complete service restart
**Time**: 30-60 minutes
**Risk**: High

Only use if all other rollback methods fail.

```bash
# 1. Scale down to single instance
kubectl scale deployment api-server --replicas=1

# 2. Restore database from backup (if necessary)
# Follow your database backup restoration procedures

# 3. Deploy known good version
# Use your disaster recovery deployment process

# 4. Gradually scale back up
kubectl scale deployment api-server --replicas=3
```

## Recovery Procedures

### Post-Rollback Recovery Steps

1. **Immediate Assessment**
   ```bash
   # Check system health
   curl http://localhost:3000/health/transactions
   
   # Verify transaction creation works
   npm run test:basic-transaction-creation
   
   # Check error rates
   grep -c "ERROR" /var/log/application.log | tail -10
   ```

2. **Data Integrity Check**
   ```sql
   -- Check for any orphaned or inconsistent data
   SELECT COUNT(*) FROM transaction WHERE referenceCode IS NULL;
   SELECT COUNT(*) FROM transaction WHERE status NOT IN ('PENDING', 'PROCESSING', 'APPROVED', 'REJECTED', 'CANCELLED');
   
   -- Check for duplicates that may have been created during the incident
   SELECT referenceCode, COUNT(*) 
   FROM transaction 
   WHERE createdAt > NOW() - INTERVAL '1 hour'
   GROUP BY referenceCode 
   HAVING COUNT(*) > 1;
   ```

3. **Performance Monitoring**
   ```bash
   # Monitor transaction processing times
   curl http://localhost:3000/health/transactions/metrics
   
   # Check database performance
   # Use your database monitoring tools
   ```

### Root Cause Analysis

After successful rollback, conduct root cause analysis:

1. **Collect Evidence**
   - Application logs from the incident period
   - Database logs and performance metrics
   - Circuit breaker metrics
   - User reports and support tickets

2. **Timeline Reconstruction**
   - When was the deployment made?
   - When were issues first detected?
   - What was the impact scope?
   - How long did rollback take?

3. **Impact Assessment**
   - How many transactions were affected?
   - Were any transactions lost or duplicated?
   - What was the financial impact?
   - How many users were affected?

## Prevention Measures

### Pre-Deployment Checklist

- [ ] All tests pass in staging environment
- [ ] Database migration tested in staging
- [ ] Feature flags configured correctly
- [ ] Circuit breakers configured and tested
- [ ] Monitoring and alerting verified
- [ ] Rollback procedures tested
- [ ] Team notified of deployment window

### Monitoring Setup

```bash
# Set up alerts for key metrics
# Error rate > 5%
# Transaction processing time > 1000ms
# Circuit breaker state changes
# Database constraint violations
```

### Gradual Rollout Plan

1. **Phase 1**: 1% of traffic (1 hour)
2. **Phase 2**: 5% of traffic (2 hours)
3. **Phase 3**: 25% of traffic (4 hours)
4. **Phase 4**: 50% of traffic (8 hours)
5. **Phase 5**: 100% of traffic

Stop and rollback if any issues are detected.

## Communication Templates

### Internal Alert Template

```
🚨 PIX Transaction System Alert

Issue: [Brief description]
Severity: [HIGH/MEDIUM/LOW]
Impact: [Number of users/transactions affected]
Status: [INVESTIGATING/ROLLING BACK/RESOLVED]

Actions Taken:
- [Action 1]
- [Action 2]

Next Steps:
- [Next step 1]
- [Next step 2]

ETA for Resolution: [Time estimate]
```

### Customer Communication Template

```
We are currently experiencing technical difficulties with our payment processing system. 

- New transactions may be temporarily delayed
- Existing transactions are not affected
- We are working to resolve this quickly

We will provide updates every 15 minutes until resolved.

Estimated resolution time: [Time]
```

## Testing Rollback Procedures

### Monthly Rollback Drill

1. Deploy to staging environment
2. Execute Level 1 rollback (feature flags)
3. Verify system functionality
4. Execute Level 2 rollback (database)
5. Verify system functionality
6. Document any issues or improvements needed

### Rollback Testing Checklist

- [ ] Feature flag rollback works correctly
- [ ] Database rollback script executes without errors
- [ ] Application restarts successfully after rollback
- [ ] Transaction creation works after rollback
- [ ] No data corruption after rollback
- [ ] Monitoring shows healthy status after rollback

## Lessons Learned Template

After any rollback incident, document:

1. **What Went Wrong**
   - Technical root cause
   - Process failures
   - Communication issues

2. **What Went Right**
   - Effective monitoring
   - Quick detection
   - Successful rollback

3. **Improvements Needed**
   - Technical improvements
   - Process improvements
   - Training needs

4. **Action Items**
   - Specific tasks with owners and deadlines

## Contact Information

For questions about these procedures:
- Technical Lead: [Name] - [Email]
- Documentation: [Link to internal docs]
- Escalation: [Manager] - [Phone]

---

**Last Updated**: [Date]
**Version**: 1.0
**Next Review**: [Date + 3 months]
