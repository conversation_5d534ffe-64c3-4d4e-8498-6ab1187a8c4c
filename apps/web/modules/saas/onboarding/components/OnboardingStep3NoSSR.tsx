'use client';

import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useDropzone } from 'react-dropzone';
import { useSearchParams } from 'next/navigation';
import { useToast } from '@ui/hooks/use-toast';
import { useActiveOrganization } from '@saas/organizations/hooks/use-active-organization';
import { config } from '@repo/config';
import { AlertCircleIcon, ArrowRightIcon, CheckCircleIcon, UploadIcon, XCircleIcon } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@ui/components/alert';
import { Button } from '@ui/components/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@ui/components/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';

interface OnboardingStep3NoSSRProps {
  onCompleted: () => void;
}

const formSchema = z.object({
  cnpjDocument: z.string().min(1, "Comprovante de CNPJ é obrigatório"),
  businessLicense: z.string().optional(),
  representativeIdDocument: z.string().min(1, "Documento de identidade é obrigatório"),
});

type FormValues = z.infer<typeof formSchema>;

type UploadStatus = 'idle' | 'uploading' | 'success' | 'error';

export function OnboardingStep3NoSSR({ onCompleted }: OnboardingStep3NoSSRProps) {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const { activeOrganization } = useActiveOrganization();

  const organizationIdFromUrl = searchParams?.get('organizationId');

  // Estado para armazenar o ID da organização confirmado
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);

  // Prevent hydration mismatch by waiting for client-side hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Determinar o ID da organização (da URL ou do contexto) após hidratação
  useEffect(() => {
    if (!isHydrated) return;

    console.log("=== ONBOARDING STEP 3 NO SSR DEBUG ===");
    console.log("Active Organization:", activeOrganization);
    console.log("Organization ID from URL:", organizationIdFromUrl);
    console.log("Is Hydrated:", isHydrated);

    // Se temos um ID na URL, vamos usá-lo prioritariamente
    if (organizationIdFromUrl) {
      console.log("Using organization ID from URL:", organizationIdFromUrl);
      setOrganizationId(organizationIdFromUrl);
    } else if (activeOrganization?.id) {
      console.log("Using active organization ID:", activeOrganization.id);
      setOrganizationId(activeOrganization.id);
    } else {
      console.error("No organization ID found");
      setOrganizationId(null);
    }
  }, [isHydrated, activeOrganization, organizationIdFromUrl]);

  const [uploadStatus, setUploadStatus] = useState<Record<string, UploadStatus>>({
    cnpjDocument: 'idle',
    businessLicense: 'idle',
    representativeIdDocument: 'idle',
  });

  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadError, setUploadError] = useState<Record<string, string>>({});
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File>>({});

  // Function to sanitize filename for S3 compatibility
  const sanitizeFilename = (filename: string): string => {
    // Remove or replace special characters that are not allowed in S3 keys
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toLowerCase(); // Convert to lowercase
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cnpjDocument: '',
      businessLicense: '',
      representativeIdDocument: '',
    },
  });

  const isUploading = Object.values(uploadStatus).some(status => status === 'uploading');

  const uploadFile = useCallback(async (file: File, fieldName: string) => {
    try {
      setUploadStatus(prev => ({ ...prev, [fieldName]: 'uploading' }));
      setUploadProgress(prev => ({ ...prev, [fieldName]: 0 }));
      setUploadError(prev => ({ ...prev, [fieldName]: '' }));

      if (!organizationId) {
        throw new Error('Organization ID not found');
      }

      console.log(`Uploading file for organization: ${organizationId}, field: ${fieldName}`);

      // Generate sanitized file path
      const originalExtension = file.name.split('.').pop() || 'pdf';
      const sanitizedFilename = sanitizeFilename(file.name.replace(`.${originalExtension}`, ''));
      const timestamp = Date.now();
      const fileName = `${timestamp}-${sanitizedFilename}.${originalExtension}`;
      const path = `organizations/${organizationId}/documents/${fieldName}/${fileName}`;

      console.log('File path generation:', {
        originalName: file.name,
        sanitizedName: sanitizedFilename,
        fileName,
        path
      });

      // Get signed URL for upload
      const response = await fetch('/api/uploads/signed-upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          query: {
            path,
            bucket: config.storage.bucketNames.documents,
            contentType: file.type || 'application/octet-stream',
          }
        }),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => "No error details");
        console.error("Failed to get upload URL:", response.status, errorText);
        throw new Error('Failed to get upload URL');
      }

      const { signedUrl } = await response.json();

      // Upload file to S3
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text().catch(() => "No error details");
        console.error("Upload failed:", uploadResponse.status, errorText);
        throw new Error('Failed to upload file');
      }

      setUploadStatus(prev => ({ ...prev, [fieldName]: 'success' }));
      setUploadProgress(prev => ({ ...prev, [fieldName]: 100 }));
      setUploadedFiles(prev => ({ ...prev, [fieldName]: file }));

      // Update form value with the file path
      form.setValue(fieldName as keyof FormValues, path);

      return path;
    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus(prev => ({ ...prev, [fieldName]: 'error' }));
      setUploadError(prev => ({
        ...prev,
        [fieldName]: error instanceof Error ? error.message : 'Upload failed'
      }));
      throw error;
    }
  }, [organizationId, form]);

  const createFileUploader = useCallback((fieldName: string) => {
    const onDrop = useCallback((acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (file) {
        uploadFile(file, fieldName);
      }
    }, [fieldName]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      accept: {
        'application/pdf': ['.pdf'],
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
      },
      maxSize: 10 * 1024 * 1024, // 10MB
      multiple: false,
    });

    return (
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors ${
          isDragActive
            ? 'border-primary bg-primary/5'
            : uploadStatus[fieldName] === 'success'
            ? 'border-green-500 bg-green-50'
            : uploadStatus[fieldName] === 'error'
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <input {...getInputProps()} />
        {uploadStatus[fieldName] === 'uploading' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
            <div className="text-sm text-muted-foreground">
              Enviando... {uploadProgress[fieldName] || 0}%
            </div>
          </div>
        )}
        {uploadStatus[fieldName] === 'success' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircleIcon className="h-6 w-6 text-green-500" />
            </div>
            <div className="text-sm text-green-600">
              {uploadedFiles[fieldName]?.name || 'Arquivo enviado com sucesso'}
            </div>
          </div>
        )}
        {uploadStatus[fieldName] === 'error' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircleIcon className="h-6 w-6 text-red-500" />
            </div>
            <div className="text-sm text-red-500">
              {uploadError[fieldName] || "Erro ao enviar arquivo"}
            </div>
          </div>
        )}
        {uploadStatus[fieldName] === 'idle' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UploadIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="text-sm text-muted-foreground">Clique ou arraste um arquivo aqui</div>
            <div className="text-xs text-muted-foreground mt-1">PDF, JPG, PNG (max 10MB)</div>
          </div>
        )}
      </div>
    );
  }, [uploadFile, uploadStatus, uploadProgress, uploadError, uploadedFiles]);

  const onSubmit = async (values: FormValues) => {
    form.clearErrors("root");

    try {
      if (!organizationId) {
        throw new Error("Organização não encontrada");
      }

      console.log("Submitting documents:", values);
      console.log("For organization ID:", organizationId);

      // Submit document information to API
      const response = await fetch(`/api/organizations/${organizationId}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error("Error submitting documents:", errorData);
        throw new Error("Falha ao enviar documentação");
      }

      toast({
        title: "Documentos enviados com sucesso",
        description: "Sua documentação foi enviada para análise. Você receberá um email quando o processo for concluído.",
        variant: "success",
      });

      onCompleted();
    } catch (e) {
      console.error(e);
      toast({
        title: "Erro ao enviar documentos",
        description: e instanceof Error ? e.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });

      form.setError("root", {
        type: "server",
        message: "Falha ao enviar documentação",
      });
    }
  };

  // Show loading state during hydration to prevent mismatch
  if (!isHydrated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Carregando...</p>
      </div>
    );
  }

  // Display organization not found error if needed
  if (!organizationId) {
    return (
      <div className="p-6">
        <Alert variant="error">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>
            Organização não encontrada. Por favor, volte ao início do cadastro.
          </AlertDescription>
        </Alert>

        <div className="mt-4">
          <p className="text-muted-foreground text-sm">Debug Info:</p>
          <ul className="text-sm list-disc list-inside mt-2">
            <li>Organization ID from URL: {organizationIdFromUrl || "N/A"}</li>
            <li>Active Organization: {activeOrganization?.id || "N/A"}</li>
            <li>Is Hydrated: {isHydrated ? "Yes" : "No"}</li>
          </ul>
        </div>

        <div className="mt-6 flex justify-center">
          <Button
            variant="primary"
            onClick={() => window.location.href = "/app/onboarding"}
          >
            Voltar ao Início do Cadastro
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {form.formState.errors.root && (
        <Alert variant="error" className="mb-6">
          <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Documentos da Empresa</CardTitle>
              <CardDescription>
                Envie os documentos necessários para completar o cadastro da sua empresa
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="cnpjDocument"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comprovante de CNPJ <span className="text-red-500">*</span></FormLabel>
                      <CardDescription className="text-xs mb-1">
                        Obrigatório para a criação da empresa
                      </CardDescription>
                      <FormControl>
                        {createFileUploader('cnpjDocument')}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="businessLicense"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contrato Social <span className="text-gray-400">(opcional)</span></FormLabel>
                      <CardDescription className="text-xs mb-1">
                        Apenas se sua empresa possui sócios
                      </CardDescription>
                      <FormControl>
                        {createFileUploader('businessLicense')}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Documento do Responsável Legal</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="representativeIdDocument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documento de Identidade (RG ou CNH) <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      {createFileUploader('representativeIdDocument')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end pt-6">
            <Button
              type="submit"
              size="lg"
              disabled={isUploading ||
                !form.watch('cnpjDocument') ||
                !form.watch('representativeIdDocument')}
              loading={form.formState.isSubmitting}
            >
              Finalizar Cadastro
              <ArrowRightIcon className="ml-2 size-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
