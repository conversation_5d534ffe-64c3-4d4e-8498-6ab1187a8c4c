"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { But<PERSON> } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { SearchIcon, ArrowRightIcon, AlertCircleIcon, AlertTriangleIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@ui/components/alert";
import { useSearchParams } from "next/navigation";

// Schema para o passo de consulta CNPJ
const cnpjSearchSchema = z.object({
  document: z.string().min(14, "CNPJ deve ter pelo menos 14 caracteres"),
});

// Schema para o formulário completo
const formSchema = z.object({
  companyName: z.string().min(2, "Nome da empresa é obrigatório"),
  tradingName: z.string().optional(),
  document: z.string().min(14, "CNPJ deve ter pelo menos 14 caracteres"),
  contactEmail: z.string().email("E-mail inválido"),
  contactPhone: z.string().min(10, "Telefone deve ter pelo menos 10 caracteres"),
  address: z.string().min(5, "Endereço é obrigatório"),
  city: z.string().min(2, "Cidade é obrigatória"),
  state: z.string().min(2, "Estado é obrigatório"),
  postalCode: z.string().min(8, "CEP é obrigatório"),
  // Adicionando campos faltantes
  legalRepresentative: z.string().default(""),
  legalRepDocumentNumber: z.string().default(""),
});

type CnpjSearchValues = z.infer<typeof cnpjSearchSchema>;
type FormValues = z.infer<typeof formSchema>;

interface CNPJInfo {
  cnpj: string;
  razao_social: string;
  nome_fantasia: string;
  email: string;
  telefone1: string;
  endereco: {
    logradouro: string;
    numero: string;
    complemento: string;
    bairro: string;
    cep: string;
    uf: string;
    municipio: string;
  };
}

function OnboardingStep2Content({ onCompleted }: { onCompleted: () => void }) {
  const t = useTranslations();
  const { toast } = useToast();
  const { activeOrganization } = useActiveOrganization();
  const searchParams = useSearchParams();
  const [searchStep, setSearchStep] = useState(true);
  const [loading, setLoading] = useState(false);
  // Estado para armazenar o ID da organização confirmado
  const [verifiedOrganizationId, setVerifiedOrganizationId] = useState<string | null>(null);

  // Buscar o organizationId da URL apenas após hidratação
  const organizationIdFromUrl = searchParams.get("organizationId");

  // Efeito para verificar e logar o ID da organização assim que o componente montar
  useEffect(() => {
    console.log("=== ONBOARDING STEP 2 DEBUG ===");
    console.log("Organization ID from URL:", organizationIdFromUrl);
    console.log("Active Organization:", activeOrganization);

    const orgId = activeOrganization?.id || organizationIdFromUrl;
    console.log("Using Organization ID:", orgId);

    if (orgId) {
      setVerifiedOrganizationId(orgId);
    }
  }, [activeOrganization, organizationIdFromUrl]);

  // Formulário para busca de CNPJ
  const searchForm = useForm<CnpjSearchValues>({
    resolver: zodResolver(cnpjSearchSchema),
    defaultValues: {
      document: "",
    },
  });

  // Formulário completo
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      companyName: "",
      tradingName: "",
      document: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      city: "",
      state: "",
      postalCode: "",
      legalRepresentative: "",
      legalRepDocumentNumber: "",
    },
  });

  // Função para buscar informações do CNPJ
  const searchCNPJ = async (cnpj: string) => {
    setLoading(true);
    try {
      // Limpar caracteres especiais do CNPJ
      const cleanCnpj = cnpj.replace(/[^\d]/g, "");

      // Buscar na API - corrigindo o uso da variável de ambiente
      const response = await fetch(`https://api.invertexto.com/v1/cnpj/${cleanCnpj}?token=${process.env.NEXT_PUBLIC_CNPJ_API_AUTOCOMPLETE || "19220|CM7hhF41PYSAsSJpdqo3czVc74neDrLj"}`);

      if (!response.ok) {
        throw new Error("Não foi possível consultar o CNPJ. Tente novamente ou preencha manualmente.");
      }

      const data: CNPJInfo = await response.json();

      // Preencher o formulário com os dados da API
      form.setValue("companyName", data.razao_social);
      form.setValue("tradingName", data.nome_fantasia || "");
      form.setValue("document", cleanCnpj);
      form.setValue("contactEmail", data.email || "");
      form.setValue("contactPhone", data.telefone1 || "");

      // Montar endereço completo
      const fullAddress = `${data.endereco.logradouro} ${data.endereco.numero}${data.endereco.complemento ? ', ' + data.endereco.complemento : ''}, ${data.endereco.bairro}`;
      form.setValue("address", fullAddress);
      form.setValue("city", data.endereco.municipio);
      form.setValue("state", data.endereco.uf);
      form.setValue("postalCode", data.endereco.cep.replace(/[^\d]/g, ""));

      // Valores padrão para os campos obrigatórios
      form.setValue("legalRepresentative", "");
      form.setValue("legalRepDocumentNumber", "");

      // Mostrar formulário completo
      setSearchStep(false);

      toast({
        title: "CNPJ encontrado",
        description: "Os dados da empresa foram preenchidos automaticamente. Verifique e complete as informações necessárias.",
        variant: "success",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro na consulta",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao consultar o CNPJ",
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handler para o formulário de busca
  const onSearch: SubmitHandler<CnpjSearchValues> = async (values) => {
    await searchCNPJ(values.document);
  };

  // Handler para o formulário completo
  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    form.clearErrors("root");

    try {
      // Log para depuração antes de enviar
      console.log("=== SUBMITTING FORM DATA ===");
      console.log("Verified Organization ID:", verifiedOrganizationId);
      console.log("Form values:", values);

      if (!verifiedOrganizationId) {
        // Tentar obter o ID da organização uma última vez
        const orgId = activeOrganization?.id || organizationIdFromUrl;

        console.log("Last attempt to get organization ID:", orgId);

        if (!orgId) {
          throw new Error("Organização não encontrada");
        }

        setVerifiedOrganizationId(orgId);
      }

      console.log("Enviando informações para organização:", verifiedOrganizationId);

      // Usar o novo endpoint de onboarding que cria a relação membro-organização
      const response = await fetch(`/api/organizations/onboarding`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          organizationId: verifiedOrganizationId,
          legalInfo: {
            ...values,
            documentType: "CNPJ",
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error("Erro ao salvar informações:", errorData);
        throw new Error("Falha ao salvar informações da empresa");
      }

      toast({
        title: "Informações da empresa salvas com sucesso",
        description: "Sua empresa foi enviada para análise e aprovação. Você receberá um email quando o processo for concluído.",
        variant: "success",
      });

      onCompleted();
    } catch (e) {
      console.error(e);
      toast({
        title: "Erro ao salvar informações da empresa",
        description: e instanceof Error ? e.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });

      form.setError("root", {
        type: "server",
        message: "Falha ao salvar informações da empresa",
      });
    }
  };

  // Passo 1: Busca por CNPJ
  if (searchStep) {
    return (
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-5">Informações da Empresa</h2>
        <p className="text-muted-foreground mb-6">
          Informe o CNPJ da sua empresa para preenchermos automaticamente os dados cadastrais.
        </p>


        <Form {...searchForm}>
          <form onSubmit={searchForm.handleSubmit(onSearch)} className="space-y-6">
            <h3 className="text-lg font-medium">Consulta de CNPJ</h3>
            <FormField
              control={searchForm.control}
              name="document"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CNPJ da Empresa</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input {...field} placeholder="00.000.000/0000-00" />
                    </FormControl>
                    <Button type="submit" loading={loading}>
                      <SearchIcon className="mr-2 h-4 w-4" />
                      Consultar
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            {!verifiedOrganizationId && (
              <Alert variant="error">
                <AlertTriangleIcon className="h-4 w-4" />
                <AlertTitle>Atenção</AlertTitle>
                <AlertDescription>
                  Não foi possível identificar a organização atual. Por favor, tente voltar ao início do onboarding.
                </AlertDescription>
              </Alert>
            )}
          </form>
        </Form>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-5">
        Informações Cadastrais da Empresa
      </h2>
      <p className="text-muted-foreground mb-6">
        Verifique e complete as informações da sua empresa para continuar.
      </p>



      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div>
            <h3 className="text-lg font-medium mb-4">Dados da Empresa</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Razão Social</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Razão Social da empresa" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tradingName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome Fantasia</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nome Fantasia (opcional)" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="document"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CNPJ</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="00.000.000/0000-00" readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Corporativo</FormLabel>
                      <FormControl>
                        <Input type="email" {...field} placeholder="<EMAIL>" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="contactPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefone Comercial</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="(00) 0000-0000" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Endereço</h3>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Endereço Completo</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Rua, número, complemento, bairro" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cidade</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Cidade" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="UF" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CEP</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="00000-000" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {!verifiedOrganizationId && (
            <Alert variant="error">
              <AlertTriangleIcon className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>
                Não foi possível identificar a organização atual. Por favor, retorne ao início do onboarding.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setSearchStep(true)}
            >
              Voltar
            </Button>
            <Button
              type="submit"
              loading={form.formState.isSubmitting}
              disabled={!verifiedOrganizationId}
            >
              Continuar
              <ArrowRightIcon className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

// Wrapper component to handle hydration
export function OnboardingStep2({ onCompleted }: { onCompleted: () => void }) {
  const [isHydrated, setIsHydrated] = useState(false);

  // Prevent hydration mismatch by waiting for client-side hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Show loading state during hydration to prevent mismatch
  if (!isHydrated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Carregando...</p>
      </div>
    );
  }

  return <OnboardingStep2Content onCompleted={onCompleted} />;
}
