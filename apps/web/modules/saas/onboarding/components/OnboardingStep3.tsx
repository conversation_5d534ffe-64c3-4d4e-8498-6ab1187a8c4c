"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { useToast } from "@ui/hooks/use-toast";
import { ArrowRightIcon, UploadIcon, CheckCircleIcon, XCircleIcon, AlertCircleIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@ui/components/card";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@ui/components/alert";
import { useSignedUploadUrlMutation } from "@saas/shared/lib/api";
import { config } from "@repo/config";
import { useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { v4 as uuid } from "uuid";
import { useSearchParams } from "next/navigation";

// Simplified schema with only required documents
const formSchema = z.object({
  cnpjDocument: z.string().min(1, "Comprovante de CNPJ é obrigatório"),
  businessLicense: z.string().optional(), // Now optional
  representativeIdDocument: z.string().min(1, "Documento de identidade é obrigatório"),
});

type FormValues = z.infer<typeof formSchema>;

export function OnboardingStep3({ onCompleted }: { onCompleted: () => void }) {
  const [isHydrated, setIsHydrated] = useState(false);

  // Prevent hydration mismatch by waiting for client-side hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-2 text-sm text-muted-foreground">Carregando...</p>
      </div>
    );
  }

  const t = useTranslations();
  const { toast } = useToast();
  const { activeOrganization } = useActiveOrganization();
  const searchParams = useSearchParams();
  const organizationIdFromUrl = searchParams.get("organizationId");
  const getSignedUploadUrlMutation = useSignedUploadUrlMutation();

  // Estado para armazenar o ID da organização confirmado
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadStatus, setUploadStatus] = useState<Record<string, 'idle' | 'uploading' | 'success' | 'error'>>({
    cnpjDocument: 'idle',
    businessLicense: 'idle',
    representativeIdDocument: 'idle',
  });
  const [uploadError, setUploadError] = useState<Record<string, string | null>>({
    cnpjDocument: null,
    businessLicense: null,
    representativeIdDocument: null,
  });

  // Function to sanitize filename for S3 compatibility
  const sanitizeFilename = (filename: string): string => {
    // Remove or replace special characters that are not allowed in S3 keys
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toLowerCase(); // Convert to lowercase
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cnpjDocument: "",
      businessLicense: "",
      representativeIdDocument: "",
    },
  });

  // Determinar o ID da organização (da URL ou do contexto)
  useEffect(() => {
    console.log("=== DOCUMENT UPLOAD DEBUG ===");
    console.log("Active Organization:", activeOrganization);
    console.log("Organization ID from URL:", organizationIdFromUrl);

    // Se temos um ID na URL, vamos usá-lo prioritariamente
    if (organizationIdFromUrl) {
      console.log("Using organization ID from URL:", organizationIdFromUrl);
      setOrganizationId(organizationIdFromUrl);
    } else if (activeOrganization?.id) {
      console.log("Using active organization ID:", activeOrganization.id);
      setOrganizationId(activeOrganization.id);
    } else {
      console.error("No organization ID found");
      setOrganizationId(null);
    }
  }, [activeOrganization, organizationIdFromUrl]);

  const uploadFile = async (file: File, fieldName: keyof FormValues) => {
    if (!organizationId) {
      toast({
        title: "Erro",
        description: "Organização não encontrada",
        variant: "error",
      });
      return null;
    }

    // Reset any previous errors
    setUploadError(prev => ({ ...prev, [fieldName]: null }));
    setUploadStatus(prev => ({ ...prev, [fieldName]: 'uploading' }));
    setUploadProgress(prev => ({ ...prev, [fieldName]: 0 }));
    setIsUploading(true);

    try {
      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error("O arquivo não pode ter mais de 10MB");
      }

      // Create a sanitized filename with organization ID to keep files organized
      const originalExtension = file.name.split('.').pop() || 'pdf';
      const sanitizedFilename = sanitizeFilename(file.name.replace(`.${originalExtension}`, ''));
      const timestamp = Date.now();
      const fileName = `${organizationId}-${fieldName}-${timestamp}-${sanitizedFilename}.${originalExtension}`;
      const path = `${fileName}`;

      console.log(`Uploading file ${fileName} for field ${fieldName}, type: ${file.type}`);

      // Enhanced debug logging
      console.log("=== DOCUMENT UPLOAD DEBUG ===");
      console.log("Organization ID:", organizationId);
      console.log("Field name:", fieldName);
      console.log("Original file name:", file.name);
      console.log("Sanitized file name:", sanitizedFilename);
      console.log("Final file name:", fileName);
      console.log("File type:", file.type);
      console.log("File size:", file.size, "bytes");
      console.log("Path (simplified):", path);

      // Add debug logs for upload parameters
      console.log("Upload parameters:", {
        path,
        bucket: config.storage.bucketNames.documents,
        contentType: file.type || `application/${originalExtension}`,
      });

      // Log the config value for documents bucket
      console.log("Config bucket value:", {
        documents: config.storage.bucketNames.documents,
        avatars: config.storage.bucketNames.avatars
      });

      // We'll use a try-catch inside to get more specific error information
      try {
        console.log("About to call mutateAsync...");

        // Try using a direct fetch call like in the API implementation to see if this works
        console.log("Trying direct fetch call to API...");
        const directApiResponse = await fetch('/api/uploads/signed-upload-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            query: {
              path,
              bucket: config.storage.bucketNames.documents,
              contentType: file.type || `application/${originalExtension}`,
            }
          }),
        });

        console.log("Direct API call status:", directApiResponse.status);

        if (directApiResponse.ok) {
          console.log("Direct API call succeeded!");
          const directData = await directApiResponse.json();
          const directSignedUrl = directData.signedUrl;
          console.log("Got signed URL from direct call:", directSignedUrl);

          // Set to 50% during upload
          setUploadProgress(prev => ({ ...prev, [fieldName]: 50 }));

          console.log("Starting fetch to signed URL...");
          const response = await fetch(directSignedUrl, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": file.type || `application/${originalExtension}`,
            },
          });

          console.log("Upload response status:", response.status);

          if (!response.ok) {
            const errorText = await response.text().catch(() => "No error details");
            console.error("Upload failed:", response.status, errorText);
            throw new Error(`Upload failed with status: ${response.status}`);
          }

          setUploadProgress(prev => ({ ...prev, [fieldName]: 100 }));
          setUploadStatus(prev => ({ ...prev, [fieldName]: 'success' }));
          console.log(`Upload successful for ${fieldName}`);
          return path;
        } else {
          console.error("Direct API call failed with status:", directApiResponse.status);
          const errorText = await directApiResponse.text().catch(() => "No error details");
          console.error("Error details:", errorText);

          // Fall back to the original approach
          console.log("Falling back to useSignedUploadUrlMutation...");
          const signedUrlResponse = await getSignedUploadUrlMutation.mutateAsync({
            path,
            bucket: config.storage.bucketNames.documents,
            contentType: file.type || `application/${originalExtension}`,
          });

          console.log("mutateAsync response:", signedUrlResponse);
          const { signedUrl } = signedUrlResponse;
          console.log("Got signed URL:", signedUrl);

          // Set to 50% during upload
          setUploadProgress(prev => ({ ...prev, [fieldName]: 50 }));

          console.log("Starting fetch to signed URL...");
          const response = await fetch(signedUrl, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": file.type || `application/${originalExtension}`,
            },
          });

          console.log("Upload response status:", response.status);

          if (!response.ok) {
            const errorText = await response.text().catch(() => "No error details");
            console.error("Upload failed:", response.status, errorText);
            throw new Error(`Upload failed with status: ${response.status}`);
          }

          setUploadProgress(prev => ({ ...prev, [fieldName]: 100 }));
          setUploadStatus(prev => ({ ...prev, [fieldName]: 'success' }));
          console.log(`Upload successful for ${fieldName}`);
          return path;
        }
      } catch (specificError: unknown) {
        console.error("Specific error during upload:", specificError);
        // Check if it's a 404 error specifically
        if (specificError instanceof Error && specificError.message && specificError.message.includes("404")) {
          console.error("404 error detected - API endpoint might be missing");
        }
        throw specificError as Error; // Rethrow to be caught by the outer try-catch
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus(prev => ({ ...prev, [fieldName]: 'error' }));
      const errorMessage = error instanceof Error ? error.message : "Não foi possível fazer o upload do arquivo";
      setUploadError(prev => ({ ...prev, [fieldName]: errorMessage }));
      toast({
        title: "Erro ao fazer upload",
        description: errorMessage,
        variant: "error",
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const createFileUploader = (fieldName: keyof FormValues) => {
    const { getRootProps, getInputProps } = useDropzone({
      onDrop: async (acceptedFiles) => {
        if (acceptedFiles.length > 0) {
          const file = acceptedFiles[0];
          const path = await uploadFile(file, fieldName);
          if (path) {
            form.setValue(fieldName, path);
          }
        }
      },
      accept: {
        'application/pdf': ['.pdf'],
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
      },
      multiple: false,
      disabled: uploadStatus[fieldName] === 'uploading' || isUploading,
      maxSize: 10 * 1024 * 1024, // 10MB
    });

    return (
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center h-32 cursor-pointer transition-colors ${
          uploadStatus[fieldName] === 'error'
            ? 'border-red-500 bg-red-50 dark:bg-red-900/10'
            : uploadStatus[fieldName] === 'success'
            ? 'border-green-500 bg-green-50 dark:bg-green-900/10'
            : 'border-border hover:border-primary/50 hover:bg-primary/5'
        }`}
      >
        <input {...getInputProps()} />
        {uploadStatus[fieldName] === 'uploading' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UploadIcon className="animate-pulse h-6 w-6 text-primary" />
            </div>
            <div className="text-sm text-muted-foreground">Enviando... {uploadProgress[fieldName]}%</div>
          </div>
        )}
        {uploadStatus[fieldName] === 'success' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircleIcon className="h-6 w-6 text-green-500" />
            </div>
            <div className="text-sm text-muted-foreground">Arquivo enviado com sucesso</div>
          </div>
        )}
        {uploadStatus[fieldName] === 'error' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircleIcon className="h-6 w-6 text-red-500" />
            </div>
            <div className="text-sm text-red-500">
              {uploadError[fieldName] || "Erro ao enviar arquivo"}
            </div>
          </div>
        )}
        {uploadStatus[fieldName] === 'idle' && (
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UploadIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div className="text-sm text-muted-foreground">Clique ou arraste um arquivo aqui</div>
            <div className="text-xs text-muted-foreground mt-1">PDF, JPG, PNG (max 10MB)</div>
          </div>
        )}
      </div>
    );
  };

  const onSubmit = async (values: FormValues) => {
    form.clearErrors("root");

    try {
      if (!organizationId) {
        throw new Error("Organização não encontrada");
      }

      console.log("Submitting documents:", values);
      console.log("For organization ID:", organizationId);

      // Submit document information to API
      const response = await fetch(`/api/organizations/${organizationId}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error("Error submitting documents:", errorData);
        throw new Error("Falha ao enviar documentação");
      }

      toast({
        title: "Documentos enviados com sucesso",
        description: "Sua documentação foi enviada para análise. Você receberá um email quando o processo for concluído.",
        variant: "success",
      });

      onCompleted();
    } catch (e) {
      console.error(e);
      toast({
        title: "Erro ao enviar documentos",
        description: e instanceof Error ? e.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });

      form.setError("root", {
        type: "server",
        message: "Falha ao enviar documentação",
      });
    }
  };

  // Display organization not found error if needed
  if (!organizationId) {
    return (
      <div className="p-6">
        <Alert variant="error">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>
            Organização não encontrada. Por favor, volte ao início do cadastro.
          </AlertDescription>
        </Alert>

        {/* <div className="mt-4">
          <p className="text-muted-foreground text-sm">Debug Info:</p>
          <ul className="text-sm list-disc list-inside mt-2">
            <li>Organization ID from URL: {organizationIdFromUrl || "N/A"}</li>
            <li>Active Organization: {activeOrganization?.id || "N/A"}</li>
          </ul>
        </div> */}

        <div className="mt-6 flex justify-center">
          <Button
            variant="primary"
            onClick={() => window.location.href = "/app/onboarding"}
          >
            Voltar ao Início do Cadastro
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {form.formState.errors.root && (
        <Alert variant="error" className="mb-6">
          <AlertDescription>{form.formState.errors.root.message}</AlertDescription>
        </Alert>
      )}



      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Documentos da Empresa</CardTitle>
              <CardDescription>
                Envie os documentos necessários para completar o cadastro da sua empresa
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="cnpjDocument"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comprovante de CNPJ <span className="text-red-500">*</span></FormLabel>
                      <CardDescription className="text-xs mb-1">
                        Obrigatório para a criação da empresa
                      </CardDescription>
                      <FormControl>
                        {createFileUploader('cnpjDocument')}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="businessLicense"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contrato Social <span className="text-gray-400">(opcional)</span></FormLabel>
                      <CardDescription className="text-xs mb-1">
                        Apenas se sua empresa possui sócios
                      </CardDescription>
                      <FormControl>
                        {createFileUploader('businessLicense')}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Documento do Responsável Legal</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="representativeIdDocument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Documento de Identidade (RG ou CNH) <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      {createFileUploader('representativeIdDocument')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button
              type="submit"
              size="lg"
              disabled={isUploading ||
                !form.watch('cnpjDocument') ||
                !form.watch('representativeIdDocument')}
              loading={form.formState.isSubmitting}
            >
              Finalizar Cadastro
              <ArrowRightIcon className="ml-2 size-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
