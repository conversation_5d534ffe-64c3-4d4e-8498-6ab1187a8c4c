"use client";
import { authClient } from "@repo/auth/client";
import { useRouter } from "@shared/hooks/router";
import { clearCache } from "@shared/lib/cache";
import { Progress } from "@ui/components/progress";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useState, useEffect, Suspense } from "react";
import { OnboardingStep1 } from "./OnboardingStep1";
import { OnboardingStep2 } from "./OnboardingStep2";
import { OnboardingStep3 } from "./OnboardingStep3";
import dynamic from 'next/dynamic';

// Carrega o Step 3 apenas no cliente para evitar problemas de hidratação
const OnboardingStep3NoSSR = dynamic(() => import('./OnboardingStep3NoSSR').then(mod => mod.OnboardingStep3NoSSR), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
    </div>
  ),
});
import { OnboardingCreateOrgStep } from "./OnboardingCreateOrgStep";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";

function OnboardingFormContent() {
	const t = useTranslations();
	const router = useRouter();
	const searchParams = useSearchParams();
	const [debugLogs, setDebugLogs] = useState<string[]>([]);
	const [isHydrated, setIsHydrated] = useState(false);

	// Prevent hydration mismatch by waiting for client-side hydration
	useEffect(() => {
		setIsHydrated(true);
	}, []);

	// Get URL parameters only after hydration
	const stepSearchParam = isHydrated ? searchParams.get("step") : null;
	const organizationId = isHydrated ? searchParams.get("organizationId") : null;

	// Determine the current step (default to 1)
	const urlStep = stepSearchParam ? Number.parseInt(stepSearchParam, 10) : 1;

	// Determine if this is an invited user (has organizationId)
	const isFromInvitation = !!organizationId;

	// IMPORTANT: For invited users, we skip the org creation step and start directly with Step 2
	// The steps array is already mapped correctly for invited users (step 1 = company info, step 2 = documents)
	const effectiveStep = urlStep;

	// Enhanced debug logging - MUST be before any conditional returns
	useEffect(() => {
		if (!isHydrated) return; // Only run after hydration

		const currentTime = new Date().toLocaleTimeString();

		console.log("=== ONBOARDING FORM DEBUG ===");
		// Create a new object with param key-value pairs
		const paramsObject: Record<string, string> = {};
		searchParams.forEach((value, key) => {
			paramsObject[key] = value;
		});
		console.log("URL Parameters:", paramsObject);
		console.log("URL Step:", urlStep);
		console.log("Effective Step:", effectiveStep);
		console.log("Organization ID:", organizationId);
		console.log("Is Invited User:", isFromInvitation);

		setDebugLogs(prev => [...prev,
			`[${currentTime}] URL Step: ${urlStep}, Effective Step: ${effectiveStep}, Organization ID: ${organizationId || "none"}, Invited: ${isFromInvitation ? "yes" : "no"}`
		]);
	}, [isHydrated, urlStep, effectiveStep, organizationId, isFromInvitation, searchParams]);

	// Show loading state during hydration to prevent mismatch
	if (!isHydrated) {
		return (
			<div className="flex flex-col items-center justify-center min-h-[400px]">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				<p className="mt-4 text-muted-foreground">Carregando...</p>
			</div>
		);
	}

	// Function to set the step and preserve organizationId
	const setStep = (step: number) => {
		const queryParams = organizationId
			? `?step=${step}&organizationId=${organizationId}`
			: `?step=${step}`;

		console.log(`Setting step to ${step} with query parameters: ${queryParams}`);
		router.replace(`/app/onboarding${queryParams}`);
	};

	// Function to restart the onboarding process
	const restartOnboarding = () => {
		console.log("Restarting onboarding process");
		const queryParams = organizationId ? `?organizationId=${organizationId}` : "";
		router.replace(`/app/onboarding${queryParams}`);
	};

	// Function to mark onboarding as complete
	const onCompleted = async () => {
		console.log("=== ONBOARDING COMPLETED ===");
		console.log("Final organization ID:", organizationId);

		try {
			await authClient.updateUser({
				onboardingComplete: true,
			});

			await clearCache();

			// Get the organization to find its slug
			if (organizationId) {
				try {
					// Fetch the organization by ID to get its slug
					const response = await fetch(`/api/organizations/${organizationId}`);
					if (response.ok) {
						const organization = await response.json();
						if (organization && organization.slug) {
							router.replace(`/app/${organization.slug}`);
							console.log(`Redirected to organization page: /app/${organization.slug}`);
							return;
						}
					}
				} catch (error) {
					console.error("Error fetching organization details:", error);
				}
			}

			// Fallback to /app if we couldn't get the slug
			router.replace("/app");
			console.log("Redirected to /app (fallback)");
		} catch (error) {
			console.error("Error completing onboarding:", error);
		}
	};

	// Define the steps for the onboarding process
	// For invited users (with organizationId), skip organization creation
	let steps = [];

	if (isFromInvitation) {
		// Invited user with existing organization - only needs to complete org details
		steps = [
				{
					// Step 1 for invited users = OnboardingStep2 (company info)
					component: <OnboardingStep2 onCompleted={() => setStep(2)} />,
					title: "Informações da Empresa",
					description: "Consulte o CNPJ e confirme os dados da sua empresa"
				},
				{
					// Step 2 for invited users = OnboardingStep3NoSSR (finalization)
					component: (
						<OnboardingStep3NoSSR
							onCompleted={() => onCompleted()}
						/>
					),
					title: "Finalizar Configuração",
					description: "Complete a configuração da sua conta"
				}
			];
	} else {
		// Regular user needs all steps including org creation
		steps = [
			{
				component: <OnboardingCreateOrgStep onCompleted={() => setStep(2)} />,
				title: "Criar Empresa",
				description: "Crie sua empresa para começar"
			},
			{
				component: <OnboardingStep1 onCompleted={() => setStep(3)} />,
				title: "Informações Pessoais",
				description: "Preencha suas informações pessoais"
			},
			{
				component: <OnboardingStep2 onCompleted={() => setStep(4)} />,
				title: "Informações da Empresa",
				description: "Consulte o CNPJ e confirme os dados da sua empresa"
			},
			{
				component: (
					<OnboardingStep3NoSSR
						onCompleted={() => onCompleted()}
					/>
				),
				title: "Finalizar Configuração",
				description: "Complete a configuração da sua conta"
			}
		];
	}

	// Ensure we don't go out of bounds
	const stepIndex = Math.min(Math.max(effectiveStep - 1, 0), steps.length - 1);

	// Display percentage progress for the current step
	const progressPercentage = ((stepIndex + 1) / steps.length) * 100;

	return (
		<div className="flex flex-col">
			<div className="px-6 pt-6 pb-4">
				<h1 className="font-bold text-2xl md:text-3xl">
					{isFromInvitation ? "Complete o Registro da Empresa" : "Configure sua conta"}
				</h1>
				<p className="mt-2 text-foreground/60">
					{isFromInvitation
						? "Complete as informações da sua empresa para começar a utilizar a plataforma"
						: "Complete as etapas abaixo para configurar sua conta e começar a utilizar o serviço"}
				</p>



				{/* Progress bar */}
				{steps.length > 1 && (
					<div className="mt-6 mb-2">
						<div className="mb-2 flex items-center gap-3">
							<Progress
								value={progressPercentage}
								className="h-2"
							/>
							<span className="shrink-0 text-foreground/60 text-xs">
								Etapa {stepIndex + 1} de {steps.length}
							</span>
						</div>
						<div className="flex justify-between items-center">
							{steps.map((step, index) => (
								<div
									key={index}
									className={`text-sm ${index === stepIndex ? 'text-primary font-semibold' : 'text-muted-foreground'}`}
								>
									{step.title}
								</div>
							))}
						</div>
					</div>
				)}
			</div>

			{/* Render the current step */}
			<div className="pb-8">
				{steps[stepIndex].component}
			</div>
		</div>
	);
}

export function OnboardingForm() {
	return (
		<Suspense fallback={
			<div className="flex flex-col items-center justify-center min-h-[400px]">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				<p className="mt-4 text-muted-foreground">Carregando...</p>
			</div>
		}>
			<OnboardingFormContent />
		</Suspense>
	);
}
