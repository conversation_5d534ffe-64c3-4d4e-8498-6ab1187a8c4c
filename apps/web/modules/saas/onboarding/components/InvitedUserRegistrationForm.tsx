"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { useRouter } from "@shared/hooks/router";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@ui/components/card";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertCircleIcon, KeyIcon, UserIcon } from "lucide-react";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";

interface InvitedUserRegistrationFormProps {
  invitationId: string;
  email: string;
  organizationName: string;
  organizationSlug: string;
  role: string;
  organizationId: string;
  logoUrl?: string;
}

const formSchema = z.object({
  name: z.string().min(2, "Nome é obrigatório"),
  password: z.string().min(8, "A senha deve ter pelo menos 8 caracteres"),
  confirmPassword: z.string().min(8, "A confirmação de senha deve ter pelo menos 8 caracteres"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

export function InvitedUserRegistrationForm({
  invitationId,
  email,
  organizationName,
  organizationSlug,
  role,
  organizationId,
  logoUrl,
}: InvitedUserRegistrationFormProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);

    try {
      // Registrar o usuário
      const registerResponse = await fetch("/api/auth/register-from-invitation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          email,
          password: values.password,
          invitationId,
          role,
          organizationId,
        }),
      });

      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        throw new Error(errorData.error || "Falha ao registrar usuário");
      }

      toast({
        title: "Conta criada com sucesso",
        description: "Você agora pode acessar a plataforma.",
        variant: "success",
      });

      // Redirecionar para a página apropriada com base no papel do usuário
      if (role === "owner") {
        // Para proprietários, redirecionar para a página de onboarding com organizationId
        router.push(`/app/onboarding?step=1&organizationId=${organizationId}`);
      } else {
        // Para membros regulares, redirecionar para a página de onboarding específica para convidados
        router.push(`/app/onboarding/invited?role=${role}&organizationId=${organizationId}&organizationSlug=${organizationSlug}`);
      }
    } catch (error) {
      console.error("Erro ao registrar usuário:", error);
      toast({
        title: "Erro ao criar conta",
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-6">
        <OrganizationLogo
          name={organizationName}
          logoUrl={logoUrl}
          className="size-16 mx-auto mb-4"
        />
        <h1 className="text-2xl font-bold">Convite para {organizationName}</h1>
        <p className="text-muted-foreground mt-2">
          Você foi convidado para participar da organização {organizationName}.
          Complete seu cadastro para aceitar o convite.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Criar sua conta</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>Informações do convite</AlertTitle>
            <AlertDescription>
              <div className="mt-2">
                <strong>Email:</strong> {email}
              </div>
              <div>
                <strong>Função:</strong> {role === "owner" ? "Proprietário" : "Membro"}
              </div>
              {role === "owner" && (
                <div className="mt-2 text-sm">
                  Como proprietário, você precisará fornecer informações legais da empresa após criar sua conta.
                </div>
              )}
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome Completo</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Seu nome completo" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Confirme sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" loading={isLoading}>
                <UserIcon className="mr-2 h-4 w-4" />
                Criar Conta e Aceitar Convite
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center text-sm text-muted-foreground">
          Ao criar uma conta, você concorda com os termos de serviço e política de privacidade.
        </CardFooter>
      </Card>
    </div>
  );
}
