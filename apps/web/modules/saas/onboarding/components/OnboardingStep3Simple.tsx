"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardHeader, CardTitle, CardContent } from "@ui/components/card";
import { ArrowRightIcon } from "lucide-react";

export function OnboardingStep3Simple({ onCompleted }: { onCompleted: () => void }) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Teste Simples - Step 3</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Este é um componente simplificado para testar se o erro persiste.</p>
          <p>Sem useSearchParams, sem hidratação, apenas componentes básicos.</p>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button
          onClick={onCompleted}
          size="lg"
        >
          Finalizar Teste
          <ArrowRightIcon className="ml-2 size-4" />
        </Button>
      </div>
    </div>
  );
}