"use client";

import { authClient } from "@repo/auth/client";
import { useRouter } from "@shared/hooks/router";
import { clearCache } from "@shared/lib/cache";
import { Progress } from "@ui/components/progress";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { OnboardingStep1 } from "./OnboardingStep1";
import { OnboardingStep2 } from "./OnboardingStep2";
import { OnboardingStep3 } from "./OnboardingStep3";
import { InvitedMemberSetupStep } from "./InvitedMemberSetupStep";

interface InvitedUserOnboardingFormProps {
  role: string;
  organizationId: string;
  organizationSlug: string;
}

export function InvitedUserOnboardingForm({
  role,
  organizationId,
  organizationSlug
}: InvitedUserOnboardingFormProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  // Prevent hydration mismatch by waiting for client-side hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  const t = useTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();
  // Theme is already managed by AuthWrapper

  const stepSearchParam = searchParams.get("step");
  const onboardingStep = stepSearchParam
    ? Number.parseInt(stepSearchParam, 10)
    : 1;

  const setStep = (step: number) => {
    router.replace(`?role=${role}&organizationId=${organizationId}&organizationSlug=${organizationSlug}&step=${step}`);
  };

  const onCompleted = async () => {
    await authClient.updateUser({
      onboardingComplete: true,
    });

    await clearCache();
    router.replace(`/app/${organizationSlug}`);
  };

  // Define steps based on the user's role
  const steps = role === "owner"
    ? [
        // Owner needs to set up their profile and provide company information
        {
          component: <OnboardingStep1 onCompleted={() => setStep(2)} />,
          title: "Informações Pessoais",
        },
        {
          component: <OnboardingStep2 onCompleted={() => setStep(3)} />,
          title: "Informações da Empresa",
        },
        {
          component: <OnboardingStep3 onCompleted={() => onCompleted()} />,
          title: "Documentos",
        }
      ]
    : [
        // Regular member just needs to set up their profile
        {
          component: <InvitedMemberSetupStep
            organizationId={organizationId}
            organizationSlug={organizationSlug}
            onCompleted={() => onCompleted()}
          />,
          title: "Configuração da Conta",
        },
      ];

  return (
    <div>
      <h1 className="font-bold text-2xl md:text-3xl">
        {role === "owner"
          ? "Configuração da Empresa"
          : "Configuração da Conta"}
      </h1>
      <p className="mt-2 mb-6 text-foreground/60">
        {role === "owner"
          ? "Complete as informações da sua empresa para começar a utilizar a plataforma."
          : "Configure sua conta para começar a utilizar a plataforma."}
      </p>

      {steps.length > 1 && (
        <div className="mb-6 flex items-center gap-3">
          <Progress
            value={(onboardingStep / steps.length) * 100}
            className="h-2"
          />
          <span className="shrink-0 text-foreground/60 text-xs">
            {t("onboarding.step", {
              step: onboardingStep,
              total: steps.length,
            })}
          </span>
        </div>
      )}

      {steps[onboardingStep - 1].component}
    </div>
  );
}
