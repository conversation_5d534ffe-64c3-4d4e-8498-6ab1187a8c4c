"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { db } from "@repo/database";
import { useToast } from "@ui/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Badge } from "@ui/components/badge";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@ui/components/table";
import { Switch } from "@ui/components/switch";
import {
    CheckCircle2,
    XCircle,
    AlertCircle,
    Edit,
    TrashIcon,
    PlusCircle
} from "lucide-react";

type Organization = {
    id: string;
    name: string;
    slug: string;
    status: string;
    paymentGateways: PaymentGateway[];
    taxes?: {
        pixChargePercentFee: number;
        pixTransferPercentFee: number;
        pixChargeFixedFee: number;
        pixTransferFixedFee: number;
    };
};

type PaymentGateway = {
    id: string;
    name: string;
    type: string;
    instanceNumber?: number;
    isActive: boolean;
    isDefault: boolean;
    priority: number;
    canReceive?: boolean;
    canSend?: boolean;
    credentials?: any;
    relationId?: string;
};

// Função para buscar organizações e gateways
async function fetchOrganizationsWithGateways() {
    const response = await fetch('/api/admin/organizations/with-gateways?limit=100');
    if (!response.ok) {
        throw new Error('Falha ao buscar organizações');
    }
    return response.json();
}

export function GatewaysAdminPage() {
    const { toast } = useToast();
    const queryClient = useQueryClient();
    const [selectedOrganization, setSelectedOrganization] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState("");

    // Buscar organizações
    const { data, isLoading, error } = useQuery({
        queryKey: ['admin', 'organizations', 'gateways'],
        queryFn: fetchOrganizationsWithGateways
    });

    const organizations = data?.organizations || [];

    // Filtrar organizações por nome
    const filteredOrganizations = organizations.filter((org: Organization) =>
        org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (org.slug && org.slug.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    // Função para atualizar gateway
    async function updateGateway(organizationId: string, gatewayId: string, data: any) {
        try {
            const response = await fetch(`/api/admin/organizations/${organizationId}/gateways/${gatewayId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                throw new Error('Falha ao atualizar gateway');
            }

            // Invalidar query para atualizar dados
            queryClient.invalidateQueries({ queryKey: ['admin', 'organizations', 'gateways'] });

            toast({
                title: "Gateway atualizado com sucesso",
                variant: "success",
            });
        } catch (error) {
            toast({
                title: "Erro ao atualizar gateway",
                description: error instanceof Error ? error.message : "Erro desconhecido",
                variant: "error",
            });
        }
    }

    // Função para excluir gateway
    async function deleteGateway(organizationId: string, gatewayId: string) {
        try {
            const response = await fetch(`/api/admin/organizations/${organizationId}/gateways/${gatewayId}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                throw new Error('Falha ao excluir gateway');
            }

            // Invalidar query para atualizar dados
            queryClient.invalidateQueries({ queryKey: ['admin', 'organizations', 'gateways'] });

            toast({
                title: "Gateway excluído com sucesso",
                variant: "success",
            });
        } catch (error) {
            toast({
                title: "Erro ao excluir gateway",
                description: error instanceof Error ? error.message : "Erro desconhecido",
                variant: "error",
            });
        }
    }

    // Status de organização com componente visual
    function OrganizationStatusBadge({ status }: { status: string }) {
        switch (status) {
            case "APPROVED":
                return <Badge className="bg-green-500"><CheckCircle2 className="w-3 h-3 mr-1" /> Aprovada</Badge>;
            case "PENDING_REVIEW":
                return <Badge className="bg-amber-500"><AlertCircle className="w-3 h-3 mr-1" /> Pendente</Badge>;
            case "REJECTED":
                return <Badge className="bg-red-500"><XCircle className="w-3 h-3 mr-1" /> Rejeitada</Badge>;
            case "BLOCKED":
                return <Badge className="bg-gray-500"><XCircle className="w-3 h-3 mr-1" /> Bloqueada</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    }

    // Renderizar detalhes da organização selecionada
    function renderOrganizationDetails() {
        if (!selectedOrganization) return null;

        const organization = organizations.find((org: Organization) => org.id === selectedOrganization);
        if (!organization) return null;

        return (
            <Card className="mt-6">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Gateways de {organization.name}</span>
                        <OrganizationStatusBadge status={organization.status} />
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {organization.status !== "APPROVED" && (
                        <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
                            <AlertCircle className="inline-block w-4 h-4 mr-2" />
                            Esta organização não está aprovada. Os gateways configurados não estarão disponíveis até a aprovação.
                        </div>
                    )}

                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Taxas configuradas</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="p-3 bg-gray-50 rounded-md">
                                <div className="text-xs text-gray-500">Taxa de cobrança (%)</div>
                                <div className="text-lg">{organization.taxes?.pixChargePercentFee ?? 0}%</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-md">
                                <div className="text-xs text-gray-500">Taxa fixa de cobrança</div>
                                <div className="text-lg">R$ {(organization.taxes?.pixChargeFixedFee ?? 0).toFixed(2)}</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-md">
                                <div className="text-xs text-gray-500">Taxa de transferência (%)</div>
                                <div className="text-lg">{organization.taxes?.pixTransferPercentFee ?? 0}%</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-md">
                                <div className="text-xs text-gray-500">Taxa fixa de transferência</div>
                                <div className="text-lg">R$ {(organization.taxes?.pixTransferFixedFee ?? 0).toFixed(2)}</div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="font-medium">Gateways de Pagamento</h3>
                            <Button size="sm">
                                <PlusCircle className="w-4 h-4 mr-2" />
                                Adicionar Gateway
                            </Button>
                        </div>

                        {organization.paymentGateways && organization.paymentGateways.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Nome</TableHead>
                                        <TableHead>Tipo</TableHead>
                                        <TableHead>Instância</TableHead>
                                        <TableHead>Credenciais</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Padrão</TableHead>
                                        <TableHead>Prioridade</TableHead>
                                        <TableHead>Ações</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {organization.paymentGateways.map((gateway: PaymentGateway) => (
                                        <TableRow key={gateway.id}>
                                            <TableCell>{gateway.name}</TableCell>
                                            <TableCell>{gateway.type}</TableCell>
                                            <TableCell>{gateway.instanceNumber || 'N/A'}</TableCell>
                                            <TableCell>
                                                {gateway.credentials ? (
                                                    <div className="text-xs">
                                                        {gateway.credentials.clientId ? (
                                                            <div className="text-green-600">
                                                                ✅ {gateway.credentials.clientId.substring(0, 8)}...
                                                            </div>
                                                        ) : (
                                                            <div className="text-red-600">❌ Não configurado</div>
                                                        )}
                                                        {gateway.credentials.environment && (
                                                            <div className="text-gray-500">
                                                                {gateway.credentials.environment}
                                                            </div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="text-red-600 text-xs">❌ Sem credenciais</div>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <Switch
                                                    checked={gateway.isActive}
                                                    onCheckedChange={(checked) => {
                                                        updateGateway(organization.id, gateway.id, {
                                                            isActive: checked
                                                        });
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell className="">
                                                <Switch
                                                    checked={gateway.isDefault}
                                                    onCheckedChange={(checked) => {
                                                        if (checked) {
                                                            updateGateway(organization.id, gateway.id, {
                                                                isDefault: true
                                                            });
                                                        }
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell>{gateway.priority}</TableCell>
                                            <TableCell>
                                                <div className="flex space-x-2">
                                                    <Button variant="ghost" size="icon">
                                                        <Edit className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => deleteGateway(organization.id, gateway.id)}
                                                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                                    >
                                                        <TrashIcon className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="text-center   p-8 bg-gray-50 rounded-md">
                                <p className="text-gray-500">Nenhum gateway configurado para esta organização.</p>
                                <Button className="mt-4" variant="outline">Adicionar Primeiro Gateway</Button>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <PageHeader
                title="Gerenciamento de Gateways"
                subtitle="Configure os gateways de pagamento para as organizações"
            />

            <Card>
                <CardHeader>
                    <CardTitle>Organizações</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="mb-6">
                        <Label htmlFor="search">Buscar organização</Label>
                        <Input
                            id="search"
                            placeholder="Nome ou slug da organização"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="max-w-md"
                        />
                    </div>

                    {isLoading ? (
                        <p>Carregando organizações...</p>
                    ) : error ? (
                        <p className="text-red-500">Erro ao carregar organizações</p>
                    ) : (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Nome</TableHead>
                                    <TableHead>Slug</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Gateways</TableHead>
                                    <TableHead>Ações</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredOrganizations.map((org: Organization) => (
                                    <TableRow
                                        key={org.id}
                                        className={selectedOrganization === org.id ? "bg-primary/5" : ""}
                                    >
                                        <TableCell>{org.name}</TableCell>
                                        <TableCell>{org.slug}</TableCell>
                                        <TableCell>
                                            <OrganizationStatusBadge status={org.status} />
                                        </TableCell>
                                        <TableCell>{org.paymentGateways?.length || 0}</TableCell>
                                        <TableCell>
                                            <Button
                                                variant="ghost"
                                                onClick={() => setSelectedOrganization(
                                                    selectedOrganization === org.id ? null : org.id
                                                )}
                                            >
                                                {selectedOrganization === org.id ? "Fechar" : "Gerenciar"}
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </CardContent>
            </Card>

            {renderOrganizationDetails()}
        </div>
    );
}
