import { useQuery } from "@tanstack/react-query";

export interface OrganizationRevenueData {
  organizationId: string;
  organizationName: string;
  organizationSlug: string | null;
  organizationStatus: string;
  organizationCreatedAt: Date;
  totalRevenue: number;
  totalFees: number;
  transactionCount: number;
  averageTransaction: number;
  netRevenue: number;
}

export interface OrganizationsRevenueSummary {
  totalRevenue: number;
  totalFees: number;
  totalTransactions: number;
  averageRevenuePerOrg: number;
  averageTransactionsPerOrg: number;
  period: number;
  periodLabel: string;
}

export interface OrganizationsRevenueReport {
  organizations: OrganizationRevenueData[];
  summary: OrganizationsRevenueSummary;
}

export const useOrganizationsRevenueReport = (period: string = "30", limit: number = 10) => {
  return useQuery<OrganizationsRevenueReport>({
    queryKey: ["admin", "reports", "organizations-revenue", period, limit],
    queryFn: async () => {
      try {
        const response = await fetch(
          `/api/admin/reports/organizations-revenue?period=${period}&limit=${limit}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Organizations Revenue Report API Error:", {
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            errorText
          });

          let errorMessage = "Failed to load organizations revenue report";
          try {
            const errorJson = JSON.parse(errorText);
            if (errorJson.error) {
              errorMessage = `${errorMessage}: ${errorJson.error}`;
            }
          } catch (e) {
            console.error("Error parsing error response:", e);
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Organizations revenue report data received:", data);

        return data;
      } catch (error) {
        console.error("Error fetching organizations revenue report:", error);
        throw error;
      }
    },
    retry: 2,
    retryDelay: 1000,
    refetchInterval: 60000, // Refresh every minute
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    staleTime: 30000, // Consider data stale after 30 seconds
  });
}; 