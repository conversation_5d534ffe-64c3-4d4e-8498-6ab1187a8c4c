import { useQuery } from "@tanstack/react-query";

export interface AdminDashboardSummary {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
    fees: number;
    monthlyFees: number;
  };
  transactions: {
    total: number;
    monthly: number;
    growth: number;
    successRate: number;
    avgValue: number;
    byStatus: Record<string, number>;
  };
  refunds: {
    total: number;
    amount: number;
  };
  organizations: {
    total: number;
    active: number;
    pending: number;
    approved: number;
    byStatus: Record<string, number>;
  };
  recentActivity: Array<{
    id: string;
    customerName: string;
    customerEmail: string;
    amount: number;
    status: string;
    type: string;
    createdAt: string;
    paymentAt: string | null;
    organizationName: string;
    organizationSlug: string | null;
  }>;
  monthlyRevenueChart: Array<{
    month: string;
    revenue: number;
    fees: number;
    transactions: number;
  }>;
}

export const useAdminDashboard = (days: number = 30) => {
  return useQuery<AdminDashboardSummary>({
    queryKey: ["admin", "dashboard", "summary", days],
    queryFn: async () => {
      try {
        const response = await fetch(
          `/api/admin/dashboard/summary?days=${days}`,
          { credentials: 'include' }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Admin Dashboard API Error:", {
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            errorText
          });

          let errorMessage = "Failed to load admin dashboard data";
          try {
            const errorJson = JSON.parse(errorText);
            if (errorJson.error) {
              errorMessage = `${errorMessage}: ${errorJson.error}`;
            }
          } catch (e) {
            console.error("Error parsing error response:", e);
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Admin dashboard data received:", data);

        return data;
      } catch (error) {
        console.error("Error fetching admin dashboard summary:", error);
        throw error;
      }
    },
    retry: 2,
    retryDelay: 1000,
    refetchInterval: 30000, // Refresh every 30 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    staleTime: 15000, // Consider data stale after 15 seconds
  });
};
