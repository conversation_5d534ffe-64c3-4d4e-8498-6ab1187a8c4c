"use client";

import { Button } from "@ui/components/button";
import { Calendar, CalendarDays, CalendarRange } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

interface PeriodFilterProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

const periods = [
  { value: "1", label: "Hoje", icon: Calendar },
  { value: "7", label: "7 dias", icon: CalendarDays },
  { value: "15", label: "15 dias", icon: CalendarRange },
  { value: "30", label: "30 dias", icon: CalendarRange },
];

export function PeriodFilter({ selectedPeriod, onPeriodChange }: PeriodFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handlePeriodChange = (period: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("period", period);
    router.push(`?${params.toString()}`);
    onPeriodChange(period);
  };

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-muted-foreground">Período:</span>
      <div className="flex items-center space-x-1">
        {periods.map((period) => {
          const Icon = period.icon;
          const isSelected = selectedPeriod === period.value;
          return (
            <Button
              key={period.value}
              variant={isSelected ? "default" : "outline"}
              size="sm"
              onClick={() => handlePeriodChange(period.value)}
              className={`flex items-center space-x-1 transition-all duration-200 ${
                isSelected
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "hover:bg-muted hover:text-muted-foreground hover:shadow-sm"
              }`}
            >
              <Icon className="h-3 w-3" />
              <span>{period.label}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
