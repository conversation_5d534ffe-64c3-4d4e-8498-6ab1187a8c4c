"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { AdminDashboardSummary } from "../hooks/use-admin-dashboard";
import { formatCurrency } from "@shared/lib/format";
import { TrendingUp, DollarSign, CreditCard, Percent, BarChart3 } from "lucide-react";

interface AdminRevenueChartProps {
  data?: AdminDashboardSummary;
}

export function AdminRevenueChart({ data }: AdminRevenueChartProps) {
  const isLoading = !data;

  if (isLoading) {
    return (
      <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Análise de Receita</CardTitle>
          <CardDescription>Métricas financeiras da plataforma</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex h-[200px] w-full items-center justify-center">
            <div className="text-sm text-muted-foreground">Carregando dados...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const revenueMetrics = [
    {
      label: "Receita Total",
      value: formatCurrency(data.revenue.total),
      icon: <DollarSign className="h-4 w-4" />,
      color: "text-emerald-500"
    },
    {
      label: "Receita Mensal",
      value: formatCurrency(data.revenue.monthly),
      icon: <TrendingUp className="h-4 w-4" />,
      color: "text-blue-500"
    },
    {
      label: "Taxas Coletadas",
      value: formatCurrency(data.revenue.fees),
      icon: <Percent className="h-4 w-4" />,
      color: "text-purple-500"
    },
    {
      label: "Taxas Mensais",
      value: formatCurrency(data.revenue.monthlyFees),
      icon: <CreditCard className="h-4 w-4" />,
      color: "text-amber-500"
    }
  ];

  const transactionBreakdown = [
    {
      status: "Aprovadas",
      count: data.transactions.byStatus.approved || 0,
      color: "bg-emerald-500",
      percentage: data.transactions.total > 0 
        ? ((data.transactions.byStatus.approved || 0) / data.transactions.total * 100).toFixed(1)
        : "0"
    },
    {
      status: "Pendentes",
      count: data.transactions.byStatus.pending || 0,
      color: "bg-amber-500",
      percentage: data.transactions.total > 0 
        ? ((data.transactions.byStatus.pending || 0) / data.transactions.total * 100).toFixed(1)
        : "0"
    },
    {
      status: "Rejeitadas",
      count: data.transactions.byStatus.rejected || 0,
      color: "bg-rose-500",
      percentage: data.transactions.total > 0 
        ? ((data.transactions.byStatus.rejected || 0) / data.transactions.total * 100).toFixed(1)
        : "0"
    },
    {
      status: "Canceladas",
      count: data.transactions.byStatus.canceled || 0,
      color: "bg-gray-500",
      percentage: data.transactions.total > 0 
        ? ((data.transactions.byStatus.canceled || 0) / data.transactions.total * 100).toFixed(1)
        : "0"
    }
  ];

  return (
    <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <CardHeader>
        <CardTitle>Análise de Receita</CardTitle>
        <CardDescription>
          Métricas financeiras e distribuição de transações
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Revenue Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          {revenueMetrics.map((metric, index) => (
            <div key={index} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-800/30">
              <div className={`${metric.color}`}>
                {metric.icon}
              </div>
              <div>
                <div className="text-sm text-muted-foreground">{metric.label}</div>
                <div className={`font-semibold ${metric.color}`}>{metric.value}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Transaction Status Breakdown */}
        <div>
          <h4 className="text-sm font-medium mb-3">Distribuição de Transações</h4>
          <div className="space-y-3">
            {transactionBreakdown.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${item.color}`} />
                  <span className="text-sm">{item.status}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{item.count.toLocaleString()}</span>
                  <span className="text-xs text-muted-foreground">({item.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Monthly Revenue Trend */}
        {data.monthlyRevenueChart && data.monthlyRevenueChart.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              Tendência Mensal (Últimos 6 meses)
            </h4>
            <div className="space-y-2">
              {data.monthlyRevenueChart.map((month, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-800/20">
                  <span className="text-sm">{month.month}</span>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm font-medium">{formatCurrency(month.revenue)}</div>
                      <div className="text-xs text-muted-foreground">{month.transactions} transações</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Growth Indicator */}
        {data.revenue.growth !== 0 && (
          <div className="flex items-center justify-center p-3 rounded-lg bg-gray-800/30">
            <div className={`flex items-center space-x-2 ${
              data.revenue.growth >= 0 ? 'text-emerald-500' : 'text-rose-500'
            }`}>
              <TrendingUp className={`h-4 w-4 ${data.revenue.growth < 0 ? 'rotate-180' : ''}`} />
              <span className="text-sm font-medium">
                {data.revenue.growth >= 0 ? '+' : ''}{data.revenue.growth.toFixed(1)}%
                crescimento mensal
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
