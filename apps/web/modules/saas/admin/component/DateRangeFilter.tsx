"use client";

import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

 

interface DateRangeFilterProps {
  selectedDays: number;
  onDaysChange: (days: number) => void;
  className?: string;
}

const DATE_RANGE_OPTIONS = [
  { label: "7 dias", value: 7 },
  { label: "14 dias", value: 14 },
  { label: "30 dias", value: 30 },
];

export function DateRangeFilter({ 
  selectedDays, 
  onDaysChange, 
  className 
}: DateRangeFilterProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-sm font-medium text-muted-foreground">
        Período:
      </span>
      <div className="flex items-center gap-1">
        {DATE_RANGE_OPTIONS.map((option) => (
          <Button
            key={option.value}
            variant={selectedDays === option.value ? "default" : "outline"}
            size="sm"
            onClick={() => onDaysChange(option.value)}
            className={cn(
              "h-8 px-3 text-xs",
              selectedDays === option.value && "bg-primary text-primary-foreground"
            )}
          >
            {option.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
