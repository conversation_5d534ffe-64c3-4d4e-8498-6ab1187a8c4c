"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@ui/components/table";
import {
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  DollarSign,
  Building2,
  User,
  Calendar,
  Search,
  Filter,
  RefreshCw,
  Loader2
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetClose } from "@ui/components/sheet";
import { cn } from "@ui/lib";
import { badge } from "@ui/components/badge";

interface MedInfraction {
  id: string;
  externalId?: string;
  transactionId?: string;
  organizationId: string;
  status: string;
  type: string;
  reportedBy: string;
  creationDate: string;
  reportDetails?: string;
  analysisResult?: string;
  analysisDetails?: string;
  transactionAmount?: any;
  lastModificationDate: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
  organization?: {
    name: string;
    slug?: string;
  };
  transaction?: {
    id: string;
    customerName: string;
    customerEmail: string;
    amount: number;
    endToEndId?: string;
  };
}

export function MedInfractionList() {
  const [infractions, setInfractions] = useState<MedInfraction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedInfraction, setSelectedInfraction] = useState<MedInfraction | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  const fetchInfractions = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/med/infractions?page=${currentPage}&search=${searchTerm}&status=${statusFilter}`);

      if (!response.ok) {
        throw new Error('Erro ao carregar infrações');
      }

      const data = await response.json();
      setInfractions(data.infractions || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Erro ao carregar infrações:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInfractions();
  }, [currentPage, searchTerm, statusFilter]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'WAITING_PSP':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'BLOCKED':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'ANALYZED':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'CLOSED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REFUNDED':
        return <DollarSign className="h-4 w-4 text-green-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    let badgeStatus: 'warning' | 'success' | 'error' | 'refunded' = 'warning';
    let icon = getStatusIcon(status);
    let label = '';
    switch (status) {
      case 'WAITING_PSP':
        badgeStatus = 'warning';
        label = 'Aguardando PSP';
        break;
      case 'BLOCKED':
        badgeStatus = 'error';
        label = 'Bloqueado';
        break;
      case 'ANALYZED':
        badgeStatus = 'warning';
        label = 'Analisado';
        break;
      case 'CLOSED':
        badgeStatus = 'success';
        label = 'Fechado';
        break;
      case 'REFUNDED':
        badgeStatus = 'refunded';
        label = 'Devolvido';
        break;
      default:
        badgeStatus = 'warning';
        label = status;
    }
    return (
      <span className={cn(badge({ status: badgeStatus }), "flex items-center gap-1 px-3 py-0.5 text-xs font-semibold whitespace-nowrap min-w-[140px] max-w-[180px]")}
        style={{ lineHeight: '20px' }}>
        <span className="flex items-center h-4 w-4">{icon}</span>
        <span className="ml-1">{label}</span>
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ptBR });
  };

  const handleProcessInfraction = async (infractionId: string, action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`/api/admin/med/infractions/${infractionId}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        throw new Error('Erro ao processar infração');
      }

      // Recarregar a lista
      fetchInfractions();
    } catch (error) {
      console.error('Erro ao processar infração:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin mr-3" />
        <span className="text-lg">Carregando infrações MED...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por organização, cliente ou endToEndId..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="WAITING_PSP">Aguardando PSP</SelectItem>
                  <SelectItem value="BLOCKED">Bloqueado</SelectItem>
                  <SelectItem value="ANALYZED">Analisado</SelectItem>
                  <SelectItem value="CLOSED">Fechado</SelectItem>
                  <SelectItem value="REFUNDED">Devolvido</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Infrações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Infrações MED ({infractions.length})
            </div>
            <Button onClick={fetchInfractions} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
          </CardTitle>
          <CardDescription>
            Lista de infrações e solicitações de devolução via Mecanismo Especial de Devolução
          </CardDescription>
        </CardHeader>
        <CardContent>
          {infractions.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Nenhuma infração MED encontrada</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[150px]">Status</TableHead>
                    <TableHead>Organização</TableHead>
                    <TableHead className="min-w-[120px] max-w-[180px]">Cliente</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Data Criação</TableHead>
                    <TableHead>Detalhes</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {infractions.map((infraction) => (
                    <TableRow key={infraction.id}>
                      <TableCell className="min-w-[150px] align-middle">
                        {getStatusBadge(infraction.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="font-medium">
                            {infraction.organization?.name || 'N/A'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="min-w-[120px] max-w-[180px]">
                        <div className="flex flex-col">
                          <span className="font-medium truncate max-w-[160px]" title={infraction.transaction?.customerName || 'N/A'}>
                            {infraction.transaction?.customerName || 'N/A'}
                          </span>
                          <span className="text-sm text-gray-500 truncate max-w-[160px]" title={infraction.transaction?.customerEmail || 'N/A'}>
                            {infraction.transaction?.customerEmail || 'N/A'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                          <span className="font-medium">
                            {infraction.transactionAmount?.amount
                              ? formatCurrency(infraction.transactionAmount.amount)
                              : infraction.transaction?.amount
                                ? formatCurrency(infraction.transaction.amount)
                                : 'N/A'
                            }
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="text-sm">
                            {formatDate(infraction.creationDate)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <button
                          type="button"
                          className="w-full text-left max-w-xs flex items-center gap-2 cursor-pointer hover:bg-muted/30 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-primary/40"
                          onClick={() => { setSelectedInfraction(infraction); setDetailsOpen(true); }}
                          title="Ver detalhes da infração"
                        >
                          <p className="text-sm text-gray-600 line-clamp-2 max-w-[180px] whitespace-nowrap overflow-hidden text-ellipsis">
                            {infraction.reportDetails || 'Sem detalhes'}
                          </p>
                          <Eye className="h-4 w-4 text-primary" />
                        </button>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="primary"
                            onClick={() => handleProcessInfraction(infraction.id, 'approve')}
                            disabled={infraction.status !== 'WAITING_PSP'}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Aprovar
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleProcessInfraction(infraction.id, 'reject')}
                            disabled={infraction.status !== 'WAITING_PSP'}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Rejeitar
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paginação */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Anterior
          </Button>
          <span className="flex items-center px-4">
            Página {currentPage} de {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Próxima
          </Button>
        </div>
      )}

      {/* Sheet de detalhes da infração */}
      <Sheet open={detailsOpen} onOpenChange={setDetailsOpen}>
        <SheetContent side="right" className="max-w-lg w-full">
          <SheetHeader>
            <SheetTitle>Detalhes da Infração MED</SheetTitle>
            <SheetDescription>
              Veja todos os dados da infração e da transação relacionada.
            </SheetDescription>
          </SheetHeader>
          {selectedInfraction && (
            <div className="space-y-4 mt-4">
              <div>
                <span className="font-semibold">Status:</span> {getStatusBadge(selectedInfraction.status)}
              </div>
              <div>
                <span className="font-semibold">Organização:</span> {selectedInfraction.organization?.name || 'N/A'}
              </div>
              <div>
                <span className="font-semibold">Cliente:</span> {selectedInfraction.transaction?.customerName || 'N/A'}<br />
                <span className="text-xs text-gray-500">{selectedInfraction.transaction?.customerEmail || 'N/A'}</span>
              </div>
              <div>
                <span className="font-semibold">Valor:</span> {selectedInfraction.transactionAmount?.amount
                  ? formatCurrency(selectedInfraction.transactionAmount.amount)
                  : selectedInfraction.transaction?.amount
                    ? formatCurrency(selectedInfraction.transaction.amount)
                    : 'N/A'}
              </div>
              <div>
                <span className="font-semibold">Data Criação:</span> {formatDate(selectedInfraction.creationDate)}
              </div>
              <div>
                <span className="font-semibold">Detalhes:</span>
                <div className="bg-gray-50 rounded p-2 mt-1 text-sm text-gray-700 whitespace-pre-line">
                  {selectedInfraction.reportDetails || 'Sem detalhes'}
                </div>
              </div>
              {selectedInfraction.transaction?.endToEndId && (
                <div>
                  <span className="font-semibold">E2E:</span> {selectedInfraction.transaction.endToEndId}
                </div>
              )}
              {selectedInfraction.analysisResult && (
                <div>
                  <span className="font-semibold">Resultado da Análise:</span> {selectedInfraction.analysisResult}
                </div>
              )}
              {selectedInfraction.analysisDetails && (
                <div>
                  <span className="font-semibold">Detalhes da Análise:</span>
                  <div className="bg-gray-50 rounded p-2 mt-1 text-sm text-gray-700 whitespace-pre-line">
                    {selectedInfraction.analysisDetails}
                  </div>
                </div>
              )}
              <div>
                <span className="font-semibold">ID da Infração:</span> {selectedInfraction.externalId}
              </div>
              <div>
                <span className="font-semibold">ID da Transação:</span> {selectedInfraction.transactionId}
              </div>
              <div>
                <span className="font-semibold">Última Modificação:</span> {formatDate(selectedInfraction.lastModificationDate)}
              </div>
              <div>
                <span className="font-semibold">Processado em:</span> {selectedInfraction.processedAt ? formatDate(selectedInfraction.processedAt) : '-'}
              </div>
            </div>
          )}
          <SheetClose asChild>
            <Button className="mt-8 w-full" variant="outline">Fechar</Button>
          </SheetClose>
        </SheetContent>
      </Sheet>
    </div>
  );
}
