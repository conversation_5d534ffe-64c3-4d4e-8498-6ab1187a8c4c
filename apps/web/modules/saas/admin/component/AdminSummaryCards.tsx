"use client";

import { Card } from "@ui/components/card";
import { formatCurrency } from "@shared/lib/format";
import { 
  CircleDollarSign, 
  TrendingUp, 
  TrendingDown, 
  FileText, 
  Building2, 
  Users,
  BarChart3
} from "lucide-react";
import { AdminDashboardSummary } from "../hooks/use-admin-dashboard";

interface AdminSummaryCardsProps {
  data?: AdminDashboardSummary;
}

interface SummaryCardProps {
  icon: React.ReactNode;
  title: string;
  value: string;
  subtitle?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  bgColor: string;
  textColor: string;
}

function SummaryCard({ 
  icon, 
  title, 
  value, 
  subtitle, 
  trend, 
  bgColor, 
  textColor 
}: SummaryCardProps) {
  return (
    <Card className="overflow-hidden border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <div className="p-6">
        <div className="flex items-center justify-between mb-3">
          <div className={`rounded-full ${bgColor} p-2`}>
            {icon}
          </div>
          {trend && (
            <div className={`flex items-center text-sm ${trend.isPositive ? 'text-emerald-500' : 'text-rose-500'}`}>
              {trend.isPositive ? (
                <TrendingUp className="h-4 w-4 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 mr-1" />
              )}
              {trend.value}
            </div>
          )}
        </div>
        
        <div className={`font-bold text-2xl ${textColor} mb-1`}>
          {value}
        </div>
        
        <div className="text-sm text-muted-foreground">
          {title}
        </div>
        
        {subtitle && (
          <div className="text-xs text-muted-foreground mt-2">
            {subtitle}
          </div>
        )}
      </div>
    </Card>
  );
}

export function AdminSummaryCards({ data }: AdminSummaryCardsProps) {
  const isLoading = !data;

  const cards = [
    {
      icon: <CircleDollarSign className="size-5 text-emerald-500" />,
      title: "Receita Total",
      value: isLoading ? "..." : formatCurrency(data.revenue.total),
      subtitle: isLoading ? "..." : `Mensal: ${formatCurrency(data.revenue.monthly)}`,
      trend: isLoading ? undefined : {
        value: `${data.revenue.growth.toFixed(1)}%`,
        isPositive: data.revenue.growth >= 0
      },
      bgColor: "bg-emerald-500/10",
      textColor: "text-emerald-500"
    },
    {
      icon: <FileText className="size-5 text-blue-500" />,
      title: "Total de Transações",
      value: isLoading ? "..." : data.transactions.total.toLocaleString(),
      subtitle: isLoading ? "..." : `Ticket médio: ${formatCurrency(data.transactions.avgValue)}`,
      trend: isLoading ? undefined : {
        value: `${data.transactions.growth.toFixed(1)}%`,
        isPositive: data.transactions.growth >= 0
      },
      bgColor: "bg-blue-500/10",
      textColor: "text-blue-500"
    },
    {
      icon: <Building2 className="size-5 text-purple-500" />,
      title: "Organizações",
      value: isLoading ? "..." : data.organizations.total.toLocaleString(),
      subtitle: isLoading ? "..." : `${data.organizations.active} ativas`,
      bgColor: "bg-purple-500/10",
      textColor: "text-purple-500"
    },
    {
      icon: <BarChart3 className="size-5 text-amber-500" />,
      title: "Taxa de Sucesso",
      value: isLoading ? "..." : `${data.transactions.successRate.toFixed(1)}%`,
      subtitle: isLoading ? "..." : `${data.transactions.byStatus.approved || 0} aprovadas`,
      bgColor: "bg-amber-500/10",
      textColor: "text-amber-500"
    },
    {
      icon: <CircleDollarSign className="size-5 text-rose-500" />,
      title: "Estornos",
      value: isLoading ? "..." : formatCurrency(data.refunds.amount),
      subtitle: isLoading ? "..." : `${data.refunds.total} transações`,
      bgColor: "bg-rose-500/10",
      textColor: "text-rose-500"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {cards.map((card, index) => (
        <SummaryCard
          key={index}
          icon={card.icon}
          title={card.title}
          value={card.value}
          subtitle={card.subtitle}
          trend={card.trend}
          bgColor={card.bgColor}
          textColor={card.textColor}
        />
      ))}
    </div>
  );
}
