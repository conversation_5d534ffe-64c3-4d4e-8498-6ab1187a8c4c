"use client";

import { useState, useEffect } from "react";
import { useOrganizationsRevenueReport } from "../hooks/use-organizations-revenue-report";
import { PeriodFilter } from "./PeriodFilter";
import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { formatCurrency } from "@shared/lib/format";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts";
import {
  Loader2,
  TrendingUp,
  Building2,
  DollarSign,
  CreditCard,
  Users,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  ChartAreaIcon
} from "lucide-react";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF6B6B', '#4ECDC4', '#45B7D1'];

export function OrganizationsRevenueReport() {
  const searchParams = useSearchParams();
  const [selectedPeriod, setSelectedPeriod] = useState(() => {
    return searchParams.get("period") || "30";
  });

  const { data: reportData, isLoading, isError, error } = useOrganizationsRevenueReport(selectedPeriod, 10);

  // Update selectedPeriod when URL changes
  useEffect(() => {
    const periodFromUrl = searchParams.get("period");
    if (periodFromUrl && periodFromUrl !== selectedPeriod) {
      setSelectedPeriod(periodFromUrl);
    }
  }, [searchParams, selectedPeriod]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin mr-3" />
        <span className="text-lg">Carregando relatório...</span>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="p-8 text-center">
        <div className="mb-4 text-rose-500 text-xl">Erro ao carregar relatório</div>
        <div className="text-muted-foreground mb-6">
          {error instanceof Error ? error.message : "Ocorreu um erro desconhecido"}
        </div>
        <div className="text-sm text-muted-foreground">
          Tente recarregar a página. Se o problema persistir, entre em contato com o suporte.
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="p-8 text-center">
        <div className="text-muted-foreground">Nenhum dado disponível</div>
      </div>
    );
  }

  // Prepare data for charts
  const barChartData = reportData.organizations.map((org, index) => ({
    name: org.organizationName.length > 15
      ? org.organizationName.substring(0, 15) + '...'
      : org.organizationName,
    revenue: org.totalRevenue,
    fees: org.totalFees,
    transactions: org.transactionCount,
    fullName: org.organizationName
  }));

  const pieChartData = reportData.organizations.map((org, index) => ({
    name: org.organizationName.length > 20
      ? org.organizationName.substring(0, 20) + '...'
      : org.organizationName,
    value: org.totalRevenue,
    fullName: org.organizationName
  }));

  const lineChartData = reportData.organizations.slice(0, 5).map((org, index) => ({
    name: org.organizationName.length > 12
      ? org.organizationName.substring(0, 12) + '...'
      : org.organizationName,
    revenue: org.totalRevenue,
    transactions: org.transactionCount,
    fullName: org.organizationName
  }));

  const summaryMetrics = [
    {
      label: "Receita Total",
      value: formatCurrency(reportData.summary.totalRevenue),
      icon: <DollarSign className="h-4 w-4" />,
      color: "text-emerald-500"
    },
    {
      label: "Taxas Totais",
      value: reportData.summary.totalFees > 0 ? formatCurrency(reportData.summary.totalFees) : "R$ 0,00",
      subtitle: reportData.summary.totalFees === 0 ? "Sem taxas cobradas" : undefined,
      icon: <CreditCard className="h-4 w-4" />,
      color: reportData.summary.totalFees > 0 ? "text-blue-500" : "text-muted-foreground"
    },
    {
      label: "Transações",
      value: reportData.summary.totalTransactions.toLocaleString(),
      icon: <BarChart3 className="h-4 w-4" />,
      color: "text-purple-500"
    },
    {
      label: "Média por Org",
      value: formatCurrency(reportData.summary.averageRevenuePerOrg),
      icon: <Building2 className="h-4 w-4" />,
      color: "text-amber-500"
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'APPROVED': { label: 'Aprovada', className: 'bg-green-500 text-white' },
      'PENDING_REVIEW': { label: 'Pendente', className: 'bg-yellow-500 text-white' },
      'REJECTED': { label: 'Rejeitada', className: 'bg-red-500 text-white' },
      'BLOCKED': { label: 'Bloqueada', className: 'bg-red-500 text-white' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, className: 'bg-gray-500 text-white' };
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header with Period Filter */}
      <div className="flex justify-between items-center">
        <div>
          {/* <h2 className="text-2xl font-bold">Relatório de Receita por Organização</h2> */}
          <p className="text-muted-foreground flex items-center gap-2">
           <ChartAreaIcon />  Top 10 organizações que mais faturaram nos últimos <b>{reportData.summary.periodLabel}</b>
          </p>
        </div>
        <PeriodFilter
          selectedPeriod={selectedPeriod}
          onPeriodChange={setSelectedPeriod}
        />
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryMetrics.map((metric, index) => (
          <Card key={index} className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`${metric.color}`}>
                  {metric.icon}
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">{metric.label}</div>
                  <div className={`font-semibold ${metric.color}`}>{metric.value}</div>
                  {metric.subtitle && (
                    <div className="text-xs text-muted-foreground mt-1">{metric.subtitle}</div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bar Chart - Revenue by Organization */}
        <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Receita por Organização
            </CardTitle>
            <CardDescription>
              Comparação de receita entre as principais organizações
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={barChartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="name"
                  stroke="#9CA3AF"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  stroke="#9CA3AF"
                  fontSize={12}
                  tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                  formatter={(value: any, name: any) => [
                    formatCurrency(value),
                    name === 'revenue' ? 'Receita' : name === 'fees' ? 'Taxas' : 'Transações'
                  ]}
                  labelFormatter={(label) => {
                    const org = barChartData.find(item => item.name === label);
                    return org?.fullName || label;
                  }}
                />
                <Bar dataKey="revenue" fill="#10B981" name="Receita" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Pie Chart - Revenue Distribution */}
        <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChartIcon className="h-5 w-5 mr-2" />
              Distribuição de Receita
            </CardTitle>
            <CardDescription>
              Percentual de receita por organização
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                  formatter={(value: any) => [formatCurrency(value), 'Receita']}
                  labelFormatter={(label) => {
                    const org = pieChartData.find(item => item.name === label);
                    return org?.fullName || label;
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Line Chart - Top 5 Organizations Trend */}
      <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Top 5 Organizações - Receita vs Transações
          </CardTitle>
          <CardDescription>
            Comparação entre receita e volume de transações
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={lineChartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                dataKey="name"
                stroke="#9CA3AF"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis
                stroke="#9CA3AF"
                fontSize={12}
                yAxisId="left"
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
              />
              <YAxis
                stroke="#9CA3AF"
                fontSize={12}
                yAxisId="right"
                orientation="right"
                tickFormatter={(value) => `${value}`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
                formatter={(value: any, name: any) => [
                  name === 'revenue' ? formatCurrency(value) : value,
                  name === 'revenue' ? 'Receita' : 'Transações'
                ]}
                labelFormatter={(label) => {
                  const org = lineChartData.find(item => item.name === label);
                  return org?.fullName || label;
                }}
              />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#10B981"
                strokeWidth={2}
                yAxisId="left"
                name="Receita"
              />
              <Line
                type="monotone"
                dataKey="transactions"
                stroke="#3B82F6"
                strokeWidth={2}
                yAxisId="right"
                name="Transações"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Table */}
      <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Detalhamento por Organização
          </CardTitle>
          <CardDescription>
            Lista completa com métricas detalhadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-3 font-medium">Organização</th>
                  <th className="text-right p-3 font-medium">Status</th>
                  <th className="text-right p-3 font-medium">Receita Total</th>
                  <th className="text-right p-3 font-medium">Taxas</th>
                  <th className="text-right p-3 font-medium">Receita Líquida</th>
                  <th className="text-right p-3 font-medium">Transações</th>
                  <th className="text-right p-3 font-medium">Ticket Médio</th>
                </tr>
              </thead>
              <tbody>
                {reportData.organizations.map((org, index) => (
                  <tr key={org.organizationId} className="border-b border-gray-800/50 hover:bg-gray-800/20">
                    <td className="p-3">
                      <div>
                        <div className="font-medium">{org.organizationName}</div>
                        <div className="text-xs text-muted-foreground">
                          Criada em {new Date(org.organizationCreatedAt).toLocaleDateString('pt-BR')}
                        </div>
                      </div>
                    </td>
                    <td className="p-3 text-right">
                      {getStatusBadge(org.organizationStatus)}
                    </td>
                    <td className="p-3 text-right font-medium text-emerald-500">
                      {formatCurrency(org.totalRevenue)}
                    </td>
                    <td className="p-3 text-right text-blue-500">
                      {formatCurrency(org.totalFees)}
                    </td>
                    <td className="p-3 text-right font-medium text-purple-500">
                      {formatCurrency(org.netRevenue)}
                    </td>
                    <td className="p-3 text-right">
                      {org.transactionCount.toLocaleString()}
                    </td>
                    <td className="p-3 text-right text-amber-500">
                      {formatCurrency(org.averageTransaction)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
