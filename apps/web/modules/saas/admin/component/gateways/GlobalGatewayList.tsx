"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { getAdminPath } from "@saas/admin/lib/links";
import { adminGatewaysQueryKey, useAdminGlobalGatewaysQuery } from "@saas/admin/lib/api";
import { EditIcon, MoreVerticalIcon, PlusIcon, TrashIcon, ChevronLeftIcon, ChevronRightIcon, Star } from "lucide-react";
import Link from "next/link";
import { useToast } from "@ui/hooks/use-toast";

const ITEMS_PER_PAGE = 10;

// Interface para os tipos de gateway
interface Gateway {
  id: string;
  name: string;
  type: string;
  displayName?: string;
  description?: string;
  instanceNumber?: number;
  isActive: boolean;
  isDefault: boolean;
  isGlobal: boolean;
  adminConfigured: boolean;
  organization?: {
    name: string;
    slug: string | null;
  };
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  priority: number;
  credentials: any;
  canReceive: boolean;
  canSend: boolean;
}

export function GlobalGatewayList() {
  const t = useTranslations();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);

  const { data, isLoading, error } = useAdminGlobalGatewaysQuery();

  const gateways = data?.gateways || [];
  const totalPages = Math.ceil(gateways.length / ITEMS_PER_PAGE);
  const paginatedGateways = gateways.slice(
    (page - 1) * ITEMS_PER_PAGE,
    page * ITEMS_PER_PAGE
  );

  const updateGateway = async (id: string, data: Partial<Gateway>) => {
    try {
      const response = await fetch(`/api/admin/gateways/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to update gateway");
      }

      toast({
        title: "Gateway atualizado",
        description: "Gateway atualizado com sucesso.",
      });

      queryClient.invalidateQueries({
        queryKey: adminGatewaysQueryKey,
      });
    } catch (error) {
      toast({
        title: "Erro ao atualizar",
        description: "Erro ao atualizar o gateway.",
        variant: "destructive",
      });
    }
  };

  const deleteGateway = async (id: string) => {
    if (!confirm("Tem certeza que deseja excluir este gateway?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/gateways/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete gateway");
      }

      toast({
        title: "Gateway excluído",
        description: "Gateway excluído com sucesso.",
      });

      queryClient.invalidateQueries({
        queryKey: adminGatewaysQueryKey,
      });
    } catch (error) {
      toast({
        title: "Erro ao excluir",
        description: "Erro ao excluir o gateway.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Gateways Globais</CardTitle>
          <CardDescription>
            Gerencie os gateways de pagamento globais do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Carregando...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Gateways Globais</CardTitle>
          <CardDescription>
            Gerencie os gateways de pagamento globais do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 p-4 rounded-md text-red-600">
            Erro ao carregar gateways
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Gateways Globais</CardTitle>
          <CardDescription>
            Gerencie os gateways de pagamento globais do sistema
          </CardDescription>
        </div>
        <Button asChild>
          <Link href={getAdminPath("/gateways/new?global=true")}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Adicionar Gateway
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {paginatedGateways.length > 0 ? (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Capacidades</TableHead>
                  <TableHead>Ativo</TableHead>
                  <TableHead>Padrão</TableHead>
                  <TableHead>Prioridade</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedGateways.map((gateway) => (
                  <TableRow key={gateway.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span>
                          {gateway.displayName || gateway.name}
                          {gateway.instanceNumber && ` #${gateway.instanceNumber}`}
                        </span>
                        {gateway.description && (
                          <span className="text-xs text-muted-foreground mt-1">
                            {gateway.description}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{gateway.type}</TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {gateway.canReceive && (
                          <Badge variant="outline" className="bg-green-50">
                            Receber
                          </Badge>
                        )}
                        {gateway.canSend && (
                          <Badge variant="outline" className="bg-blue-50">
                            Enviar
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={gateway.isActive}
                        onCheckedChange={(checked) => {
                          updateGateway(gateway.id, { isActive: checked });
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={gateway.isDefault}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateGateway(gateway.id, {
                              isDefault: true
                            });
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span>{gateway.priority}</span>
                        {gateway.priority === 1 && (
                          <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVerticalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={getAdminPath(`/gateways/${gateway.id}`)}>
                              <EditIcon className="mr-2 h-4 w-4" />
                              Editar
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => deleteGateway(gateway.id)}
                            className="text-red-600"
                          >
                            <TrashIcon className="mr-2 h-4 w-4" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {totalPages > 1 && (
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                  disabled={page === 1}
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </Button>
                <span className="text-sm">
                  Página {page} de {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={page === totalPages}
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-muted-foreground mb-4">
              Nenhum gateway configurado
            </p>
            <Button asChild>
              <Link href={getAdminPath("/gateways/new?global=true")}>
                <PlusIcon className="mr-2 h-4 w-4" />
                Adicionar Primeiro Gateway
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
