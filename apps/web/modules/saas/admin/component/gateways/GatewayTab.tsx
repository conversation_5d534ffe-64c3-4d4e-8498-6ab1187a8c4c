"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Trash2, Star, Plus } from "lucide-react";
import { useToast } from "@ui/hooks/use-toast";
import { useRouter } from "next/navigation";

interface Gateway {
  id: string;
  name: string;
  type: string;
  displayName?: string;
  instanceNumber?: number;
  isDefault: boolean;
  isActive: boolean;
  canReceive: boolean;
  canSend: boolean;
  description?: string;
  priority: number;
  relationId?: string;
}

interface GatewayTabProps {
  organizationId: string;
}

export function GatewayTab({ organizationId }: GatewayTabProps) {
  const [assignedGateways, setAssignedGateways] = useState<Gateway[]>([]);
  const [availableGateways, setAvailableGateways] = useState<Gateway[]>([]);
  const [selectedGateway, setSelectedGateway] = useState<string>("");
  const [isDefaultGateway, setIsDefaultGateway] = useState(false);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const router = useRouter();

  // Fetch organization gateways
  useEffect(() => {
    fetchGateways();
  }, [organizationId]);

  const fetchGateways = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/admin/organizations/${organizationId}/gateways`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('API Response:', data);
        
        // ✅ FIX: Properly handle API response structure
        const assignedGws = Array.isArray(data.assignedGateways) ? data.assignedGateways : [];
        const availableGws = Array.isArray(data.availableGateways) ? data.availableGateways : [];
        
        setAssignedGateways(assignedGws);
        setAvailableGateways(availableGws);
      } else {
        console.error('Failed to fetch gateways:', response.status);
        const errorData = await response.json().catch(() => ({}));
        console.error('Error details:', errorData);
        
        setAssignedGateways([]);
        setAvailableGateways([]);
        
        toast({
          title: "Erro",
          description: errorData.error || "Erro ao carregar gateways",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error fetching gateways:', error);
      setAssignedGateways([]);
      setAvailableGateways([]);
      toast({
        title: "Erro",
        description: "Erro ao carregar gateways",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getGatewayDisplayName = (gateway: Gateway) => {
    if (gateway.displayName) {
      return gateway.displayName;
    }
    
    if (gateway.instanceNumber && gateway.instanceNumber > 1) {
      return `${gateway.name} (Instância ${gateway.instanceNumber})`;
    }
    
    return gateway.name;
  };

  const assignGateway = async () => {
    if (!selectedGateway) {
      console.log("❌ No gateway selected");
      return;
    }

    if (!organizationId) {
      console.log("❌ No organization ID");
      toast({
        title: "Erro",
        description: "ID da organização não encontrado.",
        variant: "destructive",
      });
      return;
    }

    try {
      // ✅ ENHANCED: Validate payload before sending
      const payload = {
        gatewayId: String(selectedGateway),
        isActive: Boolean(true),
        isDefault: Boolean(isDefaultGateway),
        priority: Number(10),
      };

      // ✅ CRITICAL: Validate payload structure
      if (!payload.gatewayId || typeof payload.gatewayId !== 'string') {
        throw new Error("Invalid gateway ID");
      }

      console.log('🚀 Assigning gateway with payload:', JSON.stringify(payload, null, 2));
      console.log('🎯 Target organization:', organizationId);

      const response = await fetch(`/api/admin/organizations/${organizationId}/gateways`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error response text:', errorText);
        
        let errorData;
        try {
          errorData = JSON.parse(errorText);
          console.error('❌ Parsed error data:', errorData);
        } catch {
          errorData = { error: errorText || `HTTP ${response.status}` };
        }
        
        // ✅ ENHANCED: More specific error messages
        const errorMessage = errorData.details || errorData.error || `Server error (${response.status})`;
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('✅ Gateway assignment successful:', JSON.stringify(result, null, 2));

      toast({
        title: "Gateway atribuído",
        description: "Gateway atribuído à organização com sucesso.",
      });

      setSelectedGateway("");
      setIsDefaultGateway(false);
      await fetchGateways(); // Refresh data
    } catch (error) {
      console.error('❌ Error assigning gateway:', error);
      
      // ✅ ENHANCED: Better error categorization
      let errorMessage = "Não foi possível atribuir o gateway.";
      
      if (error instanceof Error) {
        if (error.message.includes("payload")) {
          errorMessage = "Erro de dados na requisição. Tente novamente.";
        } else if (error.message.includes("Unauthorized")) {
          errorMessage = "Você não tem permissão para esta operação.";
        } else if (error.message.includes("not found")) {
          errorMessage = "Gateway ou organização não encontrados.";
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Erro",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const removeGateway = async (gatewayId: string) => {
    try {
      const response = await fetch(`/api/admin/organizations/${organizationId}/gateways`, {
        method: 'DELETE',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ gatewayId }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Erro ao remover gateway');
      }

      toast({
        title: "Gateway removido",
        description: "Gateway removido da organização com sucesso.",
      });

      await fetchGateways(); // Refresh data
    } catch (error) {
      console.error('Error removing gateway:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível remover o gateway.",
        variant: "destructive",
      });
    }
  };

  const setAsDefault = async (gatewayId: string) => {
    try {
      const payload = {
        gatewayId,
        isDefault: true,
      };

      console.log('Setting gateway as default with payload:', payload);

      const response = await fetch(`/api/admin/organizations/${organizationId}/gateways`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Erro ao definir gateway padrão');
      }

      toast({
        title: "Gateway padrão definido",
        description: "Gateway definido como padrão com sucesso.",
      });

      await fetchGateways(); // Refresh data
    } catch (error) {
      console.error('Error setting default gateway:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível definir o gateway como padrão.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Carregando gateways...</div>
        </CardContent>
      </Card>
    );
  }

  // ✅ FIX: Add type guards for safety
  const safeAssignedGateways = Array.isArray(assignedGateways) ? assignedGateways : [];
  const safeAvailableGateways = Array.isArray(availableGateways) ? availableGateways : [];

  // Filter available gateways (exclude already assigned ones)
  const assignedGatewayIds = safeAssignedGateways.map(g => g.id);
  const unassignedGateways = safeAvailableGateways.filter(g => !assignedGatewayIds.includes(g.id));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gateways de Pagamento</CardTitle>
        <p className="text-sm text-muted-foreground">
          Configure os gateways da organização
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Assigned Gateways */}
        <div>
          <h3 className="text-lg font-medium mb-4">Gateways Atribuídos</h3>
          {safeAssignedGateways.length > 0 ? (
            <div className="space-y-3">
              {safeAssignedGateways.map((gateway) => (
                <div key={gateway.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{getGatewayDisplayName(gateway)}</span>
                      {gateway.isDefault && (
                        <Badge variant="default" className="bg-green-100 flex items-center text-green-800">
                          <Star className="w-3 h-3 mr-1" />
                          Padrão
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Integração: {gateway.type}
                    
                    </div>
                    <div className="flex gap-2 mt-1">
                      {gateway.canReceive && (
                        <Badge variant="outline" className="bg-green-50 text-black">Receber</Badge>
                      )}
                      {gateway.canSend && (
                        <Badge variant="outline" className="bg-blue-50  text-black">Enviar</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {!gateway.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setAsDefault(gateway.id)}
                      >
                        Definir como padrão
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeGateway(gateway.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">Nenhum gateway atribuído a esta organização.</p>
          )}
        </div>

        {/* Assign New Gateway */}
        {unassignedGateways.length > 0 && (
          <div>
            <h3 className="text-lg font-medium mb-4">Atribuir Gateway</h3>
            <div className="flex items-center gap-4">
              <Select value={selectedGateway} onValueChange={setSelectedGateway}>
                <SelectTrigger className="w-[300px]">
                  <SelectValue placeholder="Selecione um gateway" />
                </SelectTrigger>
                <SelectContent>
                  {unassignedGateways.map((gateway) => (
                    <SelectItem key={gateway.id} value={gateway.id}>
                      {getGatewayDisplayName(gateway)} ({gateway.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex items-center space-x-2">
                <Switch
                  id="default-gateway"
                  checked={isDefaultGateway}
                  onCheckedChange={setIsDefaultGateway}
                />
                <label htmlFor="default-gateway" className="text-sm">
                  Definir como gateway padrão
                </label>
              </div>

              <Button onClick={assignGateway} disabled={!selectedGateway}>
                <Plus className="w-4 h-4 mr-2" />
                Atribuir Gateway
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
