"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
// import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { useConfirmationAlert } from "@saas/shared/components/ConfirmationAlertProvider";
import { Spinner } from "@shared/components/Spinner";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
// import { Badge } from "@ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { getAdminPath } from "@saas/admin/lib/links";
import { adminGatewaysQueryKey, useAdminGatewaysQuery } from "@saas/admin/lib/api";
import { EditIcon, MoreVerticalIcon, PlusIcon, TrashIcon, ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import Link from "next/link";
import { useToast } from "@ui/hooks/use-toast";

const ITEMS_PER_PAGE = 10;

// Interface para os tipos de gateway
interface Gateway {
  id: string;
  name: string;
  type: string;
  displayName?: string;
  description?: string;
  instanceNumber?: number;
  isActive: boolean;
  isDefault: boolean;
  organization?: {
    name: string;
    slug: string | null;
  };
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  priority: number;
  credentials: any;
  canReceive: boolean;
  canSend: boolean;
}

// Componente de paginação interno
function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) {
  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="h-8 w-8"
      >
        <ChevronLeftIcon className="h-4 w-4" />
      </Button>

      <span className="text-sm text-muted-foreground">
        Página {currentPage} de {totalPages}
      </span>

      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="h-8 w-8"
      >
        <ChevronRightIcon className="h-4 w-4" />
      </Button>
    </div>
  );
}

export function GatewayList() {
  const t = useTranslations();
  const { toast } = useToast();
  const { confirm } = useConfirmationAlert();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const getPathWithBackToParemeter = (path: string) => {
    return `${path}?backTo=${encodeURIComponent("/app/admin/gateways")}`;
  };

  const getGatewayEditPath = (id: string) => {
    return getPathWithBackToParemeter(getAdminPath(`/gateways/${id}`));
  };

  useEffect(() => {
    setDebouncedSearchTerm(searchTerm);
  }, [searchTerm]);

  const { data, isLoading } = useAdminGatewaysQuery({
    itemsPerPage: ITEMS_PER_PAGE,
    currentPage,
    searchTerm: debouncedSearchTerm,
  });

  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  const deleteGateway = async (id: string) => {
    const deleteGatewayToast = toast({
      variant: "loading",
      title: t("admin.gateways.deleting"),
    });
    try {
      const response = await fetch(`/api/admin/gateways/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete gateway");
      }

      deleteGatewayToast.update({
        id: deleteGatewayToast.id,
        variant: "success",
        title: t("admin.gateways.deleteGateway.deleted"),
        duration: 5000,
      });

      queryClient.invalidateQueries({
        queryKey: adminGatewaysQueryKey,
      });
    } catch {
      deleteGatewayToast.update({
        id: deleteGatewayToast.id,
        variant: "error",
        title: t("admin.gateways.deleteGateway.notDeleted"),
        duration: 5000,
      });
    }
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("admin.gateways.columns.name"),
        cell: ({
          row: {
            original: { id, name, type, displayName, instanceNumber, description },
          },
        }: {
          row: {
            original: Gateway;
          };
        }) => (
          <div className="flex flex-col">
            <Link href={getGatewayEditPath(id)} className="font-medium hover:underline">
              {displayName || name}
              {instanceNumber && ` #${instanceNumber}`}
            </Link>
            <span className="text-xs text-muted-foreground">{type}</span>
            {description && (
              <span className="text-xs text-muted-foreground mt-1">{description}</span>
            )}
          </div>
        ),
      },
      {
        accessorKey: "organization",
        header: t("admin.gateways.columns.organization"),
        cell: ({
          row: {
            original: { organization },
          },
        }: {
          row: {
            original: Gateway;
          };
        }) => (
          <div className="flex flex-col">
            <span className="font-medium">{organization?.name}</span>
            <span className="text-xs text-muted-foreground">{organization?.slug}</span>
          </div>
        ),
      },
      {
        accessorKey: "status",
        header: t("admin.gateways.columns.status"),
        cell: ({
          row: {
            original: { isActive, isDefault },
          },
        }: {
          row: {
            original: Gateway;
          };
        }) => (
          <div className="flex flex-wrap gap-2">
            {isActive ? (
              <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                {t("admin.gateways.status.active")}
              </span>
            ) : (
              <span className="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                {t("admin.gateways.status.inactive")}
              </span>
            )}
            {isDefault && (
              <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                {t("admin.gateways.status.default")}
              </span>
            )}
          </div>
        ),
      },
      {
        accessorKey: "actions",
        header: "",
        cell: ({
          row: {
            original: { id },
          },
        }: {
          row: {
            original: Gateway;
          };
        }) => {
          return (
            <div className="flex flex-row justify-end gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="icon" variant="ghost">
                    <MoreVerticalIcon className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem asChild>
                    <Link
                      href={getGatewayEditPath(id)}
                      className="flex items-center"
                    >
                      <EditIcon className="mr-2 size-4" />
                      {t("admin.gateways.edit")}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      confirm({
                        title: t("admin.gateways.confirmDelete.title"),
                        message: t("admin.gateways.confirmDelete.message"),
                        confirmLabel: t(
                          "admin.gateways.confirmDelete.confirm",
                        ),
                        destructive: true,
                        onConfirm: () => deleteGateway(id),
                      })
                    }
                  >
                    <span className="flex items-center text-destructive hover:text-destructive">
                      <TrashIcon className="mr-2 size-4" />
                      {t("admin.gateways.delete")}
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
      },
    ],
    [t],
  );

  const gateways = useMemo(
    () => data?.gateways ?? [],
    [data?.gateways],
  );

  return (
    <Card className="p-6">
      <div className="mb-4 flex items-center justify-between gap-6">
        <h2 className="font-semibold text-2xl">
          {t("admin.gateways.title")}
        </h2>

        <Button asChild>
          <Link href={getAdminPath("/gateways/new")}>
            <PlusIcon className="mr-1.5 size-4" />
            {t("admin.gateways.create")}
          </Link>
        </Button>
      </div>
      <Input
        type="search"
        placeholder={t("admin.gateways.search")}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="mb-4"
      />

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Spinner />
        </div>
      ) : gateways.length > 0 ? (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {columns.map((column) => (
                    <TableHead key={column.accessorKey}>
                      {column.header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {gateways.map((gateway) => (
                  <TableRow key={gateway.id}>
                    {columns.map((column) => (
                      <TableCell key={`${gateway.id}-${column.accessorKey}`}>
                        {column.cell({ row: { original: gateway } })}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="mt-4 flex justify-end">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil((data?.total ?? 0) / ITEMS_PER_PAGE)}
              onPageChange={setCurrentPage}
            />
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center rounded-md border border-dashed p-8">
          <h3 className="text-lg font-medium">
            {t("admin.gateways.noGateways")}
          </h3>
          <p className="text-sm text-muted-foreground">
            {t("admin.gateways.noGatewaysDescription")}
          </p>
          <Button className="mt-4" asChild>
            <Link href={getAdminPath("/gateways/new")}>
              <PlusIcon className="mr-1.5 size-4" />
              {t("admin.gateways.create")}
            </Link>
          </Button>
        </div>
      )}
    </Card>
  );
}
