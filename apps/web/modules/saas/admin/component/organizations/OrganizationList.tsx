"use client";

import { authClient } from "@repo/auth/client";
import {
	adminOrganizationsQueryKey,
	useAdminOrganizationsQuery,
} from "@saas/admin/lib/api";
import { getAdminPath } from "@saas/admin/lib/links";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { useConfirmationAlert } from "@saas/shared/components/ConfirmationAlertProvider";
import { Pagination } from "@saas/shared/components/Pagination";
import { Spinner } from "@shared/components/Spinner";
import { useQueryClient, useQuery } from "@tanstack/react-query";
// Importação de ColumnDef removida pois não está sendo usada
// Importações de react-table removidas pois não estão sendo usadas
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { EditIcon, MoreVerticalIcon, PlusIcon, TrashIcon, CheckCircle2, AlertCircle, XCircle, UserPlus, Settings } from "lucide-react";
// Importação de useTranslations removida pois não está sendo utilizada
import Link from "next/link";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { useEffect, useMemo, useState } from "react";
import { withQuery } from "ufo";
import { useDebounceValue } from "usehooks-ts";
import { Badge } from "@ui/components/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { SimpleOrganizationModal } from "./SimpleOrganizationModal";

const ITEMS_PER_PAGE = 9;

function usePendingOrganizationsQuery() {
	return useQuery({
		queryKey: ['admin', 'organizations', 'pending'],
		queryFn: async () => {
			const response = await fetch('/api/admin/organizations/pending');
			if (!response.ok) {
				throw new Error('Failed to fetch pending organizations');
			}
			return response.json();
		}
	});
}

function OrganizationStatusBadge({ status }: { status: string }) {
	switch (status) {
		case "APPROVED":
			return <Badge className="bg-green-500 text-white flex items-center"><CheckCircle2 className="w-3 h-3 mr-1" /> Aprovada</Badge>;
		case "PENDING_REVIEW":
			return <Badge className="bg-amber-500 text-white flex items-center"><AlertCircle className="w-3 h-3 mr-1" /> Pendente</Badge>;
		case "REJECTED":
			return <Badge className="bg-red-500 flex items-center"><XCircle className="w-3 h-3 mr-1" /> Rejeitada</Badge>;
		case "BLOCKED":
			return <Badge className="bg-gray-500 flex items-center"><XCircle className="w-3 h-3 mr-1" /> Bloqueada</Badge>;
		default:
			return <Badge>{status}</Badge>;
	}
}

export function OrganizationList() {
	// router e pathname removidos pois não estão sendo utilizados
	const { confirm } = useConfirmationAlert();
	const { toast } = useToast();
	// Removendo a função de tradução pois não está mais sendo usada
	const queryClient = useQueryClient();
	const [activeTab, setActiveTab] = useState("all");
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [currentPage, setCurrentPage] = useQueryState(
		"currentPage",
		parseAsInteger.withDefault(1),
	);
	const [searchTerm, setSearchTerm] = useQueryState(
		"query",
		parseAsString.withDefault(""),
	);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounceValue(
		searchTerm,
		300,
		{
			leading: true,
			trailing: false,
		},
	);
	const [gatewayStatusMap, setGatewayStatusMap] = useState<Record<string, { hasDefaultGateway: boolean, defaultGateway?: any }>>({});

	const getPathWithBackToParemeter = (path: string) => {
		const searchParams = new URLSearchParams(window.location.search);
		return withQuery(path, {
			backTo: `${window.location.pathname}${searchParams.size ? `?${searchParams.toString()}` : ""}`,
		});
	};

	const getOrganizationEditPath = (id: string) => {
		return getPathWithBackToParemeter(getAdminPath(`/organizations/${id}`));
	};

	useEffect(() => {
		setDebouncedSearchTerm(searchTerm);
	}, [searchTerm]);

	// Fetch organization gateway status
	useEffect(() => {
		fetch("/api/admin/organizations/gateways-status")
			.then(async response => {
				if (!response.ok) {
					// Log the error but don't throw for auth errors to avoid breaking the UI
					if (response.status === 401 || response.status === 403) {
						console.warn(`Gateway status API returned ${response.status}: Authentication/authorization error`);
						return {};
					}
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const text = await response.text();
				if (!text) {
					console.warn("Empty response from gateway status API");
					return {};
				}

				try {
					return JSON.parse(text);
				} catch (parseError) {
					console.error("Failed to parse JSON response:", text);
					throw new Error("Invalid JSON response");
				}
			})
			.then(data => {
				setGatewayStatusMap(data || {});
			})
			.catch(error => {
				console.error("Error fetching gateway status:", error);
				// Set empty map as fallback to prevent UI issues
				setGatewayStatusMap({});
			});
	}, []);

	// Add gatewayStatus to each organization
	// Definindo uma interface para a organização
	interface Organization {
		id: string;
		name: string;
		slug?: string;
		status: string;
		logo?: string;
		createdAt: string;
		_count: {
			members: number;
		};
		organization_legal_info?: {
			document?: string;
			contactEmail?: string;
		};
		gatewayName?: string;
		gatewayIsDefault?: boolean;
		[key: string]: any;
	}

	const enhanceOrganizationsWithGatewayStatus = (orgs: Organization[]) => {
		return orgs.map(org => ({
			...org,
			hasDefaultGateway: gatewayStatusMap[org.id]?.hasDefaultGateway || false,
			defaultGateway: gatewayStatusMap[org.id]?.defaultGateway || null,
		}));
	};

	const { data, isLoading } = useQuery({
		queryKey: ["organizations", searchTerm, currentPage],
		queryFn: async () => {
			const response = await fetch(
				`/api/admin/organizations?query=${searchTerm}&offset=${(currentPage - 1) * ITEMS_PER_PAGE}&limit=${ITEMS_PER_PAGE}`,
			);

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const text = await response.text();
			if (!text) {
				console.warn("Empty response from organizations API");
				return { organizations: [], total: 0 };
			}

			let data: any;
			try {
				data = JSON.parse(text);
			} catch (parseError) {
				console.error("Failed to parse JSON response:", text);
				throw new Error("Invalid JSON response");
			}

			// The API returns { organizations: [...], total: number }
			if (data.organizations) {
				data.organizations = enhanceOrganizationsWithGatewayStatus(data.organizations);
			}
			return data;
		},
	});

	useEffect(() => {
		setCurrentPage(1);
	}, [debouncedSearchTerm]);

	// Function to check if an organization exists
	const checkOrganizationExists = async (id: string): Promise<boolean> => {
		try {
			const response = await fetch(`/api/admin/organizations/${id}`, {
				method: 'GET',
			});

			return response.ok;
		} catch (error) {
			console.error("Error checking organization existence:", error);
			return false;
		}
	};

	const deleteOrganization = async (id: string) => {
		const deleteOrganizationToast = toast({
			variant: "loading",
			title: "Excluindo organização...",
		});
		try {
			// Replacing authClient with a direct fetch to the API to get more detailed results
			const response = await fetch(`/api/admin/organizations/${id}`, {
				method: 'DELETE',
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Falha ao excluir organização');
			}

			// Check if the organization still exists after deletion attempt
			const stillExists = await checkOrganizationExists(id);

			if (stillExists || !result.success || !result.deletionResults?.organization) {
				console.error("Organization deletion incomplete:", result.deletionResults);
				deleteOrganizationToast.update({
					id: deleteOrganizationToast.id,
					variant: "error",
					title: "Exclusão incompleta",
					description: stillExists
						? "A organização ainda existe no sistema. Tente novamente mais tarde."
						: "A organização pode não ter sido totalmente excluída. Verifique os logs.",
					duration: 5000,
				});

				// Still refresh the list to see if it was removed from the UI
				queryClient.invalidateQueries({
					queryKey: adminOrganizationsQueryKey,
				});
				return;
			}

			deleteOrganizationToast.update({
				id: deleteOrganizationToast.id,
				variant: "success",
				title: "Organização excluída com sucesso!",
				duration: 5000,
			});

			queryClient.invalidateQueries({
				queryKey: adminOrganizationsQueryKey,
			});
		} catch (error) {
			console.error("Error deleting organization:", error);
			deleteOrganizationToast.update({
				id: deleteOrganizationToast.id,
				variant: "error",
				title: "Erro ao excluir organização",
				description: error instanceof Error ? error.message : "Erro desconhecido",
				duration: 5000,
			});
		}
	};

	const { data: pendingData, isLoading: isPendingLoading } = usePendingOrganizationsQuery();
	const pendingOrganizations = useMemo(() => {
		return enhanceOrganizationsWithGatewayStatus(pendingData?.organizations || []);
	}, [pendingData, gatewayStatusMap]);

	// Definição de columns removida pois não está sendo usada

	const organizations = useMemo(
		() => data?.organizations ?? [],
		[data?.organizations],
	);

	// Configuração da tabela removida pois não está sendo usada

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between gap-6">
				<h2 className="font-semibold text-2xl">
					Gerenciar empresas
				</h2>

				<div className="flex gap-2">
					{/* <Button asChild>
						<Link href={getAdminPath("/organizations/create")}>
							<PlusIcon className="mr-1 size-4" />
							Criar
						</Link>
					</Button> */}
					<Button
						variant="outline"
						onClick={() => setIsCreateModalOpen(true)}
					>
						<UserPlus className="mr-1 size-4" />
						Criar e Convidar
					</Button>
				</div>
			</div>

			<SimpleOrganizationModal
				open={isCreateModalOpen}
				onClose={() => setIsCreateModalOpen(false)}
			/>

			<div className="mb-4">
				<Input
					type="search"
					placeholder="Buscar por uma empresa..."
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
				/>
			</div>

			<Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
				<TabsList className=" mb-6">
					<TabsTrigger value="all">Todas</TabsTrigger>
					<TabsTrigger value="pending" className="relative">
						Pendentes
						{pendingOrganizations.length > 0 && (
							<Badge className="ml-2 bg-amber-500/60 hover:bg-amber-600">{pendingOrganizations.length}</Badge>
						)}
					</TabsTrigger>
				</TabsList>

				<TabsContent value="pending">
					{isPendingLoading ? (
						<div className="flex h-24 items-center justify-center">
							<Spinner className="mr-2 size-4 text-primary" />
							Carregando organizações pendentes...
						</div>
					) : pendingOrganizations.length === 0 ? (
						<div className="text-center p-6 text-muted-foreground border rounded-lg bg-card">
							Não há organizações pendentes de revisão.
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{pendingOrganizations.map((org: Organization) => (
								<Card key={org.id} className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
									<CardContent className="p-0">
										<div className="p-5 flex items-center gap-4">
											<div className="flex-shrink-0 w-12 h-12 rounded-full overflow-hidden bg-primary/10 flex items-center justify-center">
												<OrganizationLogo name={org.name} logoUrl={org.logo} className="w-10 h-10" />
											</div>
											<div className="flex-1 min-w-0">
												<h3 className="font-semibold text-lg truncate">{org.name}</h3>
												<div className="flex items-center gap-2 mt-1">
													<small className="text-xs text-muted-foreground">
														Criada em {new Date(org.createdAt).toLocaleDateString()}
													</small>
													<Badge className="bg-amber-50 flex items-center text-amber-700 border-amber-200 hover:bg-amber-100">
														<AlertCircle className="w-3 h-3 mr-1" /> Pendente
													</Badge>
												</div>
											</div>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button size="icon" variant="ghost">
														<MoreVerticalIcon className="size-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent>
													<DropdownMenuItem asChild>
														<Link
															href={getOrganizationEditPath(org.id)}
															className="flex items-center"
														>
															<EditIcon className="mr-2 size-4" />
															Editar
														</Link>
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>

										<div className="border-t px-5 py-4 bg-muted/10">
											{org.organization_legal_info ? (
												<div className="grid grid-cols-2 gap-4">
													<div>
														<div className="text-xs text-muted-foreground">CNPJ</div>
														<div className="font-medium">
															{org.organization_legal_info.document || "Não informado"}
														</div>
													</div>
													<div>
														<div className="text-xs text-muted-foreground">Contato</div>
														<div className="font-medium truncate">
															{org.organization_legal_info.contactEmail || "Não informado"}
														</div>
													</div>
												</div>
											) : (
												<div className="text-sm text-muted-foreground">
													Informações legais não fornecidas
												</div>
											)}
										</div>

										<div className="bg-background px-5 py-3 border-t flex justify-end">
											<Button asChild size="sm" className="bg-amber-600 border-none hover:bg-amber-700 text-white">
												<Link href={getOrganizationEditPath(org.id)}>
													Revisar agora
												</Link>
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</TabsContent>

				<TabsContent value="all">
					{isLoading ? (
						<div className="flex h-24 items-center justify-center">
							<Spinner className="mr-2 size-4 text-primary" />
							Carregando organizações...
						</div>
					) : organizations.length === 0 ? (
						<div className="text-center p-6 text-muted-foreground border rounded-lg bg-card">
							Nenhuma organização encontrada.
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{organizations.map((org: Organization) => (
								<Card key={org.id} className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
									<CardContent className="p-0">
										<div className="p-5 flex items-center gap-4">
											<div className="flex-shrink-0 w-12 h-12 rounded-full overflow-hidden bg-primary/10 flex items-center justify-center">
												<OrganizationLogo name={org.name} logoUrl={org.logo} className="w-10 h-10" />
											</div>
											<div className="flex-1 min-w-0">
												<h3 className="font-semibold text-lg truncate">{org.name}</h3>
												<div className="flex items-center gap-2 mt-1">
													<small className="text-xs text-muted-foreground">
														{org._count.members} {org._count.members === 1 ? 'membro' : 'membros'}
													</small>
													<OrganizationStatusBadge status={org.status} />
												</div>
											</div>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button size="icon" variant="ghost">
														<MoreVerticalIcon className="size-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent>
													<DropdownMenuItem asChild>
														<Link
															href={getOrganizationEditPath(org.id)}
															className="flex items-center"
														>
															<EditIcon className="mr-2 size-4" />
															Editar
														</Link>
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() =>
															confirm({
																title: "Excluir organização",
																message: (
																	<div className="space-y-2">
																		<p>Tem certeza que deseja excluir esta organização? Esta ação não pode ser desfeita.</p>
																		<div className="mt-4 p-3 bg-destructive/10 rounded-md text-sm">
																			<strong>Atenção:</strong> Esta ação excluirá permanentemente:
																			<ul className="list-disc pl-5 mt-2 space-y-1 text-xs">
																				<li>Todos os membros e convites da organização</li>
																				<li>Todas as informações legais e documentos</li>
																				<li>Todas as chaves Pix e gateways de pagamento</li>
																				<li>Todos os webhooks e histórico de transações</li>
																				<li>Todo o saldo e histórico de balanço</li>
																			</ul>
																		</div>
																	</div>
																),
																confirmLabel: "Sim, excluir",
																destructive: true,
																onConfirm: () => deleteOrganization(org.id),
															})
														}
													>
														<span className="flex items-center text-destructive hover:text-destructive">
															<TrashIcon className="mr-2 size-4" />
															Excluir
														</span>
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>

										<div className="border-t px-5 py-4 bg-muted/10">
											<div className="grid grid-cols-2 gap-4">
												<div>
													<div className="text-xs text-muted-foreground">Slug</div>
													<div className="font-medium truncate">
														{org.slug || "Não definido"}
													</div>
												</div>
												<div>
													<div className="text-xs text-muted-foreground">Status</div>
													<div className="font-medium">
														{org.status === "APPROVED" ? "Aprovada" :
														 org.status === "PENDING_REVIEW" ? "Pendente" :
														 org.status === "REJECTED" ? "Rejeitada" : "Bloqueada"}
													</div>
												</div>
											</div>
											{org.gatewayName && (
												<div className="mt-3 pt-3 border-t">
													<div className="text-xs text-muted-foreground">Gateway de Pagamento</div>
													<div className="font-medium flex items-center">
														{org.gatewayName}
														{org.gatewayIsDefault && (
															<Badge className="ml-2 bg-green-500 text-white text-xs">Padrão</Badge>
														)}
													</div>
												</div>
											)}
										</div>

										<div className="bg-background px-5 py-3 border-t flex justify-end gap-2">
											<Button asChild variant="outline" >
												<Link href={`${getOrganizationEditPath(org.id)}?tab=gateways`}>
													<Settings className="mr-1 h-4 w-4" /> Gateways
													{org.hasDefaultGateway && (
														<Badge className="ml-2 bg-green-100 text-green-800 border-green-200">Configurado</Badge>
													)}
												</Link>
											</Button>
											<Button asChild variant={org.status === "PENDING_REVIEW" ? "primary" : "outline"}>
												<Link href={getOrganizationEditPath(org.id)}>
													{org.status === "PENDING_REVIEW" ? "Revisar" : "Gerenciar"}
												</Link>
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}

					{!!data?.total && data.total > ITEMS_PER_PAGE && (
						<Pagination
							className="mt-6"
							totalItems={data.total}
							itemsPerPage={ITEMS_PER_PAGE}
							currentPage={currentPage}
							onChangeCurrentPage={setCurrentPage}
						/>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
