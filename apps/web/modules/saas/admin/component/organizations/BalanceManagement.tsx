"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { useToast } from "@ui/hooks/use-toast";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import { AlertTriangle, TrendingUp, TrendingDown, Clock, DollarSign, Database, ShieldCheck } from "lucide-react";
import { Alert, AlertDescription } from "@ui/components/alert";
import { formatCurrency as formatCurrencyCorrect } from "../../../../shared/lib/currency";

const balanceAdjustmentSchema = z.object({
  amount: z.preprocess(
    (val) => Number(val),
    z.number().positive("Valor deve ser positivo")
  ),
  operation: z.enum(["CREDIT", "DEBIT"], {
    required_error: "Selecione uma operação"
  }),
  description: z.string().min(1, "Descrição é obrigatória"),
  reason: z.string().min(1, "Motivo é obrigatório")
});

type BalanceAdjustmentValues = z.infer<typeof balanceAdjustmentSchema>;

interface BalanceData {
  balance: {
    available: number;
    pending: number;
    reserved: number;
    updatedAt: string;
  };
  organization: {
    name: string;
    status: string;
  };
  recentHistory: Array<{
    id: string;
    operation: string;
    amount: number;
    description: string;
    createdAt: string;
  }>;
}

export function BalanceManagement({ organizationId }: { organizationId: string }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showAdjustmentForm, setShowAdjustmentForm] = useState(false);

  const { data: balanceData, isLoading, error } = useQuery<BalanceData>({
    queryKey: ["admin", "organization", organizationId, "balance"],
    queryFn: async () => {
      const response = await fetch(`/api/admin/organizations/${organizationId}/balance`);
      if (!response.ok) {
        throw new Error("Erro ao carregar dados de saldo");
      }
      return response.json();
    }
  });

  const adjustBalanceMutation = useMutation({
    mutationFn: async (data: BalanceAdjustmentValues) => {
      const response = await fetch(`/api/admin/organizations/${organizationId}/balance`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erro ao ajustar saldo");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["admin", "organization", organizationId, "balance"] });
      toast({
        title: "Sucesso",
        description: "Saldo ajustado com sucesso"
      });
      setShowAdjustmentForm(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const form = useForm<BalanceAdjustmentValues>({
    resolver: zodResolver(balanceAdjustmentSchema),
    defaultValues: {
      amount: 0,
      operation: "DEBIT",
      description: "",
      reason: ""
    }
  });

  const onSubmit = form.handleSubmit((data) => {
    adjustBalanceMutation.mutate(data);
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL"
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(new Date(dateString));
  };

  const getOperationIcon = (operation: string) => {
    switch (operation) {
      case "CREDIT":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "DEBIT":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getOperationColor = (operation: string) => {
    switch (operation) {
      case "CREDIT":
        return "bg-green-50 text-green-700 border-green-200";
      case "DEBIT":
        return "bg-red-50 text-red-700 border-red-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Carregando dados de saldo...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Erro ao carregar dados de saldo. Tente novamente.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Saldo da Organização
          </CardTitle>
          <CardDescription>
            {balanceData?.organization.name} - Status: {balanceData?.organization.status}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <SummaryCard
              icon={<Database className="size-5 text-emerald-500" />}
              value={formatCurrency(balanceData?.balance.available || 0)}
              label="Saldo disponível"
              bgColor="bg-emerald-500/10"
              textColor="text-emerald-500"
            />
            <SummaryCard
              icon={<Clock className="size-5 text-amber-500" />}
              value={formatCurrency(balanceData?.balance.pending || 0)}
              label="Saldo pendente"
              bgColor="bg-amber-500/10"
              textColor="text-amber-500"
            />
            <SummaryCard
              icon={<ShieldCheck className="size-5 text-blue-500" />}
              value={formatCurrency(balanceData?.balance.reserved || 0)}
              label="Reserva de Segurança"
              bgColor="bg-blue-500/10"
              textColor="text-blue-500"
            />
          </div>

          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Última atualização: {formatDate(balanceData?.balance.updatedAt || new Date().toISOString())}
            </p>
            <Button 
              onClick={() => setShowAdjustmentForm(!showAdjustmentForm)}
              variant={showAdjustmentForm ? "outline" : "default"}
            >
              {showAdjustmentForm ? "Cancelar" : "Ajustar Saldo"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {showAdjustmentForm && (
        <Card>
          <CardHeader>
            <CardTitle>Ajustar Saldo</CardTitle>
            <CardDescription>
              Realize créditos ou débitos manuais no saldo da organização
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={onSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="operation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Operação</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione a operação" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="CREDIT">Crédito (Adicionar)</SelectItem>
                            <SelectItem value="DEBIT">Débito (Subtrair)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Valor (R$)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Motivo</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Ex: Transferência bancária recebida"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição Detalhada</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Descreva os detalhes da operação..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-2 pt-4">
                  <Button 
                    type="submit" 
                    disabled={adjustBalanceMutation.isPending}
                  >
                    {adjustBalanceMutation.isPending ? "Processando..." : "Confirmar Ajuste"}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowAdjustmentForm(false)}
                  >
                    Cancelar
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Histórico Recente</CardTitle>
          <CardDescription>
            Últimas 10 operações realizadas no saldo
          </CardDescription>
        </CardHeader>
        <CardContent>
          {balanceData?.recentHistory && balanceData.recentHistory.length > 0 ? (
            <div className="space-y-3">
              {balanceData.recentHistory.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getOperationIcon(item.operation)}
                    <div>
                      <div className="font-medium">{item.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(item.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getOperationColor(item.operation)}>
                      {item.operation}
                    </Badge>
                    <div className="text-right">
                      <div className={`font-medium ${
                        item.operation === "CREDIT" ? "text-green-600" : "text-red-600"
                      }`}>
                        {item.operation === "CREDIT" ? "+" : "-"}{formatCurrencyCorrect(Math.abs(item.amount))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Nenhuma operação encontrada
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function SummaryCard({
  icon,
  value,
  label,
  bgColor,
  textColor,
  total
}: {
  icon: React.ReactNode;
  value: string;
  label: string;
  bgColor: string;
  textColor: string;
  total?: string;
}) {
  // Convert bgColor to an appropriate style backgroundColor with higher transparency
  const getStyleColor = () => {
    if (bgColor.includes("emerald")) return "rgba(16, 185, 129, 0.05)"; // emerald-500 with 0.05 opacity
    if (bgColor.includes("rose")) return "rgba(244, 63, 94, 0.05)"; // rose-500 with 0.05 opacity
    if (bgColor.includes("blue")) return "rgba(59, 130, 246, 0.05)"; // blue-500 with 0.05 opacity
    if (bgColor.includes("amber")) return "rgba(245, 158, 11, 0.05)"; // amber-500 with 0.05 opacity
    return "rgba(75, 85, 99, 0.05)"; // gray-500 with 0.05 opacity
  };

  return (
    <Card className="overflow-hidden border border-gray-800" style={{ backgroundColor: getStyleColor() }}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className={`rounded-full ${bgColor} p-2`}>
            {icon}
          </div>
        </div>
        <div className={`font-bold text-xl ${textColor}`}>{value}</div>
        <div className="text-sm text-muted-foreground">{label}</div>
        {total && (
          <div className="text-xs text-muted-foreground mt-2">
            {total}
          </div>
        )}
      </div>
    </Card>
  );
}