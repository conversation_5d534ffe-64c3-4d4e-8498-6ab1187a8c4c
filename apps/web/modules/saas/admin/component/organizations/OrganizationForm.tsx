"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { adminOrganizationsQueryKey } from "@saas/admin/lib/api";
import { getAdminPath } from "@saas/admin/lib/links";
import { InviteMemberForm } from "@saas/organizations/components/InviteMemberForm";
import { OrganizationMembersBlock } from "@saas/organizations/components/OrganizationMembersBlock";
import {
	fullOrganizationQueryKey,
	useCreateOrganizationMutation,
	useFullOrganizationQuery,
	useUpdateOrganizationMutation,
} from "@saas/organizations/lib/api";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@ui/components/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import { useToast } from "@ui/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useEffect, useState } from "react";
import { Switch } from "@ui/components/switch";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { DocumentsReviewSection } from "./DocumentsReviewSection";
import { BalanceManagement } from "./BalanceManagement";
import { InviteOwnerForm } from "./InviteOwnerForm";
import { useSearchParams } from "next/navigation";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertTriangleIcon, CheckCircle, Clock, Loader2, XCircle } from "lucide-react";
import { useUpdateOrganizationTaxes } from "@saas/organizations/hooks/use-update-organization-taxes";
import { GatewayTab } from "../gateways/GatewayTab";

const organizationFormSchema = z.object({
	name: z.string().min(1),
	status: z.enum(["PENDING_REVIEW", "APPROVED", "REJECTED", "BLOCKED"]).default("PENDING_REVIEW"),
	// Campos para taxas
	pixChargePercentFee: z.preprocess(
		(val) => (val === "" ? 0 : Number(val)),
		z.number().min(0).max(100)
	),
	pixTransferPercentFee: z.preprocess(
		(val) => (val === "" ? 0 : Number(val)),
		z.number().min(0).max(100)
	),
	pixChargeFixedFee: z.preprocess(
		(val) => (val === "" ? 0 : Number(val)),
		z.number().min(0)
	),
	pixTransferFixedFee: z.preprocess(
		(val) => (val === "" ? 0 : Number(val)),
		z.number().min(0)
	),
	// Campos para informações legais
	companyName: z.string().optional(),
	document: z.string().optional(),
	contactEmail: z.string().email().optional(),
	contactPhone: z.string().optional(),
	reviewNotes: z.string().optional(),
	tradingName: z.string().optional(),
	legalRepresentative: z.string().optional(),
	legalRepDocumentNumber: z.string().optional(),
	address: z.string().optional(),
	city: z.string().optional(),
	state: z.string().optional(),
	postalCode: z.string().optional(),
	documentType: z.string().optional().default("CNPJ"),
});

type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

// Helper function to get badge for organization status
function OrganizationStatusBadge({ status }: { status: string }) {
	switch (status) {
		case "APPROVED":
			return <Badge className="bg-green-500 text-white flex items-center"><CheckCircle className="w-3 h-3 mr-1" /> Aprovada</Badge>;
		case "PENDING_REVIEW":
			return <Badge className="bg-amber-500 text-white flex items-center"><Clock className="w-3 h-3 mr-1" /> Pendente</Badge>;
		case "REJECTED":
			return <Badge className="bg-red-500 text-white flex items-center"><XCircle className="w-3 h-3 mr-1" /> Rejeitada</Badge>;
		case "BLOCKED":
			return <Badge className="bg-gray-500 text-white flex items-center"><XCircle className="w-3 h-3 mr-1" /> Bloqueada</Badge>;
		default:
			return <Badge>{status}</Badge>;
	}
}

export function OrganizationForm({
	organizationId,
}: {
	organizationId: string;
}) {
	const t = useTranslations();
	const router = useRouter();
	const { toast } = useToast();
	const searchParams = useSearchParams();
	const initialTab = searchParams.get('tab') || 'legalInfo';
	const [activeTab, setActiveTab] = useState(initialTab);
	const [isLoadingLegalInfo, setIsLoadingLegalInfo] = useState(false);
	const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
	const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

	const { data: organization, refetch } = useFullOrganizationQuery(organizationId);

	// Add direct fetch of legal info on component mount
	useEffect(() => {
		if (organizationId) {
			setIsLoadingLegalInfo(true);
			// Direct fetch to legal info endpoint
			fetch(`/api/admin/organizations/${organizationId}/legal-info`, {
				headers: {
					'Content-Type': 'application/json',
				},
			})
				.then(response => {
					if (response.ok) return response.json();
					console.log(`Legal info fetch status: ${response.status}`);
					if (response.status !== 404) {
						throw new Error(`Failed to fetch legal info: ${response.status}`);
					}
					return null;
				})
				.then(data => {
					if (data) {
						console.log('📋 Successfully fetched legal info directly:', data);
						// Populate form with legal info
						updateLegalInfoFormFields(data);
					} else {
						console.log('⚠️ No legal info available from direct fetch');
					}
				})
				.catch(error => {
					console.error('❌ Error fetching legal info:', error);
				})
				.finally(() => {
					setIsLoadingLegalInfo(false);
				});
		}
	}, [organizationId]);

	// Add direct fetch of taxes on component mount
	useEffect(() => {
		if (organizationId) {
			// Direct fetch to taxes endpoint
			fetch(`/api/admin/organizations/${organizationId}/taxes`, {
				headers: {
					'Content-Type': 'application/json',
				},
			})
				.then(response => {
					if (response.ok) return response.json();
					console.log(`Taxes fetch status: ${response.status}`);
					if (response.status !== 404) {
						throw new Error(`Failed to fetch taxes: ${response.status}`);
					}
					return null;
				})
				.then(data => {
					if (data) {
						console.log('💰 Successfully fetched taxes directly:', data);
						// Populate form with taxes
						updateTaxesFormFields(data);
					} else {
						console.log('⚠️ No taxes available from direct fetch');
					}
				})
				.catch(error => {
					console.error('❌ Error fetching taxes:', error);
				});
		}
	}, [organizationId]);

	// Set the selected status when organization data is loaded
	useEffect(() => {
		if (organization?.status) {
			setSelectedStatus(organization.status);
		}
	}, [organization]);

	// Function to update the form with legal info fields
	const updateLegalInfoFormFields = (legalInfo: any) => {
		if (!legalInfo) return;

		console.log('Updating form fields with legal info:', legalInfo);
		form.setValue('companyName', legalInfo.companyName || '');
		form.setValue('tradingName', legalInfo.tradingName || '');
		form.setValue('document', legalInfo.document || '');
		form.setValue('documentType', legalInfo.documentType || 'CNPJ');
		form.setValue('contactEmail', legalInfo.contactEmail || '');
		form.setValue('contactPhone', legalInfo.contactPhone || '');
		form.setValue('address', legalInfo.address || '');
		form.setValue('city', legalInfo.city || '');
		form.setValue('state', legalInfo.state || '');
		form.setValue('postalCode', legalInfo.postalCode || '');
		form.setValue('reviewNotes', legalInfo.reviewNotes || '');
	};

	// Function to update the form with taxes fields
	const updateTaxesFormFields = (taxes: any) => {
		if (!taxes) return;

		console.log('Updating form fields with taxes:', taxes);
		form.setValue('pixChargePercentFee', taxes.pixChargePercentFee || 0);
		form.setValue('pixTransferPercentFee', taxes.pixTransferPercentFee || 0);
		form.setValue('pixChargeFixedFee', taxes.pixChargeFixedFee || 0);
		form.setValue('pixTransferFixedFee', taxes.pixTransferFixedFee || 0);
	};

	const form = useForm<OrganizationFormValues>({
		resolver: zodResolver(organizationFormSchema),
		defaultValues: {
			name: organization?.name ?? "",
			status: organization?.status ?? "PENDING_REVIEW",
			// Campos de taxas - inicializar com 0 e atualizar via useEffect
		pixChargePercentFee: 0,
		pixTransferPercentFee: 0,
		pixChargeFixedFee: 0,
		pixTransferFixedFee: 0,
			// Campos de informações legais
			companyName: (organization as any)?.organization_legal_info?.companyName ?? "",
			document: (organization as any)?.organization_legal_info?.document ?? "",
			contactEmail: (organization as any)?.organization_legal_info?.contactEmail ?? "",
			contactPhone: (organization as any)?.organization_legal_info?.contactPhone ?? "",
			reviewNotes: (organization as any)?.organization_legal_info?.reviewNotes ?? "",
			tradingName: (organization as any)?.organization_legal_info?.tradingName ?? "",
			legalRepresentative: (organization as any)?.organization_legal_info?.legalRepresentative ?? "",
			legalRepDocumentNumber: (organization as any)?.organization_legal_info?.legalRepDocumentNumber ?? "",
			address: (organization as any)?.organization_legal_info?.address ?? "",
			city: (organization as any)?.organization_legal_info?.city ?? "",
			state: (organization as any)?.organization_legal_info?.state ?? "",
			postalCode: (organization as any)?.organization_legal_info?.postalCode ?? "",
			documentType: (organization as any)?.organization_legal_info?.documentType ?? "CNPJ",
		},
	});

	const queryClient = useQueryClient();

	const updateOrganizationMutation = useUpdateOrganizationMutation();
	const updateTaxesMutation = useUpdateOrganizationTaxes(organizationId);

	// Function to update organization status
	const updateOrganizationStatus = async (newStatus?: string) => {
		const statusToUpdate = newStatus || selectedStatus;
		if (!statusToUpdate) return;

		setIsUpdatingStatus(true);
		try {
			const response = await fetch(`/api/admin/organizations/${organizationId}/status`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					status: statusToUpdate,
				}),
			});

			if (!response.ok) {
				throw new Error(`Failed to update status: ${response.status}`);
			}

			// Update the form with the new status
			form.setValue('status', statusToUpdate as any);

			// Invalidate queries to refresh data
			await queryClient.invalidateQueries({
				queryKey: fullOrganizationQueryKey(organizationId),
			});

			await queryClient.invalidateQueries({
				queryKey: adminOrganizationsQueryKey,
			});

			await refetch();

			toast({
				title: "Sucesso",
				description: "Status da organização atualizado com sucesso",
			});
		} catch (error) {
			console.error("Error updating organization status:", error);
			toast({
				title: "Erro",
				description: "Falha ao atualizar status da organização",
				variant: "error",
			});
		} finally {
			setIsUpdatingStatus(false);
		}
	};

	const onSubmit = async (values: OrganizationFormValues) => {
		try {
			await updateOrganizationMutation.mutateAsync({
				id: organizationId,
				name: values.name,
			});

			await queryClient.invalidateQueries({
				queryKey: fullOrganizationQueryKey(organizationId),
			});

			await queryClient.invalidateQueries({
				queryKey: adminOrganizationsQueryKey,
			});

			toast({
				title: "Sucesso",
				description: "Organização atualizada com sucesso",
			});
		} catch {
			toast({
				title: "Erro",
				description: "Falha ao atualizar organização",
				variant: "error",
			});
		}
	};

	const onTaxesSubmit = async (values: OrganizationFormValues) => {
		try {
			await updateTaxesMutation.mutateAsync({
				pixChargePercentFee: values.pixChargePercentFee,
				pixTransferPercentFee: values.pixTransferPercentFee,
				pixChargeFixedFee: values.pixChargeFixedFee,
				pixTransferFixedFee: values.pixTransferFixedFee,
			});

			toast({
				title: "Sucesso",
				description: "Taxas atualizadas com sucesso",
			});
		} catch (error) {
			console.error("Error updating taxes:", error);
			toast({
				title: "Erro",
				description: "Falha ao atualizar taxas",
				variant: "error",
			});
		}
	};

	if (!organization) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader2 className="h-8 w-8 animate-spin" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{organization && (
				<Tabs
					defaultValue={activeTab}
					className="w-full"
					onValueChange={(value) => {
						setActiveTab(value);
						// Update URL with the new tab without causing a full page reload
						const url = new URL(window.location.href);
						url.searchParams.set('tab', value);
						window.history.pushState({}, '', url);
					}}
				>
					<TabsList className="grid w-full grid-cols-5">
						<TabsTrigger value="legalInfo">Informações Legais</TabsTrigger>
						<TabsTrigger value="taxes">Taxas</TabsTrigger>
						<TabsTrigger value="documents">Documentos</TabsTrigger>
						<TabsTrigger value="gateways">Gateways</TabsTrigger>
						<TabsTrigger value="balance">Saldo</TabsTrigger>
					</TabsList>

					<TabsContent value="legalInfo">
						<Card>
							<CardHeader>
								<CardTitle>Informações Legais</CardTitle>
								<CardDescription>Detalhes legais da organização</CardDescription>
							</CardHeader>
							<CardContent>


								<Form {...form}>
									<form
										onSubmit={form.handleSubmit(onSubmit)}
										className="flex flex-col w-full gap-4"
									>
										<div className="grid grid-cols-2 gap-4">
											<FormField
												control={form.control}
												name="companyName"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Razão Social</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite a razão social"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="tradingName"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Nome Fantasia</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o nome fantasia"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<div className="grid grid-cols-2 gap-4">
											<FormField
												control={form.control}
												name="documentType"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Tipo de Documento</FormLabel>
														<Select
															onValueChange={field.onChange}
															value={field.value}
														>
															<FormControl>
																<SelectTrigger>
																	<SelectValue placeholder="Selecione o tipo de documento" />
																</SelectTrigger>
															</FormControl>
															<SelectContent>
																<SelectItem value="CNPJ">CNPJ</SelectItem>
																<SelectItem value="CPF">CPF</SelectItem>
															</SelectContent>
														</Select>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="document"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Documento</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o número do documento"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<div className="grid grid-cols-2 gap-4">
											<FormField
												control={form.control}
												name="contactEmail"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Email de Contato</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o email de contato"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="contactPhone"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Telefone de Contato</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o telefone de contato"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<FormField
											control={form.control}
											name="address"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Endereço</FormLabel>
													<FormControl>
														<Input
															placeholder="Digite o endereço completo"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<div className="grid grid-cols-3 gap-4">
											<FormField
												control={form.control}
												name="city"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Cidade</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite a cidade"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="state"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Estado</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o estado"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="postalCode"
												render={({ field }) => (
													<FormItem>
														<FormLabel>CEP</FormLabel>
														<FormControl>
															<Input
																placeholder="Digite o CEP"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<FormField
											control={form.control}
											name="reviewNotes"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Observações de Revisão</FormLabel>
													<FormControl>
														<Textarea
															placeholder="Digite observações sobre a revisão"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<div className="mt-4">
											<Button type="submit">Salvar Informações Legais</Button>
										</div>
									</form>
								</Form>
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="taxes">
						<Card>
							<CardHeader>
								<CardTitle>Taxas</CardTitle>
								<CardDescription>Configure as taxas da organização</CardDescription>
							</CardHeader>
							<CardContent>
								<Form {...form}>
									<form
										className="flex flex-col w-full gap-4"
										onSubmit={form.handleSubmit(onTaxesSubmit)}
									>
										<div className="grid grid-cols-2 gap-4">
											<FormField
												control={form.control}
												name="pixChargePercentFee"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Taxa Percentual de Cobrança PIX (%)</FormLabel>
														<FormControl>
															<Input
																type="number"
																step="0.01"
																placeholder="Digite a taxa percentual"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="pixChargeFixedFee"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Taxa Fixa de Cobrança PIX (R$)</FormLabel>
														<FormControl>
															<Input
																type="number"
																step="0.01"
																placeholder="Digite a taxa fixa"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<div className="grid grid-cols-2 gap-4">
											<FormField
												control={form.control}
												name="pixTransferPercentFee"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Taxa Percentual de Transferência PIX (%)</FormLabel>
														<FormControl>
															<Input
																type="number"
																step="0.01"
																placeholder="Digite a taxa percentual"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="pixTransferFixedFee"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Taxa Fixa de Transferência PIX (R$)</FormLabel>
														<FormControl>
															<Input
																type="number"
																step="0.01"
																placeholder="Digite a taxa fixa"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										<div className="mt-4">
											<Button
												type="submit"
												disabled={updateTaxesMutation.isPending}
											>
												{updateTaxesMutation.isPending ? "Salvando..." : "Salvar Taxas"}
											</Button>
										</div>
									</form>
								</Form>
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="documents">
						<DocumentsReviewSection organizationId={organizationId} />
					</TabsContent>


					<TabsContent value="gateways">
						<GatewayTab organizationId={organizationId} />
					</TabsContent>

					<TabsContent value="balance">
						<BalanceManagement organizationId={organizationId} />
					</TabsContent>
				</Tabs>
			)}

			{/* Membros da organização */}
			{organization && (
				<div className="space-y-6">
					<div className="space-y-6">
						<OrganizationMembersBlock
							organizationId={organizationId}
							showInviteButton={false}
						/>
					</div>
					<Card className="mt-6">
						<CardContent className="pt-6">
							<div className="space-y-4">
								<h3 className="text-lg font-semibold">Convidar Novo Membro</h3>
								<InviteMemberForm organizationId={organizationId} />
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Organization Status Card - Moved to the end */}
			{organization && (
				<Card className="mt-6">
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<div>
							<CardTitle>Status da Organização</CardTitle>
							<CardDescription>Gerencie o status de aprovação da organização</CardDescription>
						</div>

					</CardHeader>
					<CardContent className="pt-4">
						<div className="space-y-4">
							<div className="flex flex-row items-end gap-4">
								<div className="flex-grow">
									<h3 className="text-sm font-medium mb-2">Alterar Status</h3>
									<Select
										value={selectedStatus || organization.status}
										onValueChange={(value) => {
											setSelectedStatus(value);
										}}
										disabled={isUpdatingStatus}
									>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Selecione um status" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="PENDING_REVIEW">Pendente de Revisão</SelectItem>
											<SelectItem value="APPROVED">Aprovado</SelectItem>
											<SelectItem value="REJECTED">Rejeitado</SelectItem>
											<SelectItem value="BLOCKED">Bloqueado</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<div className="flex space-x-2">
									<Button
										onClick={() => updateOrganizationStatus()}
										variant="primary"
										disabled={isUpdatingStatus}
									>
										Salvar Status
									</Button>


								</div>
							</div>

							{organization.status === "REJECTED" && (
								<Alert variant="error" className="mt-4">
									<AlertTriangleIcon className="h-4 w-4" />
									<AlertTitle>Organização Rejeitada</AlertTitle>
									<AlertDescription>
										Esta organização foi rejeitada e não pode processar pagamentos.
									</AlertDescription>
								</Alert>
							)}

							{organization.status === "BLOCKED" && (
								<Alert variant="error" className="mt-4">
									<AlertTriangleIcon className="h-4 w-4" />
									<AlertTitle>Organização Bloqueada</AlertTitle>
									<AlertDescription>
										Esta organização está bloqueada e não pode processar pagamentos.
									</AlertDescription>
								</Alert>
							)}
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
