"use client";

import { useState } from "react";
import { useAdminDashboard } from "../hooks/use-admin-dashboard";
import { AdminSummaryCards } from "./AdminSummaryCards";
import { AdminRevenueChart } from "./AdminRevenueChart";
import { AdminRecentActivity } from "./AdminRecentActivity";
import { DateRangeFilter } from "./DateRangeFilter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Loader2, Users, Building2, CreditCard, FileText, Settings, AlertTriangle, BarChart3 } from "lucide-react";
import Link from "next/link";

export function AdminDashboard() {
  const [selectedDays, setSelectedDays] = useState(30);
  const { data: dashboardData, isLoading, isError, error } = useAdminDashboard(selectedDays);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="h-8 w-8 animate-spin mr-3" />
        <span className="text-lg">Carregando dados do dashboard...</span>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="p-8 text-center">
        <div className="mb-4 text-rose-500 text-xl">Erro ao carregar dados do dashboard</div>
        <div className="text-muted-foreground mb-6">
          {error instanceof Error ? error.message : "Ocorreu um erro desconhecido"}
        </div>
        <div className="text-sm text-muted-foreground">
          Tente recarregar a página. Se o problema persistir, entre em contato com o suporte.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Filter */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          Dados dos últimos {selectedDays} dias
        </div>
        <DateRangeFilter
          selectedDays={selectedDays}
          onDaysChange={setSelectedDays}
        />
      </div>

      {/* Summary Cards */}
      <AdminSummaryCards data={dashboardData} />

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <AdminRevenueChart data={dashboardData} />
        </div>

        {/* Quick Actions & Platform Health */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Ações Rápidas
              </CardTitle>
              <CardDescription>Acesso rápido às principais funcionalidades</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/organizations">
                  <Building2 className="h-4 w-4 mr-2" />
                  Gerenciar Organizações
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/transactions">
                  <FileText className="h-4 w-4 mr-2" />
                  Ver Transações
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/users">
                  <Users className="h-4 w-4 mr-2" />
                  Gerenciar Usuários
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/gateways">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Gateways de Pagamento
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/withdrawal-control">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Controle de Saques
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/med">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  MED - Infrações
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/app/admin/reports">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Relatórios
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Platform Health Card */}
          <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Saúde da Plataforma
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Taxa de Sucesso</span>
                <span className="font-medium">
                  {dashboardData?.transactions.successRate.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Organizações Ativas</span>
                <span className="font-medium">
                  {dashboardData?.organizations.active || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Pendentes de Aprovação</span>
                <span className="font-medium text-amber-500">
                  {dashboardData?.organizations.pending || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total de Estornos</span>
                <span className="font-medium text-rose-500">
                  {dashboardData?.refunds.total || 0}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Activity */}
      <AdminRecentActivity data={dashboardData} />
    </div>
  );
}
