"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import { Loader2, Shield, AlertTriangle, CheckCircle, Clock } from "lucide-react";
import { toast } from "@ui/hooks/use-toast";

interface MedAutoApprovalSettings {
  medAutoApprovalEnabled: boolean;
  medAutoApprovalMessage: string;
  medAutoApprovalEnabledAt: string | null;
  medAutoApprovalEnabledBy: string | null;
}

export function MedAutoApprovalSection() {
  const [settings, setSettings] = useState<MedAutoApprovalSettings>({
    medAutoApprovalEnabled: false,
    medAutoApprovalMessage: "Aprovação automática de MED ativada",
    medAutoApprovalEnabledAt: null,
    medAutoApprovalEnabledBy: null,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      console.log("🔄 Carregando configurações...");
      
      // Primeiro, testar rota simples
      console.log("🧪 Testando rota simples primeiro...");
      const testResponse = await fetch("/api/admin/system-settings/test-simple");
      console.log("🧪 Status da rota de teste:", testResponse.status);
      
      if (testResponse.ok) {
        const testData = await testResponse.text();
        console.log("🧪 Resposta da rota de teste:", testData);
      }
      
      // Agora testar a rota de debug
      console.log("🔄 Testando rota de debug...");
      const response = await fetch("/api/admin/system-settings/debug");
      console.log("📡 Status da resposta debug:", response.status);
      console.log("📡 Headers da resposta:", Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Erro na resposta:", errorText);
        throw new Error(`Erro ${response.status}: ${errorText}`);
      }

      const responseText = await response.text();
      console.log("📄 Resposta bruta (length:", responseText.length, "):", responseText);

      if (!responseText || !responseText.trim()) {
        console.error("❌ Resposta vazia ou só espaços");
        throw new Error("Resposta vazia do servidor");
      }

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("✅ JSON parseado com sucesso:", data);
      } catch (parseError) {
        console.error("❌ Erro ao fazer parse do JSON:", parseError);
        console.error("❌ Texto que causou erro:", responseText);
        throw new Error(`Resposta inválida: ${responseText.substring(0, 100)}...`);
      }

      if (data.success) {
        setSettings({
          medAutoApprovalEnabled: data.data.medAutoApprovalEnabled || false,
          medAutoApprovalMessage: data.data.medAutoApprovalMessage || "Aprovação automática de MED ativada",
          medAutoApprovalEnabledAt: data.data.medAutoApprovalEnabledAt,
          medAutoApprovalEnabledBy: data.data.medAutoApprovalEnabledBy,
        });
        console.log("✅ Configurações carregadas com sucesso");
      } else {
        throw new Error(data.message || "Erro desconhecido");
      }
    } catch (error) {
      console.error("❌ Erro ao carregar configurações:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível carregar as configurações do sistema",
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      console.log("💾 Salvando configurações...");

      const requestBody = {
        medAutoApprovalEnabled: settings.medAutoApprovalEnabled,
        medAutoApprovalMessage: settings.medAutoApprovalMessage,
      };

      console.log("📦 Dados para envio:", requestBody);

      const response = await fetch("/api/admin/system-settings/debug", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📡 Status da resposta POST:", response.status);
      console.log("📡 Headers da resposta POST:", Object.fromEntries(response.headers.entries()));

      const responseText = await response.text();
      console.log("📄 Resposta bruta POST (length:", responseText.length, "):", responseText);

      if (!responseText || !responseText.trim()) {
        console.error("❌ Resposta POST vazia ou só espaços");
        throw new Error("Resposta vazia do servidor");
      }

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("✅ JSON POST parseado com sucesso:", data);
      } catch (parseError) {
        console.error("❌ Erro ao fazer parse do JSON POST:", parseError);
        console.error("❌ Texto que causou erro POST:", responseText);
        throw new Error(`Resposta inválida: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        throw new Error(data.message || `Erro ${response.status}`);
      }

      if (data.success) {
        toast({
          title: "Sucesso",
          description: "Configurações de MED atualizadas com sucesso",
        });

        // Atualizar dados locais
        setSettings({
          medAutoApprovalEnabled: data.data.medAutoApprovalEnabled,
          medAutoApprovalMessage: data.data.medAutoApprovalMessage,
          medAutoApprovalEnabledAt: data.data.medAutoApprovalEnabledAt,
          medAutoApprovalEnabledBy: data.data.medAutoApprovalEnabledBy,
        });
        console.log("✅ Configurações salvas com sucesso");
      } else {
        throw new Error(data.message || "Erro desconhecido");
      }
    } catch (error) {
      console.error("❌ Erro ao salvar configurações:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível salvar as configurações",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Aprovação Automática de MED
          </CardTitle>
          <CardDescription>
            Configure a aprovação automática de infrações MED
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Carregando configurações...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Aprovação Automática de MED
        </CardTitle>
        <CardDescription>
          Configure a aprovação automática de infrações MED. Quando ativada, as infrações serão aprovadas automaticamente sem intervenção manual.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Atual */}
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-3">
            {settings.medAutoApprovalEnabled ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <Clock className="h-5 w-5 text-amber-600" />
            )}
            <div>
              <p className="font-medium">
                Status: {settings.medAutoApprovalEnabled ? "Ativada" : "Desativada"}
              </p>
              <p className="text-sm text-muted-foreground">
                {settings.medAutoApprovalEnabled
                  ? "Infrações MED serão aprovadas automaticamente"
                  : "Infrações MED aguardam aprovação manual"
                }
              </p>
            </div>
          </div>
          <Badge className={settings.medAutoApprovalEnabled ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
            {settings.medAutoApprovalEnabled ? "ATIVA" : "INATIVA"}
          </Badge>
        </div>

        {/* Configuração Principal */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Aprovação Automática</Label>
              <p className="text-sm text-muted-foreground">
                Ativar aprovação automática de infrações MED
              </p>
            </div>
            <Switch
              checked={settings.medAutoApprovalEnabled}
              onCheckedChange={(checked) =>
                setSettings(prev => ({ ...prev, medAutoApprovalEnabled: checked }))
              }
            />
          </div>

          {/* Mensagem Personalizada */}
          <div className="space-y-2">
            <Label htmlFor="medMessage">Mensagem de Aprovação</Label>
            <Textarea
              id="medMessage"
              placeholder="Mensagem que será exibida quando uma infração for aprovada automaticamente"
              value={settings.medAutoApprovalMessage}
              onChange={(e) =>
                setSettings(prev => ({ ...prev, medAutoApprovalMessage: e.target.value }))
              }
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              Esta mensagem será incluída nos logs e eventos de webhook
            </p>
          </div>
        </div>

      

        {/* Aviso de Segurança */}
        {settings.medAutoApprovalEnabled && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Atenção:</strong> Com a aprovação automática ativada, todas as infrações MED serão aprovadas automaticamente sem revisão manual.
              Certifique-se de que esta configuração está alinhada com suas políticas de segurança.
            </AlertDescription>
          </Alert>
        )}

        {/* Botão Salvar */}
        <div className="flex justify-end">
          <Button
            onClick={saveSettings}
            disabled={saving}
            className="min-w-[120px]"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Salvando...
              </>
            ) : (
              "Salvar Configurações"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
