"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { AdminDashboardSummary } from "../hooks/use-admin-dashboard";
import { formatCurrency, formatDate } from "@shared/lib/format";
import { ExternalLink, Building2, User } from "lucide-react";

interface AdminRecentActivityProps {
  data?: AdminDashboardSummary;
}

function getStatusBadge(status: string) {
  const statusConfig = {
    APPROVED: { label: "Aprovada", variant: "default" as const, className: "bg-emerald-500/10 text-emerald-500 border-emerald-500/20" },
    PENDING: { label: "Pendente", variant: "secondary" as const, className: "bg-amber-500/10 text-amber-500 border-amber-500/20" },
    REJECTED: { label: "Rejeitada", variant: "destructive" as const, className: "bg-rose-500/10 text-rose-500 border-rose-500/20" },
    CANCELED: { label: "Cancelada", variant: "outline" as const, className: "bg-gray-500/10 text-gray-500 border-gray-500/20" },
    REFUNDED: { label: "Estornada", variant: "outline" as const, className: "bg-violet-500/10 text-violet-500 border-violet-500/20" },
    PROCESSING: { label: "Processando", variant: "secondary" as const, className: "bg-blue-500/10 text-blue-500 border-blue-500/20" },
    BLOCKED: { label: "Bloqueada", variant: "destructive" as const, className: "bg-red-500/10 text-red-500 border-red-500/20" }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
  
  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
}

function getTypeBadge(type: string) {
  const typeConfig = {
    CHARGE: { label: "Cobrança", className: "bg-blue-500/10 text-blue-500 border-blue-500/20" },
    SEND: { label: "Envio", className: "bg-purple-500/10 text-purple-500 border-purple-500/20" },
    RECEIVE: { label: "Recebimento", className: "bg-emerald-500/10 text-emerald-500 border-emerald-500/20" },
    REFUND: { label: "Estorno", className: "bg-rose-500/10 text-rose-500 border-rose-500/20" }
  };

  const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.CHARGE;
  
  return (
    <Badge variant="outline" className={config.className}>
      {config.label}
    </Badge>
  );
}

export function AdminRecentActivity({ data }: AdminRecentActivityProps) {
  const isLoading = !data;

  if (isLoading) {
    return (
      <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Atividade Recente</CardTitle>
          <CardDescription>Últimas transações na plataforma</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex h-[200px] w-full items-center justify-center">
            <div className="text-sm text-muted-foreground">Carregando atividades...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const recentTransactions = data.recentActivity || [];

  return (
    <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Atividade Recente</CardTitle>
            <CardDescription>Últimas transações na plataforma</CardDescription>
          </div>
          <div className="text-sm text-muted-foreground">
            {recentTransactions.length} transações
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {recentTransactions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhuma atividade recente encontrada
          </div>
        ) : (
          <div className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 rounded-lg border border-gray-800/50 hover:bg-gray-800/30 transition-colors"
              >
                <div className="flex items-center space-x-4 flex-1">
                  {/* Transaction Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium truncate">
                        {transaction.customerName}
                      </span>
                      {getTypeBadge(transaction.type)}
                    </div>
                    
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Building2 className="h-3 w-3" />
                      <span className="truncate">{transaction.organizationName}</span>
                    </div>
                    
                    <div className="text-xs text-muted-foreground mt-1">
                      {transaction.customerEmail}
                    </div>
                  </div>

                  {/* Amount */}
                  <div className="text-right">
                    <div className="font-semibold">
                      {formatCurrency(transaction.amount)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(transaction.createdAt)}
                    </div>
                  </div>

                  {/* Status */}
                  <div className="flex flex-col items-end space-y-1">
                    {getStatusBadge(transaction.status)}
                    {transaction.paymentAt && (
                      <div className="text-xs text-muted-foreground">
                        Pago: {formatDate(transaction.paymentAt)}
                      </div>
                    )}
                  </div>

                  {/* Action */}
                  <div className="flex items-center">
                    <button
                      className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                      title="Ver detalhes"
                    >
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
