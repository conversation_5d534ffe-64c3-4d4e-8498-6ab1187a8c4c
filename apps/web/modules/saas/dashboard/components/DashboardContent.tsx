"use client";

import { Card } from "@ui/components/card";
import { SummaryCards } from "./SummaryCards";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { DashboardChart } from "./DashboardChart";
import { TransactionsByMethodChart } from "./TransactionsByMethodChart";
import { useDashboardSummary } from "../hooks/use-dashboard";
import { useTransactions, useTransactionDetails } from "@saas/transactions/hooks/use-transactions";
import { TransactionDetailsSheet } from "@saas/transactions/components/TransactionDetailsSheet";
import { Pagination } from "@shared/components/Pagination";
import { formatCurrency, formatDate } from "@shared/lib/format";
import { Badge } from "@ui/components/badge";
import { Loader2 } from "lucide-react";
import { cn } from "@ui/lib";

export function DashboardContent() {
  const t = useTranslations();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
  const [isDetailsSheetOpen, setIsDetailsSheetOpen] = useState(false);

  // Fetch dashboard summary data
  const { data: summaryData, isLoading: isSummaryLoading, isError, error } = useDashboardSummary();

  // Fetch transactions with pagination
  const { data: transactionsData, isLoading: isTransactionsLoading } = useTransactions({
    page: currentPage,
    limit: 10,
  });

  // Get transaction details when a transaction is selected
  const { data: transactionDetails } = useTransactionDetails(selectedTransactionId);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle opening transaction details
  const openTransactionDetails = (transactionId: string) => {
    setSelectedTransactionId(transactionId);
    setIsDetailsSheetOpen(true);
  };

  // Get status badge styling
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <Badge className="bg-emerald-500/10 text-emerald-500 border-emerald-500/20">
            Aprovado
          </Badge>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <Badge className="bg-rose-500/10 text-rose-500 border-rose-500/20">
            Rejeitado
          </Badge>
        );
      case "REFUNDED":
        return (
          <Badge className="bg-violet-500/10 text-violet-500 border-violet-500/20">
            Estornado
          </Badge>
        );
      case "PROCESSING":
      case "PENDING":
      default:
        return (
          <Badge className="bg-amber-500/10 text-amber-500 border-amber-500/20">
            Pendente
          </Badge>
        );
    }
  };

  // If there's an error, show an error message
  if (isError) {
    return (
      <div className="p-8 text-center">
        <div className="mb-4 text-rose-500 text-xl">Erro ao carregar dados do dashboard</div>
        <div className="text-muted-foreground mb-6">
          {error instanceof Error ? error.message : "Ocorreu um erro desconhecido"}
        </div>
        <div className="text-sm text-muted-foreground">
          Tente recarregar a página. Se o problema persistir, entre em contato com o suporte.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-5">
      <SummaryCards />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5 min-h-[400px]">
        <DashboardChart />
        <TransactionsByMethodChart />
      </div>

      <Card className="border border-gray-800 bg-gray-900/30 backdrop-blur-sm">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Últimas Transações</h3>
            {!isTransactionsLoading && transactionsData && (
              <span className="text-sm text-muted-foreground">
                Total: {transactionsData.pagination.total} transações
              </span>
            )}
          </div>

          <div className="overflow-x-auto">
            {isTransactionsLoading ? (
              <div className="flex justify-center items-center py-10">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Carregando transações...</span>
              </div>
            ) : !transactionsData || transactionsData.data.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">Não há transações para exibir</div>
            ) : (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800 text-left">
                    <th className="pb-2 font-medium text-muted-foreground text-sm" />
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Transação</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Cliente</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Descrição</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Data</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Pagamento</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Valor</th>
                    <th className="pb-2 font-medium text-muted-foreground text-sm">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {transactionsData.data.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                      onClick={() => openTransactionDetails(transaction.id)}
                    >
                      {/* Visual indicator for hover */}
                      <td className="absolute inset-y-0 left-0 w-1 bg-[#4caf50] opacity-0 group-hover:opacity-100 transition-opacity" aria-hidden="true"></td>
                      <td className="py-3 text-sm pl-4">{transaction.referenceCode || transaction.id}</td>
                      <td className="py-3 text-sm">
                        <div>
                          <div className="font-medium">{transaction.customerName}</div>
                          <div className="text-xs text-muted-foreground">{transaction.customerEmail}</div>
                        </div>
                      </td>
                      <td className="py-3 text-sm">
                        <div className="max-w-[150px]">
                          {transaction.description ? (
                            <span className="text-sm text-gray-300 truncate block" title={transaction.description}>
                              {transaction.description}
                            </span>
                          ) : (
                            <span className="text-xs text-muted-foreground">-</span>
                          )}
                        </div>
                      </td>
                      <td className="py-3 text-sm">{formatDate(transaction.createdAt)}</td>
                      <td className="py-3 text-sm">{transaction.paymentAt ? formatDate(transaction.paymentAt) : "-"}</td>
                      <td className="py-3 text-sm">{formatCurrency(transaction.amount)}</td>
                      <td className="py-3 text-sm">
                        {getStatusBadge(transaction.status)}
                      </td>

                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {transactionsData && transactionsData.pagination.pages > 1 && (
            <div className="mt-4 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={transactionsData.pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </Card>

      {/* Transaction details sheet */}
      {selectedTransactionId && (
        <TransactionDetailsSheet
          isOpen={isDetailsSheetOpen}
          onClose={() => {
            setIsDetailsSheetOpen(false);
            setSelectedTransactionId(null);
          }}
          transaction={transactionDetails}
        />
      )}
    </div>
  );
}
