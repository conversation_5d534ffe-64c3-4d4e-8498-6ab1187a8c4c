"use client";

import { useTranslations } from "next-intl";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { useState } from "react";
import { formatDate, formatDateTime } from "@shared/lib/format";
import { TransactionDetailsSheet } from "@saas/transactions/components/TransactionDetailsSheet";
import { useTransactionDetails } from "@saas/transactions/hooks/use-transactions";
import { getStatusBadgeVariant, getStatusDisplayText } from "@repo/utils/src/transaction-status";

// Define simpler transaction type for dashboard use
export interface DashboardTransaction {
  id: string;
  client: string;
  email: string;
  date: string;
  paymentDate: string;
  value: string;
  status: string;
  paymentMethod: string;
  description?: string;
}

interface DashboardTransactionsTableProps {
  transactions: DashboardTransaction[];
  isLoading?: boolean;
}

export function DashboardTransactionsTable({ transactions, isLoading = false }: DashboardTransactionsTableProps) {
  const t = useTranslations();
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Get transaction details when a transaction is selected
  const { data: transactionDetails } = useTransactionDetails(selectedTransactionId);

  const getStatusVariant = (status: string) => {
    const variant = getStatusBadgeVariant(status);

    switch (variant) {
      case "success":
        return "bg-emerald-500/10 text-emerald-500 border-emerald-500/20";
      case "warning":
        return "bg-amber-500/10 text-amber-500 border-amber-500/20";
      case "error":
        return "bg-rose-500/10 text-rose-500 border-rose-500/20";
      case "refunded":
        return "bg-violet-500/10 text-violet-500 border-violet-500/20";
      default:
        return "bg-gray-500/10 text-gray-500 border-gray-500/20";
    }
  };

  const handleRowClick = (id: string) => {
    setSelectedTransactionId(id);
    setIsDetailsModalOpen(true);
  };

  if (isLoading) {
    return <div className="text-center py-6 text-muted-foreground">Carregando transações...</div>;
  }

  if (transactions.length === 0) {
    return <div className="text-center py-6 text-muted-foreground">Não há transações para exibir</div>;
  }

  return (
    <>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-800 text-left">
              <th className="pb-2 font-medium text-muted-foreground text-sm">Transação</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Cliente</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Descrição</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Data</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Data Pagamento</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Valor</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Status</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm text-right">Ações</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((transaction) => (
              <tr
                key={transaction.id}
                className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors"
                onClick={() => handleRowClick(transaction.id)}
              >
                <td className="py-3 text-sm">
                  <div className="max-w-[180px] truncate font-mono text-xs">{transaction.id}</div>
                </td>
                <td className="py-3 text-sm">
                  <div className="max-w-[180px]">
                    <div className="truncate font-medium">{transaction.client}</div>
                    <div className="truncate text-xs text-muted-foreground">{transaction.email}</div>
                  </div>
                </td>
                <td className="py-3 text-sm">
                  <div className="max-w-[150px]">
                    {transaction.description ? (
                      <span className="text-sm text-gray-300 truncate block" title={transaction.description}>
                        {transaction.description}
                      </span>
                    ) : (
                      <span className="text-xs text-muted-foreground">-</span>
                    )}
                  </div>
                </td>
                <td className="py-3 text-sm">{transaction.date ? formatDate(transaction.date) : "-"}</td>
                <td className="py-3 text-sm">{transaction.paymentDate ? formatDate(transaction.paymentDate) : "-"}</td>
                <td className="py-3 text-sm">{transaction.value}</td>
                <td className="py-3 text-sm">
                  <Badge className={getStatusVariant(transaction.status)}>
                    {getStatusDisplayText(transaction.status)}
                  </Badge>
                </td>
                <td className="py-3 text-sm text-right">
                  <button className="text-gray-400 hover:text-white">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 8a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0zm5 0a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0zm5 0a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0z" fill="currentColor"/>
                    </svg>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Transaction details modal */}
      {selectedTransactionId && (
        <TransactionDetailsSheet
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false);
            setSelectedTransactionId(null);
          }}
          transaction={transactionDetails}
        />
      )}
    </>
  );
}
