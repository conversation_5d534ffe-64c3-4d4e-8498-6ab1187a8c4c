import type { OrganizationMetadata } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { auth } from "@repo/auth";

export const organizationListQueryKey = ["user", "organizations"] as const;
export const useOrganizationListQuery = () => {
	return useQuery({
		queryKey: organizationListQueryKey,
		queryFn: async () => {
			const { data, error } = await authClient.organization.list();

			if (error) {
				throw new Error(error.message || "Failed to fetch organizations");
			}

			return data;
		},
	});
};

export const activeOrganizationQueryKey = (slug: string) =>
	["user", "activeOrganization", slug] as const;
export const useActiveOrganizationQuery = (
	slug: string,
	options?: {
		enabled?: boolean;
	},
) => {
	return useQuery({
		queryKey: activeOrganizationQuery<PERSON>ey(slug),
		queryFn: async () => {
			const { data, error } = await authClient.organization.getFullOrganization(
				{
					query: {
						organizationSlug: slug,
					},
				},
			);

			if (error) {
				throw new Error(error.message || "Failed to fetch active organization");
			}

			return data;
		},
		enabled: options?.enabled,
	});
};

export const fullOrganizationQueryKey = (id: string) =>
	["fullOrganization", id] as const;
export const useFullOrganizationQuery = (organizationId: string) => {
	return useQuery({
		queryKey: fullOrganizationQueryKey(organizationId),
		queryFn: async () => {
			try {
				console.log(`🔍 Fetching organization data for ID: ${organizationId}`);

				// Tentar primeiro a nova API que retorna todos os dados
				const response = await fetch(`/api/organizations/${organizationId}/full`);
				if (response.ok) {
					const data = await response.json();
					console.log('✅ Organization data fetched successfully:', {
						id: data.id,
						name: data.name,
						status: data.status,
						hasLegalInfo: !!data.organization_legal_info,
						hasTaxes: !!data.taxes,
						gatewaysCount: data.PaymentGateway?.length || 0,
						availableGatewaysCount: data.availableGateways?.length || 0
					});

					// Ensure PaymentGateway is always an array
					if (!data.PaymentGateway) {
						data.PaymentGateway = [];
						console.log('⚠️ PaymentGateway field was missing, initialized as empty array');
					}

					// Log detailed legal info data for debugging
					if (data.organization_legal_info) {
						console.log('📋 Legal info details:', {
							fields: Object.keys(data.organization_legal_info),
							companyName: data.organization_legal_info.companyName,
							document: data.organization_legal_info.document,
							contactEmail: data.organization_legal_info.contactEmail
						});
					} else {
						console.log('⚠️ No legal info available in response from /full endpoint');

						// Try fetching legal info directly as a fallback
						try {
							const legalInfoResponse = await fetch(`/api/organizations/${organizationId}/legal-info`);
							if (legalInfoResponse.ok) {
								const legalInfoData = await legalInfoResponse.json();
								console.log('✅ Fetched legal info separately:', legalInfoData);
								data.organization_legal_info = legalInfoData;
							} else {
								console.log('❌ Failed to fetch legal info separately:', legalInfoResponse.status);
							}
						} catch (error) {
							console.error('❌ Error fetching legal info separately:', error);
						}
					}

					return data;
				}

				console.log('⚠️ /full endpoint failed, using fallback method');
				// Fallback para o método antigo
				const apiResponse = await auth.api.getFullOrganization({
					query: {
						organizationId,
					},
				});
				console.log('✅ Organization data fetched via fallback method:', {
					id: apiResponse.id,
					name: apiResponse.name,
					hasLegalInfo: !!apiResponse.legalInfo
				});

				// Log legal info in fallback method
				if (apiResponse.legalInfo) {
					console.log('📋 Legal info from fallback:', apiResponse.legalInfo);
				} else {
					console.log('⚠️ No legal info available in fallback method');
				}

				// Ensure PaymentGateway is always an array in fallback method
				if (!apiResponse.PaymentGateway) {
					apiResponse.PaymentGateway = [];
					console.log('⚠️ PaymentGateway field was missing in fallback, initialized as empty array');

					// Try to fetch gateways directly
					try {
						const gatewaysResponse = await fetch(`/api/admin/organizations/${organizationId}/gateways`);
						if (gatewaysResponse.ok) {
							const gatewaysData = await gatewaysResponse.json();
							if (gatewaysData.assignedGateways && Array.isArray(gatewaysData.assignedGateways)) {
								apiResponse.PaymentGateway = gatewaysData.assignedGateways;
								console.log('✅ Fetched gateways separately:', gatewaysData.assignedGateways.length, 'gateways');
							}
						}
					} catch (gatewaysError) {
						console.error('❌ Error fetching gateways separately:', gatewaysError);
					}
				}

				return apiResponse;
			} catch (error) {
				console.error('❌ Error fetching organization data:', error);
				throw error;
			}
		},
		enabled: !!organizationId,
	});
};

export const generateOrganizationSlug = async (name: string) => {
	const response = await apiClient.organizations["generate-slug"].$get({
		query: {
			name,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to generate organization slug");
	}

	const { slug } = await response.json();

	return slug;
};

/*
 * Create organization
 */
export const createOrganizationMutationKey = ["create-organization"] as const;
export const useCreateOrganizationMutation = () => {
	return useMutation({
		mutationKey: createOrganizationMutationKey,
		mutationFn: async ({
			name,
			metadata,
		}: { name: string; metadata?: OrganizationMetadata }) =>
			(
				await authClient.organization.create({
					name,
					slug: await generateOrganizationSlug(name),
					metadata,
				})
			).data,
	});
};

/*
 * Update organization
 */
export const updateOrganizationMutationKey = ["update-organization"] as const;
export const useUpdateOrganizationMutation = () => {
	return useMutation({
		mutationKey: updateOrganizationMutationKey,
		mutationFn: async ({
			id,
			name,
			metadata,
			updateSlug,
		}: {
			id: string;
			name: string;
			metadata?: OrganizationMetadata;
			updateSlug?: boolean;
		}) =>
			(
				await authClient.organization.update({
					organizationId: id,
					data: {
						name,
						slug: updateSlug ? await generateOrganizationSlug(name) : undefined,
						metadata,
					},
				})
			).data,
	});
};
