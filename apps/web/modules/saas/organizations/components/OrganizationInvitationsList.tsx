"use client";

import type { ActiveOrganization } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";
import {
	fullOrganizationQueryKey,
	useFullOrganizationQuery,
} from "@saas/organizations/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@ui/components/table";
import { useToast } from "@ui/hooks/use-toast";
import { cn } from "@ui/lib";
import { Badge } from "@ui/components/badge";
import {
	CheckIcon,
	ClockIcon,
	MailXIcon,
	MoreVerticalIcon,
	XIcon,
	RefreshCwIcon,
} from "lucide-react";
import { useFormatter } from "next-intl";
import { useMemo } from "react";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";

export function OrganizationInvitationsList({
	organizationId,
}: {
	organizationId: string;
}) {
	const { toast } = useToast();
	const queryClient = useQueryClient();
	const { user } = useSession();
	const formatter = useFormatter();
	const { data: organization } = useFullOrganizationQuery(organizationId);

	// Handle both 'members' and 'member' field names for compatibility
	const members = organization?.members || (organization as any)?.member || [];
	const userOrganizationRole = members.find(
		(member: any) => member.userId === user?.id,
	)?.role;
	const canUserEditInvitations =
		user?.role === "admin" ||
		(userOrganizationRole && ["owner", "admin"].includes(userOrganizationRole));

	const invitations = useMemo(
		() =>
			organization?.invitations
				?.filter((invitation) => invitation.status !== "canceled")
				.sort(
					(a, b) =>
						new Date(a.expiresAt).getTime() - new Date(b.expiresAt).getTime(),
				),
		[organization?.invitations],
	);

	const revokeInvitation = (invitationId: string) => {
		const loadingToast = toast({
			variant: "loading",
			description: "Revogando convite...",
		});

		authClient.organization.cancelInvitation(
			{
				invitationId,
			},
			{
				onSettled: () => {
					loadingToast.dismiss();
				},
				onSuccess: () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "success",
						description: "Convite revogado com sucesso",
					});
					queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});
				},
				onError: () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "error",
						description: "Erro ao revogar convite",
					});
				},
			},
		);
	};

	const resendInvitation = (invitationId: string) => {
		const loadingToast = toast({
			variant: "loading",
			description: "Reenviando convite...",
		});

		// This is a placeholder - you would need to implement the actual resend functionality
		setTimeout(() => {
			loadingToast.update({
				id: loadingToast.id,
				variant: "success",
				description: "Convite reenviado com sucesso",
			});
		}, 1000);
	};

	const getStatusBadge = (status: string) => {
		const statusConfig = {
			pending: {
				variant: "secondary" as const,
				icon: <ClockIcon className="mr-1 h-3 w-3" />,
				label: "Pendente"
			},
			accepted: {
				variant: "success" as const,
				icon: <CheckIcon className="mr-1 h-3 w-3" />,
				label: "Aceito"
			},
			rejected: {
				variant: "destructive" as const,
				icon: <XIcon className="mr-1 h-3 w-3" />,
				label: "Rejeitado"
			},
			canceled: {
				variant: "outline" as const,
				icon: <XIcon className="mr-1 h-3 w-3" />,
				label: "Cancelado"
			}
		};

		const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

		return (
			<Badge variant={config.variant} className="flex items-center">
				{config.icon}
				{config.label}
			</Badge>
		);
	};

	const columns: ColumnDef<
		NonNullable<ActiveOrganization["invitations"]>[number]
	>[] = [
		{
			accessorKey: "email",
			header: "Email",
			accessorFn: (row) => row.email,
			cell: ({ row }) => (
				<div className="font-medium">{row.original.email}</div>
			),
		},
		{
			accessorKey: "status",
			header: "Status",
			cell: ({ row }) => getStatusBadge(row.original.status),
		},
		{
			accessorKey: "expiresAt",
			header: "Expira em",
			cell: ({ row }) => (
				<div className="text-sm text-muted-foreground">
					{formatter.dateTime(new Date(row.original.expiresAt), {
						dateStyle: "medium",
						timeStyle: "short",
					})}
				</div>
			),
		},
		{
			accessorKey: "role",
			header: "Função",
			cell: ({ row }) => (
				<OrganizationRoleSelect
					value={row.original.role}
					disabled
					onSelect={() => {
						return;
					}}
				/>
			),
		},
		{
			accessorKey: "actions",
			header: "Ações",
			cell: ({ row }) => {
				const isPending = row.original.status === "pending";

				return (
					<div className="flex flex-row justify-end gap-2">
						{canUserEditInvitations && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button size="sm" variant="ghost">
										<MoreVerticalIcon className="size-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end">
									<DropdownMenuItem
										disabled={!isPending}
										onClick={() => resendInvitation(row.original.id)}
									>
										<RefreshCwIcon className="mr-2 size-4" />
										Reenviar convite
									</DropdownMenuItem>
									<DropdownMenuItem
										disabled={!isPending}
										onClick={() => revokeInvitation(row.original.id)}
										className="text-destructive"
									>
										<MailXIcon className="mr-2 size-4" />
										Revogar convite
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						)}
					</div>
				);
			},
		},
	];

	const table = useReactTable({
		data: invitations ?? [],
		columns,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
	});

	return (
		<div className="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						{table.getHeaderGroups().map((headerGroup) => (
							headerGroup.headers.map((header) => (
								<TableHead key={header.id}>
									{header.isPlaceholder
										? null
										: flexRender(
												header.column.columnDef.header,
												header.getContext()
										  )}
								</TableHead>
							))
						))}
					</TableRow>
				</TableHeader>
				<TableBody>
					{table.getRowModel().rows?.length ? (
						table.getRowModel().rows.map((row) => (
							<TableRow
								key={row.id}
								data-state={row.getIsSelected() && "selected"}
							>
								{row.getVisibleCells().map((cell) => (
									<TableCell key={cell.id}>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</TableCell>
								))}
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell colSpan={columns.length} className="h-24 text-center">
								Nenhum convite pendente.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	);
}
