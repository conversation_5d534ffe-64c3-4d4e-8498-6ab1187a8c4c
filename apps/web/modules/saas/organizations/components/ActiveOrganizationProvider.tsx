"use client";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { sessionQuery<PERSON>ey } from "@saas/auth/lib/api";
import {
	activeOrganizationQueryKey,
	useActiveOrganizationQuery,
} from "@saas/organizations/lib/api";
import { purchasesQueryKey } from "@saas/payments/lib/api";
import { useRouter } from "@shared/hooks/router";
import { apiClient } from "@shared/lib/api-client";
import { useQueryClient } from "@tanstack/react-query";
import { useParams, usePathname } from "next/navigation";
import nProgress from "nprogress";
import { type ReactNode, useEffect, useState } from "react";
import { ActiveOrganizationContext } from "../lib/active-organization-context";

export function ActiveOrganizationProvider({
	children,
}: {
	children: ReactNode;
}) {
	const router = useRouter();
	const queryClient = useQueryClient();
	const { session } = useSession();
	const params = useParams();
	const pathname = usePathname();

	const activeOrganizationSlug = params.organizationSlug as string;

	// Check if we're in onboarding context
	const isOnboarding = pathname.includes('/onboarding');

	console.log("=== ACTIVE ORGANIZATION PROVIDER DEBUG ===");
	console.log("Pathname:", pathname);
	console.log("Is Onboarding:", isOnboarding);
	console.log("Active Organization Slug:", activeOrganizationSlug);
	console.log("Session:", session ? "Available" : "Not available");

	const { data: activeOrganization } = useActiveOrganizationQuery(
		activeOrganizationSlug,
		{
			enabled: !!activeOrganizationSlug && !isOnboarding,
		},
	);

	const refetchActiveOrganization = async () => {
		if (activeOrganizationSlug) {
			await queryClient.refetchQueries({
				queryKey: activeOrganizationQueryKey(activeOrganizationSlug),
			});
		}
	};

	const setActiveOrganization = async (organizationSlug: string | null) => {
		nProgress.start();
		const { data: newActiveOrganization } =
			await authClient.organization.setActive(
				organizationSlug
					? {
							organizationSlug,
						}
					: {
							organizationId: null,
						},
			);

		if (!newActiveOrganization) {
			nProgress.done();
			return;
		}

		await queryClient.setQueryData(
			activeOrganizationQueryKey(newActiveOrganization.slug),
			newActiveOrganization,
		);

		if (config.organizations.enableBilling) {
			await queryClient.prefetchQuery({
				queryKey: purchasesQueryKey(newActiveOrganization.id),
				queryFn: async () => {
					const response = await apiClient.payments.purchases.$get({
						query: {
							organizationId: newActiveOrganization.id,
						},
					});

					if (!response.ok) {
						throw new Error("Failed to fetch purchases");
					}

					return response.json();
				},
			});
		}

		await queryClient.setQueryData(sessionQueryKey, (data: any) => {
			return {
				...data,
				session: {
					...data?.session,
					activeOrganizationId: newActiveOrganization.id,
				},
			};
		});

		router.push(`/app/${newActiveOrganization.slug}`);
	};

	const [loaded, setLoaded] = useState(false);

	useEffect(() => {
		// In onboarding context, we don't need to wait for active organization
		if (isOnboarding) {
			console.log("Onboarding context detected, setting loaded to true");
			setLoaded(true);
		} else if (activeOrganization !== undefined) {
			console.log("Active organization loaded:", activeOrganization?.id);
			setLoaded(true);
		} else if (!activeOrganizationSlug) {
			// No organization slug, but we're not in onboarding - this is normal for /app
			console.log("No organization slug, setting loaded to true");
			setLoaded(true);
		}
	}, [activeOrganization, activeOrganizationSlug, isOnboarding]);

	// Handle both 'members' and 'member' field names for compatibility
	const members = activeOrganization?.members || (activeOrganization as any)?.member || [];
	const activeOrganizationUserRole = members.find(
		(member: any) => member.userId === session?.userId,
	)?.role;

	const isOrganizationAdmin =
		!!activeOrganizationUserRole &&
		["admin", "owner"].includes(activeOrganizationUserRole);

	console.log("Active Organization Provider State:", {
		loaded,
		activeOrganization: activeOrganization?.id || "null",
		activeOrganizationUserRole,
		isOrganizationAdmin,
		isOnboarding
	});

	return (
		<ActiveOrganizationContext.Provider
			value={{
				loaded,
				activeOrganization: activeOrganization ?? null,
				activeOrganizationUserRole: activeOrganizationUserRole ?? null,
				isOrganizationAdmin,
				setActiveOrganization,
				refetchActiveOrganization,
			}}
		>
			{children}
		</ActiveOrganizationContext.Provider>
	);
}
