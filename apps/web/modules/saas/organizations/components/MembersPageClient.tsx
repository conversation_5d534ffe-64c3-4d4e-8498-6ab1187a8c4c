"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { UserPlusIcon } from "lucide-react";
import { OrganizationMembersList } from "./OrganizationMembersList";
import { OrganizationInvitationsList } from "./OrganizationInvitationsList";
import { useFullOrganizationQuery } from "@saas/organizations/lib/api";
import { InviteMemberDialog } from "@saas/organizations/components/InviteMemberDialog";

export interface MembersPageClientProps {
  organizationId: string;
  isAdmin: boolean;
  showInviteButton?: boolean;
}

export function MembersPageClient({
  organizationId,
  isAdmin,
  showInviteButton = true
}: MembersPageClientProps) {
  const [activeTab, setActiveTab] = useState("members");
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const { data: organization } = useFullOrganizationQuery(organizationId);

  // Verificar se já existe um proprietário
  // Handle both 'members' and 'member' field names for compatibility
  const members = organization?.members || (organization as any)?.member || [];
  const hasOwner = members.some((member: any) => member.role === "owner") || false;

  // Determinar o texto do botão com base na existência de um proprietário
  const buttonText = hasOwner ? "Convidar Membro" : "Convidar Proprietário";
  const defaultRole = hasOwner ? "member" : "owner";

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="mb-3">
          <CardTitle>Membros da Empresa</CardTitle>
          <CardDescription>
            Gerencie os membros e convites da sua organização
          </CardDescription>
        </div>
        {isAdmin && showInviteButton && (
          <Button
            onClick={() => setInviteDialogOpen(true)}
            className="ml-auto"
            size="sm"
          >
            <UserPlusIcon className="mr-2 h-4 w-4" />
            {buttonText}
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid w-full grid-cols-2 max-w-[400px]">
            <TabsTrigger value="members">Membros ativos</TabsTrigger>
            <TabsTrigger value="invitations">Convites pendentes</TabsTrigger>
          </TabsList>
          <TabsContent value="members">
            <OrganizationMembersList organizationId={organizationId} />
          </TabsContent>
          <TabsContent value="invitations">
            <OrganizationInvitationsList organizationId={organizationId} />
          </TabsContent>
        </Tabs>
      </CardContent>

      {isAdmin && showInviteButton && (
        <InviteMemberDialog
          organizationId={organizationId}
          open={inviteDialogOpen}
          onOpenChange={setInviteDialogOpen}
          defaultRole={defaultRole}
          showRoleSelector={hasOwner}
          dialogTitle={hasOwner ? "Convidar Membro" : "Convidar Proprietário"}
          dialogDescription={hasOwner
            ? "Convide um usuário para ser membro desta organização."
            : "Convide um usuário para ser o proprietário desta empresa. O proprietário terá acesso à configuração da empresa e poderá gerenciar membros."}
        />
      )}
    </Card>
  );
}
