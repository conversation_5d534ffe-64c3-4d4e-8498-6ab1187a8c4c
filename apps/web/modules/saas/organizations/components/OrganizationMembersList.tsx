"use client";
import type { OrganizationMemberRole } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { useSessionQuery } from "@saas/auth/lib/api";
import { useOrganizationMemberRoles } from "@saas/organizations/hooks/member-roles";
import {
	fullOrganizationQueryKey,
	useFullOrganizationQuery,
} from "@saas/organizations/lib/api";
import { UserAvatar } from "@shared/components/UserAvatar";
import { useQueryClient } from "@tanstack/react-query";
import type {
	ColumnDef,
	ColumnFiltersState,
	SortingState,
} from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from "@ui/components/table";
import { useToast } from "@ui/hooks/use-toast";
import { Badge } from "@ui/components/badge";
import { LogOutIcon, MoreVerticalIcon, SquareUserRoundIcon, TrashIcon } from "lucide-react";
import { useState } from "react";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";

export function OrganizationMembersList({
	organizationId,
}: {
	organizationId: string;
}) {	const queryClient = useQueryClient();
	const { data: sessionData } = useSessionQuery();
	const user = sessionData?.user;
	const { data: organization } = useFullOrganizationQuery(organizationId);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const { toast } = useToast();
	const memberRoles = useOrganizationMemberRoles();

	const isUserAdmin = user?.role === "admin";

	// const isUserAdmin = true

	// Handle both 'members' and 'member' field names for compatibility
	const members = organization?.members || (organization as any)?.member || [];
	const userOrganizationRole = members.find(
		(member: any) => member.userId === user?.id,
	)?.role;
	const isOrganizationAdmin =
		userOrganizationRole && ["admin", "owner"].includes(userOrganizationRole);

	const updateMemberRole = async (
		memberId: string,
		role: OrganizationMemberRole,
	) => {
		const loadingToast = toast({
			variant: "loading",
			description: "Atualizando função do membro...",
		});
		await authClient.organization.updateMemberRole(
			{
				memberId,
				role,
				organizationId,
			},
			{
				onSuccess: async () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "success",
						description: "Função do membro atualizada com sucesso",
					});
					await queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});
				},
				onError: () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "error",
						description: "Erro ao atualizar função do membro",
					});
				},
			},
		);
	};

	const impersonateUser = async (
		userId: string,
		{ name }: { name: string },
	) => {
		const { dismiss } = toast({
			variant: "loading",
			title: `Personificando ${name}...`,
		});
		await authClient.admin.impersonateUser({
			userId,
		});
		dismiss();
		window.location.href = new URL("/app", window.location.origin).toString();
	};

	const removeMember = async (memberId: string) => {
		const loadingToast = toast({
			variant: "loading",
			description: "Removendo membro...",
		});

		await authClient.organization.removeMember(
			{
				memberIdOrEmail: memberId,
				organizationId,
			},
			{
				onSuccess: async () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "success",
						description: "Membro removido com sucesso",
					});

					await queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});
				},
				onError: () => {
					loadingToast.update({
						id: loadingToast.id,
						variant: "error",
						description: "Erro ao remover membro",
					});
				},
			},
		);
	};

	const getRoleBadgeVariant = (role: string) => {
		switch (role) {
			case "owner":
				return "default";
			case "admin":
				return "secondary";
			default:
				return "outline";
		}
	};

	const columns: ColumnDef<
		NonNullable<typeof organization>["members"][number]
	>[] = [
		{
			accessorKey: "user",
			header: "Usuário",
			accessorFn: (row) => row.user,
			cell: ({ row }) =>
				row.original.user ? (
					<div className="flex items-center gap-3">
						<UserAvatar
							name={row.original.user.name ?? row.original.user.email}
							avatarUrl={row.original.user?.image}
						/>
						<div>
							<div className="font-medium">{row.original.user.name}</div>
							<div className="text-sm text-muted-foreground">
								{row.original.user.email}
							</div>
						</div>
					</div>
				) : null,
		},
		{
			accessorKey: "role",
			header: "Função",
			cell: ({ row }) => (
				<Badge variant={getRoleBadgeVariant(row.original.role)}>
					{memberRoles[row.original.role as keyof typeof memberRoles]}
				</Badge>
			),
		},
		{
			accessorKey: "actions",
			header: "Ações",
			cell: ({ row }) => {
				return (
					<div className="flex flex-row justify-end gap-2">
						{isOrganizationAdmin && (
							<OrganizationRoleSelect
								value={row.original.role}
								onSelect={async (value) =>
									updateMemberRole(row.original.id, value)
								}
								disabled={
									!isOrganizationAdmin || row.original.role === "owner"
								}
							/>
						)}
						{(isOrganizationAdmin || isUserAdmin) && (
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button size="icon" variant="ghost">
										<MoreVerticalIcon className="size-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent>
									{isUserAdmin && row.original.user && (
										<DropdownMenuItem
											onClick={() =>
												impersonateUser(row.original.userId, {
													name: row.original.user.name ?? row.original.user.email,
												})
											}
										>
											<SquareUserRoundIcon className="mr-2 size-4" />
											Personificar usuário
										</DropdownMenuItem>
									)}
									{row.original.userId !== user?.id && (
										<DropdownMenuItem
											disabled={!isUserAdmin && !isOrganizationAdmin}
											className="text-destructive"
											onClick={async () => removeMember(row.original.id)}
										>
											<TrashIcon className="mr-2 size-4" />
											Remover membro
										</DropdownMenuItem>
									)}
									{row.original.userId === user?.id && (
										<DropdownMenuItem
											className="text-destructive"
											onClick={async () => removeMember(row.original.id)}
										>
											<LogOutIcon className="mr-2 size-4" />
											Sair da organização
										</DropdownMenuItem>
									)}
								</DropdownMenuContent>
							</DropdownMenu>
						)}
					</div>
				);
			},
		},
	];

	const table = useReactTable({
		data: members,
		columns,
		manualPagination: true,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		state: {
			sorting,
			columnFilters,
		},
	});

	return (
		<div className="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						{table.getHeaderGroups().map((headerGroup) => (
							headerGroup.headers.map((header) => (
								<TableHead key={header.id}>
									{header.isPlaceholder
										? null
										: flexRender(
												header.column.columnDef.header,
												header.getContext()
										  )}
								</TableHead>
							))
						))}
					</TableRow>
				</TableHeader>
				<TableBody>
					{table.getRowModel().rows?.length ? (
						table.getRowModel().rows.map((row) => (
							<TableRow
								key={row.id}
								data-state={row.getIsSelected() && "selected"}
							>
								{row.getVisibleCells().map((cell) => (
									<TableCell key={cell.id}>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</TableCell>
								))}
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell colSpan={columns.length} className="h-24 text-center">
								Nenhum membro encontrado.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</div>
	);
}
