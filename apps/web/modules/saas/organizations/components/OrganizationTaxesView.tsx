"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { Alert, AlertDescription } from "@ui/components/alert";
import { AlertTriangleIcon, CalculatorIcon, ArrowRightLeftIcon, ArrowDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useOrganizationTaxes } from "../hooks/use-organization-taxes";
import { formatCurrency, formatPercentage } from "@shared/lib/format";

interface OrganizationTaxesViewProps {
  organizationId: string;
}

export function OrganizationTaxesView({ organizationId }: OrganizationTaxesViewProps) {
  const t = useTranslations();
  const { data: taxes, isLoading, error } = useOrganizationTaxes(organizationId);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangleIcon className="h-4 w-4" />
        <AlertDescription>
          {t("organizations.taxes.error")}
        </AlertDescription>
      </Alert>
    );
  }

  if (!taxes) {
    return (
      <Alert>
        <AlertTriangleIcon className="h-4 w-4" />
        <AlertDescription>
          {t("organizations.taxes.noTaxes")}
        </AlertDescription>
      </Alert>
    );
  }

  const calculateExample = (amount: number, percentFee: number, fixedFee: number) => {
    const percentAmount = (amount * percentFee) / 100;
    const totalFee = percentAmount + fixedFee;
    const netAmount = amount - totalFee;
    return { totalFee, netAmount };
  };



  const exampleAmount = 100; // R$ 100.00
  const chargeExample = calculateExample(exampleAmount, taxes.pixChargePercentFee, taxes.pixChargeFixedFee);
  const transferExample = calculateExample(exampleAmount, taxes.pixTransferPercentFee, taxes.pixTransferFixedFee);

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* PIX Charges Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ArrowDownIcon className="h-5 w-5 text-green-600" />
              {t("organizations.taxes.pixCharges.title")}
            </CardTitle>
            <CardDescription>
              {t("organizations.taxes.pixCharges.description")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">
                  {t("organizations.taxes.pixCharges.percentFee")}
                </label>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-lg font-mono">
                    {formatPercentage(taxes.pixChargePercentFee)}{t("organizations.taxes.percent")}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">
                  {t("organizations.taxes.pixCharges.fixedFee")}
                </label>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-lg font-mono">
                    {formatCurrency(taxes.pixChargeFixedFee)}
                  </Badge>
                </div>
              </div>
            </div>
            
            {/* Example calculation */}
            <div className="mt-4 p-3 bg-muted/50 rounded-lg">
              <p className="text-sm font-medium mb-2">
                {t("organizations.taxes.pixCharges.example")}
              </p>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>{t("organizations.taxes.totalFee")}:</span>
                  <span className="font-mono">{formatCurrency(chargeExample.totalFee)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>{t("organizations.taxes.netAmount")}:</span>
                  <span className="font-mono">{formatCurrency(chargeExample.netAmount)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* PIX Transfers Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ArrowRightLeftIcon className="h-5 w-5 text-blue-600" />
              {t("organizations.taxes.pixTransfers.title")}
            </CardTitle>
            <CardDescription>
              {t("organizations.taxes.pixTransfers.description")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">
                  {t("organizations.taxes.pixTransfers.percentFee")}
                </label>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-lg font-mono">
                    {formatPercentage(taxes.pixTransferPercentFee)}{t("organizations.taxes.percent")}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">
                  {t("organizations.taxes.pixTransfers.fixedFee")}
                </label>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-lg font-mono">
                    {formatCurrency(taxes.pixTransferFixedFee)}
                  </Badge>
                </div>
              </div>
            </div>
            
            {/* Example calculation */}
            <div className="mt-4 p-3 bg-muted/50 rounded-lg">
              <p className="text-sm font-medium mb-2">
                {t("organizations.taxes.pixTransfers.example")}
              </p>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>{t("organizations.taxes.totalFee")}:</span>
                  <span className="font-mono">{formatCurrency(transferExample.totalFee)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>{t("organizations.taxes.netAmount")}:</span>
                  <span className="font-mono">{formatCurrency(transferExample.netAmount)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalculatorIcon className="h-5 w-5" />
            Informações Adicionais
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">
                Moeda
              </label>
              <Badge variant="outline">BRL</Badge>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">
                Última Atualização
              </label>
              <p className="text-sm">
                {new Date(taxes.updatedAt).toLocaleDateString('pt-BR')}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">
                Criado em
              </label>
              <p className="text-sm">
                {new Date(taxes.createdAt).toLocaleDateString('pt-BR')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
