"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { useRouter } from "@shared/hooks/router";
import { useState, useEffect } from "react";
// Theme is managed by AuthWrapper
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@ui/components/card";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertCircleIcon, KeyIcon, UserIcon } from "lucide-react";
import { OrganizationLogo } from "./OrganizationLogo";

interface InvitationRegistrationFormProps {
  invitationId: string;
  email: string;
  organizationName: string;
  organizationSlug: string;
  role: string;
  organizationId: string;
  logoUrl?: string;
  isOwner?: boolean;
}

const formSchema = z.object({
  name: z.string().min(2, "Nome é obrigatório"),
  password: z.string().min(8, "A senha deve ter pelo menos 8 caracteres"),
  confirmPassword: z.string().min(8, "A confirmação de senha deve ter pelo menos 8 caracteres"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

export function InvitationRegistrationForm({
  invitationId,
  email,
  organizationName,
  organizationSlug,
  role,
  organizationId,
  logoUrl,
  isOwner = false,
}: InvitationRegistrationFormProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  // Theme is already managed by AuthWrapper

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    console.log("Formulário de registro: Iniciando registro de usuário");
    console.log("Formulário de registro: Dados do convite:", {
      invitationId,
      email,
      role,
      organizationId,
      isOwner
    });

    try {
      console.log("Formulário de registro: Enviando requisição para API");
      // Registrar o usuário
      const registerResponse = await fetch("/api/auth/register-from-invitation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          email,
          password: values.password,
          invitationId,
          role,
          organizationId,
        }),
      });

      console.log("Formulário de registro: Resposta da API:", registerResponse.status);

      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        console.error("Formulário de registro: Erro da API:", errorData);
        throw new Error(errorData.error || "Falha ao registrar usuário");
      }

      const responseData = await registerResponse.json();
      console.log("Formulário de registro: Registro bem-sucedido:", responseData);

      // Aguardar um momento para garantir que o registro foi processado
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Toast será exibido após o redirecionamento

      // Após o registro bem-sucedido, tentar fazer login automaticamente
      console.log("Formulário de registro: Tentando fazer login automático");

      try {
        // Aguardar um momento para garantir que o registro foi processado no banco de dados
        // e que o Better Auth possa reconhecer as credenciais - aumentar para 3 segundos
        console.log("Formulário de registro: Aguardando 3 segundos antes de tentar login");
        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log("Formulário de registro: Iniciando tentativa de login com", email);

        try {
          const signInResult = await authClient.signIn.email({
            email,
            password: values.password,
          });

          console.log("Formulário de registro: Resultado do login:", signInResult);

          if (signInResult.error) {
            console.error("Formulário de registro: Erro ao fazer login automático:", signInResult.error);
            throw signInResult.error;
          }

          // Check if there's a redirect that might be for 2FA
          // Since the Better Auth type might have changed, we need to be more careful
          if (signInResult?.data?.url?.includes('2fa')) {
            // Se 2FA estiver ativado, redirecionar para a página de 2FA
            toast({
              title: "Conta criada com sucesso",
              description: "Por favor, complete a autenticação de dois fatores.",
              variant: "success",
            });

            // Instead of using the URL from the response, construct our own with the right parameters
            // Para usuários convidados com role owner, redirecionar para o step 1 do onboarding
            router.push(`/auth/2fa?redirectTo=${isOwner || role === "owner" ?
              encodeURIComponent(`/app/onboarding?step=1&organizationId=${organizationId}`) :
              encodeURIComponent(`/app/${organizationSlug}`)}`);
            return;
          }

          toast({
            title: "Conta criada com sucesso",
            description: "Você foi autenticado automaticamente.",
            variant: "success",
          });

          // Se o login for bem-sucedido, redirecionar para a página apropriada
          if (isOwner || role === "owner") {
            // Para usuários convidados com role owner, redirecionar para o step 1 do onboarding
            // que corresponde ao OnboardingStep2 (informações da empresa) para usuários convidados
            router.push(`/app/onboarding?step=1&organizationId=${organizationId}`);
          } else {
            router.push(`/app/${organizationSlug}`);
          }
        } catch (loginError) {
          console.error("Erro específico ao tentar login automático:", loginError);
          throw loginError;
        }
      } catch (error) {
        console.error("Erro ao tentar login automático após registro:", error);

        // Mostrar mensagem mais informativa
        toast({
          title: "Conta criada com sucesso",
          description: "Redirecionando para a página de login. Por favor, faça login com suas credenciais.",
          variant: "success",
        });

        // Aguardar um momento para que o usuário veja a mensagem
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Em caso de erro, redirecionar para a página de login
        router.push(`/auth/login`);
      }
    } catch (error) {
      console.error("Erro ao registrar usuário:", error);
      toast({
        title: "Erro ao criar conta",
        description: error instanceof Error ? error.message : "Ocorreu um erro desconhecido",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-6">
        <OrganizationLogo
          name={organizationName}
          logoUrl={logoUrl}
          className="size-16 mx-auto mb-4"
        />
        <h1 className="text-2xl font-bold">Convite para {organizationName}</h1>
        <p className="text-muted-foreground mt-2">
          Você foi convidado para participar da organização {organizationName}.
          Complete seu cadastro para aceitar o convite.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Criar sua conta</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>Informações do convite</AlertTitle>
            <AlertDescription>
              <div className="mt-2">
                <strong>Email:</strong> {email}
              </div>
              <div>
                <strong>Função:</strong> {role === "owner" ? "Proprietário" : "Membro"}
              </div>
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome Completo</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Seu nome completo" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Senha</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Confirme sua senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" loading={isLoading}>
                <UserIcon className="mr-2 h-4 w-4" />
                Criar Conta e Aceitar Convite
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center text-sm text-muted-foreground">
          Ao criar uma conta, você concorda com os termos de serviço e política de privacidade.
        </CardFooter>
      </Card>
    </div>
  );
}
