import { useQuery } from "@tanstack/react-query";

export interface OrganizationTaxes {
  id: string;
  organizationId: string;
  pixChargePercentFee: number;
  pixTransferPercentFee: number;
  pixChargeFixedFee: number;
  pixTransferFixedFee: number;
  gatewaySpecificTaxes?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export const organizationTaxesQueryKey = (organizationId: string) =>
  ["organization", organizationId, "taxes"] as const;

export const useOrganizationTaxes = (organizationId: string) => {
  return useQuery({
    queryKey: organizationTaxesQueryKey(organizationId),
    queryFn: async (): Promise<OrganizationTaxes> => {
      const response = await fetch(`/api/organizations/${organizationId}/taxes`, {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error:", errorText);
        throw new Error("Failed to fetch organization taxes");
      }

      const data = await response.json();
      return data;
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
