import { useContext } from "react";
import { ActiveOrganizationContext } from "../lib/active-organization-context";

export const useActiveOrganization = () => {
	const activeOrganizationContext = useContext(ActiveOrganizationContext);

	type ActiveOrganizationContextType = NonNullable<
		typeof activeOrganizationContext
	>;

	if (activeOrganizationContext === undefined) {
		console.log("=== USE ACTIVE ORGANIZATION DEBUG ===");
		console.log("ActiveOrganizationContext is undefined - returning default values");

		return {
			activeOrganization: null,
			setActiveOrganization: () => Promise.resolve(),
			refetchActiveOrganization: () => Promise.resolve(),
			activeOrganizationUserRole: null,
			isOrganizationAdmin: false,
			loaded: true,
		} satisfies ActiveOrganizationContextType;
	}

	console.log("=== USE ACTIVE ORGANIZATION DEBUG ===");
	console.log("ActiveOrganizationContext available:", {
		loaded: activeOrganizationContext.loaded,
		activeOrganization: activeOrganizationContext.activeOrganization?.id || "null",
		activeOrganizationUserRole: activeOrganizationContext.activeOrganizationUserRole,
		isOrganizationAdmin: activeOrganizationContext.isOrganizationAdmin
	});

	return activeOrganizationContext;
};
