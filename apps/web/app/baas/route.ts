import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import { TransactionStatus } from "@prisma/client";
import { createWebhookEvent } from "@repo/payments/src/webhooks/service";

/**
 * Flow2Pay webhook handler for PIX events
 * This route handles incoming webhooks from Flow2Pay payment gateway
 * Endpoint: /baas
 *
 * Supported events:
 * - PixIn: Received payments (QR Code payments)
 * - PixOut: Sent payments (transfers)
 * - PixInReversal: Refunds of received payments
 * - PixOutReversalExternal: External refunds of sent payments
 */
export async function POST(request: NextRequest) {
  try {
    // Log the raw request for debugging
    const rawBody = await request.text();
    console.log("/baas Flow2Pay webhook =================================>>> ");
    logger.info("Received Flow2Pay webhook", {
      rawBody,
      headers: Object.fromEntries(request.headers),
      url: request.url,
      method: request.method,
      userAgent: request.headers.get('user-agent'),
      contentType: request.headers.get('content-type')
    });

    // Parse the webhook payload
    let payload: any;
    try {
      payload = JSON.parse(rawBody);
      logger.info("Parsed Flow2Pay webhook payload", {
        evento: payload.evento,
        timestamp: payload.horario || payload.dataHora,
        txid: payload.txid,
        idEnvio: payload.idEnvio,
        status: payload.status,
        valor: payload.valor,
        endToEndId: payload.endToEndId,
        codigoTransacao: payload.codigoTransacao,
        chavePix: payload.chavePix
      });
    } catch (error) {
      logger.error("Failed to parse Flow2Pay webhook payload", { error, rawBody });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Validate the webhook payload structure
    if (!payload.evento) {
      logger.error("Invalid Flow2Pay webhook payload format - missing evento", { payload });
      return NextResponse.json({ error: "Invalid payload format - missing evento" }, { status: 400 });
    }

    // Validate event type
    const validEvents = ['PixIn', 'PixOut', 'PixInReversal', 'PixOutReversalExternal'];
    if (!validEvents.includes(payload.evento)) {
      logger.error("Invalid Flow2Pay webhook event type", { evento: payload.evento, validEvents });
      return NextResponse.json({ error: "Invalid event type" }, { status: 400 });
    }

    // Validate webhook signature/token if provided
    if (payload.token) {
      const expectedToken = process.env.FLOW2PAY_EVENT_TOKEN;
      if (expectedToken && payload.token !== expectedToken) {
        logger.error("Invalid Flow2Pay webhook token", {
          providedToken: payload.token?.substring(0, 8) + "...",
          expectedExists: !!expectedToken
        });
        return NextResponse.json({ error: "Invalid authentication token" }, { status: 401 });
      }
    } else {
      logger.warn("Flow2Pay webhook received without token", { evento: payload.evento });
    }

    // Normalize event type for internal processing
    let eventType: string;
    switch (payload.evento) {
      case 'PixIn':
        eventType = 'pixin';
        break;
      case 'PixOut':
        eventType = 'pixout';
        break;
      case 'PixInReversal':
        eventType = 'pixin_reversal';
        break;
      case 'PixOutReversalExternal':
        eventType = 'pixout_reversal';
        break;
      default:
        eventType = payload.evento.toLowerCase();
    }

    // Handle the event based on its normalized type
    logger.info(`Processing Flow2Pay webhook as ${eventType} event`, {
      originalEvent: payload.evento,
      normalizedType: eventType,
      timestamp: payload.horario || payload.dataHora,
      hasToken: !!payload.token
    });

    // Process the webhook based on event type
    switch (eventType) {
      case 'pixin':
        await handlePixInPayment(payload);
        break;
      case 'pixout':
        await handlePixOutTransfer(payload);
        break;
      case 'pixin_reversal':
        await handlePixInReversal(payload);
        break;
      case 'pixout_reversal':
        await handlePixOutReversal(payload);
        break;
      default:
        logger.warn("Unhandled Flow2Pay webhook event type", {
          evento: payload.evento,
          eventType,
          data: payload
        });
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: "Webhook processed successfully",
      eventType: payload.evento
    });

  } catch (error) {
    logger.error("Error processing Flow2Pay webhook", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handle PixIn payment events (received payments via QR Code)
 *
 * Expected payload structure:
 * {
 *   "evento": "PixIn",
 *   "token": "event_token",
 *   "txid": "transaction_id",
 *   "endToEndId": "*********...",
 *   "codigoTransacao": "internal_code",
 *   "status": "Sucesso|Em processamento|Falha|Erro",
 *   "chavePix": "pix_key_used",
 *   "valor": 1000, // in centavos
 *   "horario": "2024-01-15T10:30:00Z",
 *   "pagador": {
 *     "nome": "Payer Name",
 *     "codigoBanco": "bank_code",
 *     "cpf_cnpj": "***123456**"
 *   },
 *   "recebedor": {
 *     "nome": "Receiver Name",
 *     "codigoBanco": "bank_code",
 *     "cpf_cnpj": "***654321**"
 *   }
 * }
 */
async function handlePixInPayment(payload: any) {
  const txid = payload.txid;
  const endToEndId = payload.endToEndId;
  const amount = payload.valor; // Amount in centavos
  const status = payload.status || 'Sucesso'; // Default to success for PixIn events
  const codigoTransacao = payload.codigoTransacao;
  const chavePix = payload.chavePix;
  const horario = payload.horario;

  logger.info("Processing Flow2Pay PixIn payment", {
    txid,
    endToEndId,
    amount,
    status,
    codigoTransacao,
    chavePix,
    horario,
    payerInfo: payload.pagador,
    receiverInfo: payload.recebedor
  });

  // Map Flow2Pay status to internal transaction status
  let transactionStatus: TransactionStatus;
  switch (status) {
    case 'Sucesso':
      transactionStatus = TransactionStatus.APPROVED;
      break;
    case 'Em processamento':
      transactionStatus = TransactionStatus.PENDING;
      break;
    case 'Falha':
    case 'Erro':
      transactionStatus = TransactionStatus.REJECTED;
      break;
    default:
      logger.warn("Unknown Flow2Pay status for PixIn", { status });
      transactionStatus = TransactionStatus.PENDING;
      break;
  }

  try {
    // Find the transaction by multiple possible identifiers
    // Priority: txid -> codigoTransacao -> metadata search
    const transaction = await db.transaction.findFirst({
      where: {
        OR: [
          // Direct match on externalId (most common case)
          { externalId: txid },
          // Match on codigoTransacao if available
          ...(codigoTransacao ? [{ externalId: codigoTransacao }] : []),
          // Search in metadata for various Flow2Pay identifiers
          { metadata: { path: ['txid'], equals: txid } },
          { metadata: { path: ['flow2pay_txid'], equals: txid } },
          ...(codigoTransacao ? [{ metadata: { path: ['codigoTransacao'], equals: codigoTransacao } }] : []),
          // Search by endToEndId if available
          ...(endToEndId ? [{ endToEndId }] : [])
        ]
      },
      include: {
        organization: true
      }
    });

    if (transaction) {
      // Prepare enhanced metadata with Flow2Pay webhook data
      const enhancedMetadata = {
        ...transaction.metadata as any,
        flow2pay_webhook: payload,
        flow2pay_status: status,
        endToEndId,
        chavePix,
        horario,
        codigoTransacao,
        // Payer information
        ...(payload.pagador && {
          payer: {
            nome: payload.pagador.nome,
            codigoBanco: payload.pagador.codigoBanco,
            cpf_cnpj: payload.pagador.cpf_cnpj
          }
        }),
        // Receiver information
        ...(payload.recebedor && {
          receiver: {
            nome: payload.recebedor.nome,
            codigoBanco: payload.recebedor.codigoBanco,
            cpf_cnpj: payload.recebedor.cpf_cnpj
          }
        }),
        updated_at: new Date().toISOString(),
        webhook_processed_at: new Date().toISOString()
      };

      // Update existing transaction
      await updateTransactionStatus(
        transaction.id,
        transactionStatus,
        transactionStatus === TransactionStatus.APPROVED ?
          (horario ? new Date(horario) : new Date()) : undefined
      );

      // Update metadata separately
      await db.transaction.update({
        where: { id: transaction.id },
        data: { metadata: enhancedMetadata }
      });

      // Store endToEndId if provided and not already set
      if (endToEndId && !transaction.endToEndId) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: { endToEndId }
        });
      }

      // Set payment timestamp for approved transactions
      if (transactionStatus === TransactionStatus.APPROVED && !transaction.paymentAt) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            paymentAt: horario ? new Date(horario) : new Date()
          }
        });
      }

      logger.info("Updated existing transaction from Flow2Pay PixIn webhook", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: transactionStatus,
        amount: amount / 100, // Convert to reais for logging
        endToEndId,
        txid
      });

      // Trigger webhook notifications for approved payments
      if (transactionStatus === TransactionStatus.APPROVED) {
        await createWebhookEvent({
          type: 'pix.payment.completed',
          payload: {
            id: transaction.id,
            organizationId: transaction.organizationId,
            amount: transaction.amount,
            status: transactionStatus,
            endToEndId,
            txid,
            type: 'PIX_IN',
            createdAt: transaction.createdAt,
            updatedAt: new Date(),
            paymentAt: horario ? new Date(horario) : new Date()
          },
          organizationId: transaction.organizationId,
          transactionId: transaction.id
        });
      }

    } else {
      logger.warn("Transaction not found for Flow2Pay PixIn webhook", {
        txid,
        codigoTransacao,
        endToEndId,
        amount: amount / 100, // Convert to reais for logging
        status,
        searchCriteria: {
          externalId: [txid, codigoTransacao].filter(Boolean),
          endToEndId,
          metadataFields: ['txid', 'flow2pay_txid', 'codigoTransacao']
        }
      });

      // For PixIn events, we might want to create a new transaction if none exists
      // This could happen if the QR code was created outside our system
      // For now, we just log the warning
    }

  } catch (error) {
    logger.error("Error processing Flow2Pay PixIn webhook", {
      error: error instanceof Error ? error.message : String(error),
      txid,
      endToEndId,
      amount,
      status,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle PixOut transfer events (sent payments)
 */
async function handlePixOutTransfer(payload: any) {
  const idEnvio = payload.idEnvio;
  const endToEndId = payload.endToEndId;
  const amount = payload.valor; // Amount in centavos
  const status = payload.status;
  const codigoTransacao = payload.codigoTransacao;
  const chavePix = payload.chavePix;
  const horario = payload.horario;

  logger.info("Processing Flow2Pay PixOut transfer", {
    idEnvio,
    endToEndId,
    amount,
    status,
    codigoTransacao,
    chavePix,
    horario,
    receiverInfo: payload.recebedor,
    error: payload.erro
  });

  // Map Flow2Pay status to internal transaction status
  let transactionStatus: TransactionStatus;
  switch (status) {
    case 'Sucesso':
      transactionStatus = TransactionStatus.APPROVED;
      break;
    case 'Em processamento':
      transactionStatus = TransactionStatus.PENDING;
      break;
    case 'Falha':
    case 'Erro':
      transactionStatus = TransactionStatus.REJECTED;
      break;
    default:
      logger.warn("Unknown Flow2Pay status for PixOut", { status });
      transactionStatus = TransactionStatus.PENDING;
      break;
  }

  try {
    // Find the transaction by multiple possible identifiers
    // Priority: idEnvio -> codigoTransacao -> metadata search
    const transaction = await db.transaction.findFirst({
      where: {
        OR: [
          // Direct match on externalId (most common case for PixOut)
          { externalId: idEnvio },
          // Match on codigoTransacao if available
          ...(codigoTransacao ? [{ externalId: codigoTransacao }] : []),
          // Search in metadata for various Flow2Pay identifiers
          { metadata: { path: ['idEnvio'], equals: idEnvio } },
          { metadata: { path: ['flow2pay_id_envio'], equals: idEnvio } },
          ...(codigoTransacao ? [{ metadata: { path: ['codigoTransacao'], equals: codigoTransacao } }] : []),
          // Search by endToEndId if available
          ...(endToEndId ? [{ endToEndId }] : [])
        ]
      },
      include: {
        organization: true
      }
    });

    if (transaction) {
      // Prepare enhanced metadata with Flow2Pay webhook data
      const enhancedMetadata = {
        ...transaction.metadata as any,
        flow2pay_webhook: payload,
        flow2pay_status: status,
        endToEndId,
        chavePix,
        horario,
        codigoTransacao,
        idEnvio,
        // Receiver information
        ...(payload.recebedor && {
          receiver: {
            nome: payload.recebedor.nome,
            codigoBanco: payload.recebedor.codigoBanco,
            cpf_cnpj: payload.recebedor.cpf_cnpj
          }
        }),
        // Error information if present
        ...(payload.erro && {
          error: {
            origem: payload.erro.origem,
            motivo: payload.erro.motivo,
            mensagem: payload.erro.mensagem
          }
        }),
        updated_at: new Date().toISOString(),
        webhook_processed_at: new Date().toISOString()
      };

      // Update existing transaction
      await updateTransactionStatus(
        transaction.id,
        transactionStatus,
        transactionStatus === TransactionStatus.APPROVED ?
          (horario ? new Date(horario) : new Date()) : undefined
      );

      // Update metadata separately
      await db.transaction.update({
        where: { id: transaction.id },
        data: { metadata: enhancedMetadata }
      });

      // Store endToEndId if provided and not already set
      if (endToEndId && !transaction.endToEndId) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: { endToEndId }
        });
      }

      // Set payment timestamp for approved transactions
      if (transactionStatus === TransactionStatus.APPROVED && !transaction.paymentAt) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            paymentAt: horario ? new Date(horario) : new Date()
          }
        });
      }

      logger.info("Updated existing transaction from Flow2Pay PixOut webhook", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: transactionStatus,
        amount: amount / 100, // Convert to reais for logging
        endToEndId,
        idEnvio
      });

      // Trigger webhook notifications
      const webhookType = transactionStatus === TransactionStatus.APPROVED ? 'pix.transfer.completed' :
                          transactionStatus === TransactionStatus.REJECTED ? 'pix.transfer.failed' :
                          'pix.transfer.updated';

      await createWebhookEvent({
        type: webhookType,
        payload: {
          id: transaction.id,
          organizationId: transaction.organizationId,
          amount: transaction.amount,
          status: transactionStatus,
          endToEndId,
          idEnvio,
          type: 'PIX_OUT',
          createdAt: transaction.createdAt,
          updatedAt: new Date(),
          ...(transactionStatus === TransactionStatus.APPROVED && {
            paymentAt: horario ? new Date(horario) : new Date()
          }),
          ...(payload.erro && { error: payload.erro })
        },
        organizationId: transaction.organizationId,
        transactionId: transaction.id
      });

    } else {
      logger.warn("Transaction not found for Flow2Pay PixOut webhook", {
        idEnvio,
        codigoTransacao,
        endToEndId,
        amount: amount / 100, // Convert to reais for logging
        status,
        searchCriteria: {
          externalId: [idEnvio, codigoTransacao].filter(Boolean),
          endToEndId,
          metadataFields: ['idEnvio', 'flow2pay_id_envio', 'codigoTransacao']
        }
      });
    }

  } catch (error) {
    logger.error("Error processing Flow2Pay PixOut webhook", {
      error: error instanceof Error ? error.message : String(error),
      idEnvio,
      endToEndId,
      amount,
      status,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle PixIn reversal events (refunds of received payments)
 */
async function handlePixInReversal(payload: any) {
  const endToEndId = payload.endToEndId;
  const amount = payload.valor; // Amount in centavos (negative for reversals)
  const status = payload.status;
  const idEnvio = payload.idEnvio;
  const codigoTransacao = payload.codigoTransacao;
  const horario = payload.horario;

  logger.info("Processing Flow2Pay PixIn reversal", {
    endToEndId,
    amount,
    status,
    idEnvio,
    codigoTransacao,
    horario,
    receiverInfo: payload.recebedor
  });

  // Map Flow2Pay status to internal transaction status
  const transactionStatus = status === 'Sucesso' ? TransactionStatus.APPROVED : TransactionStatus.REJECTED;

  try {
    // Find the original transaction by endToEndId
    const originalTransaction = await db.transaction.findFirst({
      where: {
        endToEndId,
        type: 'CHARGE'
      },
      include: {
        organization: true
      }
    });

    if (originalTransaction) {
      // Create a reversal transaction
      const reversalTransaction = await db.transaction.create({
        data: {
          organizationId: originalTransaction.organizationId,
          type: 'REFUND',
          amount: Math.abs(amount), // Ensure positive amount for refund
          status: transactionStatus,
          description: 'Estorno de PIX recebido',
          externalId: idEnvio || codigoTransacao || `reversal_${Date.now()}`,
          endToEndId,
          gatewayId: originalTransaction.gatewayId,
          originalTransactionId: originalTransaction.id,
          customerName: originalTransaction.customerName,
          customerEmail: originalTransaction.customerEmail,
          metadata: {
            flow2pay_webhook: payload,
            original_transaction_id: originalTransaction.id,
            flow2pay_status: status,
            reversal_type: 'PixInReversal',
            idEnvio,
            codigoTransacao,
            horario,
            // Receiver information (who receives the refund)
            ...(payload.recebedor && {
              receiver: {
                nome: payload.recebedor.nome,
                codigoBanco: payload.recebedor.codigoBanco,
                cpf_cnpj: payload.recebedor.cpf_cnpj
              }
            }),
            created_at: new Date().toISOString(),
            webhook_processed_at: new Date().toISOString()
          },
          paymentAt: transactionStatus === TransactionStatus.APPROVED ?
            (horario ? new Date(horario) : new Date()) : null
        }
      });

      logger.info("Created reversal transaction from Flow2Pay PixIn reversal webhook", {
        reversalTransactionId: reversalTransaction.id,
        originalTransactionId: originalTransaction.id,
        organizationId: originalTransaction.organizationId,
        status: transactionStatus,
        amount: Math.abs(amount) / 100, // Convert to reais for logging
        endToEndId
      });

      // Trigger webhook notifications
      await createWebhookEvent({
        type: 'pix.reversal.completed',
        payload: {
          id: reversalTransaction.id,
          originalTransactionId: originalTransaction.id,
          organizationId: originalTransaction.organizationId,
          amount: Math.abs(amount),
          status: transactionStatus,
          endToEndId,
          type: 'PIX_IN_REVERSAL',
          createdAt: reversalTransaction.createdAt,
          updatedAt: reversalTransaction.updatedAt,
          ...(transactionStatus === TransactionStatus.APPROVED && {
            paymentAt: horario ? new Date(horario) : new Date()
          })
        },
        organizationId: originalTransaction.organizationId,
        transactionId: reversalTransaction.id
      });

    } else {
      logger.warn("Original transaction not found for Flow2Pay PixIn reversal webhook", {
        endToEndId,
        amount: amount / 100, // Convert to reais for logging
        status,
        searchCriteria: {
          endToEndId,
          type: 'CHARGE'
        }
      });
    }

  } catch (error) {
    logger.error("Error processing Flow2Pay PixIn reversal webhook", {
      error: error instanceof Error ? error.message : String(error),
      endToEndId,
      amount,
      status,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle PixOut reversal events (refunds of sent payments)
 */
async function handlePixOutReversal(payload: any) {
  const endToEndId = payload.endToEndId;
  const amount = payload.valor; // Amount in centavos (positive for reversals)
  const status = payload.status;
  const idEnvio = payload.idEnvio;
  const codigoTransacao = payload.codigoTransacao;
  const horario = payload.horario;

  logger.info("Processing Flow2Pay PixOut reversal", {
    endToEndId,
    amount,
    status,
    idEnvio,
    codigoTransacao,
    horario,
    receiverInfo: payload.recebedor,
    error: payload.erro
  });

  // Map Flow2Pay status to internal transaction status
  const transactionStatus = status === 'Sucesso' ? TransactionStatus.APPROVED : TransactionStatus.REJECTED;

  try {
    // Find the original transaction by endToEndId
    const originalTransaction = await db.transaction.findFirst({
      where: {
        endToEndId,
        type: 'SEND'
      },
      include: {
        organization: true
      }
    });

    if (originalTransaction) {
      // Create a reversal transaction
      const reversalTransaction = await db.transaction.create({
        data: {
          organizationId: originalTransaction.organizationId,
          type: 'REFUND',
          amount: Math.abs(amount), // Ensure positive amount for refund
          status: transactionStatus,
          description: 'Estorno de PIX enviado',
          externalId: idEnvio || codigoTransacao || `reversal_${Date.now()}`,
          endToEndId,
          gatewayId: originalTransaction.gatewayId,
          originalTransactionId: originalTransaction.id,
          customerName: originalTransaction.customerName,
          customerEmail: originalTransaction.customerEmail,
          metadata: {
            flow2pay_webhook: payload,
            original_transaction_id: originalTransaction.id,
            flow2pay_status: status,
            reversal_type: 'PixOutReversalExternal',
            idEnvio,
            codigoTransacao,
            horario,
            // Receiver information
            ...(payload.recebedor && {
              receiver: {
                nome: payload.recebedor.nome,
                codigoBanco: payload.recebedor.codigoBanco,
                cpf_cnpj: payload.recebedor.cpf_cnpj
              }
            }),
            // Error information if present
            ...(payload.erro && {
              error: {
                origem: payload.erro.origem,
                motivo: payload.erro.motivo
              }
            }),
            created_at: new Date().toISOString(),
            webhook_processed_at: new Date().toISOString()
          },
          paymentAt: transactionStatus === TransactionStatus.APPROVED ?
            (horario ? new Date(horario) : new Date()) : null
        }
      });

      logger.info("Created reversal transaction from Flow2Pay PixOut reversal webhook", {
        reversalTransactionId: reversalTransaction.id,
        originalTransactionId: originalTransaction.id,
        organizationId: originalTransaction.organizationId,
        status: transactionStatus,
        amount: Math.abs(amount) / 100, // Convert to reais for logging
        endToEndId
      });

      // Trigger webhook notifications
      await createWebhookEvent({
        type: 'pix.reversal.completed',
        payload: {
          id: reversalTransaction.id,
          originalTransactionId: originalTransaction.id,
          organizationId: originalTransaction.organizationId,
          amount: Math.abs(amount),
          status: transactionStatus,
          endToEndId,
          type: 'PIX_OUT_REVERSAL',
          createdAt: reversalTransaction.createdAt,
          updatedAt: reversalTransaction.updatedAt,
          ...(transactionStatus === TransactionStatus.APPROVED && {
            paymentAt: horario ? new Date(horario) : new Date()
          })
        },
        organizationId: originalTransaction.organizationId,
        transactionId: reversalTransaction.id
      });

    } else {
      logger.warn("Original transaction not found for Flow2Pay PixOut reversal webhook", {
        endToEndId,
        amount: amount / 100, // Convert to reais for logging
        status,
        searchCriteria: {
          endToEndId,
          type: 'SEND'
        }
      });
    }

  } catch (error) {
    logger.error("Error processing Flow2Pay PixOut reversal webhook", {
      error: error instanceof Error ? error.message : String(error),
      endToEndId,
      amount,
      status,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
