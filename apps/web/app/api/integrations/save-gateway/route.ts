import { db } from "@repo/database";
import { auth, getOrganizationMembership } from "@repo/auth";
import { z } from "zod";
import { revalidatePath } from "next/cache";
import { headers } from "next/headers";

// Schema for the form data
const saveAcquirerSchema = z.object({
  gatewayId: z.string(),
  organizationId: z.string(),
  active: z.boolean().optional().default(false),
  default: z.boolean().optional().default(false),
  priority: z.string().optional().transform(val => val ? parseInt(val, 10) : 999)
}).passthrough(); // Allow additional fields for credentials

export async function POST(request: Request) {
  try {
    console.log("Processing save-gateway request...");

    // Authenticate the request using the correct method
    const session = await auth.api.getSession({
      headers: headers(),
    });

    if (!session?.user) {
      console.log("Authentication failed: No user in session");
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`Authenticated user: ${session.user.id}`);
    const user = session.user;

    // Parse the form data
    const formData = await request.formData();
    const rawData: Record<string, string | boolean> = {};

    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
      if (key === "active" || key === "default") {
        rawData[key] = value === "on";
      } else {
        rawData[key] = value.toString();
      }
    }

    // Validate the data
    const validatedData = saveAcquirerSchema.parse(rawData);
    const { gatewayId: gatewayType, organizationId, active, default: isDefault, priority, ...credentials } = validatedData;

    // Check if the user has permission for this organization
    console.log(`Checking membership for user ${user.id} in organization ${organizationId}`);
    const member = await getOrganizationMembership(user.id, organizationId);

    if (!member) {
      console.log(`No membership found for user ${user.id} in organization ${organizationId}`);
      return Response.json({ error: "Forbidden - Not a member" }, { status: 403 });
    }

    if (!['admin', 'owner'].includes(member.role)) {
      console.log(`User ${user.id} has insufficient role: ${member.role}`);
      return Response.json({ error: "Forbidden - Insufficient permissions" }, { status: 403 });
    }

    console.log(`User ${user.id} has permission with role: ${member.role}`);

    // Remove empty credentials
    Object.keys(credentials).forEach(key => {
      if (credentials[key] === "") {
        delete credentials[key];
      }
    });

    // Validate that the gateway type is supported
    const supportedGateways = ["ASAAS", "REFLOWPAY", "PRIMEPAG", "PIXIUM", "TRANSFEERA", "MERCADOPAGO", "PAGARME"];
    const gatewayTypeUpper = gatewayType.toUpperCase();

    if (!supportedGateways.includes(gatewayTypeUpper)) {
      return Response.json({ error: "Unsupported gateway type" }, { status: 400 });
    }

    // First, find or create the global payment gateway
    let globalGateway = await db.payment_gateway.findFirst({
      where: {
        type: gatewayTypeUpper,
        isGlobal: true
      }
    });

    if (!globalGateway) {
      // Create global gateway if it doesn't exist
      globalGateway = await db.payment_gateway.create({
        data: {
          name: gatewayType.charAt(0).toUpperCase() + gatewayType.slice(1),
          type: gatewayTypeUpper,
          isGlobal: true,
          canReceive: true,
          canSend: true
        }
      });
    }

    // Check if this is the first gateway being added to this organization
    const existingAssignments = await db.organization_gateway.findMany({
      where: { organizationId }
    });

    // Set isDefault to true if it's the first gateway or if it's Mercado Pago and no default is set
    let shouldBeDefault = existingAssignments.length === 0 ||
      (gatewayTypeUpper === "MERCADOPAGO" && !existingAssignments.some(g => g.isDefault));

    // If isDefault is explicitly set in the request, use that value instead
    if (formData.has("default")) {
      shouldBeDefault = formData.get("default") === "on";
    }

    // Check if there's an existing assignment for this gateway and organization
    const existingAssignment = await db.organization_gateway.findUnique({
      where: {
        gatewayId_organizationId: {
          gatewayId: globalGateway.id,
          organizationId
        }
      }
    });

    // If setting as default, remove default from other gateways
    if (isDefault) {
      console.log(`Setting gateway as default, removing default status from other gateways`);
      await db.organization_gateway.updateMany({
        where: {
          organizationId,
          isDefault: true,
          gatewayId: { not: globalGateway.id }
        },
        data: {
          isDefault: false,
        }
      });
    }

    if (existingAssignment) {
      // Update existing assignment
      console.log(`Updating existing gateway assignment for gateway ID: ${globalGateway.id}`);
      console.log(`Settings: active=${active}, default=${isDefault}, priority=${priority}`);

      await db.organization_gateway.update({
        where: {
          id: existingAssignment.id
        },
        data: {
          isActive: active,
          isDefault: isDefault,
          priority: priority,
          credentials: credentials as any // Type casting for prisma
        }
      });
      console.log(`Gateway assignment updated successfully`);
    } else {
      // Create new assignment
      console.log(`Creating new gateway assignment for gateway type ${gatewayTypeUpper}`);
      console.log(`Settings: active=${active}, default=${isDefault}, priority=${priority}`);

      await db.organization_gateway.create({
        data: {
          gateway: {
            connect: {
              id: globalGateway.id
            }
          },
          organization: {
            connect: {
              id: organizationId
            }
          },
          isActive: active,
          isDefault: isDefault,
          priority: priority,
          credentials: credentials as any // Type casting for prisma
        }
      });
      console.log(`New gateway assignment created successfully`);
    }

    // Revalidate the integrations pages
    console.log(`Revalidating integration pages`);
    revalidatePath(`/app/[organizationSlug]/integrations`);
    revalidatePath(`/app/[organizationSlug]/integrations/gateways`);
    revalidatePath(`/app/[organizationSlug]/integrations/gateways/${gatewayType}`);

    console.log(`Gateway configuration saved successfully`);
    return Response.json({ success: true });
  } catch (error) {
    console.error("Error saving acquirer:", error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : "Unknown error occurred";

    console.error(`Detailed error: ${errorMessage}`);

    return Response.json({
      error: "Failed to save acquirer configuration",
      details: errorMessage
    }, { status: 500 });
  }
}
