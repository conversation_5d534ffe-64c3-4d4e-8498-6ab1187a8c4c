import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@saas/auth/lib/server";
import { getServerApiClient } from "@shared/lib/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType } from "@prisma/client";
import { getOrganizationMembership } from "@repo/auth";
import { getOrganizationBalance, synchronizeOrganizationBalance } from "@repo/payments/src/balance/balance-service";
import { safeMonetaryConversion } from "@shared/lib/currency";

// Simple in-memory cache for dashboard data
const dashboardCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 30000; // 30 seconds cache

export async function GET(req: NextRequest) {
  console.log("[Dashboard Summary] Iniciando requisição");
  try {
    console.log("[Dashboard Summary] Obtendo sessão");
    const session = await getSession();

    if (!session) {
      console.log("[Dashboard Summary] Usuário não autenticado");
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    console.log("[Dashboard Summary] Sessão obtida com sucesso");

    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");

    if (!organizationId) {
      return new NextResponse(JSON.stringify({ error: "Missing organizationId" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Verificar se o usuário tem acesso à organização
    console.log(`[Dashboard Summary] Verificando acesso do usuário ${session.user.id} à organização ${organizationId}`);
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      console.log(`[Dashboard Summary] Usuário ${session.user.id} não tem acesso à organização ${organizationId}`);
      return new NextResponse(JSON.stringify({ error: "You don't have access to this organization" }), {
        status: 403,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    console.log(`[Dashboard Summary] Acesso verificado com sucesso: ${membership.role}`);

    // Check cache first
    const cacheKey = `dashboard_${organizationId}`;
    const cached = dashboardCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      console.log(`[Dashboard Summary] Retornando dados do cache`);
      return new NextResponse(JSON.stringify(cached.data), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "X-Cache": "HIT",
        },
      });
    }

    let dashboardData; // Declare dashboardData here

    // Buscar dados diretamente do banco de dados com timeout
    console.log(`[Dashboard Summary] Iniciando consulta ao banco de dados`);
    try {
      // Definir um timeout para as consultas ao banco de dados
      const dbTimeout = 30000; // 30 segundos
      const dbTimeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Timeout ao consultar o banco de dados'));
        }, dbTimeout);
      });

      // Função para executar consulta com timeout
      const executeWithTimeout = async <T>(dbQuery: Promise<T>): Promise<T> => {
        return Promise.race([dbQuery, dbTimeoutPromise]) as Promise<T>;
      };

      // Get current date and date 6 months ago for chart data
      const now = new Date();
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(now.getMonth() - 5); // Get 6 months of data (current month + 5 previous)
      sixMonthsAgo.setDate(1); // Start from the 1st day of the month
      sixMonthsAgo.setHours(0, 0, 0, 0);

      // Generate months array for chart data
      const months = [];
      for (let i = 0; i < 6; i++) {
        const date = new Date(sixMonthsAgo);
        date.setMonth(date.getMonth() + i);
        const monthName = date.toLocaleString('pt-BR', { month: 'short' });
        months.push({
          month: monthName,
          date: new Date(date.getFullYear(), date.getMonth(), 1),
        });
      }

      // Execute all queries in parallel for maximum performance
      console.log(`[Dashboard Summary] Executando queries em paralelo`);

      const [
        approvedTransactionsData,
        organizationBalance,
        refundTransactionsData,
        refundedTransactionsData,
        cautionaryBlocksData,
        cautionaryBlocksAmount,
        transactionsCount,
        transactionsByMonth,
        monthlyTransactionsData
      ] = await Promise.all([
        // 1. Get approved transactions aggregate
        executeWithTimeout(db.transaction.aggregate({
          where: {
            organizationId,
            status: TransactionStatus.APPROVED,
          },
          _sum: {
            amount: true,
          },
          _count: true,
        })),

        // 2. Get cached balance data
        getOrganizationBalance(organizationId),

        // 3. Get refund transactions (type: REFUND) aggregate
        executeWithTimeout(db.transaction.aggregate({
          where: {
            organizationId,
            type: TransactionType.REFUND,
            status: TransactionStatus.APPROVED,
          },
          _sum: {
            amount: true,
          },
          _count: true,
        })),

        // 4. Get refunded transactions (status: REFUNDED) aggregate
        executeWithTimeout(db.transaction.aggregate({
          where: {
            organizationId,
            status: TransactionStatus.REFUNDED,
          },
          _sum: {
            amount: true,
          },
          _count: true,
        })),

        // 5. Get cautionary blocks count
        executeWithTimeout(db.cautionary_block.aggregate({
          where: {
            transaction: {
              organizationId,
            },
            status: "ACTIVE",
          },
          _count: true,
        })),

        // 6. Get cautionary blocks amount
        executeWithTimeout(db.transaction.aggregate({
          where: {
            organizationId,
            cautionary_block: {
              some: {
                status: "ACTIVE",
              },
            },
          },
          _sum: {
            amount: true,
          },
        })),

        // 7. Get total transaction count
        executeWithTimeout(db.transaction.count({
          where: {
            organizationId,
          },
        })),

        // 8. Get transactions grouped by status and type for charts
        executeWithTimeout(db.transaction.groupBy({
          by: ['status', 'type'],
          where: {
            organizationId,
            createdAt: {
              gte: sixMonthsAgo,
            },
          },
          _count: true,
          _sum: {
            amount: true,
          },
        })),

        // 9. Get monthly transaction data for charts
        Promise.all(months.map(month => {
          const monthStart = new Date(month.date);
          const monthEnd = new Date(month.date);
          monthEnd.setMonth(monthEnd.getMonth() + 1);
          monthEnd.setDate(0); // Last day of the month

          return executeWithTimeout(db.transaction.groupBy({
            by: ['status', 'type'],
            where: {
              organizationId,
              createdAt: {
                gte: monthStart,
                lte: monthEnd,
              },
            },
            _count: true,
          }));
        }))
      ]);

      // Combine refunds data
      const refundsAmount = (refundTransactionsData._sum.amount || 0) + (refundedTransactionsData._sum.amount || 0);
      const refundsCount = (refundTransactionsData._count || 0) + (refundedTransactionsData._count || 0);

      console.log(`[Dashboard Summary] Queries executadas com sucesso:`, {
        approvedTransactions: approvedTransactionsData._count,
        refunds: refundsCount,
        cautionaryBlocks: cautionaryBlocksData._count,
        totalTransactions: transactionsCount,
        organizationBalance: organizationBalance ? 'Sim' : 'Não'
      });

      // Process data for pixTransactionsChart using proper monthly data
      let pixTransactionsChart = months.map((month, index) => {
        const monthData = monthlyTransactionsData[index] || [];

        const transacoes = monthData
          .filter(tx => tx.type === TransactionType.CHARGE && tx.status === TransactionStatus.APPROVED)
          .reduce((sum, tx) => sum + tx._count, 0);

        const estornos = monthData
          .filter(tx =>
            (tx.type === TransactionType.REFUND && tx.status === TransactionStatus.APPROVED) ||
            tx.status === TransactionStatus.REFUNDED
          )
          .reduce((sum, tx) => sum + tx._count, 0);

        const med = monthData
          .filter(tx => tx.type === TransactionType.RECEIVE)
          .reduce((sum, tx) => sum + tx._count, 0);

        return {
          month: month.month,
          transacoes,
          estornos,
          med
        };
      });

      // If no real data, add test data for demonstration
      if (pixTransactionsChart.every(item => item.transacoes === 0 && item.estornos === 0 && item.med === 0)) {
        // Do not generate mock data for empty charts
        pixTransactionsChart = months.map((month) => {
          return {
            month: month.month,
            transacoes: 0,
            estornos: 0,
            med: 0
          };
        });
      }

      // Process data for transactionsByMethodChart using aggregated data
      const approvedCount = transactionsByMonth
        .filter(tx => tx.status === TransactionStatus.APPROVED && tx.type === TransactionType.CHARGE)
        .reduce((sum, tx) => sum + tx._count, 0);

      const pendingCount = transactionsByMonth
        .filter(tx => [TransactionStatus.PENDING, TransactionStatus.PROCESSING].includes(tx.status as any))
        .reduce((sum, tx) => sum + tx._count, 0);

      const refusedCount = transactionsByMonth
        .filter(tx => [TransactionStatus.REJECTED, TransactionStatus.CANCELED, TransactionStatus.BLOCKED].includes(tx.status as any))
        .reduce((sum, tx) => sum + tx._count, 0);

      const chartRefundsCount = transactionsByMonth
        .filter(tx =>
          (tx.type === TransactionType.REFUND && tx.status === TransactionStatus.APPROVED) ||
          tx.status === TransactionStatus.REFUNDED
        )
        .reduce((sum, tx) => sum + tx._count, 0);

      const medCount = transactionsByMonth
        .filter(tx => tx.type === TransactionType.RECEIVE)
        .reduce((sum, tx) => sum + tx._count, 0);

      let transactionsByMethodChart = {
        approved: approvedCount,
        pending: pendingCount,
        refused: refusedCount,
        refunds: chartRefundsCount,
        med: medCount
      };

      // If no real data, add mock data for demonstration
      if (Object.values(transactionsByMethodChart).every(val => val === 0)) {
        // Do not generate mock data for empty charts
        transactionsByMethodChart = {
          approved: 0,
          pending: 0,
          refused: 0,
          refunds: 0,
          med: 0
        };
      }

      // Calculate metrics for display using precise decimal arithmetic
      const blocksAmount = safeMonetaryConversion(cautionaryBlocksAmount._sum.amount || 0, 'transaction_amount');

      // Ensure all monetary values are properly converted to numbers (from Decimal)
      // Database stores values in reais as Decimal(15,2), so we need to convert to number
      const availableBalance = organizationBalance?.availableBalance ? Number(organizationBalance.availableBalance) : 0;
      const pendingBalance = organizationBalance?.pendingBalance ? Number(organizationBalance.pendingBalance) : 0;
      const reservedBalance = organizationBalance?.reservedBalance ? Number(organizationBalance.reservedBalance) : 0;

      console.log(`[Dashboard Summary] Balance values from DB:`, {
        availableBalance: { raw: organizationBalance?.availableBalance, converted: availableBalance },
        pendingBalance: { raw: organizationBalance?.pendingBalance, converted: pendingBalance },
        reservedBalance: { raw: organizationBalance?.reservedBalance, converted: reservedBalance },
        blocksAmount,
        refundsAmount
      });

      // Return the data
      dashboardData = {
        availableBalance: {
          amount: availableBalance,
          total: availableBalance + pendingBalance,
        },
        cautionaryBlocks: {
          amount: blocksAmount,
        },
        transactionsCount: {
          count: transactionsCount,
        },
        securityReserve: {
          amount: reservedBalance,
        },
        refunds: {
          amount: safeMonetaryConversion(refundsAmount, 'transaction_amount'),
          count: refundsCount,
        },
        // Add chart data to response
        pixTransactionsChart,
        transactionsByMethodChart,
      };

      // Cache the result
      dashboardCache.set(cacheKey, {
        data: dashboardData,
        timestamp: Date.now()
      });

      // Send the response
      console.log(`[Dashboard Summary] Enviando resposta`);
      return new NextResponse(JSON.stringify(dashboardData), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "X-Cache": "MISS",
        },
      });

    } catch (error: unknown) {
      console.error(`[Dashboard Summary] Erro ao consultar banco de dados:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return new NextResponse(JSON.stringify({
        error: "Failed to fetch dashboard data",
        details: errorMessage
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
  } catch (error: unknown) {
    console.error(`[Dashboard Summary] Erro não tratado:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new NextResponse(JSON.stringify({
      error: "Internal server error",
      details: errorMessage
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}
