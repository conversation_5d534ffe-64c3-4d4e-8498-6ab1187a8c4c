import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

export async function POST(req: NextRequest) {
  try {
    console.log("=== ONBOARDING API STARTED ===");
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      console.error("Unauthorized request: No session found");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    console.log("User ID:", userId);

    // Parse request body
    let body;
    try {
      body = await req.json();
    } catch (error) {
      console.error("Failed to parse request body:", error);
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 });
    }

    const {
      organizationId,
      legalInfo
    } = body;

    // Validating required parameters
    if (!organizationId) {
      console.error("Missing organizationId in request");
      return NextResponse.json({ error: "Missing organizationId" }, { status: 400 });
    }

    if (!legalInfo) {
      console.error("Missing legalInfo in request");
      return NextResponse.json({ error: "Missing legalInfo" }, { status: 400 });
    }

    console.log("Onboarding API request:", {
      userId,
      organizationId,
      legalInfoFields: Object.keys(legalInfo)
    });

    // Verificar se a organização existe
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      console.error("Organization not found:", organizationId);
      return NextResponse.json({
        error: "Organization not found",
        organizationId
      }, { status: 404 });
    }

    console.log("Organization found:", {
      id: organization.id,
      name: organization.name,
      slug: organization.slug
    });

    // Verificar se o usuário já é membro da organização
    let member = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
    });

    // Se não for membro, criar a relação
    if (!member) {
      console.log("User is not a member of the organization. Creating member relationship...");
      try {
        member = await db.member.create({
          data: {
            id: nanoid(),
            userId,
            organizationId,
            role: "owner",
            createdAt: new Date(),
          },
        });
        console.log("Member relationship created successfully:", member.id);
      } catch (error) {
        console.error("Failed to create member relationship:", error);
        return NextResponse.json({
          error: "Failed to create member relationship",
          details: error instanceof Error ? error.message : "Unknown error"
        }, { status: 500 });
      }
    } else {
      console.log("User is already a member of the organization:", member.id);
    }

    // Atualizar o status da organização para pendente de revisão
    try {
      const updatedOrg = await db.organization.update({
        where: { id: organizationId },
        data: {
          status: "PENDING_REVIEW",
        },
      });
      console.log("Organization status updated to PENDING_REVIEW:", updatedOrg.id);
    } catch (error) {
      console.error("Failed to update organization status:", error);
      // Continue processing, this is not a critical error
    }

    // Extrair os campos da legalInfo
    const {
      companyName,
      tradingName,
      document,
      contactEmail,
      contactPhone,
      address,
      city,
      state,
      postalCode,
      documentType = "CNPJ",
      legalRepresentative = "",
      legalRepDocumentNumber = ""
    } = legalInfo;

    // Validar campos obrigatórios da legal info
    if (!companyName || !document || !contactEmail || !address || !city || !state || !postalCode) {
      console.error("Missing required fields in legalInfo:", {
        hasCompanyName: !!companyName,
        hasDocument: !!document,
        hasContactEmail: !!contactEmail,
        hasAddress: !!address,
        hasCity: !!city,
        hasState: !!state,
        hasPostalCode: !!postalCode
      });
      return NextResponse.json({ error: "Missing required fields in legal information" }, { status: 400 });
    }

    // Criar ou atualizar as informações legais
    try {
      const legalInfoRecord = await db.organization_legal_info.upsert({
        where: {
          organizationId,
        },
        create: {
          id: nanoid(),
          organizationId,
          companyName,
          tradingName: tradingName || "",
          document,
          contactEmail,
          contactPhone,
          address,
          city,
          state,
          postalCode,
          documentType,
          legalRepresentative: legalRepresentative || "",
          legalRepDocumentNumber: legalRepDocumentNumber || "",
          updatedAt: new Date(),
        },
        update: {
          companyName,
          tradingName: tradingName || "",
          document,
          contactEmail,
          contactPhone,
          address,
          city,
          state,
          postalCode,
          documentType,
          legalRepresentative: legalRepresentative || "",
          legalRepDocumentNumber: legalRepDocumentNumber || "",
        },
      });
      console.log("Legal info saved successfully:", legalInfoRecord.id);

      // Comment out the problematic auth.api.setActive call
      // Instead, just log a message about this step
      console.log("Skipping setting active organization due to API type errors");
      console.log("Organization slug that would be set as active:", organization.slug);

      // We'll rely on the front-end to handle the active organization selection

      console.log("=== ONBOARDING API COMPLETED SUCCESSFULLY ===");
      return NextResponse.json({
        success: true,
        message: "Onboarding information submitted successfully",
        organizationId,
        legalInfo: legalInfoRecord,
      });
    } catch (error) {
      console.error("Failed to save legal information:", error);
      return NextResponse.json({
        error: "Failed to save legal information",
        details: error instanceof Error ? error.message : "Unknown error"
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error in onboarding API:", error);
    logger.error("Error processing onboarding request", { error });
    return NextResponse.json(
      {
        error: "Failed to process request",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
