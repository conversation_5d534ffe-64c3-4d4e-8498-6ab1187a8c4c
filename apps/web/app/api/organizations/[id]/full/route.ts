import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: id } = await params;

  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verificar se o usuário é membro da organização ou admin
    const isMember = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: id,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!isMember && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Buscar organização com todos os dados relacionados
    const organization = await db.organization.findUnique({
      where: { id },
      include: {
        member: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                role: true,
              },
            },
          },
        },
        invitation: true,
        organization_legal_info: true,
        organization_taxes: true,
        organization_gateway: {
          include: {
            payment_gateway: true,
          },
          orderBy: [
            { isDefault: 'desc' },
            { priority: 'asc' },
          ],
        },
        pix_key: true,
      },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Buscar legal info diretamente (adicionando como garantia)
    const legalInfo = await db.organization_legal_info.findUnique({
      where: { organizationId: id },
    });

    // Buscar taxes diretamente (adicionando como garantia) - using correct Prisma client method name
    const taxes = await db.organization_taxes.findUnique({
      where: { organizationId: id },
    });

    console.log('Organization legal info from direct query:', legalInfo);
    console.log('Organization taxes from direct query:', taxes);

    // Obter os gateways globais disponíveis
    const globalGateways = await db.payment_gateway.findMany({
      where: {
        isGlobal: true,
        isActive: true
      },
      orderBy: [
        { priority: 'asc' },
      ],
    });

    // Transform gateways data to match expected format
    const transformedGateways = organization.organization_gateway.map(og => ({
      ...og.payment_gateway,
      isDefault: og.isDefault,
      isActive: og.isActive,
      priority: og.priority,
      relationId: og.id
    }));

    // Adicionar os gateways globais à resposta
    const response = {
      ...organization,
      PaymentGateway: transformedGateways, // Keep backward compatibility
      availableGateways: globalGateways,
      // Ensure legal info is included, prefer the directly queried legal info if available
      legalInfo: legalInfo || organization.organization_legal_info,
      // Ensure taxes are included, prefer the directly queried taxes if available
      taxes: taxes || organization.organization_taxes,
      // Ensure members is available with the correct name (frontend expects 'members')
      members: organization.member,
    };

    console.log('Returning organization data:', {
      id: response.id,
      name: response.name,
      status: response.status,
      hasLegalInfo: !!response.legalInfo,
      hasTaxes: !!response.taxes,
      gatewaysCount: transformedGateways.length,
      availableGatewaysCount: globalGateways.length,
      legalInfoSource: legalInfo ? 'direct query' : (organization.organization_legal_info ? 'included query' : 'none'),
      taxesSource: taxes ? 'direct query' : (organization.organization_taxes ? 'included query' : 'none'),
    });

    // Log detailed taxes information
    if (response.taxes) {
      console.log('Organization taxes details:', {
        pixChargePercentFee: response.taxes.pixChargePercentFee,
        pixTransferPercentFee: response.taxes.pixTransferPercentFee,
        pixChargeFixedFee: response.taxes.pixChargeFixedFee,
        pixTransferFixedFee: response.taxes.pixTransferFixedFee,
      });
    } else {
      console.log('⚠️ No taxes data available in the response');
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching organization:", error);

    // Add more detailed error information
    let errorMessage = "Failed to fetch organization";
    let errorDetails = "";

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || "";
    }

    console.error("Detailed error:", {
      message: errorMessage,
      details: errorDetails,
      organizationId: id
    });

    return NextResponse.json(
      { error: "Failed to fetch organization", message: errorMessage },
      { status: 500 }
    );
  }
}
