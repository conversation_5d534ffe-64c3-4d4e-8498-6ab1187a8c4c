import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    const { id: organizationId } = await params;

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Buscar a organização
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        status: true,
        organization_legal_info: {
          select: {
            companyName: true,
            document: true,
          }
        }
      }
    });

    if (!organization) {
      return NextResponse.json({ message: "Organização não encontrada" }, { status: 404 });
    }

    // Retornar o status da organização
    return NextResponse.json({
      id: organization.id,
      name: organization.name,
      status: organization.status,
      hasLegalInfo: !!organization.organization_legal_info,
      document: organization.organization_legal_info?.document
    });
  } catch (error: any) {
    logger.error("Erro ao obter status da organização:", { error });
    return NextResponse.json({
      message: error.message || "Erro ao obter status da organização"
    }, { status: 500 });
  }
}
