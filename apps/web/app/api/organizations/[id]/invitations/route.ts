import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils/lib/base-url";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { nanoid } from "nanoid";
import { headers } from "next/headers";

const InvitationSchema = z.object({
  email: z.string().email(),
  role: z.enum(["member", "owner"]), // Removendo "admin" das opções
  sendEmail: z.boolean().optional().default(true), // Controla se o email deve ser enviado
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Ensure params is properly awaited in Next.js route handlers
  const { id } = await params;
  const organizationId = id;

  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json(
        { error: "You must be logged in" },
        { status: 401 }
      );
    }

    // Check if this request is coming from an admin
    const isAdmin = session.user.role === "admin";
    console.log(`Request from user ${session.user.id}, is admin: ${isAdmin}`);

    // If it's an admin, we'll skip membership verification
    let canInvite = isAdmin;
    let organization = null;

    if (!isAdmin) {
      // Check if user can invite others to this organization
      const membership = await db.member.findUnique({
        where: {
          userId_organizationId: {
            userId: session.user.id,
            organizationId,
          },
        },
        include: {
          organization: true,
        },
      });

      if (!membership) {
        return NextResponse.json(
          { error: "You do not have permission to invite members to this organization" },
          { status: 403 }
        );
      }

      // Only allow owners and admins to invite users
      canInvite = membership.role === "owner" || membership.role === "admin";
      organization = membership.organization;
    } else {
      // For admins, just get the organization
      organization = await db.organization.findUnique({
        where: { id: organizationId }
      });
    }

    if (!canInvite) {
      return NextResponse.json(
        { error: "You do not have permission to invite members to this organization" },
        { status: 403 }
      );
    }

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    console.log("Invitation request body:", body);

    const validatedData = InvitationSchema.parse(body);
    console.log("Validated invitation data:", validatedData);

    // Check if user is already a member
    const existingMember = await db.member.findFirst({
      where: {
        organizationId,
        user: {
          email: validatedData.email,
        },
      },
    });

    if (existingMember) {
      console.log(`User with email ${validatedData.email} is already a member of organization ${organizationId}`);
      return NextResponse.json(
        { error: "User is already a member of this organization" },
        { status: 400 }
      );
    }

    // Check if there's a pending invitation
    const existingInvitation = await db.invitation.findFirst({
      where: {
        organizationId,
        email: validatedData.email,
        status: "pending",
      },
    });

    if (existingInvitation) {
      console.log(`There is already a pending invitation for ${validatedData.email} in organization ${organizationId}`);
      return NextResponse.json(
        { error: "There is already a pending invitation for this email" },
        { status: 400 }
      );
    }

    // Se o papel for "owner", verificar se já existe um proprietário
    if (validatedData.role === "owner") {
      // Verificar se já existe um proprietário na organização
      const existingOwner = await db.member.findFirst({
        where: {
          organizationId,
          role: "owner",
        },
      });

      // Verificar se existe um convite pendente para proprietário
      const pendingOwnerInvitation = await db.invitation.findFirst({
        where: {
          organizationId,
          role: "owner",
          status: "pending",
        },
      });

      console.log("Checking for existing owner:", {
        organizationId,
        existingOwner: existingOwner ? { id: existingOwner.id, role: existingOwner.role } : null,
        pendingOwnerInvitation: pendingOwnerInvitation ? { id: pendingOwnerInvitation.id, email: pendingOwnerInvitation.email } : null
      });

      if (existingOwner || pendingOwnerInvitation) {
        const errorDetail = existingOwner
          ? "Organization already has an owner"
          : "Organization already has a pending owner invitation";

        console.log(`Rejecting invitation: ${errorDetail}`);

        return NextResponse.json(
          { error: "This organization already has an owner or a pending owner invitation", detail: errorDetail },
          { status: 400 }
        );
      }
    }

    // Create the invitation
    const invitationId = nanoid();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire in 7 days

    // Criar o convite
    const invitationData = {
      id: invitationId,
      email: validatedData.email,
      role: validatedData.role,
      status: "pending",
      expiresAt,
      organization: {
        connect: {
          id: organizationId,
        },
      },
      // Use the user relation to set the inviterId
      user: {
        connect: {
          id: session.user.id
        }
      },
    };

    try {
      const invitation = await db.invitation.create({
        data: invitationData,
        include: {
          organization: true,
          user: true,
        },
      });

      // Enviar email de convite apenas se sendEmail for true
      if (validatedData.sendEmail) {
        try {
          // Usar a nova rota simplificada para convites
          const inviteUrl = `${getBaseUrl()}/invitation/${invitationId}`;

          console.log(`Sending invitation email to ${validatedData.email} for organization ${organizationId}`);

          await sendEmail({
            to: validatedData.email,
            templateId: "organizationInvitation",
            context: {
              organizationName: organization?.name || "nossa plataforma",
              url: inviteUrl,
            },
          });

          console.log(`Invitation email sent successfully to ${validatedData.email}`);
        } catch (emailError) {
          console.error("Error sending invitation email:", emailError);
          // Não falhar a operação se o email não puder ser enviado
        }
      } else {
        console.log(`Skipping invitation email for ${validatedData.email} as sendEmail=false`);
      }

      console.log("Invitation created successfully:", invitation.id);
      return NextResponse.json({ success: true, invitation });
    } catch (createError) {
      console.error("Error creating invitation in database:", createError instanceof Error ? createError.message : String(createError));
      return NextResponse.json(
        { error: "Failed to create invitation in database", details: createError instanceof Error ? createError.message : String(createError) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating invitation:", error instanceof Error ? error.message : String(error));
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create invitation", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
