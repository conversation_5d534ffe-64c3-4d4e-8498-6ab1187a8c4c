import { config } from "@repo/config";
import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: organizationId } = await params;
    console.log(`Processing document upload for organization: ${organizationId}`);

    // Verify organization exists and user has access
    const userOrganization = await db.member.findFirst({
      where: {
        organizationId,
        userId: session.user.id,
      },
    });

    if (!userOrganization) {
      console.error(`User ${session.user.id} does not have access to organization ${organizationId}`);
      return NextResponse.json(
        { error: "Organization not found or you don't have access" },
        { status: 404 }
      );
    }

    const data = await request.json();
    console.log("Received document data:", {
      cnpjDocument: data.cnpjDocument ? "✓" : "✗",
      businessLicense: data.businessLicense ? "✓" : "✗",
      representativeIdDocument: data.representativeIdDocument ? "✓" : "✗",
    });

    // Validate required documents
    if (!data.cnpjDocument) {
      return NextResponse.json(
        { error: "Comprovante de CNPJ é obrigatório" },
        { status: 400 }
      );
    }

    if (!data.representativeIdDocument) {
      return NextResponse.json(
        { error: "Documento de identidade do responsável legal é obrigatório" },
        { status: 400 }
      );
    }

    // Check if documents already exist for this organization
    const existingDocumentsResult = await db.$queryRaw<Array<{ id: string }>>`
      SELECT id FROM "organization_documents" WHERE "organizationId" = ${organizationId}
    `;

    const documentExists = existingDocumentsResult.length > 0;
    let documentId;

    if (documentExists) {
      // Update existing documents
      console.log(`Updating documents for organization ${organizationId}`);
      const updatedDocumentsResult = await db.$queryRaw<Array<{ id: string }>>`
        UPDATE "organization_documents"
        SET
          "cnpjDocument" = ${data.cnpjDocument},
          "businessLicense" = ${data.businessLicense || null},
          "representativeIdDocument" = ${data.representativeIdDocument},
          "status" = 'PENDING_REVIEW',
          "submittedById" = ${session.user.id},
          "updatedAt" = now()
        WHERE "organizationId" = ${organizationId}
        RETURNING id
      `;

      documentId = updatedDocumentsResult[0]?.id;
    } else {
      // Store document references in database
      console.log(`Creating new documents for organization ${organizationId}`);
      const newDocumentsResult = await db.$queryRaw<Array<{ id: string }>>`
        INSERT INTO "organization_documents" (
          id,
          "organizationId",
          "cnpjDocument",
          "businessLicense",
          "representativeIdDocument",
          status,
          "submittedById",
          "createdAt",
          "updatedAt"
        )
        VALUES (
          gen_random_uuid()::text,
          ${organizationId},
          ${data.cnpjDocument},
          ${data.businessLicense || null},
          ${data.representativeIdDocument},
          'PENDING_REVIEW',
          ${session.user.id},
          now(),
          now()
        )
        RETURNING id
      `;

      documentId = newDocumentsResult[0]?.id;
    }

    // Update organization status to PENDING_REVIEW
    await db.organization.update({
      where: {
        id: organizationId,
      },
      data: {
        status: "PENDING_REVIEW",
      },
    });

    return NextResponse.json({
      success: true,
      documentId: documentId
    });
  } catch (error) {
    console.error("Error handling document upload:", error);
    return NextResponse.json(
      { error: "Failed to process documents" },
      { status: 500 }
    );
  }
}
