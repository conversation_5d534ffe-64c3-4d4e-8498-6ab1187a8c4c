import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: id } = await params;

  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verificar se o usuário é membro da organização ou admin
    const isMember = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: id,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!isMember && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get taxes - using correct Prisma client method name
    const taxes = await db.organization_taxes.findUnique({
      where: { organizationId: id },
    });

    if (!taxes) {
      // Create default taxes if they don't exist
      const defaultTaxes = await db.organization_taxes.create({
        data: {
          organizationId: id,
          pixChargePercentFee: 0,
          pixTransferPercentFee: 0,
          pixChargeFixedFee: 0,
          pixTransferFixedFee: 0,
        },
      });

      return NextResponse.json(defaultTaxes);
    }

    return NextResponse.json(taxes);
  } catch (error) {
    logger.error("Error getting organization taxes", error);
    return NextResponse.json(
      { error: "Failed to get taxes" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: id } = await params;

  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const body = await req.json();
    const {
      pixChargePercentFee,
      pixTransferPercentFee,
      pixChargeFixedFee,
      pixTransferFixedFee,
      gatewaySpecificTaxes
    } = body;

    // Verificar se o usuário é membro da organização ou admin
    const isMember = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: id,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!isMember && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Convert string values to numbers
    const pixChargePercentFeeNum = pixChargePercentFee !== undefined ? Number(pixChargePercentFee) : 0;
    const pixTransferPercentFeeNum = pixTransferPercentFee !== undefined ? Number(pixTransferPercentFee) : 0;
    const pixChargeFixedFeeNum = pixChargeFixedFee !== undefined ? Number(pixChargeFixedFee) : 0;
    const pixTransferFixedFeeNum = pixTransferFixedFee !== undefined ? Number(pixTransferFixedFee) : 0;

    // Upsert taxes - using correct Prisma client method name
    const taxes = await db.organization_taxes.upsert({
      where: { organizationId: id },
      create: {
        organizationId: id,
        pixChargePercentFee: pixChargePercentFeeNum,
        pixTransferPercentFee: pixTransferPercentFeeNum,
        pixChargeFixedFee: pixChargeFixedFeeNum,
        pixTransferFixedFee: pixTransferFixedFeeNum,
        gatewaySpecificTaxes: gatewaySpecificTaxes,
      },
      update: {
        pixChargePercentFee: pixChargePercentFeeNum,
        pixTransferPercentFee: pixTransferPercentFeeNum,
        pixChargeFixedFee: pixChargeFixedFeeNum,
        pixTransferFixedFee: pixTransferFixedFeeNum,
        gatewaySpecificTaxes: gatewaySpecificTaxes !== undefined ? gatewaySpecificTaxes : undefined,
      },
    });

    return NextResponse.json(taxes);
  } catch (error) {
    logger.error("Error processing organization taxes request", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
