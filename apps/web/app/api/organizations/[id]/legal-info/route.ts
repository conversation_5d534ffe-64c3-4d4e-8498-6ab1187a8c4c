import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await req.json();
    const {
      companyName,
      document,
      contactEmail,
      contactPhone,
      reviewNotes,
      tradingName,
      legalRepresentative,
      legalRepDocumentNumber,
      address,
      city,
      state,
      postalCode,
      documentType = "CNPJ"
    } = body;

    // Verificar se o usuário é membro da organização ou admin
    const isMember = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: id,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!isMember && !isAdmin) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Check if legal info exists
    const existingLegalInfo = await db.organization_legal_info.findUnique({
      where: { organizationId: id },
    });

    if (existingLegalInfo) {
      // Update existing legal info
      const updatedLegalInfo = await db.organization_legal_info.update({
        where: { organizationId: id },
        data: {
          companyName: companyName || existingLegalInfo.companyName,
          document: document || existingLegalInfo.document,
          contactEmail: contactEmail || existingLegalInfo.contactEmail,
          contactPhone: contactPhone || existingLegalInfo.contactPhone,
          reviewNotes: reviewNotes !== undefined ? reviewNotes : existingLegalInfo.reviewNotes,
          tradingName: tradingName !== undefined ? tradingName : existingLegalInfo.tradingName,
          legalRepresentative: legalRepresentative || existingLegalInfo.legalRepresentative,
          legalRepDocumentNumber: legalRepDocumentNumber || existingLegalInfo.legalRepDocumentNumber,
          address: address || existingLegalInfo.address,
          city: city || existingLegalInfo.city,
          state: state || existingLegalInfo.state,
          postalCode: postalCode || existingLegalInfo.postalCode,
          documentType: documentType || existingLegalInfo.documentType,
        },
      });

      // Atualizar status da organização para PENDING_REVIEW se ainda não estiver definido
      if (organization.status === "PENDING_REVIEW") {
        await db.organization.update({
          where: { id },
          data: { status: "PENDING_REVIEW" },
        });
      }

      return NextResponse.json(updatedLegalInfo);
    } else {
      // Create new legal info with all required fields
      const newLegalInfo = await db.organization_legal_info.create({
        data: {
          id: nanoid(),
          organizationId: id,
          companyName: companyName || "",
          document: document || "",
          contactEmail: contactEmail || "",
          contactPhone: contactPhone || "",
          reviewNotes: reviewNotes,
          documentType: documentType || "CNPJ",
          legalRepresentative: legalRepresentative || "",
          legalRepDocumentNumber: legalRepDocumentNumber || "",
          address: address || "",
          city: city || "",
          state: state || "",
          postalCode: postalCode || "",
          tradingName: tradingName,
        },
      });

      // Atualizar status da organização para PENDING_REVIEW
      await db.organization.update({
        where: { id },
        data: { status: "PENDING_REVIEW" },
      });

      return NextResponse.json(newLegalInfo);
    }
  } catch (error) {
    logger.error("Error processing organization legal info request", { error });
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: id } = await params;
  console.log(`🔍 Fetching legal info for organization ID: ${id}`);

  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      console.log(`❌ Unauthorized request for legal info, ID: ${id}`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verificar se o usuário é membro da organização ou admin
    const isMember = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: id,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!isMember && !isAdmin) {
      console.log(`❌ Forbidden: User ${session.user.id} is not a member of organization ${id} and not an admin`);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      console.log(`❌ Organization not found, ID: ${id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // First check - direct query for legal info
    const legalInfo = await db.organization_legal_info.findUnique({
      where: { organizationId: id },
    });

    if (!legalInfo) {
      console.log(`⚠️ Legal info not found for organization: ${id}`);

      // Try fallback query to ensure it's not a relation issue
      const orgWithLegalInfo = await db.organization.findUnique({
        where: { id },
        include: { organization_legal_info: true }
      });

      if (orgWithLegalInfo?.organization_legal_info) {
        console.log(`✅ Found legal info through relation: ${JSON.stringify(orgWithLegalInfo.organization_legal_info)}`);
        return NextResponse.json(orgWithLegalInfo.organization_legal_info);
      }

      return NextResponse.json({ error: "Legal info not found" }, { status: 404 });
    }

    console.log(`✅ Found legal info: ${JSON.stringify({
      id: legalInfo.id,
      organizationId: legalInfo.organizationId,
      companyName: legalInfo.companyName,
      document: legalInfo.document
    })}`);

    return NextResponse.json(legalInfo);
  } catch (error) {
    console.error(`❌ Error getting organization legal info: ${error instanceof Error ? error.message : String(error)}`);
    logger.error("Error getting organization legal info", { error });
    return NextResponse.json(
      { error: "Failed to get legal info" },
      { status: 500 }
    );
  }
}
