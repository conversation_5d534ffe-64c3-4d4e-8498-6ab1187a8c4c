import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { getOrganizationMembershipBySlug } from "@saas/organizations/lib/server";

export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ slug: string }> | { slug: string } }
) {
  try {
    // Ensure params is awaited if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { slug } = resolvedParams;

    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    if (!slug) {
      return NextResponse.json(
        { error: "Slug da organização é obrigatório" },
        { status: 400 }
      );
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembershipBySlug(session.user.id, slug);

    // Buscar a organização
    const organization = await db.organization.findUnique({
      where: { slug },
      select: {
        id: true,
        name: true,
        status: true,
      }
    });

    if (!organization) {
      return NextResponse.json({ message: "Organização não encontrada" }, { status: 404 });
    }

    // Buscar informações legais separadamente para evitar problemas de relação
    let legalInfo = null;
    try {
      legalInfo = await db.organization_legal_info.findUnique({
        where: { organizationId: organization.id },
        select: {
          companyName: true,
          document: true,
        }
      });
    } catch (error) {
      console.log("Erro ao buscar informações legais:", error);
      // Continue sem as informações legais
    }

    // Verificar se o usuário é admin (pode acessar qualquer organização)
    const isAdmin = session.user.role === "admin";

    // Se não for admin e não for membro, não pode acessar
    if (!isAdmin && !membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Retornar o status da organização
    return NextResponse.json({
      id: organization.id,
      name: organization.name,
      status: organization.status,
      hasLegalInfo: !!legalInfo,
      document: legalInfo?.document
    });
  } catch (error: any) {
    // Safely resolve params for error logging
    let slugForLogging: string | undefined;
    try {
      const resolvedParams = params instanceof Promise ? await params : params;
      slugForLogging = resolvedParams?.slug;
    } catch {
      slugForLogging = "unknown";
    }

    // Safely handle error logging with proper object structure
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      slug: slugForLogging
    };

    logger.error("Erro ao obter status da organização por slug:", errorInfo);

    return NextResponse.json({
      message: error.message || "Erro ao obter status da organização"
    }, { status: 500 });
  }
}
