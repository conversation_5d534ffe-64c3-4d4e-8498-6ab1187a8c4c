import { NextRequest, NextResponse } from "next/server";
import { auth, getOrganizationMembership } from "@repo/auth";
import { headers } from "next/headers";
import * as apiKeysApi from "@repo/api/src/api-keys";
import { z } from "zod";
import { withApiKeyRateLimit } from "../../../middleware/rate-limit";


// Define schemas for validation
const ApiKeyTypeEnum = z.enum(["production", "test"]);

const CreateApiKeySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  type: ApiKeyTypeEnum,
  organizationId: z.string().min(1, "ID da organização é obrigatório"),
  permissions: z.object({
    transactions: z.object({
      read: z.boolean(),
      write: z.boolean(),
    }),
    customers: z.object({
      read: z.boolean(),
      write: z.boolean(),
    }),
  }),
});

async function postHandler(req: NextRequest) {
  try {
    // Verify authentication
    const headersList = headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    if (!session?.user?.id) {
      console.error("Unauthorized access attempt to create API key");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    console.log("Received API key creation request:", { ...body, userId: session.user.id });
    const result = CreateApiKeySchema.safeParse(body);

    if (!result.success) {
      console.error("Invalid API key creation data", result.error.flatten());
      return NextResponse.json(
        { error: "Invalid input", details: result.error.flatten() },
        { status: 400 }
      );
    }

    const { name, type, permissions, organizationId } = result.data;

    // Call the API directly
    try {
      console.log(`Creating API key '${name}' for organization ${organizationId} by user ${session.user.id}`);
      const apiKey = await apiKeysApi.createApiKey({
        name,
        type,
        permissions,
        organizationId,
        userId: session.user.id,
      });

      console.log(`Successfully created API key with ID ${apiKey.id}`);
      return NextResponse.json(apiKey, { status: 201 });
    } catch (error: any) {
      console.error(`Error from API keys service: ${error.message}`);
      if (error.message === "Organization not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message === "You don't have permission to create API keys for this organization") {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
      throw error;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error creating API key", errorMessage);
    return NextResponse.json(
      { error: "Failed to create API key", details: errorMessage },
      { status: 500 }
    );
  }
}

async function getHandler(req: NextRequest) {
  try {
    // Verify authentication
    const headersList = headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    if (!session?.user?.id) {
      console.error("Unauthorized access attempt to API keys");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get organizationId from query
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      console.error("Missing organizationId in API keys request");
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    // SECURITY FIX: Verify user has access to the organization
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      console.error(`User ${session.user.id} attempted to access API keys for organization ${organizationId} without permission`);
      return NextResponse.json(
        { error: "Forbidden: You don't have access to this organization" },
        { status: 403 }
      );
    }

    console.log(`Listing API keys for organization ${organizationId} by user ${session.user.id}`);

    // Call the API directly
    try {
      const apiKeys = await apiKeysApi.listApiKeys(organizationId);
      console.log(`Successfully retrieved ${apiKeys.length} API keys for organization ${organizationId}`);
      return NextResponse.json(apiKeys);
    } catch (error: any) {
      console.error(`Error from API keys service: ${error.message}`);
      if (error.message === "Organization not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      throw error;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error listing API keys", errorMessage);
    return NextResponse.json(
      { error: "Failed to list API keys", details: errorMessage },
      { status: 500 }
    );
  }
}

async function deleteHandler(req: NextRequest) {
  try {
    // Verify authentication
    const headersList = headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    if (!session?.user?.id) {
      console.error("Unauthorized access attempt to delete API key");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get parameters from query
    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");
    const organizationId = searchParams.get("organizationId");

    if (!id || !organizationId) {
      console.error("Missing required parameters for API key deletion", { id, organizationId });
      return NextResponse.json(
        { error: "ID and Organization ID are required" },
        { status: 400 }
      );
    }

    console.log(`Deleting API key ${id} from organization ${organizationId} by user ${session.user.id}`);

    // Call the API directly
    try {
      const result = await apiKeysApi.deleteApiKey({
        id,
        organizationId,
        userId: session.user.id,
      });

      console.log(`Successfully deleted API key ${id}`);
      return NextResponse.json(result);
    } catch (error: any) {
      console.error(`Error from API keys service: ${error.message}`);
      if (error.message === "API key not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message === "API key does not belong to this organization" ||
          error.message === "You don't have permission to delete API keys for this organization") {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
      throw error;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error deleting API key", errorMessage);
    return NextResponse.json(
      { error: "Failed to delete API key", details: errorMessage },
      { status: 500 }
    );
  }
}

// Export handlers with rate limiting applied
export const GET = withApiKeyRateLimit(getHandler);
export const POST = withApiKeyRateLimit(postHandler);
export const DELETE = withApiKeyRateLimit(deleteHandler);
