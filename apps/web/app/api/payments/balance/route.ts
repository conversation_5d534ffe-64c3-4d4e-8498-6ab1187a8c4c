import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { createId } from "@paralleldrive/cuid2";

/**
 * Endpoint para obter o saldo atual da organização
 * GET /api/payments/balance?organizationId=xyz
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    logger.info("GET /api/payments/balance - Iniciando requisição", {
      url: request.url,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString()
    });

    // Verificar autenticação
    const session = await getSession();
    if (!session) {
      logger.warn("GET /api/payments/balance - Usuário não autenticado", {
        url: request.url,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    logger.info("GET /api/payments/balance - Usuário autenticado", {
      userId: session.user.id,
      userEmail: session.user.email,
      timestamp: new Date().toISOString()
    });

    // Obter ID da organização da query
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    logger.info("GET /api/payments/balance - Parâmetros da requisição", {
      organizationId,
      searchParams: Object.fromEntries(searchParams.entries()),
      userId: session.user.id,
      timestamp: new Date().toISOString()
    });

    if (!organizationId) {
      logger.warn("GET /api/payments/balance - Organization ID não fornecido", {
        userId: session.user.id,
        searchParams: Object.fromEntries(searchParams.entries()),
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    logger.info("GET /api/payments/balance - Verificando membership", {
      userId: session.user.id,
      organizationId,
      timestamp: new Date().toISOString()
    });

    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId,
        },
      },
    });

    logger.info("GET /api/payments/balance - Resultado da verificação de membership", {
      userId: session.user.id,
      organizationId,
      hasMembership: !!membership,
      membershipId: membership?.id,
      timestamp: new Date().toISOString()
    });

    if (!membership) {
      logger.warn("GET /api/payments/balance - Acesso não autorizado à organização", {
        userId: session.user.id,
        organizationId,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({ error: "Unauthorized for this organization" }, { status: 403 });
    }

    // Verificar se a organização está com status APPROVED
    logger.info("GET /api/payments/balance - Verificando status da organização", {
      userId: session.user.id,
      organizationId,
      timestamp: new Date().toISOString()
    });

    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true }
    });

    logger.info("GET /api/payments/balance - Resultado da busca da organização", {
      userId: session.user.id,
      organizationId,
      organizationFound: !!organization,
      organizationStatus: organization?.status,
      timestamp: new Date().toISOString()
    });

    if (!organization) {
      logger.warn("GET /api/payments/balance - Organização não encontrada", {
        userId: session.user.id,
        organizationId,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    if (organization.status !== "APPROVED") {
      logger.warn("GET /api/payments/balance - Organização não aprovada", {
        userId: session.user.id,
        organizationId,
        organizationStatus: organization.status,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({
        error: "Organization is not approved",
        status: organization.status
      }, { status: 403 });
    }

    // Buscar o saldo atual da organização no banco de dados
    logger.info("GET /api/payments/balance - Buscando saldo da organização", {
      userId: session.user.id,
      organizationId,
      timestamp: new Date().toISOString()
    });

    let balance = await db.organization_balance.findUnique({
      where: { organizationId }
    });

    logger.info("GET /api/payments/balance - Resultado da busca de saldo", {
      userId: session.user.id,
      organizationId,
      balanceFound: !!balance,
      availableBalance: balance?.availableBalance,
      pendingBalance: balance?.pendingBalance,
      reservedBalance: balance?.reservedBalance,
      timestamp: new Date().toISOString()
    });

    // Se não existir registro de saldo, criar um com valores zerados
    if (!balance) {
      logger.info("GET /api/payments/balance - Criando registro inicial de saldo", {
        userId: session.user.id,
        organizationId,
        timestamp: new Date().toISOString()
      });

      balance = await db.organization_balance.create({
        data: {
          id: createId(),
          organizationId,
          availableBalance: 0,
          pendingBalance: 0,
          reservedBalance: 0,
          updatedAt: new Date(),
        }
      });

      logger.info("GET /api/payments/balance - Registro inicial de saldo criado", {
        userId: session.user.id,
        organizationId,
        balanceId: balance.id,
        availableBalance: balance.availableBalance,
        pendingBalance: balance.pendingBalance,
        reservedBalance: balance.reservedBalance,
        timestamp: new Date().toISOString()
      });
    }

    // Retornar dados de saldo em formato amigável
    const responseData = {
      success: true,
      available: balance.availableBalance,
      pending: balance.pendingBalance,
      reserved: balance.reservedBalance,
      updatedAt: balance.updatedAt
    };

    const duration = Date.now() - startTime;

    logger.info("GET /api/payments/balance - Resposta enviada com sucesso", {
      userId: session.user.id,
      organizationId,
      availableBalance: balance.availableBalance,
      pendingBalance: balance.pendingBalance,
      reservedBalance: balance.reservedBalance,
      updatedAt: balance.updatedAt,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(responseData);
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error("GET /api/payments/balance - Erro interno", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * Verificar se uma organização tem saldo disponível suficiente para uma transação
 * Função utilitária que pode ser usada em outras partes do sistema
 */
export async function hasAvailableBalance(organizationId: string, amount: number): Promise<boolean> {
  try {
    const balance = await db.organization_balance.findUnique({
      where: { organizationId }
    });

    if (!balance) {
      logger.warn("Balance record not found for organization", { organizationId });
      return false;
    }

    return Number(balance.availableBalance) >= amount;
  } catch (error) {
    logger.error("Error checking available balance", { error, organizationId, amount });
    return false;
  }
}
