import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";

/**
 * Endpoint para obter o histórico de saldo para uma transação
 * GET /api/payments/balance/history?transactionId=xyz&organizationId=abc
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const transactionId = searchParams.get('transactionId');
    const organizationId = searchParams.get('organizationId');

    if (!transactionId || !organizationId) {
      return NextResponse.json({
        error: "Missing required parameters",
        requiredParams: ["transactionId", "organizationId"]
      }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId,
        },
      },
    });

    if (!membership) {
      logger.warn("Unauthorized access attempt to balance history", {
        userId: session.user.id,
        organizationId,
        transactionId
      });
      return NextResponse.json({ error: "Unauthorized for this organization" }, { status: 403 });
    }

    // Verificar se a transação pertence à organização
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      select: { organizationId: true }
    });

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    if (transaction.organizationId !== organizationId) {
      logger.warn("Attempt to access balance history for transaction from another organization", {
        userId: session.user.id,
        organizationId,
        transactionId,
        transactionOrganizationId: transaction.organizationId
      });
      return NextResponse.json({ error: "Transaction does not belong to this organization" }, { status: 403 });
    }

    // Buscar o histórico de saldo relacionado à transação
    const balanceHistory = await db.balance_history.findMany({
      where: {
        transactionId,
        organizationId
      },
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        operation: true,
        amount: true,
        description: true,
        balanceAfterOperation: true,
        createdAt: true
      }
    });

    // Buscar a transação para obter detalhes adicionais
    const transactionDetails = await db.transaction.findUnique({
      where: { id: transactionId },
      select: {
        amount: true,
        status: true,
        type: true,
        metadata: true,
        createdAt: true,
        paymentAt: true,
        gatewayName: true
      }
    });

    // Retornar os dados do histórico de saldo
    return NextResponse.json({
      success: true,
      balanceHistory,
      transaction: transactionDetails
    });
  } catch (error) {
    logger.error("Error fetching balance history", { error });
    return NextResponse.json({
      error: "Failed to fetch balance history",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
