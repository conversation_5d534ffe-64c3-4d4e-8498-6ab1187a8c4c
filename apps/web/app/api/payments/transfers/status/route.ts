import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";

/**
 * Endpoint para atualizar o status de uma transferência
 * POST /api/payments/transfers/status
 */
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Obter dados da atualização
    const data = await request.json();
    const {
      transactionId,
      status,
      paymentDate
    } = data;

    if (!transactionId || !status) {
      return NextResponse.json({
        error: "Missing required fields",
        requiredFields: ["transactionId", "status"]
      }, { status: 400 });
    }

    // Verificar se o status é válido
    const validStatus = ["APPROVED", "REJECTED", "CANCELED"];
    if (!validStatus.includes(status)) {
      return NextResponse.json({
        error: "Invalid status",
        validStatus
      }, { status: 400 });
    }

    // Buscar a transação
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId }
    });

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId: transaction.organizationId,
        },
      },
    });

    if (!membership) {
      logger.warn("Unauthorized access attempt to update transaction", {
        userId: session.user.id,
        organizationId: transaction.organizationId,
        transactionId
      });
      return NextResponse.json({ error: "Unauthorized for this organization" }, { status: 403 });
    }

    // Verificar se a transação já foi finalizada
    if (transaction.status === "APPROVED" ||
        transaction.status === "REJECTED" ||
        transaction.status === "CANCELED") {
      return NextResponse.json({
        error: "Transaction already finalized",
        currentStatus: transaction.status
      }, { status: 400 });
    }

    // Buscar o saldo da organização
    const balance = await db.organization_balance.findUnique({
      where: { organizationId: transaction.organizationId }
    });

    if (!balance) {
      return NextResponse.json({
        error: "Organization balance not found"
      }, { status: 500 });
    }

    // Atualizar a transação
    const updatedTransaction = await db.transaction.update({
      where: { id: transactionId },
      data: {
        status,
        paymentAt: status === "APPROVED" ? (paymentDate ? new Date(paymentDate) : new Date()) : undefined
      }
    });

    // Recuperar os metadados da transação para obter o valor total (incluindo taxa)
    const metadata = transaction.metadata as any || {};
    const totalAmount = metadata.totalAmount || transaction.amount;
    const fee = metadata.fee || 0;

    logger.info("Processing transfer status update with fee", {
      transactionId: transaction.id,
      status,
      amount: transaction.amount,
      fee,
      totalAmount
    });

    // Atualizar o saldo da organização com base no status
    if (status === "APPROVED") {
      // Se aprovada, confirmar a transferência debitando do saldo reservado
      await updateOrganizationBalance(
        transaction.organizationId,
        totalAmount,
        BalanceOperationType.DEBIT_RESERVED,
        transaction.id,
        `Transferência PIX aprovada manualmente: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
      );

      logger.info("Balance updated for approved transfer", {
        transactionId,
        organizationId: transaction.organizationId,
        amount: transaction.amount,
        fee,
        totalAmount,
        operation: BalanceOperationType.DEBIT_RESERVED
      });
    } else {
      // Se rejeitada ou cancelada, devolver o valor para o saldo disponível
      await updateOrganizationBalance(
        transaction.organizationId,
        totalAmount,
        BalanceOperationType.UNRESERVE,
        transaction.id,
        `Transferência PIX ${status.toLowerCase()} manualmente: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
      );

      logger.info("Balance restored for rejected/canceled transfer", {
        transactionId,
        organizationId: transaction.organizationId,
        amount: transaction.amount,
        fee,
        totalAmount,
        operation: BalanceOperationType.UNRESERVE,
        status
      });
    }

    return NextResponse.json({
      success: true,
      transaction: {
        id: updatedTransaction.id,
        status: updatedTransaction.status,
        paymentAt: updatedTransaction.paymentAt
      }
    });
  } catch (error) {
    logger.error("Error updating transaction status:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
