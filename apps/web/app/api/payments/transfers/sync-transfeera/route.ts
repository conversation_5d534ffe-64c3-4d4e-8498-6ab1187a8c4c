import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { z } from "zod";

// Schema de validação
const syncSchema = z.object({
  transferId: z.string().min(1),
  organizationId: z.string().min(1),
  batchId: z.string().optional(),
});

/**
 * Endpoint para sincronizar manualmente o status de uma transferência Transfeera
 * POST /api/payments/transfers/sync-transfeera
 */
export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const validationResult = syncSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const { transferId, organizationId, batchId } = validationResult.data;

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId,
        },
      },
    });

    const isAdmin = session.user.role === "admin";

    if (!membership && !isAdmin) {
      logger.warn("Tentativa de sincronização sem acesso à organização", {
        userId: session.user.id,
        organizationId
      });
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Verificar se a transação existe
    const transaction = await db.transaction.findFirst({
      where: {
        externalId: transferId,
        organizationId,
        type: "SEND"
      }
    });

    if (!transaction) {
      return NextResponse.json({
        success: false,
        message: "Transação não encontrada"
      }, { status: 404 });
    }

    // Sincronizar o status da transferência
    const result = await syncTransferStatus({
      transferId,
      organizationId,
      batchId,
      transactionId: transaction.id
    });

    logger.info("Sincronização manual de transferência Transfeera", {
      userId: session.user.id,
      result,
      transactionId: transaction.id,
      externalId: transferId
    });

    return NextResponse.json({
      success: true,
      result
    });
  } catch (error: any) {
    const errorMessage = error.message || "Erro desconhecido";
    const errorName = error.name || "Error";
    const errorStack = error.stack?.split('\n')[0] || "";

    logger.error("Erro ao sincronizar transferência Transfeera:", {
      errorMessage,
      errorName,
      errorStack
    });

    return NextResponse.json({
      success: false,
      message: errorMessage
    }, { status: 500 });
  }
}
