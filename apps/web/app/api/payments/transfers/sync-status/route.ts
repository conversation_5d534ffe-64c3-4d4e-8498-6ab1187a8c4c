import { auth } from "@repo/auth";
import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { getPaymentProvider } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { headers } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();

    // Log para debug
    logger.info("Headers recebidos:", { headers: Object.fromEntries(headersList.entries()) });

    const session = await auth.api.getSession({
      headers: headersList,
    });

    // Log para debug
    logger.info("Session:", { authenticated: !!session?.user?.id });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter dados da requisição
    const { transactionId, organizationId, externalId } = await request.json();

    // Verificar se temos pelo menos um ID de transação
    if ((!transactionId && !externalId) || !organizationId) {
      return NextResponse.json({ message: "Dados incompletos" }, { status: 400 });
    }

    // Se temos um externalId, vamos usar ele diretamente
    const idToUse = externalId || transactionId;

    logger.info("Verificando status da transferência", {
      transactionId,
      externalId,
      idToUse,
      organizationId
    });

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Acesso negado à organização" }, { status: 403 });
    }

    // Se temos um ID interno, buscar a transação no banco de dados
    let transaction = null;
    let gatewayType = null;

    // Verificar se o ID é um ID externo da Transfeera (numérico)
    const isExternalId = /^\d+$/.test(idToUse);

    if (!isExternalId) {
      // Buscar a transação pelo ID interno
      transaction = await db.transaction.findUnique({
        where: { id: idToUse },
        include: {
          payment_gateway: true,
        },
      });

      if (!transaction) {
        return NextResponse.json({ message: "Transação não encontrada" }, { status: 404 });
      }

      // Verificar se a transação pertence à organização
      if (transaction.organizationId !== organizationId) {
        return NextResponse.json({ message: "Transação não pertence a esta organização" }, { status: 403 });
      }

      gatewayType = transaction.payment_gateway?.type;
    } else {
      // Se é um ID externo, vamos assumir que é da Transfeera
      gatewayType = "TRANSFEERA";

      logger.info("Consultando status diretamente pelo ID externo da Transfeera", {
        externalId: idToUse,
        organizationId
      });
    }

    // Obter o provider de pagamento
    logger.info("Obtendo provider de pagamento", {
      organizationId,
      gatewayType,
      transactionId: transaction?.id,
      externalId: isExternalId ? idToUse : transaction?.externalId
    });

    try {
      const paymentProvider = await getPaymentProvider(organizationId, {
        forceType: gatewayType,
        action: 'status'
      });

      logger.info("Provider obtido", {
        provider: paymentProvider?.constructor?.name,
        gatewayType
      });

      // Consultar o status da transação no gateway
      logger.info("Consultando status da transação no gateway", {
        transactionId: transaction?.id,
        externalId: isExternalId ? idToUse : transaction?.externalId,
        idToUse,
        organizationId
      });

      const statusResult = await paymentProvider.getTransactionStatus({
        transactionId: idToUse,
        organizationId,
        transactionType: 'SEND', // Transfer operations are always SEND type
      });

      logger.info("Status obtido do gateway", { statusResult });

      // Mapear o status
      // Definir um status padrão para quando não temos a transação no banco
      let mappedStatus = transaction?.status || "PENDING";
      if (statusResult.mappedStatus) {
        // Se o provider retorna um status mapeado, usar diretamente
        mappedStatus = statusResult.mappedStatus;
      } else if (statusResult.status) {
        // Mapeamento de fallback
        const statusMap: Record<string, string> = {
          "PENDING": "PENDING",
          "APPROVED": "APPROVED",
          "CONFIRMED": "APPROVED",
          "RECEIVED": "APPROVED",
          "REJECTED": "REJECTED",
          "CANCELED": "CANCELED",
          "PROCESSING": "PROCESSING",
          "REFUNDED": "REFUNDED",
        };
        // Se temos a transação, usamos seu status como fallback, caso contrário usamos PENDING
        mappedStatus = statusMap[statusResult.status.toUpperCase()] || transaction?.status || "PENDING";
      }

      // Atualizar a transação se existe e o status mudou
      let updatedTransaction = transaction;
      if (transaction && mappedStatus !== transaction.status) {
        const isCompleted = mappedStatus === "APPROVED" ||
                          mappedStatus === "REFUNDED";

        logger.info("Atualizando status da transação", {
          transactionId: transaction.id,
          oldStatus: transaction.status,
          newStatus: mappedStatus,
          isCompleted
        });

        updatedTransaction = await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: mappedStatus as any,
            paymentAt: isCompleted ? new Date() : transaction.paymentAt,
          },
          include: {
            payment_gateway: true,
          },
        });
      }

      // Se temos uma transação no banco, retornamos os detalhes completos
      if (transaction) {
        return NextResponse.json({
          success: true,
          transfer: {
            id: updatedTransaction.id,
            externalId: updatedTransaction.externalId,
            status: updatedTransaction.status,
            amount: updatedTransaction.amount,
            pixKey: updatedTransaction.pixKey,
            pixKeyType: updatedTransaction.pixKeyType,
            date: updatedTransaction.createdAt.toISOString(),
            paymentDate: updatedTransaction.paymentAt?.toISOString(),
            updatedAt: updatedTransaction.updatedAt.toISOString(),
            description: updatedTransaction.description,
            recipient: {
              name: updatedTransaction.customerName,
              pixKey: updatedTransaction.pixKey,
              pixKeyType: updatedTransaction.pixKeyType,
            },
            gateway: updatedTransaction.gateway ? {
              name: updatedTransaction.gateway.name,
              type: updatedTransaction.gateway.type,
            } : null,
          },
          previousStatus: transaction.status,
          newStatus: updatedTransaction.status,
          message: mappedStatus !== transaction.status
            ? "Status da transferência atualizado com sucesso"
            : "Status da transferência já está atualizado",
        });
      }
      // Se não temos a transação no banco (consulta direta por ID externo)
      else {
        // Construir uma resposta simplificada com os dados da Transfeera
        return NextResponse.json({
          success: true,
          externalTransfer: {
            externalId: idToUse,
            status: mappedStatus,
            rawStatus: statusResult.data?.status || statusResult.status,
            amount: statusResult.data?.value,
            pixKey: statusResult.data?.destination_bank_account?.pix_key,
            pixKeyType: statusResult.data?.destination_bank_account?.pix_key_type,
            date: statusResult.data?.created_at,
            paymentDate: statusResult.data?.payment_date,
            description: statusResult.data?.pix_description || statusResult.data?.description,
            gateway: gatewayType,
            rawData: statusResult.data
          },
          message: "Status da transferência externa obtido com sucesso",
        });
      }
    } catch (providerError) {
      // Capturar erros específicos do provider
      logger.error("Erro ao obter provider ou consultar status", {
        error: providerError,
        message: providerError instanceof Error ? providerError.message : 'Erro desconhecido',
        transactionId,
        organizationId
      });

      // Retornar erro mais amigável para o usuário
      return NextResponse.json({
        success: false,
        message: "Não foi possível verificar o status da transferência no momento. Tente novamente mais tarde.",
        error: providerError instanceof Error ? providerError.message : 'Erro desconhecido'
      }, { status: 500 });
    }
  } catch (error) {
    logger.error("Erro ao sincronizar status da transferência", {
      error,
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined
    });

    // Retornar mensagem de erro mais detalhada para ajudar na depuração
    return NextResponse.json(
      {
        message: "Erro ao sincronizar status da transferência",
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
