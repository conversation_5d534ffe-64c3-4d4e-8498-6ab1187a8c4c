import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter o ID da organização da query string
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ message: "ID da organização é obrigatório" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Buscar gateways ativos que podem receber pagamentos
    const gateways = await db.payment_gateway.findMany({
      where: {
        organization_gateway: {
          some: {
            organizationId,
            isActive: true
          }
        },
        isActive: true,
        canReceive: true,
      },
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        canReceive: true,
        canSend: true,
        createdAt: true,
        updatedAt: true,
        organization_gateway: {
          where: {
            organizationId
          },
          select: {
            isDefault: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' },
      ],
    });

    // Transformar os resultados para manter a compatibilidade com o formato anterior
    const formattedGateways = gateways.map(gateway => ({
      ...gateway,
      isDefault: gateway.organizations[0]?.isDefault || false,
      organizations: undefined
    }));

    // Verificar se existem gateways que podem receber
    const hasReceiveEnabledGateways = gateways.length > 0;

    logger.info(`${gateways.length} gateways que podem receber encontrados para a organização ${organizationId}`);

    return NextResponse.json({
      success: true,
      hasReceiveEnabledGateways,
      data: formattedGateways,
    });
  } catch (error: any) {
    logger.error("Erro ao listar gateways que podem receber:", { error });
    return NextResponse.json({
      success: false,
      message: error.message || "Erro ao listar gateways que podem receber"
    }, { status: 500 });
  }
}
