import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    const { id: gatewayId } = await params;
    const data = await request.json();
    const organizationId = data.organizationId;

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    // Get the target gateway
    const gateway = await db.payment_gateway.findUnique({
      where: { id: gatewayId },
    });

    if (!gateway) {
      return NextResponse.json(
        { error: "Gateway not found" },
        { status: 404 }
      );
    }

    // Check if this gateway is already associated with the organization
    const gatewayAssociation = await db.organization_gateway.findUnique({
      where: {
        gatewayId_organizationId: {
          gatewayId,
          organizationId
        }
      }
    });

    // Reset all other gateways for this organization
    await db.organization_gateway.updateMany({
      where: {
        organizationId,
        isDefault: true,
        gatewayId: { not: gatewayId }
      },
      data: {
        isDefault: false,
      },
    });

    if (gatewayAssociation) {
      // Update the existing relationship
      await db.organization_gateway.update({
        where: {
          gatewayId_organizationId: {
            gatewayId,
            organizationId
          }
        },
        data: {
          isDefault: true,
          isActive: true,
        },
      });
    } else {
      // Create a new relationship if it doesn't exist
      await db.organization_gateway.create({
        data: {
          gatewayId,
          organizationId,
          isDefault: true,
          isActive: true,
          priority: 1
        }
      });
    }

    // Update the gateway to ensure it's active
    const updatedGateway = await db.payment_gateway.update({
      where: { id: gatewayId },
      data: {
        isActive: true,
      },
    });

    logger.info("Gateway set as default successfully", {
      gatewayId,
      organizationId
    });

    return NextResponse.json(updatedGateway);
  } catch (error) {
    logger.error("Error setting gateway as default", {
      error,
      gatewayId: (await params).id
    });
    return NextResponse.json(
      { error: "Failed to set gateway as default" },
      { status: 500 }
    );
  }
}
