import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter o ID da organização da query string
    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ message: "ID da organização é obrigatório" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Obter o gateway Transfeera da organização
    const gateway = await db.payment_gateway.findFirst({
      where: {
        organization_gateway: {
          some: {
            organizationId,
            isActive: true
          }
        },
        type: "TRANSFEERA",
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        canReceive: true,
        canSend: true,
        organization_gateway: {
          where: {
            organizationId
          },
          select: {
            isDefault: true
          }
        }
      },
    });

    // Transformar o resultado para manter a compatibilidade com o formato anterior
    const formattedGateway = gateway ? {
      ...gateway,
      isDefault: gateway.organization_gateway[0]?.isDefault || false,
      organization_gateway: undefined
    } : null;

    if (!formattedGateway) {
      return NextResponse.json({
        success: false,
        message: "Gateway Transfeera não encontrado",
      });
    }

    return NextResponse.json({
      success: true,
      gateway: formattedGateway,
    });
  } catch (error) {
    logger.error("Erro ao obter gateway Transfeera:", { error });
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Erro ao obter gateway Transfeera",
    }, { status: 500 });
  }
}
