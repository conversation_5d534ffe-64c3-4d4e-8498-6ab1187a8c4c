import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter o ID da organização da query string
    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ message: "ID da organização é obrigatório" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Obter os gateways de pagamento da organização que podem enviar dinheiro
    const gateways = await db.payment_gateway.findMany({
      where: {
        organization_gateway: {
          some: {
            organizationId,
            isActive: true
          }
        },
        isActive: true,
        canSend: true,
      },
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        canReceive: true,
        canSend: true,
        priority: true,
        organization_gateway: {
          where: {
            organizationId
          },
          select: {
            isDefault: true
          }
        }
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    // Transformar os resultados para manter a compatibilidade com o formato anterior
    const formattedGateways = gateways.map(gateway => ({
      ...gateway,
      isDefault: gateway.organizations[0]?.isDefault || false,
      organizations: undefined
    }));

    // Log dos gateways encontrados
    logger.info(`Encontrados ${gateways.length} gateways que podem enviar dinheiro para a organização ${organizationId}`);

    return NextResponse.json({
      success: true,
      data: formattedGateways,
      hasSendEnabledGateways: gateways.length > 0,
    });
  } catch (error) {
    logger.error("Erro ao listar gateways que podem enviar dinheiro:", { error });
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Erro ao listar gateways que podem enviar dinheiro"
    }, { status: 500 });
  }
}
