import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = req.nextUrl.searchParams;
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Verify organization membership
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ error: "You don't have access to this organization" }, { status: 403 });
    }

    // Get active payment gateways (globally configured by admin)
    const gateways = await db.payment_gateway.findMany({
      where: {
        isActive: true,
        OR: [
          { isGlobal: true },
          {
            organization_gateway: {
              some: {
                organizationId,
                isActive: true
              }
            }
          }
        ]
      },
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        canReceive: true,
        canSend: true,
        priority: true,
        organization_gateway: {
          where: {
            organizationId
          },
          select: {
            isDefault: true
          }
        }
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    // Transformar os resultados para manter a compatibilidade com o formato anterior
    const formattedGateways = gateways.map(gateway => ({
      ...gateway,
      isDefault: gateway.organization_gateway[0]?.isDefault || false,
      organization_gateway: undefined
    }));

    // Filter gateways by capability
    const receiveEnabledGateways = formattedGateways.filter(g => g.canReceive);
    const sendEnabledGateways = formattedGateways.filter(g => g.canSend);

    // Log the gateways found
    logger.info(`Found ${formattedGateways.length} gateways for organization ${organizationId}`);
    logger.info(`Receive-enabled gateways: ${receiveEnabledGateways.length}, Send-enabled gateways: ${sendEnabledGateways.length}`);

    return NextResponse.json({
      success: true,
      data: formattedGateways,
      receiveEnabledGateways,
      sendEnabledGateways,
      hasReceiveEnabledGateways: receiveEnabledGateways.length > 0,
      hasSendEnabledGateways: sendEnabledGateways.length > 0,
    });
  } catch (error) {
    logger.error("Error listing payment gateways:", { error });
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Error listing payment gateways",
      stack: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.stack : undefined) : undefined,
    }, { status: 500 });
  }
}
