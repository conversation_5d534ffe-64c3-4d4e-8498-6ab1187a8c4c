import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema de validação
const setupGatewaySchema = z.object({
  organizationId: z.string(),
  type: z.enum(["ASAAS", "REFLOWPAY"]).default("ASAAS"),
  apiKey: z.string().optional(),
  apiSecret: z.string().optional(),
  name: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({
      headers: headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const validationResult = setupGatewaySchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const {
      organizationId,
      type = "ASAAS",
      apiKey: rawApiKey = process.env.ASAAS_API_KEY || "aact_YWdlbnRlQGdtYWlsLmNvbToxMjM0NTY",
      apiSecret = process.env.ASAAS_API_SECRET || "",
      name = type === "ASAAS" ? "Asaas" : "ReflowPay"
    } = validationResult.data;

    // Ensure API key has the correct format (with $ prefix if needed)
    const apiKey = type === "ASAAS" && !rawApiKey.startsWith('$') ? `$${rawApiKey}` : rawApiKey;
    console.log("Using formatted API key for gateway setup (first 10 chars):", apiKey.substring(0, 10) + "...");

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Verificar se já existe um gateway do mesmo tipo
    const existingGateway = await db.payment_gateway.findFirst({
      where: {
        organizations: {
          some: {
            organizationId
          }
        },
        type,
      },
    });

    let gateway;

    if (existingGateway) {
      // Atualizar o gateway existente
      gateway = await db.payment_gateway.update({
        where: { id: existingGateway.id },
        data: {
          isActive: true,
          isDefault: true,
          credentials: {
            apiKey,
            apiSecret,
          },
        },
      });

      // Desativar outros gateways como padrão na relação OrganizationGateway
      await db.organization_gateway.updateMany({
        where: {
          organizationId,
          gatewayId: { not: existingGateway.id },
        },
        data: {
          isDefault: false,
        },
      });
    } else {
      // Criar um novo gateway
      gateway = await db.payment_gateway.create({
        data: {
          type,
          name,
          isActive: true,
          credentials: {
            apiKey,
            apiSecret,
          },
          organizations: {
            create: {
              organizationId,
              isDefault: true,
              isActive: true,
              priority: 1
            }
          }
        },
      });

      // Desativar outros gateways como padrão na relação OrganizationGateway
      await db.organization_gateway.updateMany({
        where: {
          organizationId,
          gatewayId: { not: gateway.id },
        },
        data: {
          isDefault: false,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Gateway de pagamento configurado com sucesso",
      gateway: {
        id: gateway.id,
        type: gateway.type,
        name: gateway.name,
        isDefault: gateway.isDefault,
        isActive: gateway.isActive,
      },
    });
  } catch (error) {
    logger.error("Erro ao configurar gateway de pagamento:", { error });
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Erro ao configurar gateway de pagamento",
    }, { status: 500 });
  }
}
