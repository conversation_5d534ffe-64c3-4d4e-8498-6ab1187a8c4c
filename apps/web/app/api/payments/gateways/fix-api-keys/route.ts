import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: headers(),
    });
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Get organization ID from request
    const data = await req.json();
    const { organizationId } = data;

    if (!organizationId) {
      return NextResponse.json({ message: "Organization ID is required" }, { status: 400 });
    }

    // Verify user has access to the organization
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "You don't have access to this organization" }, { status: 403 });
    }

    // Find all Asaas gateways for this organization
    const gateways = await db.payment_gateway.findMany({
      where: {
        organizationId,
        type: "ASAAS",
      },
    });

    logger.info(`Found ${gateways.length} Asaas gateways for organization ${organizationId}`);

    const updatedGateways = [];

    // Update each gateway
    for (const gateway of gateways) {
      // Get the current credentials
      const credentials = gateway.credentials as any;
      
      if (!credentials || !credentials.apiKey) {
        logger.info(`Gateway ${gateway.id} has no API key, skipping.`);
        continue;
      }

      let apiKey = credentials.apiKey;
      
      // Check if the API key already has the $ prefix
      if (!apiKey.startsWith('$')) {
        logger.info(`Gateway ${gateway.id} API key needs fixing. Adding $ prefix.`);
        
        // Add the $ prefix
        apiKey = `$${apiKey}`;
        
        // Update the gateway
        const updatedGateway = await db.payment_gateway.update({
          where: {
            id: gateway.id,
          },
          data: {
            credentials: {
              ...credentials,
              apiKey,
            },
          },
        });
        
        logger.info(`Gateway ${gateway.id} updated successfully.`);
        updatedGateways.push(updatedGateway.id);
      } else {
        logger.info(`Gateway ${gateway.id} API key already has $ prefix, no update needed.`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `${updatedGateways.length} gateways updated successfully`,
      updatedGateways,
    });
  } catch (error) {
    logger.error("Error fixing Asaas API keys:", { error });
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Error fixing Asaas API keys",
    }, { status: 500 });
  }
}
