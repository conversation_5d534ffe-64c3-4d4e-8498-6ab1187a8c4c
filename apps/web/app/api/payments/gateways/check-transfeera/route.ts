import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter o ID da organização da query string
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ message: "ID da organização não fornecido" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Buscar o gateway Transfeera
    const transfeera = await db.payment_gateway.findFirst({
      where: {
        type: "TRANSFEERA",
      },
      select: {
        id: true,
        name: true,
        type: true,
        isActive: true,
        canSend: true,
        canReceive: true,
        isGlobal: true,
        priority: true,
        pixTransferFixedFee: true, // Incluir a taxa fixa de transferência
        organization_gateway: {
          where: {
            organizationId
          },
          select: {
            isActive: true,
            isDefault: true,
            priority: true
          }
        }
      }
    });

    if (!transfeera) {
      return NextResponse.json({
        success: false,
        message: "Gateway Transfeera não encontrado",
        needsSetup: true
      });
    }

    // Verificar se o gateway está associado à organização
    const isAssociated = transfeera.organization_gateway.length > 0;
    const isActiveForOrg = isAssociated && transfeera.organization_gateway[0].isActive;
    const isDefaultForOrg = isAssociated && transfeera.organization_gateway[0].isDefault;

    // Verificar se o gateway está configurado corretamente
    const isConfiguredCorrectly = transfeera.isActive && transfeera.canSend;

    // Verificar se a organização está aprovada
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true }
    });

    const isOrgApproved = organization?.status === "APPROVED";

    // Verificar saldo da organização
    const balance = await db.organization_balance.findUnique({
      where: { organizationId },
      select: {
        availableBalance: true,
        pendingBalance: true,
        reservedBalance: true
      }
    });

    const hasSufficientBalance = balance && balance.availableBalance > 0;

    return NextResponse.json({
      success: true,
      gateway: {
        id: transfeera.id,
        name: transfeera.name,
        type: transfeera.type,
        isActive: transfeera.isActive,
        canSend: transfeera.canSend,
        isGlobal: transfeera.isGlobal,
        isAssociated,
        isActiveForOrg,
        isDefaultForOrg,
        isConfiguredCorrectly,
        pixTransferFixedFee: transfeera.pixTransferFixedFee || 1.5 // Incluir a taxa fixa com fallback para 1.5
      },
      organization: {
        id: organizationId,
        status: organization?.status,
        isApproved: isOrgApproved
      },
      balance: {
        available: balance?.availableBalance || 0,
        pending: balance?.pendingBalance || 0,
        reserved: balance?.reservedBalance || 0,
        hasSufficientBalance
      }
    });
  } catch (error: any) {
    logger.error("Erro ao verificar gateway Transfeera:", { error });
    return NextResponse.json({
      success: false,
      message: error.message || "Erro ao verificar gateway Transfeera"
    }, { status: 500 });
  }
}
