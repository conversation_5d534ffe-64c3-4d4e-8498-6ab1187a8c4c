import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const organizationId = searchParams.get("organizationId");
    const status = searchParams.get("status");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const search = searchParams.get("search");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Build the where clause for filtering
    const where: any = {
      transaction: {
        organizationId
      }
    };

    // Add status filter if provided
    if (status) {
      where.status = status;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // Add search filter if provided
    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { externalId: { contains: search, mode: 'insensitive' } },
        { transactionId: { contains: search, mode: 'insensitive' } },
        { transaction: { customerName: { contains: search, mode: 'insensitive' } } },
        { transaction: { customerEmail: { contains: search, mode: 'insensitive' } } }
      ];
    }

    // Get total count for pagination
    const total = await db.transaction.count({
      where: {
        type: 'REFUND',
        organizationId
      }
    });

    const refunds = await db.transaction.findMany({
      where: {
        type: 'REFUND',
        organizationId
      },
      include: {
        transaction: {
          select: {
            id: true,
            customerName: true,
            customerEmail: true,
            referenceCode: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // Calculate pagination details
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      refunds,
      pagination: {
        total,
        page,
        limit,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error("Error fetching refunds:", error);
    return NextResponse.json(
      { error: "Failed to fetch refunds" },
      { status: 500 }
    );
  }
}
