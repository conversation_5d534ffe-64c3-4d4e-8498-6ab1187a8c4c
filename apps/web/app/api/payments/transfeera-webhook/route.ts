import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";
import { headers } from "next/headers";
import crypto from "crypto";

const TRANSFEERA_SIGNATURE_SECRET = process.env.TRANSFEERA_SIGNATURE_SECRET || "";

function validateTransfeeraSignature(payload: string, signature: string | null): boolean {
  if (!signature || !TRANSFEERA_SIGNATURE_SECRET) return false;

  const hmac = crypto.createHmac('sha256', TRANSFEERA_SIGNATURE_SECRET);
  hmac.update(payload);
  const expectedSignature = hmac.digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação via signature secret
    const signature = headers().get("X-Transfeera-Signature");
    if (!signature) {
      return NextResponse.json(
        { message: "Assinatura não fornecida" },
        { status: 401 }
      );
    }

        // Obter dados do webhook
        const payload = await req.json();

    // Validar assinatura do webhook
    const isValidSignature = validateTransfeeraSignature(JSON.stringify(payload), signature);
    if (!isValidSignature) {
      logger.warn("Assinatura inválida no webhook da Transfeera", { signature });
      return NextResponse.json(
        { message: "Assinatura inválida" },
        { status: 401 }
      );
    }



    logger.info("Webhook da Transfeera recebido", { payload });

    // Verificar se é um evento de transferência
    if (payload.object !== "Transfer") {
      return NextResponse.json(
        { message: "Tipo de objeto não suportado" },
        { status: 400 }
      );
    }

    const { id: transferId, data } = payload;
    const { status } = data;

    // Buscar transação pelo externalId (transferId)
    const transaction = await db.transaction.findFirst({
      where: {
        externalId: transferId,
        type: "SEND"
      },
      include: {
        payment_gateway: true,
      },
    });

    if (!transaction) {
      logger.warn("Transação não encontrada para transferId", { transferId });
      return NextResponse.json(
        { message: "Transação não encontrada" },
        { status: 404 }
      );
    }

    // Sincronizar status da transferência
    const result = await syncTransferStatus({
      transferId,
      organizationId: transaction.organizationId,
      transactionId: transaction.id,
      batchId: transaction.metadata?.batchId
    });

    logger.info("Status da transferência atualizado via webhook", {
      transferId,
      transactionId: transaction.id,
      newStatus: result.newStatus
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error("Erro ao processar webhook da Transfeera", { error });
    return NextResponse.json(
      { message: error.message || "Erro ao processar webhook" },
      { status: 500 }
    );
  }
}
