import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { getTransactionStatus } from "@repo/payments/provider/ecomovi";
import { z } from "zod";

// Schema de validação para consulta de status
const StatusQuerySchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required")
});

interface RouteParams {
  params: {
    transactionId: string;
  };
}

// Endpoint para consultar status de PIX withdrawal
export async function GET(
  req: NextRequest,
  { params }: RouteParams
) {
  try {
    const { transactionId } = await params;

    logger.info("PIX withdrawal status endpoint called", { transactionId });

    // Obter parâmetros da query string
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");

    // Validar parâmetros
    const validationResult = StatusQuerySchema.safeParse({ organizationId });

    if (!validationResult.success) {
      logger.error("Invalid status query parameters", {
        errors: validationResult.error.errors,
        transactionId
      });

      return NextResponse.json(
        {
          success: false,
          error: "Invalid query parameters",
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { organizationId: validatedOrgId } = validationResult.data;

    // Verificar se a transação existe no banco de dados
    const transaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              { id: transactionId },
              { externalId: transactionId }
            ]
          },
          { organizationId: validatedOrgId },
          { type: "SEND" }
        ]
      },
      select: {
        id: true,
        externalId: true,
        status: true,
        amount: true,
        createdAt: true,
        updatedAt: true,
        paymentAt: true,
        metadata: true,
        organizationId: true
      }
    });

    if (!transaction) {
      logger.error("Withdrawal transaction not found", {
        transactionId,
        organizationId: validatedOrgId
      });

      return NextResponse.json(
        {
          success: false,
          error: "Withdrawal transaction not found"
        },
        { status: 404 }
      );
    }

    // Se a transação está pendente ou em processamento, consultar status na Ecomovi
    let updatedStatus = transaction.status;
    let providerData = null;

    if (["PENDING", "PROCESSING"].includes(transaction.status)) {
      try {
        logger.info("Consulting withdrawal status with Ecomovi", {
          transactionId: transaction.externalId || transaction.id,
          organizationId: validatedOrgId
        });

        const statusResult = await getTransactionStatus({
          transactionId: transaction.externalId || transaction.id,
          organizationId: validatedOrgId
        });

        if (statusResult.success) {
          // Use mapped status if available, otherwise use original status
          const newStatus = statusResult.mappedStatus || statusResult.status;
          updatedStatus = newStatus;
          providerData = statusResult.providerData;

          // Atualizar status no banco se mudou
          if (newStatus !== transaction.status) {
            await db.transaction.update({
              where: { id: transaction.id },
              data: {
                status: newStatus,
                updatedAt: new Date(),
                ...(newStatus === "APPROVED" && { paymentAt: new Date() })
              }
            });

            logger.info("Withdrawal status updated", {
              transactionId: transaction.id,
              oldStatus: transaction.status,
              newStatus: newStatus,
              originalStatus: statusResult.status,
              mappedStatus: statusResult.mappedStatus
            });
          }
        }
      } catch (statusError) {
        logger.warn("Error consulting withdrawal status with provider", {
          error: statusError instanceof Error ? statusError.message : statusError,
          transactionId: transaction.id
        });
        // Continue with local status if provider consultation fails
      }
    }

    // Extrair informações relevantes do metadata
    const metadata = transaction.metadata as any;
    const ecomoviData = metadata?.ecomovi || {};

    logger.info("PIX withdrawal status retrieved", {
      transactionId: transaction.id,
      status: updatedStatus
    });

    return NextResponse.json({
      success: true,
      data: {
        transactionId: transaction.id,
        externalId: transaction.externalId,
        status: updatedStatus,
        amount: transaction.amount,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
        paymentAt: transaction.paymentAt,
        pixKey: ecomoviData.pixKey,
        pixKeyType: ecomoviData.pixKeyType,
        endToEndId: ecomoviData.endToEndId,
        providerData
      }
    });

  } catch (error) {
    logger.error("Error retrieving PIX withdrawal status", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: params.transactionId
    });

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error while retrieving withdrawal status"
      },
      { status: 500 }
    );
  }
}
