/**
 * Test script for PIX Withdrawal API endpoints
 * 
 * This script tests all the PIX withdrawal endpoints to ensure they work correctly.
 * Run with: npx tsx test-endpoints.ts
 */

import { logger } from "@repo/logs";

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";
const TEST_ORG_ID = process.env.TEST_ORGANIZATION_ID || "test-org-123";

interface TestConfig {
  organizationId: string;
  pixKey: string;
  pixKeyType: "EMAIL" | "CPF" | "CNPJ" | "TELEFONE" | "EVP";
  amount: number;
}

const TEST_CONFIG: TestConfig = {
  organizationId: TEST_ORG_ID,
  pixKey: "<EMAIL>",
  pixKeyType: "EMAIL",
  amount: 1000 // R$ 10,00 em centavos
};

class PIXWithdrawalTester {
  private baseUrl: string;
  private testTransactionId: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    
    logger.info(`Making request to: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    
    logger.info(`Response [${response.status}]:`, data);
    
    return { response, data };
  }

  async testServiceAvailability() {
    logger.info("\n🔍 Testing service availability...");
    
    try {
      const { response, data } = await this.makeRequest("/api/payments/pix/withdrawal", {
        method: "GET"
      });

      if (response.ok && data.success) {
        logger.info("✅ Service is available");
        logger.info(`Provider: ${data.provider}`);
        logger.info(`Supported PIX key types: ${data.supportedPixKeyTypes.join(", ")}`);
        return true;
      } else {
        logger.error("❌ Service availability check failed");
        return false;
      }
    } catch (error) {
      logger.error("❌ Error checking service availability:", error);
      return false;
    }
  }

  async testCreateWithdrawal() {
    logger.info("\n🔍 Testing PIX withdrawal creation...");
    
    try {
      const withdrawalData = {
        amount: TEST_CONFIG.amount,
        pixKey: TEST_CONFIG.pixKey,
        pixKeyType: TEST_CONFIG.pixKeyType,
        customerName: "Test User",
        description: "Test PIX withdrawal",
        organizationId: TEST_CONFIG.organizationId,
        idempotencyKey: `test-${Date.now()}`
      };

      const { response, data } = await this.makeRequest("/api/payments/pix/withdrawal", {
        method: "POST",
        body: JSON.stringify(withdrawalData)
      });

      if (response.ok && data.success) {
        logger.info("✅ PIX withdrawal created successfully");
        this.testTransactionId = data.data.transactionId;
        logger.info(`Transaction ID: ${this.testTransactionId}`);
        logger.info(`External ID: ${data.data.externalId}`);
        logger.info(`Status: ${data.data.status}`);
        return true;
      } else {
        logger.error("❌ PIX withdrawal creation failed");
        logger.error("Error details:", data);
        return false;
      }
    } catch (error) {
      logger.error("❌ Error creating PIX withdrawal:", error);
      return false;
    }
  }

  async testCreateWithdrawalValidation() {
    logger.info("\n🔍 Testing PIX withdrawal validation...");
    
    try {
      // Test with invalid data
      const invalidData = {
        amount: -100, // Invalid amount
        pixKey: "", // Empty PIX key
        pixKeyType: "INVALID", // Invalid PIX key type
        organizationId: "" // Empty organization ID
      };

      const { response, data } = await this.makeRequest("/api/payments/pix/withdrawal", {
        method: "POST",
        body: JSON.stringify(invalidData)
      });

      if (response.status === 400 && !data.success) {
        logger.info("✅ Validation working correctly - rejected invalid data");
        logger.info("Validation errors:", data.details);
        return true;
      } else {
        logger.error("❌ Validation failed - should have rejected invalid data");
        return false;
      }
    } catch (error) {
      logger.error("❌ Error testing validation:", error);
      return false;
    }
  }

  async testGetWithdrawalStatus() {
    if (!this.testTransactionId) {
      logger.warn("⚠️ Skipping status test - no transaction ID available");
      return false;
    }

    logger.info("\n🔍 Testing PIX withdrawal status...");
    
    try {
      const { response, data } = await this.makeRequest(
        `/api/payments/pix/withdrawal/status/${this.testTransactionId}?organizationId=${TEST_CONFIG.organizationId}`,
        { method: "GET" }
      );

      if (response.ok && data.success) {
        logger.info("✅ PIX withdrawal status retrieved successfully");
        logger.info(`Status: ${data.data.status}`);
        logger.info(`Amount: ${data.data.amount}`);
        logger.info(`PIX Key: ${data.data.pixKey}`);
        return true;
      } else {
        logger.error("❌ PIX withdrawal status retrieval failed");
        return false;
      }
    } catch (error) {
      logger.error("❌ Error getting PIX withdrawal status:", error);
      return false;
    }
  }

  async testListWithdrawals() {
    logger.info("\n🔍 Testing PIX withdrawal list...");
    
    try {
      const { response, data } = await this.makeRequest(
        `/api/payments/pix/withdrawal/list?organizationId=${TEST_CONFIG.organizationId}&page=1&limit=10`,
        { method: "GET" }
      );

      if (response.ok && data.success) {
        logger.info("✅ PIX withdrawal list retrieved successfully");
        logger.info(`Total count: ${data.data.pagination.totalCount}`);
        logger.info(`Page: ${data.data.pagination.page}`);
        logger.info(`Transactions found: ${data.data.transactions.length}`);
        return true;
      } else {
        logger.error("❌ PIX withdrawal list retrieval failed");
        return false;
      }
    } catch (error) {
      logger.error("❌ Error listing PIX withdrawals:", error);
      return false;
    }
  }

  async testListWithdrawalsWithFilters() {
    logger.info("\n🔍 Testing PIX withdrawal list with filters...");
    
    try {
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // 24 hours ago
      const endDate = new Date().toISOString();
      
      const { response, data } = await this.makeRequest(
        `/api/payments/pix/withdrawal/list?organizationId=${TEST_CONFIG.organizationId}&status=PENDING&startDate=${startDate}&endDate=${endDate}&minAmount=500&maxAmount=2000`,
        { method: "GET" }
      );

      if (response.ok && data.success) {
        logger.info("✅ PIX withdrawal list with filters retrieved successfully");
        logger.info(`Filtered results: ${data.data.transactions.length}`);
        logger.info("Applied filters:", data.data.filters);
        return true;
      } else {
        logger.error("❌ PIX withdrawal list with filters failed");
        return false;
      }
    } catch (error) {
      logger.error("❌ Error listing PIX withdrawals with filters:", error);
      return false;
    }
  }

  async testCancelWithdrawal() {
    if (!this.testTransactionId) {
      logger.warn("⚠️ Skipping cancel test - no transaction ID available");
      return false;
    }

    logger.info("\n🔍 Testing PIX withdrawal cancellation...");
    
    try {
      const cancelData = {
        organizationId: TEST_CONFIG.organizationId,
        reason: "Test cancellation"
      };

      const { response, data } = await this.makeRequest(
        `/api/payments/pix/withdrawal/cancel/${this.testTransactionId}`,
        {
          method: "POST",
          body: JSON.stringify(cancelData)
        }
      );

      if (response.ok && data.success) {
        logger.info("✅ PIX withdrawal cancelled successfully");
        logger.info(`New status: ${data.data.status}`);
        logger.info(`Cancellation reason: ${data.data.cancellation.reason}`);
        return true;
      } else if (response.status === 400 && data.error?.includes("cannot be cancelled")) {
        logger.info("✅ Cancellation correctly rejected - transaction cannot be cancelled");
        return true;
      } else {
        logger.error("❌ PIX withdrawal cancellation failed");
        logger.error("Error details:", data);
        return false;
      }
    } catch (error) {
      logger.error("❌ Error cancelling PIX withdrawal:", error);
      return false;
    }
  }

  async runAllTests() {
    logger.info("🚀 Starting PIX Withdrawal API tests...");
    logger.info(`Base URL: ${this.baseUrl}`);
    logger.info(`Test Organization ID: ${TEST_CONFIG.organizationId}`);
    
    const results = {
      serviceAvailability: await this.testServiceAvailability(),
      createWithdrawal: await this.testCreateWithdrawal(),
      createWithdrawalValidation: await this.testCreateWithdrawalValidation(),
      getWithdrawalStatus: await this.testGetWithdrawalStatus(),
      listWithdrawals: await this.testListWithdrawals(),
      listWithdrawalsWithFilters: await this.testListWithdrawalsWithFilters(),
      cancelWithdrawal: await this.testCancelWithdrawal()
    };

    logger.info("\n📊 Test Results Summary:");
    Object.entries(results).forEach(([test, passed]) => {
      logger.info(`${passed ? "✅" : "❌"} ${test}: ${passed ? "PASSED" : "FAILED"}`);
    });

    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    
    logger.info(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      logger.info("🎉 All tests passed!");
    } else {
      logger.warn(`⚠️ ${totalTests - passedTests} test(s) failed`);
    }

    return results;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new PIXWithdrawalTester(BASE_URL);
  
  tester.runAllTests()
    .then(() => {
      logger.info("\n✨ Test execution completed");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("💥 Test execution failed:", error);
      process.exit(1);
    });
}

export { PIXWithdrawalTester, TEST_CONFIG };