# PIX Withdrawal API Endpoints

Esta documentação descreve os endpoints da API para processamento de saques PIX via provedor Ecomovi.

## Endpoints Disponíveis

### 1. Criar Saque PIX

**POST** `/api/payments/pix/withdrawal`

Cria uma nova solicitação de saque PIX.

#### Parâmetros do Body (JSON)

```json
{
  "amount": 10000,                    // Valor em centavos (obrigatório)
  "pixKey": "<EMAIL>",       // Chave PIX de destino (obrigatório)
  "pixKeyType": "EMAIL",             // Tipo da chave PIX (obrigatório)
  "customerName": "<PERSON>",       // Nome do beneficiário (opcional)
  "customerDocument": "12345678901",  // CPF/CNPJ do beneficiário (opcional)
  "description": "Saque PIX",        // Descrição da transação (opcional)
  "organizationId": "org_123",       // ID da organização (obrigatório)
  "transactionId": "tx_456",         // ID personalizado da transação (opcional)
  "metadata": {},                     // Metadados adicionais (opcional)
  "idempotencyKey": "key_789"        // Chave de idempotência (opcional)
}
```

#### Tipos de Chave PIX Suportados
- `CPF`: Documento CPF
- `CNPJ`: Documento CNPJ
- `EMAIL`: Endereço de email
- `TELEFONE`: Número de telefone
- `EVP`: Chave aleatória

#### Resposta de Sucesso (200)

```json
{
  "success": true,
  "data": {
    "transactionId": "tx_internal_123",
    "externalId": "ecomovi_456",
    "status": "PENDING",
    "amount": 10000,
    "endToEndId": "E12345678202312151234567890123456",
    "metadata": {}
  }
}
```

#### Possíveis Erros
- `400`: Dados inválidos ou saldo insuficiente
- `404`: Organização não encontrada
- `403`: Organização inativa
- `500`: Erro interno do servidor

---

### 2. Consultar Status do Saque

**GET** `/api/payments/pix/withdrawal/status/{transactionId}?organizationId={orgId}`

Consulta o status atual de um saque PIX.

#### Parâmetros
- `transactionId` (path): ID da transação (interno ou externo)
- `organizationId` (query): ID da organização

#### Resposta de Sucesso (200)

```json
{
  "success": true,
  "data": {
    "transactionId": "tx_internal_123",
    "externalId": "ecomovi_456",
    "status": "APPROVED",
    "amount": 10000,
    "createdAt": "2023-12-15T10:30:00Z",
    "updatedAt": "2023-12-15T10:35:00Z",
    "approvedAt": "2023-12-15T10:35:00Z",
    "pixKey": "<EMAIL>",
    "pixKeyType": "EMAIL",
    "endToEndId": "E12345678202312151234567890123456",
    "providerData": {}
  }
}
```

#### Status Possíveis
- `PENDING`: Aguardando processamento
- `PROCESSING`: Em processamento
- `APPROVED`: Aprovado e processado
- `REJECTED`: Rejeitado
- `CANCELLED`: Cancelado
- `FAILED`: Falhou no processamento

---

### 3. Listar Saques PIX

**GET** `/api/payments/pix/withdrawal/list`

Lista os saques PIX de uma organização com filtros e paginação.

#### Parâmetros de Query

| Parâmetro | Tipo | Obrigatório | Descrição |
|-----------|------|-------------|------------|
| `organizationId` | string | Sim | ID da organização |
| `page` | number | Não | Página (padrão: 1) |
| `limit` | number | Não | Itens por página (padrão: 20, máx: 100) |
| `status` | string | Não | Filtrar por status |
| `startDate` | string | Não | Data inicial (ISO 8601) |
| `endDate` | string | Não | Data final (ISO 8601) |
| `minAmount` | number | Não | Valor mínimo em centavos |
| `maxAmount` | number | Não | Valor máximo em centavos |
| `pixKey` | string | Não | Buscar por chave PIX (parcial) |

#### Exemplo de Requisição

```
GET /api/payments/pix/withdrawal/list?organizationId=org_123&page=1&limit=10&status=APPROVED&startDate=2023-12-01T00:00:00Z&endDate=2023-12-31T23:59:59Z
```

#### Resposta de Sucesso (200)

```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "transactionId": "tx_internal_123",
        "externalId": "ecomovi_456",
        "status": "APPROVED",
        "amount": 10000,
        "createdAt": "2023-12-15T10:30:00Z",
        "updatedAt": "2023-12-15T10:35:00Z",
        "approvedAt": "2023-12-15T10:35:00Z",
        "pixKey": "<EMAIL>",
        "pixKeyType": "EMAIL",
        "customerName": "João Silva",
        "description": "Saque PIX",
        "endToEndId": "E12345678202312151234567890123456"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalCount": 25,
      "totalPages": 3,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "filters": {
      "status": "APPROVED",
      "startDate": "2023-12-01T00:00:00Z",
      "endDate": "2023-12-31T23:59:59Z"
    }
  }
}
```

---

### 4. Cancelar Saque PIX

**POST** `/api/payments/pix/withdrawal/cancel/{transactionId}`

Cancela um saque PIX que ainda está pendente ou em processamento.

#### Parâmetros
- `transactionId` (path): ID da transação

#### Parâmetros do Body (JSON)

```json
{
  "organizationId": "org_123",        // ID da organização (obrigatório)
  "reason": "Cancelado pelo usuário"  // Motivo do cancelamento (opcional)
}
```

#### Resposta de Sucesso (200)

```json
{
  "success": true,
  "data": {
    "transactionId": "tx_internal_123",
    "externalId": "ecomovi_456",
    "status": "CANCELLED",
    "amount": 10000,
    "createdAt": "2023-12-15T10:30:00Z",
    "updatedAt": "2023-12-15T10:40:00Z",
    "pixKey": "<EMAIL>",
    "pixKeyType": "EMAIL",
    "cancellation": {
      "cancelledAt": "2023-12-15T10:40:00Z",
      "reason": "Cancelado pelo usuário",
      "cancelledBy": "API"
    }
  }
}
```

#### Possíveis Erros
- `400`: Transação não pode ser cancelada (status inválido)
- `404`: Transação não encontrada
- `500`: Erro interno do servidor

---

### 5. Verificar Disponibilidade do Serviço

**GET** `/api/payments/pix/withdrawal`

Verifica se o serviço de saque PIX está disponível.

#### Resposta de Sucesso (200)

```json
{
  "success": true,
  "message": "PIX withdrawal endpoint is available",
  "provider": "ecomovi",
  "supportedPixKeyTypes": ["CPF", "CNPJ", "EMAIL", "TELEFONE", "EVP"]
}
```

---

## Códigos de Status HTTP

- `200`: Sucesso
- `400`: Requisição inválida (dados incorretos, saldo insuficiente, etc.)
- `403`: Acesso negado (organização inativa)
- `404`: Recurso não encontrado
- `500`: Erro interno do servidor

## Observações Importantes

1. **Idempotência**: Use o campo `idempotencyKey` para evitar duplicação de transações.

2. **Saldo**: O sistema verifica automaticamente se há saldo suficiente antes de processar o saque.

3. **Webhook**: Atualizações de status são recebidas via webhook da Ecomovi em `/api/webhooks/ecomovi`.

4. **Logs**: Todas as operações são registradas nos logs do sistema para auditoria.

5. **Cancelamento**: Apenas transações com status `PENDING` ou `PROCESSING` podem ser canceladas.

6. **Estorno**: Ao cancelar uma transação, o valor é automaticamente devolvido ao saldo da organização.

## Exemplo de Fluxo Completo

```bash
# 1. Criar saque PIX
curl -X POST /api/payments/pix/withdrawal \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 10000,
    "pixKey": "<EMAIL>",
    "pixKeyType": "EMAIL",
    "organizationId": "org_123"
  }'

# 2. Consultar status
curl -X GET "/api/payments/pix/withdrawal/status/tx_123?organizationId=org_123"

# 3. Listar saques
curl -X GET "/api/payments/pix/withdrawal/list?organizationId=org_123&page=1&limit=10"

# 4. Cancelar saque (se necessário)
curl -X POST /api/payments/pix/withdrawal/cancel/tx_123 \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org_123",
    "reason": "Cancelado pelo usuário"
  }'
```