import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { processPixWithdrawal } from "@repo/payments/provider/ecomovi";
import { getGatewayCredentials } from "@repo/payments/provider/factory";
import { z } from "zod";

// Schema de validação para PIX withdrawal
const PixWithdrawalSchema = z.object({
  amount: z.number().positive().min(1, "Amount must be at least 1 centavo"),
  pixKey: z.string().min(1, "PIX key is required"),
  pixKeyType: z.enum(["CPF", "CNPJ", "EMAIL", "TELEFONE", "EVP"], {
    errorMap: () => ({ message: "PIX key type must be CPF, CNPJ, EMAIL, TELEFONE, or EVP" })
  }),
  customerName: z.string().optional(),
  customerDocument: z.string().optional(),
  description: z.string().optional(),
  organizationId: z.string().min(1, "Organization ID is required"),
  transactionId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  idempotencyKey: z.string().optional()
});

// Endpoint para processar PIX withdrawal via Ecomovi
export async function POST(req: NextRequest) {
  try {
    logger.info("PIX withdrawal endpoint called");

    // Parse do corpo da requisição
    const body = await req.json();

    // Validar dados de entrada
    const validationResult = PixWithdrawalSchema.safeParse(body);

    if (!validationResult.success) {
      logger.error("Invalid PIX withdrawal request data", {
        errors: validationResult.error.errors
      });

      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const {
      amount,
      pixKey,
      pixKeyType,
      customerName,
      customerDocument,
      description,
      organizationId,
      transactionId,
      metadata,
      idempotencyKey
    } = validationResult.data;

    // Verificar se a organização existe
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true, status: true }
    });

    if (!organization) {
      logger.error("Organization not found", { organizationId });
      return NextResponse.json(
        {
          success: false,
          error: "Organization not found"
        },
        { status: 404 }
      );
    }

    if (organization.status !== "ACTIVE") {
      logger.error("Organization is not active", { organizationId, status: organization.status });
      return NextResponse.json(
        {
          success: false,
          error: "Organization is not active"
        },
        { status: 403 }
      );
    }

    // Verificar se a organização tem gateway Ecomovi configurado
    try {
      await getGatewayCredentials(organizationId, "ECOMOVI");
    } catch (error) {
      logger.error("Ecomovi gateway not configured for organization", {
        organizationId,
        error: error instanceof Error ? error.message : error
      });

      return NextResponse.json(
        {
          success: false,
          error: "Ecomovi gateway not configured for this organization"
        },
        { status: 400 }
      );
    }

    // Verificar saldo da organização (opcional - pode ser implementado depois)
    const balance = await db.balance.findUnique({
      where: { organizationId },
      select: { available: true }
    });

    if (balance && balance.available < amount) {
      logger.error("Insufficient balance for withdrawal", {
        organizationId,
        requestedAmount: amount,
        availableBalance: balance.available
      });

      return NextResponse.json(
        {
          success: false,
          error: "Insufficient balance for withdrawal"
        },
        { status: 400 }
      );
    }

    logger.info("Processing PIX withdrawal", {
      organizationId,
      amount,
      pixKey: `${pixKey.substring(0, 3)}...`,
      pixKeyType
    });

    // Processar o withdrawal via Ecomovi
    const result = await processPixWithdrawal({
      amount,
      pixKey,
      pixKeyType,
      customerName,
      customerDocument,
      description,
      organizationId,
      transactionId,
      metadata,
      idempotencyKey
    });

    logger.info("PIX withdrawal processed successfully", {
      transactionId: result.transactionId,
      status: result.status
    });

    return NextResponse.json({
      success: true,
      data: {
        transactionId: result.transactionId,
        externalId: result.externalId,
        status: result.status,
        amount: result.amount,
        endToEndId: result.endToEndId,
        metadata: result.metadata
      }
    });

  } catch (error) {
    logger.error("Error processing PIX withdrawal", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error while processing withdrawal"
      },
      { status: 500 }
    );
  }
}

// Endpoint GET para verificar status do serviço
export async function GET() {
  return NextResponse.json({
    success: true,
    message: "PIX withdrawal endpoint is available",
    provider: "ecomovi",
    supportedPixKeyTypes: ["CPF", "CNPJ", "EMAIL", "TELEFONE", "EVP"]
  });
}
