import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { z } from "zod";

// Schema de validação para listagem de withdrawals
const WithdrawalListSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  status: z.enum(["PENDING", "PROCESSING", "APPROVED", "REJECTED", "CANCELLED", "FAILED"]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  minAmount: z.coerce.number().positive().optional(),
  maxAmount: z.coerce.number().positive().optional(),
  pixKey: z.string().optional()
});

// Endpoint para listar PIX withdrawals
export async function GET(req: NextRequest) {
  try {
    logger.info("PIX withdrawal list endpoint called");

    // Obter parâmetros da query string
    const { searchParams } = new URL(req.url);
    const queryParams = {
      organizationId: searchParams.get("organizationId"),
      page: searchParams.get("page"),
      limit: searchParams.get("limit"),
      status: searchParams.get("status"),
      startDate: searchParams.get("startDate"),
      endDate: searchParams.get("endDate"),
      minAmount: searchParams.get("minAmount"),
      maxAmount: searchParams.get("maxAmount"),
      pixKey: searchParams.get("pixKey")
    };

    // Validar parâmetros
    const validationResult = WithdrawalListSchema.safeParse(queryParams);
    
    if (!validationResult.success) {
      logger.error("Invalid withdrawal list query parameters", {
        errors: validationResult.error.errors
      });
      
      return NextResponse.json(
        {
          success: false,
          error: "Invalid query parameters",
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const {
      organizationId,
      page,
      limit,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      pixKey
    } = validationResult.data;

    // Verificar se a organização existe
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true, status: true }
    });

    if (!organization) {
      logger.error("Organization not found", { organizationId });
      return NextResponse.json(
        {
          success: false,
          error: "Organization not found"
        },
        { status: 404 }
      );
    }

    // Construir filtros para a consulta
    const whereClause: any = {
      organizationId,
      type: "WITHDRAWAL"
    };

    // Filtro por status
    if (status) {
      whereClause.status = status;
    }

    // Filtro por data
    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereClause.createdAt.lte = new Date(endDate);
      }
    }

    // Filtro por valor
    if (minAmount || maxAmount) {
      whereClause.amount = {};
      if (minAmount) {
        whereClause.amount.gte = minAmount;
      }
      if (maxAmount) {
        whereClause.amount.lte = maxAmount;
      }
    }

    // Filtro por chave PIX (busca no metadata)
    if (pixKey) {
      whereClause.metadata = {
        path: ["ecomovi", "pixKey"],
        string_contains: pixKey
      };
    }

    // Calcular offset para paginação
    const offset = (page - 1) * limit;

    // Buscar transações com paginação
    const [transactions, totalCount] = await Promise.all([
      db.transaction.findMany({
        where: whereClause,
        select: {
          id: true,
          externalId: true,
          status: true,
          amount: true,
          createdAt: true,
          updatedAt: true,
          approvedAt: true,
          metadata: true
        },
        orderBy: {
          createdAt: "desc"
        },
        skip: offset,
        take: limit
      }),
      db.transaction.count({
        where: whereClause
      })
    ]);

    // Processar dados das transações
    const processedTransactions = transactions.map(transaction => {
      const metadata = transaction.metadata as any;
      const ecomoviData = metadata?.ecomovi || {};

      return {
        transactionId: transaction.id,
        externalId: transaction.externalId,
        status: transaction.status,
        amount: transaction.amount,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
        approvedAt: transaction.approvedAt,
        pixKey: ecomoviData.pixKey,
        pixKeyType: ecomoviData.pixKeyType,
        customerName: ecomoviData.customerName,
        description: ecomoviData.description,
        endToEndId: ecomoviData.endToEndId
      };
    });

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    logger.info("PIX withdrawal list retrieved", {
      organizationId,
      totalCount,
      page,
      limit,
      totalPages
    });

    return NextResponse.json({
      success: true,
      data: {
        transactions: processedTransactions,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPreviousPage
        },
        filters: {
          status,
          startDate,
          endDate,
          minAmount,
          maxAmount,
          pixKey
        }
      }
    });

  } catch (error) {
    logger.error("Error retrieving PIX withdrawal list", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error while retrieving withdrawal list"
      },
      { status: 500 }
    );
  }
}