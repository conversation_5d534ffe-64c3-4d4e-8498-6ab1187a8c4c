import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { auth } from "@repo/auth";

export const POST = async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({
      headers: req.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: transactionId } = await params;

    if (!transactionId) {
      return NextResponse.json({ error: "Transaction ID is required" }, { status: 400 });
    }

    const data = await req.json();
    const { organizationId, externalId, originalStatus } = data;

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    logger.info("Forçando atualização de status de transação para APPROVED", {
      transactionId,
      organizationId,
      externalId,
      originalStatus
    });

    // Verificar se a transação existe
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
    });

    if (!transaction) {
      logger.error("Transaction not found", { transactionId });
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Verificar se a transação pertence à organização correta
    if (transaction.organizationId !== organizationId) {
      logger.error("Transaction does not belong to this organization", {
        transactionId,
        organizationId,
        transactionOrg: transaction.organizationId
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Se já estiver aprovada, apenas retornar
    if (transaction.status === "APPROVED") {
      logger.info("Transaction already approved", { transactionId });
      return NextResponse.json({
        success: true,
        message: "Transaction already approved",
        transaction
      });
    }

    // Preparar os metadados atualizados
    const currentMetadata = transaction.metadata || {};
    const updatedMetadata = {
      ...(typeof currentMetadata === 'object' ? currentMetadata : {}),
      forcedApproval: {
        approvedAt: new Date().toISOString(),
        reason: "Manual force approve after Transfeera FINALIZADO status",
        originalStatus: originalStatus || "FINALIZADO"
      }
    };

    // Atualizar o status da transação para APPROVED
    const updatedTransaction = await db.transaction.update({
      where: { id: transactionId },
      data: {
        status: "APPROVED",
        paymentAt: new Date(),
        metadata: updatedMetadata
      }
    });

    logger.info("Transaction status forcefully updated to APPROVED", {
      transactionId,
      previousStatus: transaction.status,
      newStatus: "APPROVED"
    });

    // Trigger webhook events for the status change
    try {
      const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
      await triggerTransactionEvents(updatedTransaction, transaction.status);
      logger.info("Triggered webhook events for force-approved transaction", {
        transactionId,
        previousStatus: transaction.status,
        newStatus: "APPROVED"
      });
    } catch (webhookError) {
      logger.error("Failed to trigger webhook events for force-approved transaction", {
        error: webhookError,
        transactionId,
        previousStatus: transaction.status
      });
      // Don't fail the force approval if webhook triggering fails
    }

    return NextResponse.json({
      success: true,
      message: "Transaction status updated to APPROVED",
      transaction: updatedTransaction
    });
  } catch (error) {
    logger.error("Error forcing transaction approval", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { error: "Failed to force approve transaction" },
      { status: 500 }
    );
  }
};
