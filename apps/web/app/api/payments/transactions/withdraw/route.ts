import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { PixTransferService } from "@repo/payments/src/transfers/pix-transfer-service";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema de validação
const withdrawSchema = z.object({
  amount: z.number().positive(),
  pixKey: z.string().min(1),
  pixKeyType: z.string().min(1),
  organizationId: z.string(),
  gatewayType: z.string().optional(), // Opcional: permite forçar um tipo específico de gateway
});

// Helper function to map pixKeyType to database enum values
function mapToDbPixKeyType(pixKeyType: string): 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM' {
  switch (pixKeyType.toLowerCase()) {
    case "cpf":
      return "CPF";
    case "cnpj":
      return "CNPJ";
    case "email":
      return "EMAIL";
    case "phone":
      return "PHONE";
    case "random":
    case "evp":
      return "RANDOM";
    default:
      return "RANDOM"; // Fallback to RANDOM
  }
}

// Função para verificar se os saques estão bloqueados
async function checkWithdrawalBlocking(organizationId: string) {
  try {
    // Verificar bloqueio global do sistema
    const systemSettings = await db.system_settings.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    if (systemSettings?.globalWithdrawalBlocked) {
      return {
        blocked: true,
        message: systemSettings.globalWithdrawalMessage || "Saques temporariamente indisponíveis. Para dúvidas, entre em contato com o suporte.",
        type: 'global'
      };
    }

    // Verificar bloqueio específico da organização
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: {
        withdrawalBlocked: true,
        withdrawalBlockedReason: true
      }
    });

    if (organization?.withdrawalBlocked) {
      return {
        blocked: true,
        message: organization.withdrawalBlockedReason || "Saques temporariamente indisponíveis para sua organização. Para dúvidas, entre em contato com o suporte.",
        type: 'organization'
      };
    }

    return { blocked: false };
  } catch (error) {
    logger.error("Erro ao verificar bloqueio de saques", { error, organizationId });
    // Em caso de erro, assumir que não está bloqueado para não impedir transações válidas
    return { blocked: false };
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário tem 2FA habilitado
    if (!session.user.twoFactorEnabled) {
      // Se o usuário não tem 2FA habilitado, não permitir transferências
      logger.warn('Tentativa de transferência sem 2FA habilitado', {
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: "Autenticação em duas etapas é obrigatória para transferências",
        code: "TWO_FACTOR_REQUIRED",
        userId: session.user.id
      }, { status: 403 });
    }

    // Verificar verificação 2FA usando múltiplas abordagens para garantir compatibilidade
    const cookieHeader = headersList.get('cookie') || '';
    const x2FAHeader = headersList.get('x-2fa-verified') || '';

    // Log detalhado para depuração
    logger.info('Verificando autenticação 2FA para transferência:', {
      userId: session.user.id,
      twoFactorEnabled: session.user.twoFactorEnabled,
      hasX2FAHeader: x2FAHeader === 'true',
      cookieLength: cookieHeader.length,
      requestUrl: req.url,
      requestMethod: req.method,
      requestHeaders: Object.fromEntries(
        Array.from(headersList.entries())
          .filter(([key]) => !key.toLowerCase().includes('cookie')) // Não incluir cookies completos no log
          .map(([key, value]) => [key, value])
      )
    });

    // Verificar o cookie 2FA
    const cookies = cookieHeader.split(';').map(cookie => cookie.trim());
    const has2FAVerifiedCookie = cookies.some(cookie => cookie === '2fa_verified=true');
    const has2FATransactionToken = cookies.some(cookie => cookie.startsWith('2fa_transaction_token='));

    // Verificar se temos alguma das verificações 2FA necessárias
    const is2FAVerified = has2FAVerifiedCookie || has2FATransactionToken || x2FAHeader === 'true';

    if (!is2FAVerified) {
      logger.warn('Verificação 2FA necessária para transferência', {
        userId: session.user.id,
        has2FAVerifiedCookie,
        has2FATransactionToken,
        hasX2FAHeader: x2FAHeader === 'true'
      });

      return NextResponse.json({
        success: false,
        message: "Verificação em duas etapas necessária",
        code: "TWO_FACTOR_REQUIRED",
        userId: session.user.id
      }, { status: 403 });
    }

    // Se a verificação 2FA é válida, permitir a transferência
    logger.info("Verificação 2FA válida encontrada, permitindo transferência", {
      userId: session.user.id,
      verificationType: has2FAVerifiedCookie ? 'cookie' :
                       has2FATransactionToken ? 'token' :
                       'header'
    });

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const validationResult = withdrawSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      gatewayType
    } = validationResult.data;

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Verificar bloqueio de saques
    const blockingResult = await checkWithdrawalBlocking(organizationId);
    if (blockingResult.blocked) {
      logger.warn("Transferência bloqueada", {
        userId: session.user.id,
        organizationId,
        message: blockingResult.message,
        type: blockingResult.type
      });
      return NextResponse.json({
        success: false,
        message: blockingResult.message
      }, { status: 403 });
    }

    // Use the shared PIX transfer service
    try {
      const transferResult = await PixTransferService.processTransfer({
        amount,
        pixKey,
        pixKeyType: mapToDbPixKeyType(pixKeyType),
        organizationId,
        gatewayType,
        metadata: {
          source: "web_interface",
          createdBy: session.user.id,
          twoFactorVerified: true // Web interface requires 2FA
        },
        userId: session.user.id,
        requiresTwoFactor: true
      });

      // Trigger webhook events for the transaction
      try {
        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        const transaction = await db.transaction.findUnique({
          where: { id: transferResult.id }
        });
        if (transaction) {
          await triggerTransactionEvents(transaction);
          logger.info("Webhook events triggered for transfer transaction", { transactionId: transferResult.id });
        }
      } catch (webhookError) {
        logger.error("Error triggering webhook events for transfer", {
          error: webhookError,
          transactionId: transferResult.id
        });
        // Don't interrupt the flow if webhook fails
      }

      return NextResponse.json({
        success: true,
        id: transferResult.id,
        status: transferResult.status,
        amount: transferResult.amount,
        pixKey: transferResult.pixKey,
        pixKeyType: transferResult.pixKeyType,
        gatewayType: transferResult.gatewayType,
        fee: transferResult.totalFee,
        totalAmount: transferResult.totalAmount,
        externalId: transferResult.externalId,
        message: transferResult.message || "Transfer processed successfully"
      }, { status: 202 }); // Status 202 - Accepted

    } catch (transferError: any) {
      logger.error("Error processing PIX transfer via shared service", {
        error: transferError.message || transferError,
        organizationId,
        userId: session.user.id
      });

      return NextResponse.json({
        success: false,
        message: transferError.message || "Failed to process transfer"
      }, { status: 500 });
    }
  } catch (error: any) {
    const errorMessage = error.message || "Erro desconhecido";
    const errorName = error.name || "Error";
    const errorStack = error.stack?.split('\n')[0] || "";

    logger.error("Erro ao processar transferência Pix:", {
      errorMessage,
      errorName,
      errorStack
    });

    return NextResponse.json({
      success: false,
      message: errorMessage
    }, { status: 500 });
  }
}
