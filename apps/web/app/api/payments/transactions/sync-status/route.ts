import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { getPaymentProvider } from "@repo/payments/provider/factory";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema de validação
const syncStatusSchema = z.object({
  transactionId: z.string(),
  organizationId: z.string().optional(),
  originalTransactionId: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = headers();
    const session = await auth.api.getSession({
      headers: req.headers,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const validationResult = syncStatusSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const { transactionId, originalTransactionId } = validationResult.data;

    // Usar o ID da transação original se fornecido, senão usar o ID padrão
    const internalTransactionId = originalTransactionId || transactionId;

    // Buscar a transação usando o ID interno
    const transaction = await db.transaction.findUnique({
      where: { id: internalTransactionId },
      include: {
        payment_gateway: true,
      },
    });

    if (!transaction) {
      logger.error("Transação não encontrada", { internalTransactionId, transactionId });
      return NextResponse.json({ message: "Transação não encontrada" }, { status: 404 });
    }

    const organizationId = validationResult.data.organizationId || transaction.organizationId;

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Verificar se a transação pertence à organização
    if (transaction.organizationId !== organizationId) {
      return NextResponse.json({ message: "Transação não pertence a esta organização" }, { status: 403 });
    }

    // Obter o provider de pagamento
    const paymentProvider = await getPaymentProvider(organizationId, {
      forceType: transaction.payment_gateway?.type,
      action: 'status'
    });

    // Para transações do tipo PIX, usar o ID fornecido pelo cliente para busca no gateway
    // ao invés do ID interno, isso é importante especialmente para o Pluggou
    const externalIdToCheck = transactionId !== internalTransactionId
      ? transactionId
      : (transaction.externalId || transactionId);

    logger.info("Consultando status da transação no gateway", {
      transactionId: internalTransactionId,
      externalId: externalIdToCheck,
      gatewayType: transaction.payment_gateway?.type
    });

    // Verificar se temos um ID para consultar no gateway
    if (!externalIdToCheck) {
      return NextResponse.json({
        message: "Transação não possui ID externo para sincronização"
      }, { status: 400 });
    }

    try {
      // Consultar o status da transação no gateway
      const statusResult = await paymentProvider.getTransactionStatus({
        transactionId: externalIdToCheck,
        organizationId,
        transactionType: transaction.type as 'CHARGE' | 'SEND',
      });

      // Use mapped status if available, otherwise use original status
      const newStatus = statusResult.mappedStatus || statusResult.status;
      
      // Verificar se o status mudou
      if (newStatus === transaction.status) {
        logger.info(`Status da transação não mudou: ${newStatus}`, { 
          transactionId: internalTransactionId,
          originalStatus: statusResult.status,
          mappedStatus: statusResult.mappedStatus
        });
        return NextResponse.json({
          success: true,
          message: "Status da transação não mudou",
          transaction: {
            id: transaction.id,
            status: transaction.status,
            paymentAt: transaction.paymentAt,
            updatedAt: transaction.updatedAt,
          },
        });
      }

      logger.info(`Atualizando status da transação de ${transaction.status} para ${newStatus}`, {
        transactionId: internalTransactionId,
        externalId: externalIdToCheck,
        originalStatus: statusResult.status,
        mappedStatus: statusResult.mappedStatus
      });

      // Preparar dados para atualização
      const updateData: any = {
        status: newStatus,
        paymentAt: newStatus === "APPROVED" ? new Date() : transaction.paymentAt,
        updatedAt: new Date(),
      };

      // Atualizar metadados com informações da sincronização
      const updatedMetadata = {
        ...((transaction.metadata as Record<string, any>) || {}),
        lastSync: {
          syncedAt: new Date().toISOString(),
          previousStatus: transaction.status,
          newStatus: newStatus,
          originalGatewayStatus: statusResult.status,
          mappedStatus: statusResult.mappedStatus,
          gatewayResponse: statusResult
        }
      };

      updateData.metadata = updatedMetadata;

      // Atualizar o status da transação no banco de dados
      const updatedTransaction = await db.transaction.update({
        where: { id: internalTransactionId },
        data: updateData,
      });

      return NextResponse.json({
        success: true,
        message: "Status da transação atualizado com sucesso",
        transaction: {
          id: updatedTransaction.id,
          status: updatedTransaction.status,
          previousStatus: transaction.status,
          paymentAt: updatedTransaction.paymentAt,
          updatedAt: updatedTransaction.updatedAt,
        },
      });
    } catch (error: any) {
      logger.error("Erro ao obter status da transação no gateway", {
        error: error.message,
        transactionId: internalTransactionId,
        externalId: externalIdToCheck
      });

      return NextResponse.json({
        success: false,
        message: `Erro ao consultar status: ${error.message}`,
      }, { status: 500 });
    }
  } catch (error: any) {
    logger.error("Erro ao sincronizar status da transação:", error);
    return NextResponse.json({
      success: false,
      message: error.message || "Erro ao sincronizar status da transação",
    }, { status: 500 });
  }
}
