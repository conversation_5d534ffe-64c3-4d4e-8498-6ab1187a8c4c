import { auth, getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";
import { safeMonetaryConversion } from "@shared/lib/currency";

// Schema de validação para os parâmetros de consulta
const querySchema = z.object({
  organizationId: z.string(),
  page: z.string().transform(Number).default("1"),
  limit: z.string().transform(Number).default("10"),
  status: z.string().optional().nullable(),
  search: z.string().optional().nullable(),
  type: z.string().optional().nullable(),
  startDate: z.string().optional().nullable(),
  endDate: z.string().optional().nullable(),
  searchId: z.string().optional().nullable(),
  searchClient: z.string().optional().nullable(),
});

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter parâmetros da URL
    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");
    const page = url.searchParams.get("page") || "1";
    const limit = url.searchParams.get("limit") || "10";
    const status = url.searchParams.get("status");
    const search = url.searchParams.get("search");
    const type = url.searchParams.get("type");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");

    // Validar parâmetros
    const validationResult = querySchema.safeParse({
      organizationId,
      page,
      limit,
      status,
      search,
      type,
      startDate,
      endDate,
    });

    if (!validationResult.success) {
      return NextResponse.json({
        message: "Parâmetros inválidos",
        errors: validationResult.error.format(),
      }, { status: 400 });
    }

    const params = validationResult.data;

    // Verificar se o usuário tem acesso à organização
    const membership = await getOrganizationMembership(session.user.id, params.organizationId);
    if (!membership) {
      return NextResponse.json({ message: "Você não tem acesso a esta organização" }, { status: 403 });
    }

    // Construir a consulta
    const where: any = {
      organizationId: params.organizationId,
    };

    // Filtrar por status
    if (params.status && params.status !== "all") {
      where.status = params.status.toUpperCase();
    }

    // Filtrar por tipo
    if (params.type && params.type !== "all") {
      where.type = params.type.toUpperCase();
    }

    // Filtrar por data
    if (params.startDate) {
      where.createdAt = {
        ...(where.createdAt || {}),
        gte: new Date(params.startDate),
      };
    }

    if (params.endDate) {
      where.createdAt = {
        ...(where.createdAt || {}),
        lte: new Date(params.endDate),
      };
    }

    // Filtrar por termo de busca
    if (params.search) {
      where.OR = [
        { id: { contains: params.search, mode: "insensitive" } },
        { customerName: { contains: params.search, mode: "insensitive" } },
        { customerEmail: { contains: params.search, mode: "insensitive" } },
        { externalId: { contains: params.search, mode: "insensitive" } },
      ];
    }

    // Calcular paginação
    const skip = (params.page - 1) * params.limit;

    // Buscar transações
    const [transactions, total] = await Promise.all([
      db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: params.limit,
        include: {
          payment_gateway: {
            select: {
              name: true,
              type: true,
            },
          },
        },
      }) as any, // Type assertion to handle Prisma client type mismatch
      db.transaction.count({ where }),
    ]);

    // Calcular total de páginas
    const totalPages = Math.ceil(total / params.limit);

    // Formatar resposta
    const formattedTransactions = transactions.map((transaction) => {
      // PRODUCTION-SAFE: Handle monetary values correctly based on actual database format
      // Database stores transaction amounts as Float in REAIS (confirmed by production data analysis)
      const amountInReais = safeMonetaryConversion(transaction.amount, 'transaction_amount');
      const fixedFeeInReais = safeMonetaryConversion(transaction.fixedFee, 'fee_amount');
      const totalFeeInReais = safeMonetaryConversion(transaction.totalFee, 'fee_amount');
      const netAmountInReais = transaction.netAmount ? safeMonetaryConversion(transaction.netAmount, 'transaction_amount') : null;

      return {
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        customerName: transaction.customerName,
        customerEmail: transaction.customerEmail,
        customerPhone: transaction.customerPhone,
        customerDocument: transaction.customerDocument,
        createdAt: transaction.createdAt.toISOString(),
        paymentAt: transaction.paymentAt?.toISOString(),
        amount: amountInReais,
        status: transaction.status,
        pixKey: transaction.pixKey,
        pixKeyType: transaction.pixKeyType,
        type: transaction.type,
        description: transaction.description,
        // Fee information in reais
        fixedFee: fixedFeeInReais,
        totalFee: totalFeeInReais,
        percentFee: transaction.percentFee, // Percentage remains as-is
        netAmount: netAmountInReais,
        gateway: transaction.payment_gateway ? {
          name: transaction.payment_gateway.name,
          type: transaction.payment_gateway.type,
        } : null,
      };
    });

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages,
      },
    });
  } catch (error: any) {
    logger.error("Erro ao listar transações:", error);
    return NextResponse.json({
      message: error.message || "Erro ao listar transações",
    }, { status: 500 });
  }
}
