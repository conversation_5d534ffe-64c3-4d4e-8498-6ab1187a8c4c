import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter os dados da requisição
    const data = await req.json();
    const { transactionId, externalId, gatewayType, organizationId } = data;

    if (!transactionId || !externalId) {
      return NextResponse.json(
        { message: "Os IDs da transação e externo são obrigatórios" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { message: "ID da organização é obrigatório" },
        { status: 400 }
      );
    }

    logger.info("Registrando mapeamento de ID externo", {
      transactionId,
      externalId,
      gatewayType,
      organizationId,
    });

    // Buscar a transação para verificar se existe
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
    });

    if (!transaction) {
      return NextResponse.json(
        { message: "Transação não encontrada" },
        { status: 404 }
      );
    }

    // Verificar se a transação pertence à organização informada
    if (transaction.organizationId !== organizationId) {
      return NextResponse.json(
        { message: "Transação não pertence a esta organização" },
        { status: 403 }
      );
    }

    // Atualizar a transação com o ID externo
    const existingMetadata = (transaction.metadata as Record<string, any>) || {};
    const updatedTransaction = await db.transaction.update({
      where: { id: transactionId },
      data: {
        externalId,
        metadata: {
          ...existingMetadata,
          mappedAt: new Date().toISOString(),
          mappedBy: session.user.id,
          gatewayType
        }
      },
    });

    logger.info("Mapeamento de ID externo registrado com sucesso", {
      transactionId,
      externalId,
      gatewayType,
    });

    return NextResponse.json({
      success: true,
      message: "Mapeamento de ID externo registrado com sucesso",
      transaction: {
        id: updatedTransaction.id,
        externalId: updatedTransaction.externalId,
      },
    });
  } catch (error: any) {
    logger.error("Erro ao registrar mapeamento de ID externo:", error);
    return NextResponse.json(
      {
        message: error.message || "Erro ao registrar mapeamento de ID externo",
      },
      { status: 500 }
    );
  }
}
