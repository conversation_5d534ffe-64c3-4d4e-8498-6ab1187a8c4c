import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";

export async function POST(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Obter os dados da requisição
    const data = await req.json();
    const { externalId, gatewayType, organizationId } = data;

    if (!externalId) {
      return NextResponse.json(
        { message: "ID externo é obrigatório" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { message: "ID da organização é obrigatório" },
        { status: 400 }
      );
    }

    if (!gatewayType) {
      return NextResponse.json(
        { message: "Tipo de gateway é obrigatório" },
        { status: 400 }
      );
    }

    logger.info("Sincronizando transação por ID externo", {
      externalId,
      gatewayType,
      organizationId,
    });

    // Verificar se existe uma transação com este ID externo
    const transaction = await db.transaction.findFirst({
      where: {
        externalId,
        organizationId
      },
      include: {
        payment_gateway: true
      }
    });

    if (!transaction) {
      // Se não existe transação com este ID externo, tenta forçar uma sincronização
      // especial para Transfeera
      if (gatewayType === "TRANSFEERA") {
        logger.info("Tentando sincronização especial da Transfeera", {
          transferId: externalId,
          organizationId
        });

        try {
          const result = await syncTransferStatus({
            transferId: externalId,
            organizationId
          });

          return NextResponse.json({
            success: result.success,
            message: "Tentativa de sincronização especial realizada",
            result
          });
        } catch (error) {
          logger.error("Erro na sincronização especial da Transfeera", {
            error,
            externalId,
            organizationId
          });

          return NextResponse.json(
            { message: "Transação não encontrada e falha na sincronização especial" },
            { status: 404 }
          );
        }
      }

      return NextResponse.json(
        { message: "Transação não encontrada com este ID externo" },
        { status: 404 }
      );
    }

    // Verificar se a transação pertence à organização informada
    if (transaction.organizationId !== organizationId) {
      return NextResponse.json(
        { message: "Transação não pertence a esta organização" },
        { status: 403 }
      );
    }

    // Obter o provider de pagamento
    const paymentProvider = await getPaymentProvider(organizationId, {
      forceType: gatewayType,
      action: 'status'
    });

    // Consultar o status da transação no gateway
    let statusResult;

    if (gatewayType === "TRANSFEERA" && transaction.type === "SEND") {
      // Tratamento especial para transferências da Transfeera
      statusResult = await syncTransferStatus({
        transferId: externalId,
        organizationId,
        transactionId: transaction.id
      });
    } else {
      // Fluxo padrão para outros gateways ou tipos de transação
      statusResult = await paymentProvider.getTransactionStatus({
        transactionId: externalId,
        organizationId,
        transactionType: transaction.type as 'CHARGE' | 'SEND',
      });
    }

    // Use mapped status if available, otherwise use original status
    const newStatus = statusResult.mappedStatus || statusResult.status;
    
    // Verificar se o status mudou
    if (newStatus === transaction.status) {
      logger.info(`Status da transação não mudou: ${newStatus}`, {
        transactionId: transaction.id,
        externalId,
        originalStatus: statusResult.status,
        mappedStatus: statusResult.mappedStatus
      });

      return NextResponse.json({
        success: true,
        message: "Status da transação não mudou",
        transaction: {
          id: transaction.id,
          externalId: transaction.externalId,
          status: transaction.status,
        },
      });
    }

    logger.info(`Atualizando status da transação de ${transaction.status} para ${newStatus}`, {
      transactionId: transaction.id,
      originalStatus: statusResult.status,
      mappedStatus: statusResult.mappedStatus,
      externalId
    });

    // Preparar dados para atualização
    const updateData: any = {
      status: newStatus,
      paymentAt: newStatus === "APPROVED" ? new Date() : transaction.paymentAt,
      updatedAt: new Date(),
    };

    // Atualizar metadados com informações da sincronização
    const currentMetadata = (transaction.metadata as Record<string, any>) || {};
    const updatedMetadata = {
      ...currentMetadata,
      lastSync: {
        syncedAt: new Date().toISOString(),
        previousStatus: transaction.status,
        newStatus: newStatus,
        originalGatewayStatus: statusResult.status,
        mappedStatus: statusResult.mappedStatus,
        syncMethod: "external_id",
        gatewayResponse: statusResult
      }
    };

    updateData.metadata = updatedMetadata;

    // Atualizar o status da transação no banco de dados
    const updatedTransaction = await db.transaction.update({
      where: { id: transaction.id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      message: "Status da transação atualizado com sucesso",
      transaction: {
        id: updatedTransaction.id,
        externalId: updatedTransaction.externalId,
        previousStatus: transaction.status,
        status: updatedTransaction.status,
        updatedAt: updatedTransaction.updatedAt,
      },
    });
  } catch (error: any) {
    logger.error("Erro ao sincronizar transação por ID externo:", error);
    return NextResponse.json(
      {
        message: error.message || "Erro ao sincronizar transação por ID externo",
      },
      { status: 500 }
    );
  }
}
