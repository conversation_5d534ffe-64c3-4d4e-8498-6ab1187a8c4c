import { app } from "@repo/api";
import { handle } from "hono/vercel";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { createHash } from "crypto";
import { logger } from "@repo/logs";
import { safeMonetaryConversion } from "@shared/lib/currency";

// Use the handler for default behavior, but implement custom handler for GET
const honoHandler = handle(app);

// Custom API key authentication
async function validateApiKey(req: NextRequest) {
  // Get API key from header
  const apiKey = req.headers.get("X-API-Key");

  if (!apiKey) {
    return null;
  }

  try {
    // Calculate hash of the API key
    const hash = createHash("sha256").update(apiKey).digest("hex");

    // Find API key by hash
    const key = await db.api_key.findFirst({
      where: { hash },
      include: {
        organization: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!key) {
      return null;
    }

    // Check if API key has expired (expiresAt is optional)
    // Access expiresAt using bracket notation to avoid TypeScript errors
    const expiresAt = (key as any).expiresAt as Date | null | undefined;
    if (expiresAt && expiresAt < new Date()) {
      return null;
    }

    // Update lastUsedAt
    try {
      await db.api_key.update({
        where: { id: key.id },
        data: { lastUsedAt: new Date() }
      });
    } catch (error) {
      // Continue even if update fails
      console.warn("Failed to update lastUsedAt", error);
    }

    return {
      apiKey: key,
      organization: key.organization,
      user: key.user
    };
  } catch (error) {
    console.error("Error validating API key", error);
    return null;
  }
}

// Custom handler for GET transactions
export async function GET(req: NextRequest) {
  try {
    // Validate API key
    const auth = await validateApiKey(req);

    if (!auth) {
      // Fall back to Hono handler which will handle session auth
      return honoHandler(req);
    }

    // Get the organizationId from query params
    const url = new URL(req.url);
    const organizationId = url.searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ error: "organizationId is required" }, { status: 400 });
    }

    // Check if the API key has access to this organization
    if (auth.organization.id !== organizationId) {
      return NextResponse.json({ error: "Invalid organization access" }, { status: 403 });
    }

    // Get transaction ID from path (if any)
    const path = url.pathname;
    const idMatch = path.match(/\/api\/payments\/transactions\/([^\/]+)/);
    const transactionId = idMatch ? idMatch[1] : null;

    // Get query parameters
    const status = url.searchParams.get("status");
    const type = url.searchParams.get("type");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");

    // If transaction ID is provided, return a single transaction
    if (transactionId) {
      const transaction = await db.transaction.findUnique({
        where: {
          id: transactionId,
          organizationId: auth.organization.id
        },
        include: {
          payment_gateway: true,
          refunds: true,
          blocks: true
        }
      }) as any;

      if (!transaction) {
        return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
      }

      return NextResponse.json({
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        customerName: transaction.customerName,
        customerEmail: transaction.customerEmail,
        customerPhone: transaction.customerPhone,
        customerDocument: transaction.customerDocument,
        customerDocumentType: transaction.customerDocumentType,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        description: transaction.description,
        createdAt: transaction.createdAt,
        paymentAt: transaction.paymentAt,
        updatedAt: transaction.updatedAt,
        refunds: transaction.refunds.map(refund => ({
          id: refund.id,
          amount: refund.amount,
          status: refund.status,
          reason: refund.reason,
          createdAt: refund.createdAt,
          processedAt: refund.processedAt,
        })),
        blocks: transaction.blocks.map(block => ({
          id: block.id,
          reason: block.reason,
          status: block.status,
          createdAt: block.createdAt,
          releasedAt: block.releasedAt,
        })),
      });
    }

    // Build the where clause for listing transactions
    const where: any = {
      organizationId: auth.organization.id
    };

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // Get transaction count and data
    const total = await db.transaction.count({ where });

    const transactions = await db.transaction.findMany({
      where,
      orderBy: {
        createdAt: "desc"
      },
      skip: (page - 1) * limit,
      take: limit
    });

    // Format response
    return NextResponse.json({
      data: transactions.map(tx => {
        const metadata = tx.metadata as Record<string, any> || {};

        // PRODUCTION-SAFE: Handle monetary values correctly based on actual database format
        // Database stores transaction amounts as Float in REAIS (confirmed by production data analysis)
        const amountInReais = safeMonetaryConversion(tx.amount, 'transaction_amount');

        return {
          id: tx.id,
          externalId: tx.externalId,
          referenceCode: tx.referenceCode,
          customerName: tx.customerName,
          customerEmail: tx.customerEmail,
          amount: amountInReais,
          status: tx.status,
          type: tx.type,
          createdAt: tx.createdAt,
          paymentAt: tx.paymentAt,
          pix: tx.type === "CHARGE" ? {
            qrCode: metadata.pixCode,
            qrCodeImage: metadata.pixQrCode,
            expirationDate: metadata.pixExpiresAt,
            txid: metadata.txid
          } : null
        };
      }),
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("Error processing transactions request", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Use Hono handler for other methods
export const POST = honoHandler;
export const PUT = honoHandler;
export const PATCH = honoHandler;
export const DELETE = honoHandler;
export const OPTIONS = honoHandler;

