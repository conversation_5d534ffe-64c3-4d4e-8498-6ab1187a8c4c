import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";
import { z } from "zod";

const manualSyncSchema = z.object({
  transactionId: z.string().optional(), // ID interno da transação
  externalId: z.string().optional(), // ID externo da Transfeera
  organizationId: z.string(), // ID da organização
  forceApproved: z.boolean().optional().default(false), // Forçar status APPROVED
});

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    const isAdmin = session.user.role === "admin";

    // Obter dados da requisição
    const data = await req.json();

    // Validar dados
    const result = manualSyncSchema.safeParse(data);

    if (!result.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: result.error.format()
      }, { status: 400 });
    }

    const { transactionId, externalId, organizationId, forceApproved } = result.data;

    // É necessário pelo menos um dos IDs
    if (!transactionId && !externalId) {
      return NextResponse.json({
        message: "É necessário fornecer um ID de transação interno ou externo"
      }, { status: 400 });
    }

    logger.info("Sincronização manual de transação Transfeera", {
      transactionId,
      externalId,
      organizationId,
      forceApproved,
      requestedBy: session.user.id
    });

    // Se temos um ID interno, verificar se a transação existe
    let transaction = null;
    if (transactionId) {
      transaction = await db.transaction.findUnique({
        where: { id: transactionId }
      });

      if (!transaction) {
        return NextResponse.json({
          message: "Transação não encontrada"
        }, { status: 404 });
      }

      // Verificar se a transação pertence à organização
      if (transaction.organizationId !== organizationId && !isAdmin) {
        return NextResponse.json({
          message: "Transação não pertence a esta organização"
        }, { status: 403 });
      }
    }

    // Parâmetros para a sincronização
    const syncParams: any = {
      transferId: externalId || transaction?.externalId,
      organizationId,
      transactionId: transactionId || transaction?.id
    };

    // Se forceApproved for true, adicionar esse parâmetro
    if (forceApproved) {
      // Verificar se o usuário é admin para realizar esta ação
      if (!isAdmin) {
        return NextResponse.json({
          message: "Apenas administradores podem forçar aprovação de transações"
        }, { status: 403 });
      }

      syncParams.forceApproved = true;
    }

    // Realizar a sincronização
    try {
      const result = await syncTransferStatus(syncParams);

      return NextResponse.json({
        success: true,
        message: "Sincronização realizada com sucesso",
        result
      });
    } catch (error) {
      logger.error("Erro ao sincronizar transação", {
        error,
        transactionId,
        externalId,
        organizationId
      });

      return NextResponse.json({
        success: false,
        message: error instanceof Error ? error.message : "Erro ao sincronizar transação"
      }, { status: 500 });
    }
  } catch (error) {
    logger.error("Erro ao processar requisição", { error });

    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}
