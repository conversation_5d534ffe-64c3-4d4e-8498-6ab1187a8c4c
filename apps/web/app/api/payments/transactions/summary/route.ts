import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { safeMonetaryConversion } from "@shared/lib/currency";

export async function GET(req: NextRequest) {
  try {
    // Get organization ID and filters from query
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");
    const status = searchParams.get("status");
    const type = searchParams.get("type");

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    try {
      // Construir o filtro base
      const baseFilter: any = { organizationId };

      // Adicionar filtros de status e tipo se fornecidos
      if (status) {
        baseFilter.status = status;
      }

      if (type) {
        baseFilter.type = type;
      }

      console.log("Filtros aplicados:", baseFilter);

      // Get total transactions count
      const totalTransactions = await db.transaction.count({
        where: baseFilter
      });

      // Get approved transactions count (baseado no filtro atual)
      const approvedFilter: any = { ...baseFilter };
      if (!status) {
        approvedFilter.status = "APPROVED";
      }

      const approvedTransactions = await db.transaction.count({
        where: approvedFilter
      });

      // Get pending transactions count (baseado no filtro atual)
      const pendingFilter: any = { ...baseFilter };
      if (!status) {
        pendingFilter.status = "PENDING";
      }

      const pendingTransactions = await db.transaction.count({
        where: pendingFilter
      });

      // Special fix for refund view: when status is REFUNDED, there are no pending refunds
      // since pendingTransactions only makes sense when counting real pending transactions
      const actualPendingTransactions = (status === "REFUNDED") ? 0 : pendingTransactions;

      // Get total financial volume
      const transactions = await db.transaction.findMany({
        where: baseFilter,
        select: { amount: true }
      });

      // Convert monetary values using precise decimal arithmetic
      const financialVolume = transactions.reduce((sum, tx) => sum + safeMonetaryConversion(tx.amount, 'transaction_amount'), 0);
      const averageTicket = transactions.length > 0 ? financialVolume / transactions.length : 0;

      // Get 30 day old counts for growth calculation
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Filtro para dados antigos (30 dias atrás)
      const oldBaseFilter: any = {
        ...baseFilter,
        createdAt: { lt: thirtyDaysAgo }
      };

      const oldTotalTransactions = await db.transaction.count({
        where: oldBaseFilter
      });

      // Filtro para transações aprovadas antigas
      const oldApprovedFilter: any = { ...oldBaseFilter };
      if (!status) {
        oldApprovedFilter.status = "APPROVED";
      }

      const oldApprovedTransactions = await db.transaction.count({
        where: oldApprovedFilter
      });

      // Filtro para transações pendentes antigas
      const oldPendingFilter: any = { ...oldBaseFilter };
      if (!status) {
        oldPendingFilter.status = "PENDING";
      }

      const oldPendingTransactions = await db.transaction.count({
        where: oldPendingFilter
      });

      // Volume financeiro antigo
      const oldTransactions = await db.transaction.findMany({
        where: oldBaseFilter,
        select: { amount: true }
      });

      const oldFinancialVolume = oldTransactions.reduce((sum, tx) => sum + safeMonetaryConversion(tx.amount, 'transaction_amount'), 0);

      // Calculate growth percentages
      const calculateGrowth = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const totalTransactionsGrowth = calculateGrowth(totalTransactions, oldTotalTransactions);
      const approvedTransactionsGrowth = calculateGrowth(approvedTransactions, oldApprovedTransactions);
      const pendingTransactionsGrowth = calculateGrowth(pendingTransactions, oldPendingTransactions);
      const financialVolumeGrowth = calculateGrowth(financialVolume, oldFinancialVolume);

      // Calculate approval rate (considerando o contexto do filtro)
      const approvalRate = totalTransactions > 0 ? (approvedTransactions / totalTransactions) * 100 : 0;

      // Montar a resposta
      const data = {
        totalTransactions: {
          count: totalTransactions,
          growth: totalTransactionsGrowth,
        },
        approvedTransactions: {
          count: approvedTransactions,
          growth: approvedTransactionsGrowth,
          approvalRate,
        },
        pendingTransactions: {
          count: actualPendingTransactions,
          growth: pendingTransactionsGrowth,
        },
        financialVolume: {
          amount: financialVolume,
          growth: financialVolumeGrowth,
          averageTicket,
        },
      };

      return NextResponse.json(data);
    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to fetch transaction data from database" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Error in transaction summary route:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
