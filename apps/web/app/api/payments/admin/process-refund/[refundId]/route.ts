import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { getSession } from "@saas/auth/lib/server";

export async function POST(
  req: NextRequest,
  { params }: { params: { refundId: string } }
) {
  try {
    const session = await getSession();

    // Verifica se o usuário está autenticado
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { refundId } = await params;

    // Verificar se o estorno existe
    const refund = await db.refund.findUnique({
      where: { id: refundId },
      include: {
        transaction: true,
      },
    });

    if (!refund) {
      return NextResponse.json(
        { error: "Refund not found" },
        { status: 404 }
      );
    }

    // Verificar se a transação é uma transferência (SEND)
    if (refund.transaction.type === "SEND") {
      return NextResponse.json(
        { error: "Transferências não podem ser estornadas. Use cancelamento para transferências pendentes/processando." },
        { status: 400 }
      );
    }

    // Verificar se o usuário tem acesso à organização
    const organizationId = refund.transaction.organizationId;
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this organization" },
        { status: 403 }
      );
    }

    // Processar o estorno
    if (refund.status === "APPROVED") {
      // Import handleRefundApproved function
      const { handleRefundApproved } = await import("@repo/payments/src/transactions/listeners");

      // Update the organization balance
      await handleRefundApproved(refund, refund.transaction);

      // Ensure transaction is REFUNDED
      let updatedTransaction = refund.transaction;
      if (refund.transaction.status !== "REFUNDED") {
        updatedTransaction = await db.transaction.update({
          where: { id: refund.transaction.id },
          data: { status: "REFUNDED" },
        });
      }

      // Trigger PIX reversal webhook after successful refund processing
      try {
        const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");
        const { WebhookEventType } = await import("@repo/payments/src/webhooks/events");

        // Create PIX reversal webhook payload
        const pixReversalPayload = {
          id: refund.id,
          type: "REFUND",
          amount: refund.amount,
          status: "REFUNDED",
          reason: refund.reason,
          processedAt: refund.processedAt,
          customerName: updatedTransaction.customerName,
          customerEmail: updatedTransaction.customerEmail,
          customerPhone: updatedTransaction.customerPhone,
          customerDocument: updatedTransaction.customerDocument,
          customerDocumentType: updatedTransaction.customerDocumentType,
          organizationId: updatedTransaction.organizationId,
          gatewayId: updatedTransaction.gatewayId,
          gatewayName: updatedTransaction.gatewayName,
          originalTransaction: {
            id: updatedTransaction.id,
            externalId: updatedTransaction.externalId,
            referenceCode: updatedTransaction.referenceCode,
            amount: updatedTransaction.amount,
            status: "REFUNDED",
            type: updatedTransaction.type,
            createdAt: updatedTransaction.createdAt,
            paymentAt: updatedTransaction.paymentAt,
            pixKey: updatedTransaction.pixKey,
            pixKeyType: updatedTransaction.pixKeyType,
            endToEndId: updatedTransaction.endToEndId,
          },
          refund: {
            id: refund.id,
            amount: refund.amount,
            reason: refund.reason,
            status: refund.status,
            processedAt: refund.processedAt,
            createdAt: refund.createdAt,
          },
          createdAt: refund.createdAt,
          updatedAt: refund.updatedAt,
        };

        // Trigger PIX reversal webhook event
        await createWebhookEvent({
          type: WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
          payload: pixReversalPayload,
          transactionId: updatedTransaction.id,
          organizationId: updatedTransaction.organizationId,
        });

        console.log("PIX reversal webhook triggered successfully", {
          refundId: refund.id,
          transactionId: updatedTransaction.id,
          organizationId: updatedTransaction.organizationId,
        });
      } catch (webhookError) {
        // Log webhook error but don't fail the refund process
        console.error("Failed to trigger PIX reversal webhook:", webhookError);
      }

      return NextResponse.json({
        success: true,
        message: "Refund processed successfully",
        refundId: refund.id,
      });
    } else {
      // If not already approved, update it to approved
      const { updateRefundStatus } = await import("@repo/payments/src/refunds/service");

      const updatedRefund = await updateRefundStatus(refundId, "APPROVED");

      return NextResponse.json({
        success: true,
        message: "Refund approved and processed",
        refundId: updatedRefund.id,
      });
    }
  } catch (error) {
    console.error("Error processing refund:", error);
    return NextResponse.json(
      { error: "Failed to process refund" },
      { status: 500 }
    );
  }
}
