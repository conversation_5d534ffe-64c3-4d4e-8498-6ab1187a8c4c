import { NextRequest, NextResponse } from 'next/server';
import { getOwempayV2Balance } from '@repo/payments/provider/owempayv2';
import { getMicrocashBalance } from '@repo/payments/provider/microcash';
import { db } from '@repo/database';
import { logger } from '@repo/logs';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    logger.info("POST /api/gateways/balance - Iniciando requisição", {
      url: request.url,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString()
    });

    const body = await request.json();
    const { gatewayId, organizationId } = body;

    logger.info("POST /api/gateways/balance - Parâmetros recebidos", {
      gatewayId,
      organizationId,
      bodyKeys: Object.keys(body),
      timestamp: new Date().toISOString()
    });

    if (!gatewayId || !organizationId) {
      logger.warn("POST /api/gateways/balance - Parâmetros obrigatórios ausentes", {
        gatewayId: !!gatewayId,
        organizationId: !!organizationId,
        body,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json(
        { error: 'Gateway ID e Organization ID são obrigatórios' },
        { status: 400 }
      );
    }

    // Buscar informações do gateway no banco de dados
    logger.info("POST /api/gateways/balance - Buscando informações do gateway", {
      gatewayId,
      organizationId,
      timestamp: new Date().toISOString()
    });

    const gateway = await db.payment_gateway.findUnique({
      where: { id: gatewayId },
      select: { type: true, isActive: true }
    });

    logger.info("POST /api/gateways/balance - Resultado da busca do gateway", {
      gatewayId,
      organizationId,
      gatewayFound: !!gateway,
      gatewayType: gateway?.type,
      gatewayIsActive: gateway?.isActive,
      timestamp: new Date().toISOString()
    });

    if (!gateway) {
      logger.warn("POST /api/gateways/balance - Gateway não encontrado", {
        gatewayId,
        organizationId,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json(
        { error: 'Gateway não encontrado' },
        { status: 404 }
      );
    }

    if (!gateway.isActive) {
      logger.warn("POST /api/gateways/balance - Gateway não está ativo", {
        gatewayId,
        organizationId,
        gatewayType: gateway.type,
        gatewayIsActive: gateway.isActive,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json(
        { error: 'Gateway não está ativo' },
        { status: 400 }
      );
    }

    logger.info('POST /api/gateways/balance - Iniciando busca de saldo do gateway', { 
      gatewayId, 
      organizationId, 
      gatewayType: gateway.type,
      timestamp: new Date().toISOString()
    });

    let balanceData;

    // Determinar qual função de balance usar baseado no tipo do gateway
    logger.info('POST /api/gateways/balance - Determinando função de balance', {
      gatewayId,
      organizationId,
      gatewayType: gateway.type,
      gatewayTypeLower: gateway.type.toLowerCase(),
      timestamp: new Date().toISOString()
    });

    switch (gateway.type.toLowerCase()) {
      case 'owempay_v2':
        logger.info('POST /api/gateways/balance - Chamando getOwempayV2Balance', {
          gatewayId,
          organizationId,
          gatewayType: gateway.type,
          timestamp: new Date().toISOString()
        });
        balanceData = await getOwempayV2Balance(organizationId);
        logger.info('POST /api/gateways/balance - getOwempayV2Balance concluída', {
          gatewayId,
          organizationId,
          balanceDataSuccess: balanceData?.success,
          balanceDataKeys: balanceData ? Object.keys(balanceData) : [],
          timestamp: new Date().toISOString()
        });
        break;
      case 'microcash':
        logger.info('POST /api/gateways/balance - Chamando getMicrocashBalance', {
          gatewayId,
          organizationId,
          gatewayType: gateway.type,
          timestamp: new Date().toISOString()
        });
        balanceData = await getMicrocashBalance(organizationId);
        logger.info('POST /api/gateways/balance - getMicrocashBalance concluída', {
          gatewayId,
          organizationId,
          balanceDataSuccess: balanceData?.success,
          balanceDataKeys: balanceData ? Object.keys(balanceData) : [],
          timestamp: new Date().toISOString()
        });
        break;
      default:
        logger.warn('POST /api/gateways/balance - Gateway não suporta consulta de saldo', {
          gatewayId,
          organizationId,
          gatewayType: gateway.type,
          timestamp: new Date().toISOString()
        });
        return NextResponse.json(
          { error: `Gateway '${gateway.type}' não suporta consulta de saldo` },
          { status: 400 }
        );
    }

    const responseData = {
      success: true,
      data: balanceData.data,
    };

    const duration = Date.now() - startTime;

    logger.info('POST /api/gateways/balance - Resposta enviada com sucesso', {
      gatewayId,
      organizationId,
      gatewayType: gateway.type,
      balanceDataSuccess: balanceData?.success,
      responseDataKeys: Object.keys(responseData),
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(responseData);
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error('POST /api/gateways/balance - Erro interno', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro interno do servidor'
      },
      { status: 500 }
    );
  }
}
