import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { createHash } from "crypto";

export async function GET(req: NextRequest) {
  try {
    // Obter a API key do cabeçalho
    const apiKey = req.headers.get("X-API-Key");
    
    if (!apiKey) {
      return NextResponse.json(
        { error: "API key is required" },
        { status: 401 }
      );
    }
    
    console.log("Testando API key:", apiKey);
    
    // Calcular o hash da API key
    const hash = createHash("sha256").update(apiKey).digest("hex");
    console.log("Hash calculado:", hash);
    
    // Buscar a API key no banco de dados pelo hash
    const key = await db.api_key.findFirst({
      where: { hash },
      include: { organization: true }
    });

    if (!key) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }

    // Atualizar lastUsedAt
    await db.api_key.update({
      where: { id: key.id },
      data: { lastUsedAt: new Date() }
    });
    
    return NextResponse.json({
      success: true,
      message: "API key is valid",
      apiKey: {
        id: key.id,
        name: key.name,
        type: key.type,
        prefix: key.prefix,
        permissions: key.permissions,
        organization: {
          id: key.organization.id,
          name: key.organization.name,
          slug: key.organization.slug
        }
      }
    });
  } catch (error) {
    console.error("Error testing API key:", error);
    return NextResponse.json(
      { error: "Failed to validate API key" },
      { status: 500 }
    );
  }
}
