import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
// import { asaas } from "@repo/payments/provider";
import { getPaymentProvider } from "@repo/payments/provider";

export async function GET(
  req: NextRequest,
  { params }: { params: { transactionId: string } }
) {
  try {
    const { transactionId } = await params;

    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: "Transaction not found" },
        { status: 404 }
      );
    }

    if (transaction.externalId && transaction.gatewayName) {
      try {
        // Get the appropriate payment provider
        const provider = await getPaymentProvider(transaction.organizationId, {
          forceType: transaction.gatewayName,
          action: 'status'
        });

        // Get status from the provider
        const statusResult = await provider.getTransactionStatus({
          transactionId: transaction.externalId,
          organizationId: transaction.organizationId,
        });

        // Update transaction status if needed
        if (statusResult.mappedStatus !== transaction.status) {
          await db.transaction.update({
            where: { id: transaction.id },
            data: {
              status: statusResult.mappedStatus,
              paymentAt: statusResult.mappedStatus === "APPROVED" ? new Date() : transaction.paymentAt,
            },
          });
        }

        return NextResponse.json({
          success: true,
          status: statusResult.mappedStatus,
          isPaid: statusResult.mappedStatus === "APPROVED",
        });
      } catch (error) {
        console.error("Error checking payment status with provider:", error);
        // Fall back to returning the current status
        return NextResponse.json({
          success: true,
          status: transaction.status,
          isPaid: transaction.status === "APPROVED",
          error: error.message,
        });
      }
    }

    return NextResponse.json({
      success: true,
      status: transaction.status,
      isPaid: transaction.status === "APPROVED",
    });
  } catch (error) {
    console.error("Error checking payment status:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to check payment status" },
      { status: 400 }
    );
  }
}
