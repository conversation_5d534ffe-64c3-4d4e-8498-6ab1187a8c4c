import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(req: NextRequest) {
  try {
    // Get the current URL to extract query parameters
    const url = new URL(req.url);
    const currentURL = url.searchParams.get("currentURL");

    if (!currentURL) {
      return NextResponse.json({ error: "Missing currentURL parameter" }, { status: 400 });
    }

    // Extract organization ID from the currentURL
    const organizationIdMatch = currentURL.match(/\/organizations\/([^/?&]+)/);
    const organizationId = organizationIdMatch ? organizationIdMatch[1] : null;

    if (!organizationId) {
      return NextResponse.json({ error: "Could not extract organization ID from URL" }, { status: 400 });
    }

    // Get session
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const isAdmin = session.user.role === "admin";

    // If user is an admin, they can modify any organization
    // If not an admin, check if they are a member of the organization
    if (!isAdmin) {
      const isMember = await db.member.findUnique({
        where: {
          userId_organizationId: {
            userId: session.user.id,
            organizationId,
          },
        },
      });

      // If user is not an admin and not a member, return forbidden
      if (!isMember) {
        return NextResponse.json({ message: "User is not a member of the organization" }, { status: 400 });
      }
    }

    // Clone the request to avoid "Body has already been read" error
    const clonedReq = req.clone();

    // Parse request body
    const body = await clonedReq.json();

    // Check if this is a taxes update request
    if (currentURL.includes("tab=taxes") && body.taxes) {
      const {
        pixChargePercentFee,
        pixTransferPercentFee,
        pixChargeFixedFee,
        pixTransferFixedFee,
        gatewaySpecificTaxes
      } = body.taxes;

      // Check if organization exists
      const organization = await db.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        return NextResponse.json({ error: "Organization not found" }, { status: 404 });
      }

      // Convert string values to numbers
      const pixChargePercentFeeNum = pixChargePercentFee !== undefined ? Number(pixChargePercentFee) : 0;
      const pixTransferPercentFeeNum = pixTransferPercentFee !== undefined ? Number(pixTransferPercentFee) : 0;
      const pixChargeFixedFeeNum = pixChargeFixedFee !== undefined ? Number(pixChargeFixedFee) : 0;
      const pixTransferFixedFeeNum = pixTransferFixedFee !== undefined ? Number(pixTransferFixedFee) : 0;

      // Upsert taxes - using correct Prisma client method name
      const taxes = await db.organization_taxes.upsert({
        where: { organizationId },
        create: {
          organizationId,
          pixChargePercentFee: pixChargePercentFeeNum,
          pixTransferPercentFee: pixTransferPercentFeeNum,
          pixChargeFixedFee: pixChargeFixedFeeNum,
          pixTransferFixedFee: pixTransferFixedFeeNum,
          gatewaySpecificTaxes: gatewaySpecificTaxes,
        },
        update: {
          pixChargePercentFee: pixChargePercentFeeNum,
          pixTransferPercentFee: pixTransferPercentFeeNum,
          pixChargeFixedFee: pixChargeFixedFeeNum,
          pixTransferFixedFee: pixTransferFixedFeeNum,
          gatewaySpecificTaxes: gatewaySpecificTaxes !== undefined ? gatewaySpecificTaxes : undefined,
        },
      });

      return NextResponse.json({ success: true, taxes });
    }

    // If not a taxes update, pass to the auth handler
    // Use the original request since we haven't consumed its body
    return auth.handler(req);
  } catch (error) {
    logger.error("Error in organization update handler", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Forward other methods to the auth handler
export async function GET(req: NextRequest) {
  return auth.handler(req);
}

export async function PUT(req: NextRequest) {
  return auth.handler(req);
}

export async function DELETE(req: NextRequest) {
  return auth.handler(req);
}

export async function PATCH(req: NextRequest) {
  return auth.handler(req);
}

export async function OPTIONS(req: NextRequest) {
  return auth.handler(req);
}
