import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { createHash } from "crypto";

export async function GET(req: NextRequest) {
  try {
    // Obter a API key do cabeçalho
    const apiKey = req.headers.get("X-API-Key");
    
    if (!apiKey) {
      return NextResponse.json(
        { error: "API key is required" },
        { status: 401 }
      );
    }
    
    // Calcular o hash da API key
    const hash = createHash("sha256").update(apiKey).digest("hex");
    
    // Buscar a API key no banco de dados pelo hash
    const key = await db.api_key.findFirst({
      where: { hash },
      include: { organization: true }
    });

    if (!key) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Verificar permissões
    const permissions = key.permissions as any;
    if (!permissions?.transactions?.read) {
      return NextResponse.json(
        { error: "API key does not have permission to read transactions" },
        { status: 403 }
      );
    }
    
    // Obter o organizationId da query
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get("organizationId");
    
    // Se não foi fornecido, usar o da API key
    const orgId = organizationId || key.organizationId;
    
    // Verificar se a API key pertence à organização solicitada
    if (organizationId && key.organizationId !== organizationId) {
      return NextResponse.json(
        { error: "API key does not have access to this organization" },
        { status: 403 }
      );
    }
    
    // Obter parâmetros de paginação e filtros
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    
    // Construir a query
    const where: any = {
      organizationId: orgId
    };
    
    if (status) {
      where.status = status;
    }
    
    if (type) {
      where.type = type;
    }
    
    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else if (startDate) {
      where.createdAt = {
        gte: new Date(startDate)
      };
    } else if (endDate) {
      where.createdAt = {
        lte: new Date(endDate)
      };
    }
    
    // Contar total de registros
    const total = await db.transaction.count({ where });
    
    // Buscar transações
    const transactions = await db.transaction.findMany({
      where,
      orderBy: {
        createdAt: "desc"
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        externalId: true,
        referenceCode: true,
        customerName: true,
        customerEmail: true,
        customerPhone: true,
        customerDocument: true,
        customerDocumentType: true,
        createdAt: true,
        paymentAt: true,
        amount: true,
        status: true,
        pixKey: true,
        pixKeyType: true,
        type: true,
        description: true,
        metadata: true,
        organizationId: true,
        gatewayId: true,
        updatedAt: true
      }
    });
    
    // Atualizar lastUsedAt da API key
    await db.api_key.update({
      where: { id: key.id },
      data: { lastUsedAt: new Date() }
    });
    
    return NextResponse.json({
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return NextResponse.json(
      { error: "Failed to fetch transactions" },
      { status: 500 }
    );
  }
}
