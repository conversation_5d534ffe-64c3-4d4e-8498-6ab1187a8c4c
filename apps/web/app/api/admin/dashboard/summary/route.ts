import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType, OrganizationStatus } from "@prisma/client";
import { safeMonetaryConversion } from "@shared/lib/currency";

export async function GET(req: NextRequest) {
  try {
    // Verify if user is admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    try {
      // Get days parameter from query string (default to 30 days)
      const { searchParams } = new URL(req.url);
      const daysParam = searchParams.get("days");
      const days = daysParam ? parseInt(daysParam, 10) : 30;

      // Get current date for time-based calculations
      const now = new Date();
      const periodStart = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // === REVENUE METRICS ===

      // Total revenue from approved transactions within selected period
      const totalRevenueData = await db.transaction.aggregate({
        where: {
          status: TransactionStatus.APPROVED,
          type: { in: [TransactionType.CHARGE, TransactionType.RECEIVE] },
          paymentAt: {
            gte: periodStart
          }
        },
        _sum: {
          amount: true,
          totalFee: true
        },
        _count: true
      });

      // Monthly revenue (current month)
      const monthlyRevenueData = await db.transaction.aggregate({
        where: {
          status: TransactionStatus.APPROVED,
          type: { in: [TransactionType.CHARGE, TransactionType.RECEIVE] },
          paymentAt: {
            gte: startOfMonth
          }
        },
        _sum: {
          amount: true,
          totalFee: true
        },
        _count: true
      });

      // Last month revenue for comparison
      const lastMonthRevenueData = await db.transaction.aggregate({
        where: {
          status: TransactionStatus.APPROVED,
          type: { in: [TransactionType.CHARGE, TransactionType.RECEIVE] },
          paymentAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        },
        _sum: {
          amount: true,
          totalFee: true
        },
        _count: true
      });

      // === TRANSACTION ANALYTICS ===

      // Total transactions by status within selected period
      const transactionsByStatus = await db.transaction.groupBy({
        by: ['status'],
        where: {
          createdAt: {
            gte: periodStart
          }
        },
        _count: true,
        _sum: {
          amount: true
        }
      });

      // Refund statistics within selected period
      const refundData = await db.transaction.aggregate({
        where: {
          type: TransactionType.REFUND,
          createdAt: {
            gte: periodStart
          }
        },
        _sum: {
          amount: true
        },
        _count: true
      });

      // === ORGANIZATION METRICS ===

      // Total organizations by status
      const organizationsByStatus = await db.organization.groupBy({
        by: ['status'],
        _count: true
      });

      // Organizations with activity within selected period
      const activeOrganizations = await db.organization.count({
        where: {
          transaction: {
            some: {
              createdAt: {
                gte: periodStart
              }
            }
          }
        }
      });

      // === MONTHLY REVENUE CHART DATA ===

      // Get last 6 months of revenue data
      const monthlyRevenueChart = [];
      for (let i = 5; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

        const monthData = await db.transaction.aggregate({
          where: {
            status: TransactionStatus.APPROVED,
            type: { in: [TransactionType.CHARGE, TransactionType.RECEIVE] },
            paymentAt: {
              gte: monthStart,
              lte: monthEnd
            }
          },
          _sum: {
            amount: true,
            totalFee: true
          },
          _count: true
        });

        monthlyRevenueChart.push({
          month: monthStart.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' }),
          revenue: safeMonetaryConversion(monthData._sum.amount || 0, 'transaction_amount'), // Already in reais
          fees: safeMonetaryConversion(monthData._sum.totalFee || 0, 'fee_amount'), // Already in reais
          transactions: monthData._count || 0
        });
      }

      // === RECENT ACTIVITY ===

      // Recent transactions within selected period
      const recentTransactions = await db.transaction.findMany({
        where: {
          createdAt: {
            gte: periodStart
          }
        },
        take: 10,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          organization: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      });

      // === CALCULATE METRICS ===
      
      // PRODUCTION-SAFE: Handle monetary values correctly (already in reais format)
      const totalRevenue = safeMonetaryConversion(totalRevenueData._sum.amount || 0, 'transaction_amount');
      const totalFees = safeMonetaryConversion(totalRevenueData._sum.totalFee || 0, 'fee_amount');
      const totalTransactions = totalRevenueData._count || 0;

      const monthlyRevenue = safeMonetaryConversion(monthlyRevenueData._sum.amount || 0, 'transaction_amount');
      const monthlyFees = safeMonetaryConversion(monthlyRevenueData._sum.totalFee || 0, 'fee_amount');
      const monthlyTransactionCount = monthlyRevenueData._count || 0;
      
      const lastMonthRevenue = safeMonetaryConversion(lastMonthRevenueData._sum.amount || 0, 'transaction_amount');
      const lastMonthTransactionCount = lastMonthRevenueData._count || 0;

      // Calculate growth percentages
      const revenueGrowth = lastMonthRevenue > 0 
        ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
        : 0;
      
      const transactionGrowth = lastMonthTransactionCount > 0 
        ? ((monthlyTransactionCount - lastMonthTransactionCount) / lastMonthTransactionCount) * 100 
        : 0;

      // Process transaction status counts
      const statusCounts = transactionsByStatus.reduce((acc, item) => {
        acc[item.status.toLowerCase()] = item._count;
        return acc;
      }, {} as Record<string, number>);

      // Process organization status counts
      const orgStatusCounts = organizationsByStatus.reduce((acc, item) => {
        acc[item.status.toLowerCase()] = item._count;
        return acc;
      }, {} as Record<string, number>);

      // Calculate success rate
      const approvedCount = statusCounts.approved || 0;
      const totalTransactionCount = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
      const successRate = totalTransactionCount > 0 ? (approvedCount / totalTransactionCount) * 100 : 0;

      // Average transaction value
      const avgTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

      // Format recent transactions
      const formattedRecentTransactions = recentTransactions.map(transaction => ({
        id: transaction.id,
        customerName: transaction.customerName,
        customerEmail: transaction.customerEmail,
        amount: safeMonetaryConversion(transaction.amount, 'transaction_amount'), // Convert to reais
        status: transaction.status,
        type: transaction.type,
        createdAt: transaction.createdAt.toISOString(),
        paymentAt: transaction.paymentAt?.toISOString() || null,
        organizationName: transaction.organization.name,
        organizationSlug: transaction.organization.slug
      }));

      // Prepare response data
      const dashboardData = {
        // Revenue metrics
        revenue: {
          total: totalRevenue,
          monthly: monthlyRevenue,
          growth: revenueGrowth,
          fees: totalFees,
          monthlyFees: monthlyFees
        },
        
        // Transaction analytics
        transactions: {
          total: totalTransactionCount,
          monthly: monthlyTransactionCount,
          growth: transactionGrowth,
          successRate: successRate,
          avgValue: avgTransactionValue,
          byStatus: statusCounts
        },
        
        // Refund statistics
        refunds: {
          total: refundData._count || 0,
          amount: safeMonetaryConversion(refundData._sum.amount || 0, 'transaction_amount') // Convert to reais
        },
        
        // Organization metrics
        organizations: {
          total: Object.values(orgStatusCounts).reduce((sum, count) => sum + count, 0),
          active: activeOrganizations,
          pending: orgStatusCounts.pending_review || 0,
          approved: orgStatusCounts.approved || 0,
          byStatus: orgStatusCounts
        },
        
        // Recent activity
        recentActivity: formattedRecentTransactions,

        // Monthly revenue chart
        monthlyRevenueChart: monthlyRevenueChart
      };

      return NextResponse.json(dashboardData);

    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Database Error" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error fetching admin dashboard summary:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
