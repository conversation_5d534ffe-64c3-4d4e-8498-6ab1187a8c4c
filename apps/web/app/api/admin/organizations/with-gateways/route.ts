import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem acessar esta informação." }, { status: 403 });
    }

    // Extrair parâmetros de query
    const url = new URL(req.url);
    const query = url.searchParams.get('query') || '';
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Construir filtros de busca
    const whereClause: any = {};
    if (query) {
      whereClause.name = {
        contains: query,
        mode: 'insensitive'
      };
    }

    // Buscar organizações com gateways
    const organizations = await db.organization.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        slug: true,
        status: true,
        logo: true,
        createdAt: true,
        withdrawalBlocked: true,
        withdrawalBlockedReason: true,
        withdrawalBlockedAt: true,
        withdrawalBlockedBy: true,
        organization_gateway: {
          include: {
            payment_gateway: {
              select: {
                id: true,
                name: true,
                type: true,
                instanceNumber: true,
                isActive: true,
                isGlobal: true,
                priority: true,
                credentials: true,
                canReceive: true,
                canSend: true
              }
            }
          },
          orderBy: [
            { isDefault: 'desc' },
            { priority: 'asc' }
          ]
        },
        _count: {
          select: {
            member: true,
            transaction: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    // Processar os dados para o formato esperado pelo frontend
    const processedOrganizations = organizations.map(org => ({
      id: org.id,
      name: org.name,
      slug: org.slug,
      status: org.status,
      logo: org.logo,
      createdAt: org.createdAt,
      _count: {
        members: org._count.member
      },
      withdrawalBlocked: org.withdrawalBlocked,
      withdrawalBlockedReason: org.withdrawalBlockedReason,
      withdrawalBlockedAt: org.withdrawalBlockedAt,
      withdrawalBlockedBy: org.withdrawalBlockedBy,
      // Processar gateways para o formato esperado
      paymentGateways: org.organization_gateway.map(og => ({
        id: og.payment_gateway.id,
        name: og.payment_gateway.name,
        type: og.payment_gateway.type,
        instanceNumber: og.payment_gateway.instanceNumber,
        isActive: og.isActive,
        isDefault: og.isDefault,
        priority: og.priority,
        canReceive: og.payment_gateway.canReceive,
        canSend: og.payment_gateway.canSend,
        credentials: og.payment_gateway.credentials,
        relationId: og.id
      }))
    }));

    const total = await db.organization.count({
      where: whereClause
    });

    return NextResponse.json({
      organizations: processedOrganizations,
      total
    });

  } catch (error) {
    console.error("Erro ao buscar organizações com gateways:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}
