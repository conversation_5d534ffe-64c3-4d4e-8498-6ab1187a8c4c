import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { nanoid } from "nanoid";
import slugify from "slugify";
import { headers } from "next/headers";
import { z } from "zod";

export async function GET(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem acessar esta informação." }, { status: 403 });
    }

    // Extrair parâmetros de query
    const url = new URL(req.url);
    const query = url.searchParams.get('query') || '';
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Construir filtros de busca
    const whereClause: any = {};
    if (query) {
      whereClause.name = {
        contains: query,
        mode: 'insensitive'
      };
    }

    // Buscar organizações com paginação e filtros
    const [organizations, total] = await Promise.all([
      db.organization.findMany({
        where: whereClause,
        select: {
          id: true,
          name: true,
          slug: true,
          status: true,
          logo: true,
          createdAt: true,
          withdrawalBlocked: true,
          withdrawalBlockedReason: true,
          withdrawalBlockedAt: true,
          withdrawalBlockedBy: true,
          _count: {
            select: {
              member: true,
              transaction: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset
      }),
      db.organization.count({
        where: whereClause
      })
    ]);

    // Retornar no formato esperado pelo frontend
    return NextResponse.json({
      organizations: organizations.map(org => ({
        id: org.id,
        name: org.name,
        slug: org.slug,
        status: org.status,
        logo: org.logo,
        createdAt: org.createdAt,
        _count: {
          members: org._count.member
        },
        withdrawalBlocked: org.withdrawalBlocked,
        withdrawalBlockedReason: org.withdrawalBlockedReason,
        withdrawalBlockedAt: org.withdrawalBlockedAt,
        withdrawalBlockedBy: org.withdrawalBlockedBy
      })),
      total
    });

  } catch (error) {
    console.error("Erro ao buscar organizações:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();

    try {
      // Validate the request data
      const schema = z.object({
        name: z.string().min(1, "Organization name is required"),
        createMembership: z.boolean().optional().default(false),
        slug: z.string().optional(),
        documentType: z.enum(["CNPJ", "CPF"]).optional().default("CNPJ"),
      });

      const data = schema.parse(body);

      // Generate a slug if not provided
      let slug = data.slug;
      if (!slug) {
        const baseSlug = slugify(data.name, { lower: true });
        slug = baseSlug;

        // Check if slug already exists and generate a unique one if needed
        const existingOrg = await db.organization.findUnique({ where: { slug } });
        if (existingOrg) {
          slug = `${baseSlug}-${nanoid(5)}`;
        }
      }

      // Create the organization
      console.log(`Creating organization "${data.name}" with slug "${slug}"`);
      const organization = await db.organization.create({
        data: {
          id: nanoid(),
          name: data.name,
          slug,
          createdAt: new Date(),
        },
      });

      // Create initial legal info record with document type
      await db.organization_legal_info.create({
        data: {
          id: nanoid(),
          organizationId: organization.id,
          documentType: data.documentType || "CNPJ",
          companyName: data.name,
          // Initialize other fields as empty strings
          document: "",
          contactEmail: "",
          contactPhone: "",
          legalRepresentative: "",
          legalRepDocumentNumber: "",
          address: "",
          city: "",
          state: "",
          postalCode: "",
          updatedAt: new Date(),
        }
      });

      // Optionally create a membership for the admin user
      if (data.createMembership) {
        console.log(`Creating owner membership for admin user ${session.user.id}`);
        await db.member.create({
          data: {
            id: nanoid(),
            role: "admin",
            organizationId: organization.id,
            userId: session.user.id,
            createdAt: new Date(),
          },
        });
      } else {
        console.log("Skipping membership creation for admin user");
      }

      return NextResponse.json(organization, { status: 201 });
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Invalid data", details: validationError.errors },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Error creating organization:", error);
    logger.error("Error creating organization", { error });
    return NextResponse.json(
      { error: "Failed to create organization", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
