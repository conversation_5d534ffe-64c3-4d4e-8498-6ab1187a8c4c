import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

// GET endpoint to fetch which organizations have default gateways
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    // Get organizations with their default gateways
    const organizationsWithDefaultGateways = await db.organization.findMany({
      select: {
        id: true,
        organization_gateway: {
          where: {
            isDefault: true,
          },
          select: {
            payment_gateway: {
              select: {
                id: true,
                name: true,
                type: true,
              }
            }
          }
        }
      }
    });

    // Transform the result to a simple map of organization IDs to gateway info
    const gatewayStatusMap = organizationsWithDefaultGateways.reduce((acc, org) => {
      acc[org.id] = {
        hasDefaultGateway: org.organization_gateway.length > 0,
        defaultGateway: org.organization_gateway[0]?.payment_gateway || null
      };
      return acc;
    }, {} as Record<string, { hasDefaultGateway: boolean, defaultGateway: any | null }>);

    return NextResponse.json(gatewayStatusMap);
  } catch (error) {
    console.error("Error fetching gateway status for organizations:", error);
    return NextResponse.json(
      { error: "Failed to fetch gateway status" },
      { status: 500 }
    );
  }
}
