import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Primeiro, buscar organizações básicas
    const organizations = await db.organization.findMany({
      where: {
        status: "PENDING_REVIEW",
      },
      select: {
        id: true,
        name: true,
        slug: true,
        status: true,
        logo: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Buscar contagem de membros separadamente para evitar problemas
    const organizationsWithCounts = await Promise.all(
      organizations.map(async (org) => {
        const memberCount = await db.member.count({
          where: { organizationId: org.id }
        });

        // Buscar informações legais separadamente
        const legalInfo = await db.organization_legal_info.findUnique({
          where: { organizationId: org.id },
          select: {
            id: true,
            document: true,
            contactEmail: true,
            companyName: true,
          }
        });

        return {
          ...org,
          _count: { member: memberCount },
          organization_legal_info: legalInfo
        };
      })
    );

    // Mapear os dados para garantir a estrutura correta
    const mappedOrganizations = organizationsWithCounts.map(org => ({
      id: org.id,
      name: org.name,
      slug: org.slug,
      status: org.status,
      logo: org.logo,
      createdAt: org.createdAt,
      _count: {
        members: org._count.member // Mapear member para members para compatibilidade
      },
      organization_legal_info: org.organization_legal_info
    }));

    return NextResponse.json({ organizations: mappedOrganizations });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error fetching pending organizations:", errorMessage);
    return NextResponse.json(
      { error: "Failed to fetch pending organizations" },
      { status: 500 }
    );
  }
}
