import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = await params;
    const body = await req.json();
    const { 
      pixChargePercentFee, 
      pixTransferPercentFee, 
      pixChargeFixedFee, 
      pixTransferFixedFee,
      gatewaySpecificTaxes
    } = body;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Upsert taxes
    const taxes = await db.organization_taxes.upsert({
      where: { organizationId: id },
      create: {
        organizationId: id,
        pixChargePercentFee: pixChargePercentFee !== undefined ? pixChargePercentFee : 0,
        pixTransferPercentFee: pixTransferPercentFee !== undefined ? pixTransferPercentFee : 0,
        pixChargeFixedFee: pixChargeFixedFee !== undefined ? pixChargeFixedFee : 0,
        pixTransferFixedFee: pixTransferFixedFee !== undefined ? pixTransferFixedFee : 0,
        gatewaySpecificTaxes: gatewaySpecificTaxes,
      },
      update: {
        pixChargePercentFee: pixChargePercentFee !== undefined ? pixChargePercentFee : undefined,
        pixTransferPercentFee: pixTransferPercentFee !== undefined ? pixTransferPercentFee : undefined,
        pixChargeFixedFee: pixChargeFixedFee !== undefined ? pixChargeFixedFee : undefined,
        pixTransferFixedFee: pixTransferFixedFee !== undefined ? pixTransferFixedFee : undefined,
        gatewaySpecificTaxes: gatewaySpecificTaxes !== undefined ? gatewaySpecificTaxes : undefined,
      },
    });

    return NextResponse.json(taxes);
  } catch (error) {
    logger.error("Error processing organization taxes request", { error });
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = await params;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Get taxes
    const taxes = await db.organization_taxes.findUnique({
      where: { organizationId: id },
    });

    if (!taxes) {
      return NextResponse.json({ error: "Taxes not found" }, { status: 404 });
    }

    return NextResponse.json(taxes);
  } catch (error) {
    logger.error("Error getting organization taxes", { error });
    return NextResponse.json(
      { error: "Failed to get taxes" },
      { status: 500 }
    );
  }
}
