import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: organizationId } = await params;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Fetch documents for this organization using raw SQL query
    const documentsResult = await db.$queryRaw`
      SELECT * FROM "organization_documents" WHERE "organizationId" = ${organizationId}
    `;

    // Se não houver documentos, retornar array vazio
    if (!documentsResult || (Array.isArray(documentsResult) && documentsResult.length === 0)) {
      return NextResponse.json({ documents: [] });
    }

    const organizationDocument = Array.isArray(documentsResult) ? documentsResult[0] : documentsResult;

    // Transform the OrganizationDocument into an array of document objects
    const documents = [];

    if (organizationDocument.cnpjDocument) {
      documents.push({
        id: "cnpj",
        type: "CNPJ",
        name: "Comprovante de CNPJ",
        url: organizationDocument.cnpjDocument,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    if (organizationDocument.businessLicense) {
      documents.push({
        id: "license",
        type: "BUSINESS_LICENSE",
        name: "Contrato Social",
        url: organizationDocument.businessLicense,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    if (organizationDocument.bankStatement) {
      documents.push({
        id: "bank",
        type: "BANK_STATEMENT",
        name: "Extrato Bancário",
        url: organizationDocument.bankStatement,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    if (organizationDocument.representativeIdDocument) {
      documents.push({
        id: "id",
        type: "ID_DOCUMENT",
        name: "Documento de Identidade do Representante",
        url: organizationDocument.representativeIdDocument,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    if (organizationDocument.proofOfAddress) {
      documents.push({
        id: "address",
        type: "PROOF_OF_ADDRESS",
        name: "Comprovante de Endereço",
        url: organizationDocument.proofOfAddress,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    if (organizationDocument.additionalDocument) {
      documents.push({
        id: "additional",
        type: "ADDITIONAL",
        name: "Documento Adicional",
        url: organizationDocument.additionalDocument,
        status: organizationDocument.status,
        createdAt: organizationDocument.createdAt.toISOString ?
          organizationDocument.createdAt.toISOString() :
          new Date(organizationDocument.createdAt).toISOString(),
      });
    }

    return NextResponse.json({ documents });
  } catch (error) {
    console.error("Error fetching organization documents:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization documents" },
      { status: 500 }
    );
  }
}
