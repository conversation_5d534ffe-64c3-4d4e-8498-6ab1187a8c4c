import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    console.log(`📝 Admin updating legal info for organization ID: ${id}`);

    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      console.log(`❌ Unauthorized attempt to update legal info`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      console.log(`❌ Forbidden: User ${session.user.id} is not an admin`);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      console.log(`❌ Organization not found, ID: ${id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Clone the request to avoid "Body has already been read" error
    const clonedReq = req.clone();
    const body = await clonedReq.json();
    console.log(`📦 Received legal info data:`, body);

    const {
      companyName,
      tradingName,
      document,
      contactEmail,
      contactPhone,
      reviewNotes,
      address,
      city,
      state,
      postalCode,
      documentType = "CNPJ",
      legalRepresentative = "",
      legalRepDocumentNumber = ""
    } = body;

    // Check if legal info exists
    const existingLegalInfo = await db.organization_legal_info.findUnique({
      where: { organizationId: id },
    });

    if (existingLegalInfo) {
      console.log(`✅ Updating existing legal info for organization: ${id}`);
      // Update existing legal info
      const updatedLegalInfo = await db.organization_legal_info.update({
        where: { organizationId: id },
        data: {
          companyName: companyName ?? existingLegalInfo.companyName,
          tradingName: tradingName ?? existingLegalInfo.tradingName,
          document: document ?? existingLegalInfo.document,
          contactEmail: contactEmail ?? existingLegalInfo.contactEmail,
          contactPhone: contactPhone ?? existingLegalInfo.contactPhone,
          reviewNotes: reviewNotes !== undefined ? reviewNotes : existingLegalInfo.reviewNotes,
          address: address ?? existingLegalInfo.address,
          city: city ?? existingLegalInfo.city,
          state: state ?? existingLegalInfo.state,
          postalCode: postalCode ?? existingLegalInfo.postalCode,
          documentType: documentType ?? existingLegalInfo.documentType,
          legalRepresentative: legalRepresentative ?? existingLegalInfo.legalRepresentative,
          legalRepDocumentNumber: legalRepDocumentNumber ?? existingLegalInfo.legalRepDocumentNumber,
        },
      });

      console.log(`✅ Legal info updated successfully:`, {
        id: updatedLegalInfo.id,
        companyName: updatedLegalInfo.companyName,
        document: updatedLegalInfo.document
      });

      return NextResponse.json(updatedLegalInfo);
    }

    console.log(`📄 Creating new legal info for organization: ${id}`);
    // Create new legal info with all required fields
    const newLegalInfo = await db.organization_legal_info.create({
      data: {
        id: nanoid(),
        organizationId: id,
        companyName: companyName || "",
        tradingName: tradingName || "",
        document: document || "",
        contactEmail: contactEmail || "",
        contactPhone: contactPhone || "",
        reviewNotes: reviewNotes || "",
        documentType: documentType || "CNPJ",
        legalRepresentative: legalRepresentative || "",
        legalRepDocumentNumber: legalRepDocumentNumber || "",
        address: address || "",
        city: city || "",
        state: state || "",
        postalCode: postalCode || "",
      },
    });

    console.log(`✅ New legal info created successfully:`, {
      id: newLegalInfo.id,
      companyName: newLegalInfo.companyName,
      document: newLegalInfo.document
    });

    return NextResponse.json(newLegalInfo);
  } catch (error) {
    console.error(`❌ Error processing organization legal info request: ${error instanceof Error ? error.message : String(error)}`);
    logger.error("Error processing organization legal info request", { error });
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    console.log(`🔍 Admin fetching legal info for organization ID: ${id}`);

    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      console.log(`❌ Unauthorized request for legal info`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      console.log(`❌ Forbidden: User ${session.user.id} is not an admin`);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      console.log(`❌ Organization not found, ID: ${id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // First check - direct query for legal info
    const legalInfo = await db.organization_legal_info.findUnique({
      where: { organizationId: id },
    });

    if (!legalInfo) {
      console.log(`⚠️ Legal info not found for organization: ${id}`);

      // Try fallback query to ensure it's not a relation issue
      const orgWithLegalInfo = await db.organization.findUnique({
        where: { id },
        include: { organization_legal_info: true }
      });

      if (orgWithLegalInfo?.organization_legal_info) {
        console.log(`✅ Found legal info through relation: ${JSON.stringify(orgWithLegalInfo.organization_legal_info)}`);
        return NextResponse.json(orgWithLegalInfo.organization_legal_info);
      }

      return NextResponse.json({ error: "Legal info not found" }, { status: 404 });
    }

    console.log(`✅ Found legal info: ${JSON.stringify({
      id: legalInfo.id,
      organizationId: legalInfo.organizationId,
      companyName: legalInfo.companyName,
      document: legalInfo.document
    })}`);

    return NextResponse.json(legalInfo);
  } catch (error) {
    console.error(`❌ Error getting organization legal info: ${error instanceof Error ? error.message : String(error)}`);
    logger.error("Error getting organization legal info", { error });
    return NextResponse.json(
      { error: "Failed to get legal info" },
      { status: 500 }
    );
  }
}
