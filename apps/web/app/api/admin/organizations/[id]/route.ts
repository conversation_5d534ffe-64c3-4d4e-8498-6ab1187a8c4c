import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { OrganizationStatus } from "@prisma/client";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;
    console.log(`🔍 Admin fetching organization data, ID: ${id}`);

    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      console.log(`❌ Unauthorized request for organization data`);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      console.log(`❌ Forbidden: User ${session.user.id} is not an admin`);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Fetch organization with all related data
    const organization = await db.organization.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            member: true,
          },
        },
        organization_legal_info: true,
        organization_taxes: true,
      },
    });

    if (!organization) {
      console.log(`❌ Organization not found, ID: ${id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Log organization data for debugging
    console.log(`✅ Found organization: ${organization.name} (${organization.id})`);
    console.log(`Legal info included: ${!!organization.legalInfo}`);
    console.log(`Taxes included: ${!!organization.taxes}`);

    // Ensure legal info is fetched directly in case it's not properly included
    if (!organization.organization_legal_info) {
      console.log(`⚠️ Legal info not included in organization data, fetching separately`);
      const legalInfo = await db.organization_legal_info.findUnique({
        where: { organizationId: id },
      });

      if (legalInfo) {
        console.log(`✅ Legal info found through direct query: ${legalInfo.id}`);
        organization.organization_legal_info = legalInfo;
      } else {
        console.log(`⚠️ No legal info found for organization: ${id}`);
      }
    } else {
      console.log(`✅ Organization legal info fields: ${Object.keys(organization.organization_legal_info).join(', ')}`);
    }

    return NextResponse.json(organization);
  } catch (error) {
    console.error(`❌ Error fetching organization: ${error instanceof Error ? error.message : String(error)}`);
    logger.error("Error fetching organization", { error });
    return NextResponse.json(
      { error: "Failed to fetch organization" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    // Clone the request to avoid "Body has already been read" error
    const clonedReq = req.clone();
    const body = await clonedReq.json();
    const { name, status } = body;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Validate status if provided
    if (status && !Object.values(OrganizationStatus).includes(status)) {
      return NextResponse.json(
        { error: "Invalid status value. Must be one of: PENDING_REVIEW, APPROVED, REJECTED, BLOCKED" },
        { status: 400 }
      );
    }

    // Update the organization
    const updated = await db.organization.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(status && { status: status as OrganizationStatus }),
      },
    });

    return NextResponse.json(updated);
  } catch (error) {
    logger.error("Error updating organization", { error });
    return NextResponse.json(
      { error: "Failed to update organization" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    // Clone the request to avoid "Body has already been read" error
    const clonedReq = req.clone();
    const body = await clonedReq.json();

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Handle different types of POST requests based on the URL path
    const url = req.nextUrl.pathname;

    // Handle legal info updates
    if (url.includes('/legal-info')) {
      const { companyName, document, contactEmail, contactPhone, reviewNotes } = body;

      // Check if legal info exists
      const existingLegalInfo = await db.organization_legal_info.findUnique({
        where: { organizationId: id },
      });

      if (existingLegalInfo) {
        // Update existing legal info
        const updatedLegalInfo = await db.organization_legal_info.update({
          where: { organizationId: id },
          data: {
            companyName,
            document,
            contactEmail,
            contactPhone,
            reviewNotes,
          },
        });

        return NextResponse.json(updatedLegalInfo);
      } else {
        // Create new legal info
        const newLegalInfo = await db.organization_legal_info.create({
          data: {
            id: nanoid(),
            organizationId: id,
            companyName,
            document,
            contactEmail,
            contactPhone,
            reviewNotes,
            documentType: "CNPJ", // Default value
            legalRepresentative: "", // Required field with default value
            legalRepDocumentNumber: "", // Required field with default value
            address: "", // Required field with default value
            city: "", // Required field with default value
            state: "", // Required field with default value
            postalCode: "", // Required field with default value
          },
        });

        return NextResponse.json(newLegalInfo);
      }
    }

    // Handle taxes updates
    if (url.endsWith('/taxes')) {
      const { pixChargePercentFee, pixTransferPercentFee, pixChargeFixedFee, pixTransferFixedFee } = body;

      // Upsert taxes - using correct Prisma client method name
      const taxes = await db.organization_taxes.upsert({
        where: { organizationId: id },
        create: {
          organizationId: id,
          pixChargePercentFee: pixChargePercentFee || 0,
          pixTransferPercentFee: pixTransferPercentFee || 0,
          pixChargeFixedFee: pixChargeFixedFee || 0,
          pixTransferFixedFee: pixTransferFixedFee || 0,
        },
        update: {
          pixChargePercentFee: pixChargePercentFee !== undefined ? pixChargePercentFee : undefined,
          pixTransferPercentFee: pixTransferPercentFee !== undefined ? pixTransferPercentFee : undefined,
          pixChargeFixedFee: pixChargeFixedFee !== undefined ? pixChargeFixedFee : undefined,
          pixTransferFixedFee: pixTransferFixedFee !== undefined ? pixTransferFixedFee : undefined,
        },
      });

      return NextResponse.json(taxes);
    }

    // Default response for unhandled POST requests
    return NextResponse.json({ error: "Unsupported operation" }, { status: 400 });
  } catch (error) {
    logger.error("Error processing organization request", { error });
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Ensure params is resolved if it's a Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    console.log(`Starting deletion process for organization ID: ${id}`);

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            member: true,
            invitation: true,
            transaction: true,
            pix_key: true,
            webhook: true,
            webhook_event: true,
          }
        }
      }
    });

    if (!organization) {
      console.log(`Organization not found: ${id}`);
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    console.log(`Found organization: ${organization.name} with related records:`, organization._count);

    // First, delete related records that might have dependencies
    const deletionResults = {
      invitations: false,
      members: false,
      legalInfo: false,
      taxes: false,
      paymentGateways: false,
      balances: false,
      balanceHistory: false,
      documents: false,
      pixKeys: false,
      webhookEvents: false,
      webhooks: false,
      transactions: false,
      organization: false
    };

    try {
      // Delete invitations
      await db.invitation.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.invitations = true;
      console.log(`✅ Deleted invitations for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting invitations:`, error);
    }

    try {
      // Delete members
      await db.member.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.members = true;
      console.log(`✅ Deleted members for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting members:`, error);
    }

    try {
      // Delete legal info
      await db.organization_legal_info.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.legalInfo = true;
      console.log(`✅ Deleted legal info for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting legal info:`, error);
    }

    try {
      // Delete taxes - using correct Prisma client method name
      await db.organization_taxes.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.taxes = true;
      console.log(`✅ Deleted taxes for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting taxes:`, error);
    }

    try {
      // Delete organization gateways
      await db.organization_gateway.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.paymentGateways = true;
      console.log(`✅ Deleted payment gateways for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting payment gateways:`, error);
    }

    try {
      // Delete balance history
      await db.balance_history.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.balanceHistory = true;
      console.log(`✅ Deleted balance history for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting balance history:`, error);
    }

    try {
      // Delete organization balance
      await db.organization_balance.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.balances = true;
      console.log(`✅ Deleted balance for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting organization balance:`, error);
    }

    try {
      // Delete organization documents
      await db.organizationDocument.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.documents = true;
      console.log(`✅ Deleted documents for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting organization documents:`, error);
    }

    try {
      // Delete PIX keys
      await db.pixKey.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.pixKeys = true;
      console.log(`✅ Deleted PIX keys for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting PIX keys:`, error);
    }

    try {
      // Delete webhook events
      await db.webhook_event.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.webhookEvents = true;
      console.log(`✅ Deleted webhook events for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting webhook events:`, error);
    }

    try {
      // Delete webhooks (and cascade to webhook deliveries)
      await db.webhook.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.webhooks = true;
      console.log(`✅ Deleted webhooks for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting webhooks:`, error);
    }

    try {
      // Delete API keys
      await db.api_key.deleteMany({
        where: { organizationId: id },
      });
      console.log(`✅ Deleted API keys for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting API keys:`, error);
    }

    try {
      // Delete transactions (this one might be problematic if there are many)
      await db.transaction.deleteMany({
        where: { organizationId: id },
      });
      deletionResults.transactions = true;
      console.log(`✅ Deleted transactions for organization: ${id}`);
    } catch (error) {
      console.error(`❌ Error deleting transactions:`, error);
    }

    // Finally, delete the organization itself
    try {
      const deleted = await db.organization.delete({
        where: { id },
      });
      deletionResults.organization = true;
      console.log(`✅ Successfully deleted organization: ${deleted.name} (${deleted.id})`);

      return NextResponse.json({
        success: true,
        deleted,
        deletionResults
      });
    } catch (error) {
      console.error(`❌ Error deleting organization:`, error);
      // If we couldn't delete the organization, return an error with details about what was deleted
      return NextResponse.json(
        {
          error: "Failed to delete organization",
          details: error instanceof Error ? error.message : String(error),
          deletionResults
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in deletion process:", error);
    logger.error("Error deleting organization", { error });
    return NextResponse.json(
      { error: "Failed to delete organization", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
