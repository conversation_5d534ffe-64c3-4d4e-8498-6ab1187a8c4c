import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { OrganizationStatus } from "@prisma/client";
import { z } from "zod";
import { headers } from "next/headers";

const statusUpdateSchema = z.object({
	status: z.enum(["PENDING_REVIEW", "APPROVED", "REJECTED", "BLOCKED"]),
	reason: z.string().optional(),
});

export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const session = await auth.api.getSession({
			headers: await headers(),
		});
		if (!session?.user) {
			return NextResponse.json(
				{ error: "Unauthorized" },
				{ status: 401 }
			);
		}

		// Check if user is admin
		if (session.user.role !== "admin") {
			return NextResponse.json(
				{ error: "Forbidden" },
				{ status: 403 }
			);
		}

		const { id: organizationId } = await params;
		const body = await request.json();

		// Validate request body
		const validationResult = statusUpdateSchema.safeParse(body);
		if (!validationResult.success) {
			return NextResponse.json(
				{ error: "Invalid request data", details: validationResult.error.errors },
				{ status: 400 }
			);
		}

		const { status, reason } = validationResult.data;

		// Check if organization exists
		const organization = await db.organization.findUnique({
			where: { id: organizationId },
			select: { id: true, name: true, status: true },
		});

		if (!organization) {
			return NextResponse.json(
				{ error: "Organization not found" },
				{ status: 404 }
			);
		}

		// Update organization status
		const updatedOrganization = await db.organization.update({
			where: { id: organizationId },
			data: {
				status: status as OrganizationStatus,
			},
			select: {
				id: true,
				name: true,
				status: true,
				createdAt: true,
			},
		});

		// Log the status change
		console.log(`Organization ${organization.name} (${organizationId}) status changed from ${organization.status} to ${status} by admin ${session.user.email}`);

		return NextResponse.json({
			message: "Organization status updated successfully",
			organization: updatedOrganization,
		});
	} catch (error) {
		console.error("Error updating organization status:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}