import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

// Add helper function at the top of the file
function validateCreateData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data || typeof data !== 'object') {
    errors.push("Data must be an object");
  }
  
  if (!data.id || typeof data.id !== 'string') {
    errors.push("id must be a non-empty string");
  }
  
  if (!data.gatewayId || typeof data.gatewayId !== 'string') {
    errors.push("gatewayId must be a non-empty string");
  }
  
  if (!data.organizationId || typeof data.organizationId !== 'string') {
    errors.push("organizationId must be a non-empty string");
  }
  
  if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
    errors.push("isActive must be a boolean");
  }
  
  if (data.isDefault !== undefined && typeof data.isDefault !== 'boolean') {
    errors.push("isDefault must be a boolean");
  }
  
  if (data.priority !== undefined && (typeof data.priority !== 'number' || isNaN(data.priority))) {
    errors.push("priority must be a valid number");
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// GET endpoint to fetch organization gateways
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // ✅ FIX: Await params before using
    const { id: organizationId } = await params;
    console.log("Fetching gateways for organization:", organizationId);

    // Get organization gateways (via the join table)
    const orgGateways = await db.organization_gateway.findMany({
      where: {
        organizationId,
      },
      include: {
        payment_gateway: true,
      },
      orderBy: [
        { isDefault: 'desc' },
        { priority: 'asc' },
      ],
    });

    console.log("Found organization gateways:", orgGateways.length);

    // Also get available global gateways that could be assigned
    const availableGateways = await db.payment_gateway.findMany({
      where: {
        isGlobal: true,
      },
    });

    console.log("Found available global gateways:", availableGateways.length);

    // Filter out gateways that are already assigned
    const assignedGatewayIds = orgGateways.map(og => og.gatewayId);
    const filteredAvailableGateways = availableGateways.filter(
      gateway => !assignedGatewayIds.includes(gateway.id)
    );

    console.log("Filtered available gateways:", filteredAvailableGateways.length);

    // ✅ FIX: Return proper structure expected by GatewayTab
    const response = {
      assignedGateways: orgGateways.map((og) => ({
        ...og.payment_gateway,
        isDefault: og.isDefault,
        isActive: og.isActive,
        priority: og.priority,
        relationId: og.id
      })),
      availableGateways: filteredAvailableGateways
    };

    console.log("Returning response:", JSON.stringify(response, null, 2));
    return NextResponse.json(response);

  } catch (error) {
    console.error("Error fetching organization gateways:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch gateways",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// POST endpoint to assign gateway
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log("=== Gateway Assignment Request Started ===");
    
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      console.log("❌ Unauthorized request");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      console.log("❌ Forbidden - user is not admin");
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    // ✅ CRITICAL: Test database connection
    try {
      await db.$queryRaw`SELECT 1`;
      console.log("✅ Database connection verified");
    } catch (dbError) {
      console.error("❌ Database connection failed:", dbError);
      return NextResponse.json(
        { error: "Database connection failed" },
        { status: 500 }
      );
    }

    // ✅ FIX: Await params before using
    const { id: organizationId } = await params;
    console.log("✅ Organization ID:", organizationId);

    // ✅ ENHANCED: More robust request body parsing
    let data;
    try {
      const text = await request.text();
      console.log("📥 Raw request body:", text);
      
      if (!text || text.trim() === '') {
        console.log("❌ Empty request body");
        return NextResponse.json(
          { error: "Empty request body" },
          { status: 400 }
        );
      }
      
      data = JSON.parse(text);
      console.log("✅ Parsed data:", JSON.stringify(data, null, 2));
      
      // ✅ CRITICAL: Validate data is not null/undefined
      if (!data || typeof data !== 'object') {
        console.log("❌ Invalid data object:", data);
        return NextResponse.json(
          { error: "Invalid request data format" },
          { status: 400 }
        );
      }
      
    } catch (parseError) {
      console.error("❌ Error parsing request body:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    // ✅ ENHANCED: Comprehensive data validation
    if (!data.gatewayId || typeof data.gatewayId !== 'string') {
      console.log("❌ Missing or invalid gatewayId:", data.gatewayId);
      return NextResponse.json(
        { error: "Missing or invalid gatewayId" },
        { status: 400 }
      );
    }

    // ✅ ENHANCED: Validate organizationId format
    if (!organizationId || typeof organizationId !== 'string') {
      console.log("❌ Invalid organizationId:", organizationId);
      return NextResponse.json(
        { error: "Invalid organization ID" },
        { status: 400 }
      );
    }

    console.log("🔍 Checking if organization exists...");
    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      console.log("❌ Organization not found");
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }
    console.log("✅ Organization found:", organization.name);

    console.log("🔍 Checking if gateway exists...");
    // Check if gateway exists
    const gateway = await db.payment_gateway.findUnique({
      where: { id: data.gatewayId },
    });

    if (!gateway) {
      console.log("❌ Gateway not found");
      return NextResponse.json(
        { error: "Gateway not found" },
        { status: 404 }
      );
    }
    console.log("✅ Gateway found:", gateway.name, gateway.type);

    // If setting as default, unset any existing default gateways
    if (data.isDefault) {
      console.log("🔄 Unsetting existing default gateways...");
      try {
        const updateResult = await db.organization_gateway.updateMany({
          where: {
            organizationId,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
        console.log("✅ Existing default gateways unset, count:", updateResult.count);
      } catch (updateError) {
        console.error("❌ Error unsetting default gateways:", updateError);
        return NextResponse.json(
          { error: "Failed to update existing default gateways" },
          { status: 500 }
        );
      }
    }

    console.log("🔍 Checking for existing assignment...");
    // Check if this gateway is already assigned
    let existingAssignment;
    try {
      existingAssignment = await db.organization_gateway.findUnique({
        where: {
          organizationId_gatewayId: {
            organizationId: organizationId,
            gatewayId: data.gatewayId
          }
        }
      });
      console.log("🔍 Existing assignment found:", !!existingAssignment);
    } catch (findError) {
      console.error("❌ Error finding existing assignment:", findError);
      return NextResponse.json(
        { error: "Failed to check existing assignment" },
        { status: 500 }
      );
    }

    let orgGateway;

    if (existingAssignment) {
      console.log("🔄 Updating existing assignment...");
      
      // ✅ CRITICAL: Prepare update data object carefully
      const updateData = {
        isActive: data.isActive !== undefined ? Boolean(data.isActive) : true,
        isDefault: data.isDefault !== undefined ? Boolean(data.isDefault) : false,
        priority: data.priority !== undefined ? Number(data.priority) : existingAssignment.priority,
        updatedAt: new Date(),
      };
      
      console.log("📝 Update data prepared:", JSON.stringify(updateData, null, 2));
      console.log("📝 Existing assignment ID:", existingAssignment.id);
      
      try {
        console.log("🔄 About to call db.organization_gateway.update with data:", JSON.stringify(updateData, null, 2));
        
        // Try without include first to isolate the issue
        const updatedOrgGateway = await db.organization_gateway.update({
          where: {
            id: existingAssignment.id,
          },
          data: updateData
        });
        
        console.log("✅ Basic assignment updated, now fetching with gateway data...");
        
        // Fetch the complete data separately
        orgGateway = await db.organization_gateway.findUnique({
          where: { id: updatedOrgGateway.id },
          include: {
            payment_gateway: true,
          }
        });
        
        console.log("✅ Assignment updated successfully with gateway data");
      } catch (updateError) {
        console.error("❌ Error updating assignment:", updateError);
        console.error("❌ Update error details:", {
          message: updateError instanceof Error ? updateError.message : "Unknown error",
          stack: updateError instanceof Error ? updateError.stack : "No stack",
          updateData,
          existingAssignmentId: existingAssignment.id
        });
        return NextResponse.json(
          { 
            error: "Failed to update gateway assignment",
            details: updateError instanceof Error ? updateError.message : "Unknown update error"
          },
          { status: 500 }
        );
      }
    } else {
      console.log("➕ Creating new assignment...");
      
      // ✅ CRITICAL: Generate ID and prepare create data object carefully
      const createData = {
        id: nanoid(), // Generate unique ID
        gatewayId: String(data.gatewayId),
        organizationId: String(organizationId),
        isActive: data.isActive !== undefined ? Boolean(data.isActive) : true,
        isDefault: data.isDefault !== undefined ? Boolean(data.isDefault) : false,
        priority: data.priority !== undefined ? Number(data.priority) : 10,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      console.log("📝 Create data prepared:", JSON.stringify(createData, null, 2));
      
      // ✅ CRITICAL: Validate all required fields are present
      if (!createData.id || !createData.gatewayId || !createData.organizationId) {
        console.error("❌ Missing required fields for creation:", createData);
        return NextResponse.json(
          { error: "Missing required fields for gateway assignment" },
          { status: 400 }
        );
      }
      
      // Use validation function before database operations
      const validation = validateCreateData(createData);
      if (!validation.isValid) {
        console.error("❌ Data validation failed:", validation.errors);
        return NextResponse.json(
          { 
            error: "Invalid data format",
            details: validation.errors.join(", ")
          },
          { status: 400 }
        );
      }
      
      try {
        console.log("🔄 About to call db.organization_gateway.create with data:", JSON.stringify(createData, null, 2));
        
        // Try without include first to isolate the issue
        const newOrgGateway = await db.organization_gateway.create({
          data: createData
        });
        
        console.log("✅ Basic assignment created, now fetching with gateway data...");
        
        // Fetch the complete data separately
        orgGateway = await db.organization_gateway.findUnique({
          where: { id: newOrgGateway.id },
          include: {
            payment_gateway: true,
          }
        });
        
        console.log("✅ New assignment created successfully with gateway data");
      } catch (createError) {
        console.error("❌ Error creating assignment:", createError);
        console.error("❌ Create error details:", {
          message: createError instanceof Error ? createError.message : "Unknown error",
          stack: createError instanceof Error ? createError.stack : "No stack",
          createData
        });
        
        // Check if it's a unique constraint violation
        if (createError instanceof Error && createError.message.includes('Unique constraint')) {
          console.log("🔄 Unique constraint violation, trying to find and update instead...");
          try {
            const existingRecord = await db.organization_gateway.findFirst({
              where: {
                organizationId,
                gatewayId: data.gatewayId
              }
            });
            
            if (existingRecord) {
              const updateData = {
                isActive: data.isActive !== undefined ? Boolean(data.isActive) : true,
                isDefault: data.isDefault !== undefined ? Boolean(data.isDefault) : false,
                priority: data.priority !== undefined ? Number(data.priority) : 10,
                updatedAt: new Date(),
              };
              
              console.log("🔄 About to call db.organization_gateway.update with data:", JSON.stringify(updateData, null, 2));
              
              orgGateway = await db.organization_gateway.update({
                where: {
                  id: existingRecord.id,
                },
                data: updateData,
                include: {
                  payment_gateway: true,
                }
              });
              console.log("✅ Updated existing record after constraint violation");
            } else {
              throw createError;
            }
          } catch (recoveryError) {
            console.error("❌ Recovery attempt failed:", recoveryError);
            return NextResponse.json(
              { 
                error: "Failed to create or update gateway assignment",
                details: recoveryError instanceof Error ? recoveryError.message : "Recovery failed"
              },
              { status: 500 }
            );
          }
        } else {
          return NextResponse.json(
            { 
              error: "Failed to create gateway assignment",
              details: createError instanceof Error ? createError.message : "Unknown create error"
            },
            { status: 500 }
          );
        }
      }
    }

    // ✅ CRITICAL: Validate orgGateway before using
    if (!orgGateway || !orgGateway.payment_gateway) {
      console.error("❌ Invalid orgGateway result:", orgGateway);
      return NextResponse.json(
        { error: "Failed to retrieve gateway assignment result" },
        { status: 500 }
      );
    }

    // ✅ FIX: Return structure expected by GatewayTab
    const response = {
      ...orgGateway.payment_gateway,
      isDefault: orgGateway.isDefault,
      isActive: orgGateway.isActive,
      priority: orgGateway.priority,
      relationId: orgGateway.id
    };

    console.log("✅ Returning successful response:", JSON.stringify(response, null, 2));
    console.log("=== Gateway Assignment Request Completed ===");
    
    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error("❌ Error handling gateway organization assignment:", error);
    console.error("❌ Error stack:", error instanceof Error ? error.stack : "No stack trace");
    console.error("❌ Error details:", {
      name: error instanceof Error ? error.name : "Unknown",
      message: error instanceof Error ? error.message : "Unknown error",
      cause: error instanceof Error ? error.cause : "No cause"
    });
    
    return NextResponse.json(
      {
        error: "Failed to process gateway assignment",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove gateway assignment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log("=== Gateway Removal Request Started ===");
    
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    // ✅ FIX: Await params before using
    const { id: organizationId } = await params;
    console.log("✅ Organization ID:", organizationId);

    // Parse request body
    const data = await request.json();
    console.log("📥 Delete request data:", JSON.stringify(data, null, 2));

    if (!data.gatewayId) {
      return NextResponse.json(
        { error: "Missing gatewayId" },
        { status: 400 }
      );
    }

    console.log("🗑️ Removing gateway assignment...");
    // Remove the assignment
    const deleteResult = await db.organization_gateway.deleteMany({
      where: {
        organizationId,
        gatewayId: data.gatewayId,
      },
    });

    console.log("✅ Delete result:", deleteResult);
    console.log("=== Gateway Removal Request Completed ===");

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("❌ Error removing gateway assignment:", error);
    return NextResponse.json(
      { 
        error: "Failed to remove gateway assignment",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
