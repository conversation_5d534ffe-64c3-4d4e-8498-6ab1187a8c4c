import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";

// Create the missing DELETE endpoint with correct parameter structure
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; gatewayId: string }> }
) {
  try {
    const { id: organizationId, gatewayId } = await params;
    
    // Remove gateway from organization
    await db.organization_gateway.deleteMany({
      where: {
        organizationId,
        gatewayId,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to remove gateway" },
      { status: 500 }
    );
  }
}