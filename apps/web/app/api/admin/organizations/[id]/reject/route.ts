import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    const { id: organizationId } = await params;
    const { reason } = await request.json();

    if (!reason) {
      return NextResponse.json(
        { error: "Rejection reason is required" },
        { status: 400 }
      );
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        organization_legal_info: true,
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Update organization status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        status: "REJECTED",
      },
    });

    // Update legal info if exists
    if (organization.organization_legal_info) {
      await db.organization_legal_info.update({
        where: { organizationId },
        data: {
          reviewedBy: session.user.id,
          reviewedAt: new Date(),
          reviewNotes: reason,
        },
      });
    }

    // Verificação de documentos removida temporariamente devido à estrutura do banco

    return NextResponse.json({
      success: true,
      organization: updatedOrganization,
    });
  } catch (error) {
    console.error("Error rejecting organization:", error);
    return NextResponse.json(
      { error: "Failed to reject organization" },
      { status: 500 }
    );
  }
}
