import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { z } from "zod";

const balanceAdjustmentSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  operation: z.enum(["CREDIT", "DEBIT"]),
  description: z.string().min(1, "Description is required"),
  reason: z.string().min(1, "Reason is required")
});

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: organizationId } = await params;
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const balance = await db.organization_balance.findUnique({
      where: { organizationId },
      include: {
        organization: {
          select: {
            name: true,
            status: true
          }
        }
      }
    });

    if (!balance) {
      return NextResponse.json({ error: "Balance not found" }, { status: 404 });
    }

    const balanceHistory = await db.balance_history.findMany({
      where: { organizationId },
      orderBy: { createdAt: "desc" },
      take: 10
    });

    return NextResponse.json({
      balance: {
        available: Number(balance.availableBalance),
        pending: Number(balance.pendingBalance),
        reserved: Number(balance.reservedBalance),
        updatedAt: balance.updatedAt
      },
      organization: balance.organization,
      recentHistory: balanceHistory.map(h => ({
        id: h.id,
        operation: h.operation,
        amount: Number(h.amount),
        description: h.description,
        createdAt: h.createdAt
      }))
    });
  } catch (error) {
    logger.error("Error fetching organization balance", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: organizationId } = await params;
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = balanceAdjustmentSchema.parse(body);

    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { name: true, status: true }
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    // Amount is already in reais (not cents) as per the database schema
    const amount = validatedData.amount;
    const operation = validatedData.operation === "CREDIT" 
      ? BalanceOperationType.CREDIT 
      : BalanceOperationType.DEBIT;

    const description = `Manual ${validatedData.operation.toLowerCase()} by admin: ${validatedData.description} - Reason: ${validatedData.reason}`;

    const updatedBalance = await updateOrganizationBalance(
      organizationId,
      amount,
      operation,
      undefined,
      description
    );

    logger.info("Manual balance adjustment performed", {
      organizationId,
      organizationName: organization.name,
      adminUserId: session.user.id,
      operation: validatedData.operation,
      amount: validatedData.amount,
      reason: validatedData.reason,
      description: validatedData.description
    });

    return NextResponse.json({
      success: true,
      balance: {
        available: Number(updatedBalance.availableBalance),
        pending: Number(updatedBalance.pendingBalance),
        reserved: Number(updatedBalance.reservedBalance)
      },
      message: `Balance ${validatedData.operation.toLowerCase()}ed successfully`
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: "Validation error", 
        details: error.errors 
      }, { status: 400 });
    }

    logger.error("Error adjusting organization balance", { error });
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Internal server error" 
    }, { status: 500 });
  }
}