import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    const { id: organizationId } = await params;

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        organization_legal_info: true,
        member: {
          where: { role: "owner" },
          include: { user: true }
        },
      },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    // Update organization status
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        status: "APPROVED",
      },
    });

    // Update legal info if exists
    if (organization.organization_legal_info) {
      await db.organization_legal_info.update({
        where: { organizationId },
        data: {
          reviewedBy: session.user.id,
          reviewedAt: new Date(),
        },
      });
    }

    // Verificação de documentos removida temporariamente devido à estrutura do banco

    // Enviar e-mail para o proprietário da organização
    const owner = organization.members?.find(member => member.role === "owner");

    if (owner && owner.user) {
      try {
        const appUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
        const organizationUrl = `${appUrl}/app/${organization.slug}`;

        logger.info("Sending organization approval email", {
          organizationId,
          ownerEmail: owner.user.email,
          organizationName: organization.name
        });

        await sendEmail({
          to: owner.user.email,
          templateId: "organizationApproved",
          context: {
            organizationName: organization.name,
            organizationUrl: organizationUrl,
          },
        });

        logger.info("Organization approval email sent successfully");
      } catch (emailError) {
        logger.error("Failed to send organization approval email", {
          error: emailError,
          organizationId,
          ownerEmail: owner.user.email
        });
        // Não interromper o fluxo se o e-mail falhar
      }
    } else {
      logger.warn("No owner found for organization, approval email not sent", { organizationId });
    }

    return NextResponse.json({
      success: true,
      organization: updatedOrganization,
    });
  } catch (error) {
    logger.error("Error approving organization", { error, organizationId: (await params).id });
    return NextResponse.json(
      { error: "Failed to approve organization" },
      { status: 500 }
    );
  }
}
