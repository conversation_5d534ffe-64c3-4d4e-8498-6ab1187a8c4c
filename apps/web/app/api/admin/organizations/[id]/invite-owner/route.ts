import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { nanoid } from "nanoid";

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: organizationId } = await params;
    const { email, name, message } = await req.json();

    if (!email || !name) {
      return NextResponse.json(
        { error: "Email e nome são obrigatórios" },
        { status: 400 }
      );
    }

    // Check if organization exists
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      include: {
        member: {
          where: { role: "owner" },
          include: { user: true }
        }
      }
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organização não encontrada" },
        { status: 404 }
      );
    }

    // Check if organization already has an owner
    if (organization.member && organization.member.length > 0) {
      const existingOwners = organization.member.filter(member => member.role === "owner");

      if (existingOwners.length > 0) {
        return NextResponse.json(
          {
            error: "Esta organização já possui um proprietário",
            owners: existingOwners.map(owner => ({
              email: owner.user.email,
              name: owner.user.name
            }))
          },
          { status: 400 }
        );
      }
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email }
    });

    // Generate invite token
    const token = nanoid(32);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    // Create invite
    const invite = await db.organizationInvite.create({
      data: {
        email,
        name,
        role: "owner", // Always owner in this case
        token,
        message: message || "",
        expiresAt,
        organizationId,
        inviterId: session.user.id
      }
    });

    // If user already exists, link them to the invite
    if (existingUser) {
      await db.organizationInvite.update({
        where: { id: invite.id },
        data: { userId: existingUser.id }
      });
    }

    // TODO: Send email notification with invite link
    // This would normally involve a email service integration
    logger.info("Organization owner invite created", {
      inviteId: invite.id,
      organization: organization.name,
      email,
      existingUser: existingUser ? true : false
    });

    return NextResponse.json({
      success: true,
      invite: {
        id: invite.id,
        email: invite.email,
        token: invite.token,
        expiresAt: invite.expiresAt
      }
    });
  } catch (error) {
    logger.error("Error inviting organization owner", { error });
    return NextResponse.json(
      { error: "Falha ao processar o convite" },
      { status: 500 }
    );
  }
}
