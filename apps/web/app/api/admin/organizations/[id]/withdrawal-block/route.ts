import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema para validar o bloqueio de organização
const organizationBlockSchema = z.object({
  withdrawalBlocked: z.boolean(),
  withdrawalBlockedReason: z.string().optional(),
});

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem acessar estas informações." }, { status: 403 });
    }

    const { id: organizationId } = await params;

    // Buscar informações da organização
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        withdrawalBlocked: true,
        withdrawalBlockedReason: true,
        withdrawalBlockedAt: true,
        withdrawalBlockedBy: true
      }
    });

    if (!organization) {
      return NextResponse.json({ message: "Organização não encontrada" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        id: organization.id,
        name: organization.name,
        withdrawalBlocked: organization.withdrawalBlocked,
        withdrawalBlockedReason: organization.withdrawalBlockedReason,
        withdrawalBlockedAt: organization.withdrawalBlockedAt,
        withdrawalBlockedBy: organization.withdrawalBlockedBy
      }
    });

  } catch (error) {
    console.error("Erro ao buscar informações de bloqueio da organização:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem modificar estas configurações." }, { status: 403 });
    }

    const { id: organizationId } = await params;
    const data = await req.json();
    const validationResult = organizationBlockSchema.safeParse(data);

    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const { withdrawalBlocked, withdrawalBlockedReason } = validationResult.data;

    // Verificar se a organização existe
    const existingOrganization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true }
    });

    if (!existingOrganization) {
      return NextResponse.json({ message: "Organização não encontrada" }, { status: 404 });
    }

    // Atualizar configurações de bloqueio da organização
    const updatedOrganization = await db.organization.update({
      where: { id: organizationId },
      data: {
        withdrawalBlocked,
        withdrawalBlockedReason: withdrawalBlocked ? withdrawalBlockedReason : null,
        withdrawalBlockedAt: withdrawalBlocked ? new Date() : null,
        withdrawalBlockedBy: withdrawalBlocked ? session.user.id : null
      },
      select: {
        id: true,
        name: true,
        withdrawalBlocked: true,
        withdrawalBlockedReason: true,
        withdrawalBlockedAt: true,
        withdrawalBlockedBy: true
      }
    });

    logger.info("Configurações de bloqueio da organização atualizadas", {
      adminUserId: session.user.id,
      organizationId,
      organizationName: existingOrganization.name,
      withdrawalBlocked,
      withdrawalBlockedReason
    });

    return NextResponse.json({
      success: true,
      data: {
        id: updatedOrganization.id,
        name: updatedOrganization.name,
        withdrawalBlocked: updatedOrganization.withdrawalBlocked,
        withdrawalBlockedReason: updatedOrganization.withdrawalBlockedReason,
        withdrawalBlockedAt: updatedOrganization.withdrawalBlockedAt,
        withdrawalBlockedBy: updatedOrganization.withdrawalBlockedBy
      },
      message: withdrawalBlocked
        ? "Saques bloqueados para a organização com sucesso"
        : "Saques desbloqueados para a organização com sucesso"
    });

  } catch (error) {
    console.error("Erro ao atualizar configurações de bloqueio da organização:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}
