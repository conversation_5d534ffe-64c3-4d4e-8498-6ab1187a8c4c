import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  console.log("🔍 [DEBUG] GET /api/admin/system-settings/debug - INICIADO");
  
  try {
    console.log("🔗 [DEBUG] Conectando ao banco de dados...");
    
    // Buscar configurações reais do banco
    const systemSettings = await db.system_settings.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    console.log("📊 [DEBUG] Configurações encontradas no banco:", systemSettings ? "SIM" : "NÃO");
    console.log("📊 [DEBUG] Dados do banco:", systemSettings);

    const responseData = {
      success: true,
      data: {
        globalWithdrawalBlocked: systemSettings?.globalWithdrawalBlocked || false,
        globalWithdrawalMessage: systemSettings?.globalWithdrawalMessage || "Saques temporariamente indisponíveis",
        globalWithdrawalBlockedAt: systemSettings?.globalWithdrawalBlockedAt,
        globalWithdrawalBlockedBy: systemSettings?.globalWithdrawalBlockedBy,
        medAutoApprovalEnabled: systemSettings?.medAutoApprovalEnabled || false,
        medAutoApprovalMessage: systemSettings?.medAutoApprovalMessage || "Aprovação automática de MED ativada",
        medAutoApprovalEnabledAt: systemSettings?.medAutoApprovalEnabledAt,
        medAutoApprovalEnabledBy: systemSettings?.medAutoApprovalEnabledBy
      },
      message: "Configurações carregadas com sucesso (REAL)"
    };

    console.log("📤 [DEBUG] Retornando dados reais:", responseData);
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("❌ [DEBUG] Erro no GET:", error);
    
    // Se der erro no banco, retornar dados padrão
    const fallbackData = {
      success: true,
      data: {
        globalWithdrawalBlocked: false,
        globalWithdrawalMessage: "Saques temporariamente indisponíveis",
        globalWithdrawalBlockedAt: null,
        globalWithdrawalBlockedBy: null,
        medAutoApprovalEnabled: false,
        medAutoApprovalMessage: "Aprovação automática de MED ativada",
        medAutoApprovalEnabledAt: null,
        medAutoApprovalEnabledBy: null
      },
      message: "Configurações padrão (erro no banco)",
      error: error instanceof Error ? error.message : String(error)
    };

    console.log("📤 [DEBUG] Retornando dados padrão devido ao erro:", fallbackData);
    return NextResponse.json(fallbackData);
  }
}

export async function POST(request: NextRequest) {
  console.log("💾 [DEBUG] POST /api/admin/system-settings/debug - INICIADO");
  
  try {
    const body = await request.json();
    console.log("📦 [DEBUG] Dados recebidos:", body);

    console.log("🔗 [DEBUG] Conectando ao banco para salvar...");

    // Preparar dados para atualização
    const updateData: any = {
      updatedAt: new Date()
    };

    // Campos de saque global
    if (body.globalWithdrawalBlocked !== undefined) {
      updateData.globalWithdrawalBlocked = body.globalWithdrawalBlocked;
      updateData.globalWithdrawalMessage = body.globalWithdrawalMessage || "Saques temporariamente indisponíveis";
      updateData.globalWithdrawalBlockedAt = body.globalWithdrawalBlocked ? new Date() : null;
      updateData.globalWithdrawalBlockedBy = body.globalWithdrawalBlocked ? "debug-user" : null;
    }

    // Campos de MED
    if (body.medAutoApprovalEnabled !== undefined) {
      updateData.medAutoApprovalEnabled = body.medAutoApprovalEnabled;
      updateData.medAutoApprovalMessage = body.medAutoApprovalMessage || "Aprovação automática de MED ativada";
      updateData.medAutoApprovalEnabledAt = body.medAutoApprovalEnabled ? new Date() : null;
      updateData.medAutoApprovalEnabledBy = body.medAutoApprovalEnabled ? "debug-user" : null;
    }

    console.log("🔄 [DEBUG] Dados para atualização:", updateData);

    // Fazer upsert no banco
    const systemSettings = await db.system_settings.upsert({
      where: { id: 'default_settings' },
      create: {
        id: 'default_settings',
        globalWithdrawalBlocked: body.globalWithdrawalBlocked ?? false,
        globalWithdrawalMessage: body.globalWithdrawalMessage || "Saques temporariamente indisponíveis",
        globalWithdrawalBlockedAt: body.globalWithdrawalBlocked ? new Date() : null,
        globalWithdrawalBlockedBy: body.globalWithdrawalBlocked ? "debug-user" : null,
        medAutoApprovalEnabled: body.medAutoApprovalEnabled ?? false,
        medAutoApprovalMessage: body.medAutoApprovalMessage || "Aprovação automática de MED ativada",
        medAutoApprovalEnabledAt: body.medAutoApprovalEnabled ? new Date() : null,
        medAutoApprovalEnabledBy: body.medAutoApprovalEnabled ? "debug-user" : null,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      update: updateData
    });

    console.log("✅ [DEBUG] Configurações salvas no banco:", systemSettings);

    const responseData = {
      success: true,
      data: {
        globalWithdrawalBlocked: systemSettings.globalWithdrawalBlocked,
        globalWithdrawalMessage: systemSettings.globalWithdrawalMessage,
        globalWithdrawalBlockedAt: systemSettings.globalWithdrawalBlockedAt,
        globalWithdrawalBlockedBy: systemSettings.globalWithdrawalBlockedBy,
        medAutoApprovalEnabled: systemSettings.medAutoApprovalEnabled,
        medAutoApprovalMessage: systemSettings.medAutoApprovalMessage,
        medAutoApprovalEnabledAt: systemSettings.medAutoApprovalEnabledAt,
        medAutoApprovalEnabledBy: systemSettings.medAutoApprovalEnabledBy
      },
      message: "Configurações atualizadas com sucesso (REAL)"
    };

    console.log("📤 [DEBUG] Retornando resposta real:", responseData);
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("❌ [DEBUG] Erro no POST:", error);
    
    return NextResponse.json({
      success: false,
      message: "Erro ao salvar configurações",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
