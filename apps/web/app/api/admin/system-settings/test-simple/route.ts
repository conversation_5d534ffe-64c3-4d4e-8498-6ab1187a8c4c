import { NextResponse } from "next/server";

export async function GET() {
  console.log("🧪 [TEST] Rota de teste simples acessada");
  
  return NextResponse.json({
    success: true,
    message: "Teste simples funcionando",
    timestamp: new Date().toISOString()
  });
}

export async function POST() {
  console.log("🧪 [TEST] POST de teste simples");
  
  return NextResponse.json({
    success: true,
    message: "POST de teste funcionando",
    timestamp: new Date().toISOString()
  });
}