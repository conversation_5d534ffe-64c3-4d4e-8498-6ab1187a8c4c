import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema para validar as configurações do sistema
const systemSettingsSchema = z.object({
  globalWithdrawalBlocked: z.boolean(),
  globalWithdrawalMessage: z.string().optional(),
});

export async function GET() {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem acessar estas configurações." }, { status: 403 });
    }

    // Buscar configurações do sistema
    // Temporariamente retornar valores padrão até que a migração seja aplicada
    return NextResponse.json({
      success: true,
      data: {
        globalWithdrawalBlocked: false,
        globalWithdrawalMessage: "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte."
      }
    });

  } catch (error) {
    console.error("Erro ao buscar configurações do sistema:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem modificar estas configurações." }, { status: 403 });
    }

    const data = await req.json();
    const validationResult = systemSettingsSchema.safeParse(data);

    if (!validationResult.success) {
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    const { globalWithdrawalBlocked, globalWithdrawalMessage } = validationResult.data;

    // Temporariamente simular a atualização até que a migração seja aplicada
    console.log("Configurações do sistema atualizadas (simulado):", {
      userId: session.user.id,
      globalWithdrawalBlocked,
      globalWithdrawalMessage
    });

    return NextResponse.json({
      success: true,
      data: {
        globalWithdrawalBlocked,
        globalWithdrawalMessage: globalWithdrawalMessage || "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte.",
        globalWithdrawalBlockedAt: globalWithdrawalBlocked ? new Date() : null,
        globalWithdrawalBlockedBy: globalWithdrawalBlocked ? session.user.id : null
      },
      message: "Configurações atualizadas com sucesso"
    });

  } catch (error) {
    console.error("Erro ao atualizar configurações do sistema:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}
