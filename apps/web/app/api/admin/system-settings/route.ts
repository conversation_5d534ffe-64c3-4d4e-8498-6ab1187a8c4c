import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { z } from "zod";

// Schema para validar as configurações do sistema
const systemSettingsSchema = z.object({
  globalWithdrawalBlocked: z.boolean().optional(),
  globalWithdrawalMessage: z.string().optional(),
  medAutoApprovalEnabled: z.boolean().optional(),
  medAutoApprovalMessage: z.string().optional(),
});

export async function GET() {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem acessar estas configurações." }, { status: 403 });
    }

    // Buscar configurações do sistema
    const systemSettings = await db.system_settings.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    // Se não existir configuração, retornar valores padrão
    if (!systemSettings) {
      return NextResponse.json({
        success: true,
        data: {
          globalWithdrawalBlocked: false,
          globalWithdrawalMessage: "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte.",
          globalWithdrawalBlockedAt: null,
          globalWithdrawalBlockedBy: null,
          medAutoApprovalEnabled: false,
          medAutoApprovalMessage: "Aprovação automática de MED ativada",
          medAutoApprovalEnabledAt: null,
          medAutoApprovalEnabledBy: null
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        globalWithdrawalBlocked: systemSettings.globalWithdrawalBlocked,
        globalWithdrawalMessage: systemSettings.globalWithdrawalMessage || "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte.",
        globalWithdrawalBlockedAt: systemSettings.globalWithdrawalBlockedAt,
        globalWithdrawalBlockedBy: systemSettings.globalWithdrawalBlockedBy,
        // @ts-ignore - Campos serão adicionados após migração
        medAutoApprovalEnabled: (systemSettings as any).medAutoApprovalEnabled || false,
        medAutoApprovalMessage: (systemSettings as any).medAutoApprovalMessage || "Aprovação automática de MED ativada",
        medAutoApprovalEnabledAt: (systemSettings as any).medAutoApprovalEnabledAt,
        medAutoApprovalEnabledBy: (systemSettings as any).medAutoApprovalEnabledBy
      }
    });

  } catch (error) {
    console.error("Erro ao buscar configurações do sistema:", error);
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    console.log("🔍 POST /api/admin/system-settings - Iniciando...");

    const headersList = await headers();
    console.log("📋 Headers recebidos:", Object.fromEntries(headersList.entries()));

    const session = await auth.api.getSession({
      headers: headersList,
    });

    console.log("👤 Sessão:", session ? { userId: session.user?.id, role: session.user?.role } : "Nenhuma sessão");

    if (!session?.user?.id) {
      console.log("❌ Usuário não autenticado");
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      console.log("❌ Usuário não é admin:", session.user.role);
      return NextResponse.json({ message: "Acesso negado. Apenas administradores podem modificar estas configurações." }, { status: 403 });
    }

    console.log("✅ Usuário autenticado e autorizado");

    const data = await req.json();
    console.log("📦 Dados recebidos:", data);

    const validationResult = systemSettingsSchema.safeParse(data);

    if (!validationResult.success) {
      console.log("❌ Validação falhou:", validationResult.error.format());
      return NextResponse.json({
        message: "Dados inválidos",
        errors: validationResult.error.format()
      }, { status: 400 });
    }

    console.log("✅ Dados validados com sucesso");

    const { globalWithdrawalBlocked, globalWithdrawalMessage, medAutoApprovalEnabled, medAutoApprovalMessage } = validationResult.data;

    console.log("🔧 Dados extraídos:", { globalWithdrawalBlocked, globalWithdrawalMessage, medAutoApprovalEnabled, medAutoApprovalMessage });

    // Buscar configurações atuais para preservar valores não enviados
    console.log("🔍 Buscando configurações atuais...");
    const currentSettings = await db.system_settings.findFirst({
      where: { id: 'default_settings' }
    });

    console.log("📊 Configurações atuais:", currentSettings);

    // Preparar dados para atualização
    const updateData: any = {
      updatedAt: new Date()
    };

    // Adicionar campos de withdrawal se fornecidos
    if (globalWithdrawalBlocked !== undefined) {
      console.log("🔧 Adicionando campos de withdrawal");
      updateData.globalWithdrawalBlocked = globalWithdrawalBlocked;
      updateData.globalWithdrawalMessage = globalWithdrawalMessage || "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte.";
      updateData.globalWithdrawalBlockedAt = globalWithdrawalBlocked ? new Date() : null;
      updateData.globalWithdrawalBlockedBy = globalWithdrawalBlocked ? session.user.id : null;
    }

    // Adicionar campos de MED se fornecidos
    if (medAutoApprovalEnabled !== undefined) {
      console.log("🔧 Adicionando campos de MED");
      // @ts-ignore - Campos serão adicionados após migração
      updateData.medAutoApprovalEnabled = medAutoApprovalEnabled;
      // @ts-ignore - Campos serão adicionados após migração
      updateData.medAutoApprovalMessage = medAutoApprovalMessage || "Aprovação automática de MED ativada";
      // @ts-ignore - Campos serão adicionados após migração
      updateData.medAutoApprovalEnabledAt = medAutoApprovalEnabled ? new Date() : null;
      // @ts-ignore - Campos serão adicionados após migração
      updateData.medAutoApprovalEnabledBy = medAutoApprovalEnabled ? session.user.id : null;
    }

    console.log("📦 Dados para atualização:", updateData);

        // Atualizar ou criar configurações do sistema
    console.log("💾 Executando upsert no banco de dados...");

    let systemSettings;
    try {
      systemSettings = await db.system_settings.upsert({
        where: { id: 'default_settings' },
        create: {
          id: 'default_settings',
          globalWithdrawalBlocked: globalWithdrawalBlocked ?? false,
          globalWithdrawalMessage: globalWithdrawalMessage || "Saques temporariamente indisponíveis por manutenção. Em caso de dúvidas, entre em contato com o suporte.",
          globalWithdrawalBlockedAt: globalWithdrawalBlocked ? new Date() : null,
          globalWithdrawalBlockedBy: globalWithdrawalBlocked ? session.user.id : null,
          // @ts-ignore - Campos serão adicionados após migração
          medAutoApprovalEnabled: medAutoApprovalEnabled ?? false,
          // @ts-ignore - Campos serão adicionados após migração
          medAutoApprovalMessage: medAutoApprovalMessage || "Aprovação automática de MED ativada",
          // @ts-ignore - Campos serão adicionados após migração
          medAutoApprovalEnabledAt: medAutoApprovalEnabled ? new Date() : null,
          // @ts-ignore - Campos serão adicionados após migração
          medAutoApprovalEnabledBy: medAutoApprovalEnabled ? session.user.id : null,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        update: updateData
      });

      console.log("✅ Upsert executado com sucesso:", systemSettings);
    } catch (dbError) {
      console.error("❌ Erro no banco de dados:", dbError);
      throw dbError;
    }

    console.log("📝 Logging da operação...");
    logger.info("Configurações do sistema atualizadas", {
      userId: session.user.id,
      globalWithdrawalBlocked,
      globalWithdrawalMessage,
      medAutoApprovalEnabled,
      medAutoApprovalMessage
    });

    console.log("📤 Preparando resposta...");
    const responseData = {
      success: true,
      data: {
        globalWithdrawalBlocked: systemSettings.globalWithdrawalBlocked,
        globalWithdrawalMessage: systemSettings.globalWithdrawalMessage,
        globalWithdrawalBlockedAt: systemSettings.globalWithdrawalBlockedAt,
        globalWithdrawalBlockedBy: systemSettings.globalWithdrawalBlockedBy,
        // @ts-ignore - Campos serão adicionados após migração
        medAutoApprovalEnabled: (systemSettings as any).medAutoApprovalEnabled,
        // @ts-ignore - Campos serão adicionados após migração
        medAutoApprovalMessage: (systemSettings as any).medAutoApprovalMessage,
        // @ts-ignore - Campos serão adicionados após migração
        medAutoApprovalEnabledAt: (systemSettings as any).medAutoApprovalEnabledAt,
        // @ts-ignore - Campos serão adicionados após migração
        medAutoApprovalEnabledBy: (systemSettings as any).medAutoApprovalEnabledBy
      },
      message: "Configurações atualizadas com sucesso"
    };

    console.log("📤 Resposta final:", responseData);
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("❌ Erro ao atualizar configurações do sistema:", error);
    console.error("❌ Stack trace:", error instanceof Error ? error.stack : "Sem stack trace");

    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Erro interno do servidor"
    }, { status: 500 });
  }
}
