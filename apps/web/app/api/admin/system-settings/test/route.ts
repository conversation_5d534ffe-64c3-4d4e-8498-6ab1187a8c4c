import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json({
    success: true,
    message: "API de teste funcionando",
    timestamp: new Date().toISOString()
  });
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    return NextResponse.json({
      success: true,
      message: "POST de teste funcionando",
      receivedData: data,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Erro no POST de teste",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
