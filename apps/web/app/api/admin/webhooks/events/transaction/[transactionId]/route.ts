import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth/auth";
import { getWebhookEvents } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(
  request: NextRequest,
  { params }: { params: { transactionId: string } }
) {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({ headers: request.headers });

    // Debug: Log full session info
    logger.info("Session debug info", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id,
      userRole: session?.user?.role,
      sessionKeys: session ? Object.keys(session) : [],
      userKeys: session?.user ? Object.keys(session.user) : []
    });

    if (!session?.user?.id) {
      logger.warn("No session or user ID found");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Debug: Log session info
    logger.info("Admin webhook events request", {
      userId: session.user.id,
      userRole: session.user.role,
      isAdmin: session.user.role === "admin"
    });

    // Verificar se o usuário é admin
    const isAdmin = session.user.role === "admin";
    if (!isAdmin) {
      logger.warn("Access denied - user is not admin", {
        userId: session.user.id,
        userRole: session.user.role
      });
      return NextResponse.json({ error: "Acesso restrito a administradores" }, { status: 403 });
    }

    const transactionId = params.transactionId;
    if (!transactionId) {
      return NextResponse.json({ error: "ID da transação não fornecido" }, { status: 400 });
    }

    // Obter o organizationId da query string
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json({ error: "ID da organização não fornecido" }, { status: 400 });
    }

    logger.info("Admin fetching webhook events for transaction", {
      adminId: session.user.id,
      organizationId,
      transactionId,
      url: request.url
    });

    try {
      // Tentar usar o serviço existente para buscar eventos relacionados à transação
      logger.info("Calling getWebhookEvents service", { organizationId, transactionId });

      const events = await getWebhookEvents(organizationId, {
        transactionId: transactionId,
        limit: 100
      });

      logger.info("getWebhookEvents service response", {
        eventsCount: events.messages?.length || 0,
        hasMessages: !!events.messages
      });

      return NextResponse.json({
        data: events.messages
      });
    } catch (svixError) {
      logger.warn("SVIX service failed, returning mock data", {
        error: svixError instanceof Error ? svixError.message : String(svixError),
        transactionId,
        organizationId,
        stack: svixError instanceof Error ? svixError.stack : undefined
      });

      // Retornar dados mock para demonstração quando o SVIX não estiver configurado
      const mockEvents = [
        {
          id: `msg_${Date.now()}_1`,
          eventType: "Transaction.Created",
          timestamp: new Date().toISOString(),
          payload: {
            data: {
              id: transactionId,
              status: "PENDING",
              amount: 100.00,
              customerName: "Cliente Exemplo",
              customerEmail: "<EMAIL>"
            }
          }
        },
        {
          id: `msg_${Date.now()}_2`,
          eventType: "Transaction.Updated",
          timestamp: new Date(Date.now() - 60000).toISOString(),
          payload: {
            data: {
              id: transactionId,
              status: "APPROVED",
              amount: 100.00,
              customerName: "Cliente Exemplo",
              customerEmail: "<EMAIL>"
            }
          }
        }
      ];

      return NextResponse.json({
        data: mockEvents,
        mock: true // Flag para indicar que são dados mock
      });
    }
  } catch (error) {
    logger.error("Erro ao buscar eventos de webhook para transação (admin)", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: params.transactionId
    });
    return NextResponse.json(
      { error: "Erro ao buscar eventos de webhook" },
      { status: 500 }
    );
  }
}
