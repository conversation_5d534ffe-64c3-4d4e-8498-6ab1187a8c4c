import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth/auth";
import { resendWebhook } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function POST(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({ headers: request.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    const isAdmin = session.user.role === "admin";
    if (!isAdmin) {
      return NextResponse.json({ error: "Acesso restrito a administradores" }, { status: 403 });
    }

    const messageId = params.messageId;
    if (!messageId) {
      return NextResponse.json({ error: "ID da mensagem não fornecido" }, { status: 400 });
    }

    // Obter os dados do corpo da requisição
    const body = await request.json();
    const { endpointId } = body;

    if (!endpointId) {
      return NextResponse.json({ error: "ID do endpoint não fornecido" }, { status: 400 });
    }

    logger.info("Admin resending webhook", {
      adminId: session.user.id,
      messageId,
      endpointId
    });

    await resendWebhook(messageId, endpointId);

    return NextResponse.json({
      success: true,
      message: "Webhook reenviado com sucesso"
    });
  } catch (error) {
    // Usar a variável messageId já extraída anteriormente
    const msgId = params.messageId || "unknown";

    logger.error("Erro ao reenviar webhook (admin)", {
      error: error instanceof Error ? error.message : String(error),
      messageId: msgId
    });

    return NextResponse.json(
      { error: "Erro ao reenviar webhook" },
      { status: 500 }
    );
  }
}
