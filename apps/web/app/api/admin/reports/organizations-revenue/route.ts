import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType } from "@prisma/client";
import { safeMonetaryConversion } from "@shared/lib/currency";

export async function GET(req: NextRequest) {
  try {
    // Verify if user is admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    try {
      // Get period parameter from query string
      const { searchParams } = new URL(req.url);
      const period = searchParams.get("period") || "30"; // Default to 30 days
      const limit = parseInt(searchParams.get("limit") || "10", 10); // Default to top 10

      // Calculate date ranges
      const now = new Date();
      const periods = {
        "1": 1, // Today
        "7": 7, // 7 days
        "15": 15, // 15 days
        "30": 30 // 30 days
      };

      const days = periods[period as keyof typeof periods] || 30;
      const periodStart = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));



      // Get organizations with highest revenue in the period
      const organizationsRevenue = await db.transaction.groupBy({
        by: ['organizationId'],
        where: {
          status: TransactionStatus.APPROVED,
          type: { in: [TransactionType.CHARGE, TransactionType.RECEIVE] },
          paymentAt: {
            gte: periodStart
          }
        },
        _sum: {
          amount: true,
          totalFee: true
        },
        _count: true,
        orderBy: {
          _sum: {
            amount: 'desc'
          }
        },
        take: limit
      });

      // Get organization details for the top performers
      const organizationIds = organizationsRevenue.map(org => org.organizationId);

      const organizations = await db.organization.findMany({
        where: {
          id: {
            in: organizationIds
          }
        },
        select: {
          id: true,
          name: true,
          slug: true,
          status: true,
          createdAt: true
        }
      });

      // Create a map for quick lookup
      const orgMap = new Map(organizations.map(org => [org.id, org]));

      // Format the response data
      const formattedData = organizationsRevenue.map(orgRevenue => {
        const organization = orgMap.get(orgRevenue.organizationId);
        const totalAmount = safeMonetaryConversion(orgRevenue._sum.amount || 0, 'transaction_amount');
        const totalFees = safeMonetaryConversion(orgRevenue._sum.totalFee || 0, 'fee_amount');
        const transactionCount = orgRevenue._count || 0;
        const averageTransaction = transactionCount > 0 ? totalAmount / transactionCount : 0;



        return {
          organizationId: orgRevenue.organizationId,
          organizationName: organization?.name || 'Organização Desconhecida',
          organizationSlug: organization?.slug,
          organizationStatus: organization?.status,
          organizationCreatedAt: organization?.createdAt,
          totalRevenue: totalAmount,
          totalFees: totalFees,
          transactionCount: transactionCount,
          averageTransaction: averageTransaction,
          netRevenue: totalAmount - totalFees
        };
      });

      // Get summary statistics
      const totalRevenue = formattedData.reduce((sum, org) => sum + org.totalRevenue, 0);
      const totalFees = formattedData.reduce((sum, org) => sum + org.totalFees, 0);
      const totalTransactions = formattedData.reduce((sum, org) => sum + org.transactionCount, 0);

      const summary = {
        totalRevenue,
        totalFees,
        totalTransactions,
        averageRevenuePerOrg: formattedData.length > 0 ? totalRevenue / formattedData.length : 0,
        averageTransactionsPerOrg: formattedData.length > 0 ? totalTransactions / formattedData.length : 0,
        period: days,
        periodLabel: days === 1 ? 'Hoje' : `${days} dias`
      };

      return NextResponse.json({
        organizations: formattedData,
        summary
      });

    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Database Error" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error fetching organizations revenue report:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
