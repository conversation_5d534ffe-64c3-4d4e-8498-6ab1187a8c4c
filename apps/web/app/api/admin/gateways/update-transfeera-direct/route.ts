import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";

/**
 * This endpoint updates the Transfeera gateway to enable receiving PIX payments
 * It doesn't require authentication for direct access during development
 */
export async function GET(request: NextRequest) {
  try {
    // Find the Transfeera gateway
    const transfeera = await db.payment_gateway.findFirst({
      where: {
        type: "TRANSFEERA",
      },
    });

    if (!transfeera) {
      return NextResponse.json(
        { error: "Transfeera gateway not found" },
        { status: 404 }
      );
    }

    logger.info(`Found Transfeera gateway: ${transfeera.id}`, {
      canReceive: transfeera.canReceive,
      canSend: transfeera.canSend,
      isActive: transfeera.isActive,
      isDefault: transfeera.isDefault,
      priority: transfeera.priority,
    });

    // Update the Transfeera gateway to enable receiving PIX payments
    const updatedGateway = await db.payment_gateway.update({
      where: { id: transfeera.id },
      data: {
        canReceive: true,
        isActive: true,
        isDefault: true,
        priority: 1,
      },
    });

    logger.info("Transfeera gateway updated to enable receiving PIX payments", {
      gatewayId: updatedGateway.id,
      canReceive: updatedGateway.canReceive,
      isActive: updatedGateway.isActive,
      isDefault: updatedGateway.isDefault,
      priority: updatedGateway.priority,
    });

    // Update all organization associations with Transfeera to enable receiving
    const organizationGateways = await db.organization_gateway.findMany({
      where: {
        gatewayId: transfeera.id,
      },
      include: {
        organization: {
          select: {
            name: true,
          },
        },
      },
    });

    logger.info(`Found ${organizationGateways.length} organization-specific Transfeera configurations`);

    // Update each organization-specific configuration
    for (const orgGateway of organizationGateways) {
      await db.organization_gateway.update({
        where: {
          id: orgGateway.id,
        },
        data: {
          isDefault: true, // Make it the default gateway
          isActive: true,  // Ensure it's active
          priority: 1,     // Set high priority
        },
      });

      logger.info(`Updated organization-specific Transfeera configuration`, {
        organizationId: orgGateway.organizationId,
        organizationName: orgGateway.organization.name,
        gatewayId: orgGateway.gatewayId,
        isDefault: true,
        isActive: true,
        priority: 1,
      });
    }

    return NextResponse.json({
      success: true,
      gateway: {
        id: updatedGateway.id,
        name: updatedGateway.name,
        type: updatedGateway.type,
        canReceive: updatedGateway.canReceive,
        canSend: updatedGateway.canSend,
        isActive: updatedGateway.isActive,
        isDefault: updatedGateway.isDefault,
        priority: updatedGateway.priority,
      },
      organizationsUpdated: organizationGateways.length,
    });
  } catch (error) {
    logger.error("Error updating Transfeera gateway:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal error updating Transfeera gateway" },
      { status: 500 }
    );
  }
}
