import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json({ message: "Acesso negado" }, { status: 403 });
    }

    // Buscar o gateway Transfeera
    const transfeera = await db.payment_gateway.findFirst({
      where: {
        type: "TRANSFEERA",
      },
    });

    if (!transfeera) {
      return NextResponse.json({
        success: false,
        message: "Gateway Transfeera não encontrado",
      }, { status: 404 });
    }

    // Atualizar o gateway Transfeera com a taxa fixa de 1.5
    const updated = await db.payment_gateway.update({
      where: { id: transfeera.id },
      data: {
        pixTransferFixedFee: 1.5 // Taxa fixa de 1.5 BRL para transferências
      }
    });

    logger.info("Gateway Transfeera atualizado com taxa fixa", {
      id: updated.id,
      pixTransferFixedFee: updated.pixTransferFixedFee
    });

    return NextResponse.json({
      success: true,
      gateway: {
        id: updated.id,
        name: updated.name,
        type: updated.type,
        pixTransferFixedFee: updated.pixTransferFixedFee
      }
    });
  } catch (error: any) {
    logger.error("Erro ao configurar taxa fixa para o gateway Transfeera:", { error });
    return NextResponse.json({
      success: false,
      message: error.message || "Erro ao configurar taxa fixa para o gateway Transfeera"
    }, { status: 500 });
  }
}
