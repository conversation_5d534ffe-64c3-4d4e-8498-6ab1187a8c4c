import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror, z } from "zod";

// Schema for validating gateway updates
const gatewayUpdateSchema = z.object({
  name: z.string().min(1).optional(),
  type: z.string().min(1).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  priority: z.number().int().min(1).optional(),
  credentials: z.record(z.any()).optional(),
  canReceive: z.boolean().optional(),
  canSend: z.boolean().optional(),
  webhooksEnabled: z.boolean().optional(),
  pixChargePercentFee: z.number().min(0).optional(),
  pixTransferPercentFee: z.number().min(0).optional(),
  pixChargeFixedFee: z.number().min(0).optional(),
  pixTransferFixedFee: z.number().min(0).optional(),
});

// Schema para validação dos dados recebidos
const gatewaySchema = z.object({
  type: z.string().optional(),
  displayName: z.string().min(1).optional(),
  description: z.string().optional(),
  instanceNumber: z.number().int().min(1).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  priority: z.coerce.number().int().min(0).optional(),
  credentials: z.record(z.any()).optional(),
  canReceive: z.boolean().optional(),
  canSend: z.boolean().optional(),
  webhooksEnabled: z.boolean().optional(),
  pixChargePercentFee: z.coerce.number().min(0).max(100).optional(),
  pixTransferPercentFee: z.coerce.number().min(0).max(100).optional(),
  pixChargeFixedFee: z.coerce.number().min(0).optional(),
  pixTransferFixedFee: z.coerce.number().min(0).optional(),
});

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    // Check if user is admin
    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Get the gateway by ID
    const gateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!gateway) {
      return NextResponse.json(
        { error: "Gateway not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(gateway);
  } catch (error) {
    console.error("Error fetching gateway:", error instanceof Error ? error.message : "Unknown error");
    return NextResponse.json(
      { error: "Failed to fetch gateway" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Verificar se o gateway existe
    const existingGateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!existingGateway) {
      return NextResponse.json(
        { error: "Gateway não encontrado" },
        { status: 404 }
      );
    }

    // Receber e validar os dados do gateway
    const body = await request.json();
    const validationResult = gatewaySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Dados inválidos", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const gatewayData = validationResult.data;

    // Se isDefault for true, desmarcar outros gateways como padrão
    if (gatewayData.isDefault) {
      await db.payment_gateway.updateMany({
        where: {
          id: { not: id },
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Atualizar o gateway
    const updatedGateway = await db.payment_gateway.update({
      where: { id },
      data: {
        displayName: gatewayData.displayName,
        description: gatewayData.description,
        instanceNumber: gatewayData.instanceNumber,
        isActive: gatewayData.isActive,
        isDefault: gatewayData.isDefault,
        priority: gatewayData.priority,
        credentials: gatewayData.credentials,
        canReceive: gatewayData.canReceive,
        canSend: gatewayData.canSend,
        webhooksEnabled: gatewayData.webhooksEnabled,
        pixChargePercentFee: gatewayData.pixChargePercentFee,
        pixTransferPercentFee: gatewayData.pixTransferPercentFee,
        pixChargeFixedFee: gatewayData.pixChargeFixedFee,
        pixTransferFixedFee: gatewayData.pixTransferFixedFee,
      },
    });


    return NextResponse.json(updatedGateway);
  } catch (error) {
    console.error("Error updating gateway:", error instanceof Error ? error.message : "Unknown error");
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Verificar se o gateway existe
    const existingGateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!existingGateway) {
      return NextResponse.json(
        { error: "Gateway não encontrado" },
        { status: 404 }
      );
    }

    // Não permitir exclusão de gateways padrão
    if (existingGateway.isDefault) {
      return NextResponse.json(
        { error: "Não é possível excluir um gateway definido como padrão" },
        { status: 400 }
      );
    }

    // Excluir o gateway
    await db.payment_gateway.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting gateway:", error instanceof Error ? error.message : "Unknown error");
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
