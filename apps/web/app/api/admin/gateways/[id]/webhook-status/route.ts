import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { invalidateWebhookCache } from "@repo/payments/provider/microcash";

// Schema para validação do status de webhook
const webhookStatusSchema = z.object({
  webhooksEnabled: z.boolean(),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Verificar se o gateway existe
    const existingGateway = await db.payment_gateway.findUnique({
      where: { id },
      select: {
        id: true,
        type: true,
        name: true,
        webhooksEnabled: true,
      },
    });

    if (!existingGateway) {
      return NextResponse.json(
        { error: "Gateway não encontrado" },
        { status: 404 }
      );
    }

    // Receber e validar os dados
    const body = await request.json();
    console.log("🔄 PATCH webhook-status - Dados recebidos:", {
      gatewayId: id,
      gatewayType: existingGateway.type,
      currentStatus: existingGateway.webhooksEnabled,
      newStatus: body.webhooksEnabled
    });

    const validationResult = webhookStatusSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Dados inválidos", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { webhooksEnabled } = validationResult.data;

    // Atualizar apenas o status de webhook
    const updatedGateway = await db.payment_gateway.update({
      where: { id },
      data: {
        webhooksEnabled: webhooksEnabled,
      },
    });

    console.log("✅ Webhook status atualizado:", {
      gatewayId: id,
      gatewayType: existingGateway.type,
      oldStatus: existingGateway.webhooksEnabled,
      newStatus: webhooksEnabled
    });

    // Invalidate webhook cache para este gateway
    if (existingGateway.type.toLowerCase() === 'microcash') {
      invalidateWebhookCache(undefined, 'MICROCASH');
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedGateway.id,
        type: updatedGateway.type,
        name: updatedGateway.name,
        webhooksEnabled: (updatedGateway as any).webhooksEnabled,
      },
    });

  } catch (error) {
    console.error("Error updating webhook status:", error instanceof Error ? error.message : "Unknown error");
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Buscar apenas o status de webhook do gateway
    const gateway = await db.payment_gateway.findUnique({
      where: { id },
      select: {
        id: true,
        type: true,
        name: true,
        webhooksEnabled: true,
      },
    });

    if (!gateway) {
      return NextResponse.json(
        { error: "Gateway não encontrado" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: gateway.id,
        type: gateway.type,
        name: gateway.name,
        webhooksEnabled: (gateway as any).webhooksEnabled,
      },
    });

  } catch (error) {
    console.error("Error fetching webhook status:", error instanceof Error ? error.message : "Unknown error");
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
