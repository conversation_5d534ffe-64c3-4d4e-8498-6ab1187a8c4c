import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType } from "@prisma/client";
import { safeMonetaryConversion } from "@shared/lib/currency";

export async function GET(req: NextRequest) {
  try {
    // Verify if user is admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Get query params
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as TransactionStatus | null;
    const type = searchParams.get("type") as TransactionType | null;
    const searchId = searchParams.get("searchId");
    const searchClient = searchParams.get("searchClient");
    const searchOrganization = searchParams.get("searchOrganization");
    const searchEndToEnd = searchParams.get("searchEndToEnd");
    const searchReferenceCode = searchParams.get("searchReferenceCode");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Build filter conditions
    const whereConditions: any = {};

    if (status) {
      whereConditions.status = status;
    }

    if (type) {
      whereConditions.type = type;
    }

    if (searchId) {
      whereConditions.OR = [
        { id: { contains: searchId, mode: 'insensitive' } },
        { referenceCode: { contains: searchId, mode: 'insensitive' } },
        { externalId: { contains: searchId, mode: 'insensitive' } }
      ];
    }

    if (searchClient) {
      whereConditions.OR = [
        ...(whereConditions.OR || []),
        { customerName: { contains: searchClient, mode: 'insensitive' } },
        { customerEmail: { contains: searchClient, mode: 'insensitive' } }
      ];
    }

    if (searchOrganization) {
      // Join with organization to filter by organization name
      const orgsQuery = await db.organization.findMany({
        where: {
          OR: [
            { name: { contains: searchOrganization, mode: 'insensitive' } },
            { slug: { contains: searchOrganization, mode: 'insensitive' } }
          ]
        },
        select: { id: true }
      });

      const orgIds = orgsQuery.map(org => org.id);

      if (orgIds.length > 0) {
        whereConditions.organizationId = { in: orgIds };
      }
    }

    // Handle endToEndId search
    if (searchEndToEnd) {
      whereConditions.endToEndId = { contains: searchEndToEnd, mode: 'insensitive' };
    }

    // Handle reference code search
    if (searchReferenceCode) {
      whereConditions.referenceCode = { contains: searchReferenceCode, mode: 'insensitive' };
    }

    // Handle date range filtering
    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        // Add 23:59:59 to include the entire end date
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        whereConditions.createdAt.lte = endDateTime;
      }
    }

    try {
      // Get total count
      const totalCount = await db.transaction.count({
        where: whereConditions
      });

      // Get paginated transactions with organization data
      const transactions = await db.transaction.findMany({
        where: whereConditions,
        include: {
          organization: {
            select: {
              name: true,
              slug: true
            }
          },
          payment_gateway: {
            select: {
              name: true,
              type: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: (page - 1) * limit,
        take: limit
      });

      // Se houver transações reais, retorne-as
      if (transactions.length > 0) {
        // Format transactions for response
        const formattedTransactions = transactions.map(transaction => {
          // Debug: Log gateway information
          console.log("Processing transaction:", {
            id: transaction.id,
            gatewayId: transaction.gatewayId,
            payment_gateway: transaction.payment_gateway
          });

          // Tratamento seguro do metadata que pode ser um objeto JSON
          const metadata = transaction.metadata as Record<string, any> || {};

          // Get endToEndId from the dedicated column, with fallback to metadata for backward compatibility
          const endToEndId = transaction.endToEndId ||
                            metadata?.endToEndId ||
                            metadata?.pixEndToEndId ||
                            metadata?.transferDetails?.endToEndId ||
                            metadata?.paymentDetails?.endToEndId;

          // PRODUCTION-SAFE: Handle monetary values correctly based on actual database format
          // Database stores transaction amounts as Float in REAIS (confirmed by production data analysis)
          const amountInReais = safeMonetaryConversion(transaction.amount, 'transaction_amount');
          const fixedFeeInReais = safeMonetaryConversion(transaction.fixedFee, 'fee_amount');
          const totalFeeInReais = safeMonetaryConversion(transaction.totalFee, 'fee_amount');
          const netAmountInReais = transaction.netAmount ? safeMonetaryConversion(transaction.netAmount, 'transaction_amount') : null;

          // Legacy fee handling from metadata (also convert if needed)
          const metadataFee = typeof metadata === 'object' ? (metadata.fee || 0) : 0;
          const metadataPlatformFee = typeof metadata === 'object' ? (metadata.platformFee || 0) : 0;

          // Convert legacy fees (assuming they might be in centavos too)
          const legacyFeeInReais = metadataFee > 100 ? metadataFee / 100 : metadataFee;
          const legacyPlatformFeeInReais = metadataPlatformFee > 100 ? metadataPlatformFee / 100 : metadataPlatformFee;

          return {
            id: transaction.id,
            externalId: transaction.externalId,
            referenceCode: transaction.referenceCode,
            customerName: transaction.customerName,
            customerEmail: transaction.customerEmail,
            customerPhone: transaction.customerPhone,
            customerDocument: transaction.customerDocument,
            customerDocumentType: transaction.customerDocumentType,
            amount: amountInReais,
            status: transaction.status,
            type: transaction.type,
            createdAt: transaction.createdAt.toISOString(),
            paymentAt: transaction.paymentAt ? transaction.paymentAt.toISOString() : null,
            organizationId: transaction.organizationId,
            organizationName: transaction.organization.name,
            organizationSlug: transaction.organization.slug || '',
            description: transaction.description,
            gateway: transaction.payment_gateway ? {
              name: transaction.payment_gateway.name,
              type: transaction.payment_gateway.type
            } : null,
            // Include PIX specific identifiers
            endToEndId: endToEndId,
            pixEndToEndId: endToEndId,
            // Fee information in reais
            fixedFee: fixedFeeInReais,
            totalFee: totalFeeInReais,
            percentFee: transaction.percentFee, // Percentage remains as-is
            netAmount: netAmountInReais,
            // Legacy fee handling (for backward compatibility)
            fee: legacyFeeInReais,
            platformFee: legacyPlatformFeeInReais,
            // Include metadata for additional fields
            metadata: metadata
          };
        });

        return NextResponse.json({
          transactions: formattedTransactions,
          pagination: {
            total: totalCount,
            page,
            limit,
            pages: Math.ceil(totalCount / limit)
          }
        });
      } else {
        // Se não houver transações, retorna um array vazio com informações de paginação
        return NextResponse.json({
          transactions: [],
          pagination: {
            total: 0,
            page,
            limit,
            pages: 0
          }
        });
      }
    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Database Error" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error fetching admin transactions:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
