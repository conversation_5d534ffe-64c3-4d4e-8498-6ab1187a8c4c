import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType } from "@prisma/client";
import { safeMonetaryConversion } from "@shared/lib/currency";

export async function GET(req: NextRequest) {
  try {
    // Verify if user is admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Get query params
    const searchParams = req.nextUrl.searchParams;
    const type = searchParams.get("type") as TransactionType | null;

    // Build base filter conditions
    const baseFilter: any = {};
    if (type) {
      baseFilter.type = type;
    }

    try {
      // Run queries in parallel to reduce latency and DB round-trips
      const [countsByStatus, financialData, activeOrganizationsData] = await Promise.all([
        db.transaction.groupBy({
          by: ['status'],
          where: baseFilter,
          _count: { _all: true },
        }),
        db.transaction.aggregate({
          where: { ...baseFilter, status: TransactionStatus.APPROVED },
          _sum: { amount: true },
        }),
        db.transaction.groupBy({
          by: ['organizationId'],
          where: baseFilter,
          _count: true,
        }),
      ]);

      const totalTransactions = countsByStatus.reduce((sum, row: any) => sum + (row._count?._all ?? 0), 0);
      const approvedTransactions = countsByStatus.find((r: any) => r.status === TransactionStatus.APPROVED)?._count?._all ?? 0;
      const pendingTransactions = countsByStatus.find((r: any) => r.status === TransactionStatus.PENDING)?._count?._all ?? 0;
      const financialVolume = financialData._sum.amount || 0;
      const activeOrganizations = activeOrganizationsData.length;

      // Se houver pelo menos algumas transações
      if (totalTransactions > 0) {
        const summary = {
          totalTransactions: {
            count: totalTransactions,
            growth: 0, // We would need historical data to calculate this
          },
          approvedTransactions: {
            count: approvedTransactions,
            growth: 0,
            approvalRate: totalTransactions > 0 ? (approvedTransactions / totalTransactions) * 100 : 0,
          },
          pendingTransactions: {
            count: pendingTransactions,
            growth: 0,
          },
          financialVolume: {
            amount: safeMonetaryConversion(financialVolume, 'transaction_amount'), // Already in reais format
            growth: 0,
            averageTicket: approvedTransactions > 0 ? safeMonetaryConversion(financialVolume, 'transaction_amount') / approvedTransactions : 0,
          },
          activeOrganizations: {
            count: activeOrganizations,
            growth: 0,
          }
        };

        return NextResponse.json(summary);
      } else {
        // Se não houver transações, retorna dados zerados
        const emptySummary = {
          totalTransactions: {
            count: 0,
            growth: 0,
          },
          approvedTransactions: {
            count: 0,
            growth: 0,
            approvalRate: 0,
          },
          pendingTransactions: {
            count: 0,
            growth: 0,
          },
          financialVolume: {
            amount: 0, // Already in reais (zero)
            growth: 0,
            averageTicket: 0,
          },
          activeOrganizations: {
            count: 0,
            growth: 0,
          }
        };

        return NextResponse.json(emptySummary);
      }
    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Database Error" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error fetching admin transactions summary:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
