import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { createId } from "@paralleldrive/cuid2";
import { auth } from "@repo/auth";
import { headers } from "next/headers";

/**
 * POST /api/admin/transactions/[id]/cancel
 * Cancela uma transação SEND em estado PROCESSING e libera o saldo reservado
 * Apenas para administradores
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticação e permissões de admin
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Acesso negado. Apenas administradores podem cancelar transações." },
        { status: 403 }
      );
    }

    const { id: transactionId } = await params;
    const { reason, releaseReservedBalance = true } = await request.json();

    // Buscar a transação
    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      include: {
        organization: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { error: "Transação não encontrada" },
        { status: 404 }
      );
    }

    // Verificar se é uma transação SEND em estado PENDING ou PROCESSING
    if (transaction.type !== "SEND") {
      return NextResponse.json(
        { error: "Apenas transações de envio (SEND) podem ser canceladas" },
        { status: 400 }
      );
    }

    if (transaction.status !== "PROCESSING" && transaction.status !== "PENDING") {
      return NextResponse.json(
        { error: `Transação não pode ser cancelada. Status atual: ${transaction.status}. Apenas transações PENDING ou PROCESSING podem ser canceladas.` },
        { status: 400 }
      );
    }

    // Buscar saldo atual da organização
    const currentBalance = await db.organization_balance.findUnique({
      where: { organizationId: transaction.organizationId }
    });

    if (!currentBalance) {
      return NextResponse.json(
        { error: "Saldo da organização não encontrado" },
        { status: 404 }
      );
    }

    // Calcular o valor total que foi reservado (valor + taxas)
    const totalReservedAmount = transaction.amount + (transaction.totalFee || 0);

    logger.info("Iniciando cancelamento de transação SEND", {
      transactionId,
      organizationId: transaction.organizationId,
      amount: transaction.amount,
      totalFee: transaction.totalFee,
      totalReservedAmount,
      currentStatus: transaction.status,
      adminUserId: session.user.id,
      reason,
      releaseReservedBalance
    });

    // Executar cancelamento em transação do banco
    const result = await db.$transaction(async (tx) => {
      // 1. Atualizar status da transação para CANCELED
      const updatedTransaction = await tx.transaction.update({
        where: { id: transactionId },
        data: {
          status: "CANCELED",
          metadata: {
            ...(transaction.metadata as any || {}),
            canceledAt: new Date().toISOString(),
            canceledBy: session.user.id,
            cancelReason: reason || "Cancelamento manual pelo administrador",
            originalStatus: transaction.status,
            adminCancellation: true
          }
        }
      });

      // 2. Liberar saldo reservado (condicional)
      let updatedBalance;
      let releasedAmount = 0;
      
      if (releaseReservedBalance) {
        const newReservedBalance = Math.max(0, Number(currentBalance.reservedBalance) - totalReservedAmount);
        const newAvailableBalance = Number(currentBalance.availableBalance) + totalReservedAmount;
        releasedAmount = totalReservedAmount;

        updatedBalance = await tx.organization_balance.update({
          where: { id: currentBalance.id },
          data: {
            reservedBalance: newReservedBalance,
            availableBalance: newAvailableBalance,
            updatedAt: new Date()
          }
        });

        // 3. Registrar no histórico de saldo (apenas se liberou o saldo)
        await tx.balance_history.create({
          data: {
            id: createId(),
            organizationId: transaction.organizationId,
            transactionId: transactionId,
            operation: "UNRESERVE",
            amount: totalReservedAmount,
            description: `Cancelamento manual de transação SEND ${transactionId} pelo admin - liberação de saldo reservado`,
            balanceAfterOperation: {
              available: updatedBalance.availableBalance,
              pending: updatedBalance.pendingBalance,
              reserved: updatedBalance.reservedBalance
            },
            balanceId: updatedBalance.id
          }
        });
      } else {
        // Apenas atualizar o timestamp, sem alterar os valores
        updatedBalance = await tx.organization_balance.update({
          where: { id: currentBalance.id },
          data: {
            updatedAt: new Date()
          }
        });
      }

      return {
        transaction: updatedTransaction,
        balance: updatedBalance,
        releasedAmount,
        releaseReservedBalance
      };
    });

    logger.info("Transação cancelada com sucesso", {
      transactionId,
      organizationId: transaction.organizationId,
      releasedAmount: result.releasedAmount,
      releaseReservedBalance: result.releaseReservedBalance,
      newAvailableBalance: result.balance.availableBalance,
      adminUserId: session.user.id
    });

    return NextResponse.json({
      success: true,
      message: "Transação cancelada com sucesso",
      data: {
        transactionId,
        status: "CANCELED",
        releasedAmount: result.releasedAmount,
        releaseReservedBalance: result.releaseReservedBalance,
        organizationName: transaction.organization.name,
        newAvailableBalance: Number(result.balance.availableBalance)
      }
    });

  } catch (error) {
    logger.error("Erro ao cancelar transação", {
      transactionId: (await params).id,
      error: error instanceof Error ? error.message : String(error),
      adminUserId: session?.user?.id
    });

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: error instanceof Error ? error.message : "Erro desconhecido"
      },
      { status: 500 }
    );
  }
}