import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { TransactionStatus, TransactionType } from "@prisma/client";
import { createId } from "@paralleldrive/cuid2";
import { Money } from "@repo/utils/src/financial/decimal-money";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { createWebhookEvent } from "@repo/payments/src/webhooks/service";
import { WebhookEventType } from "@repo/payments/src/webhooks/events";

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify if user is admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    const { id: transactionId } = await params;
    const body = await req.json();
    const { reason, amount } = body;

    if (!reason || !reason.trim()) {
      return NextResponse.json(
        { error: "Reason is required" },
        { status: 400 }
      );
    }

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: "Valid amount is required" },
        { status: 400 }
      );
    }

    // Find the original transaction
    const originalTransaction = await db.transaction.findUnique({
      where: { id: transactionId },
      include: {
        organization: true,
      },
    });

    if (!originalTransaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }

    // FIXED: Check if transaction is already refunded first
    if (originalTransaction.status === TransactionStatus.REFUNDED) {
      return NextResponse.json(
        { error: "Transaction has already been refunded" },
        { status: 400 }
      );
    }

    // Check if transaction can be refunded
    if (originalTransaction.status !== TransactionStatus.APPROVED) {
      return NextResponse.json(
        { error: "Only approved transactions can be refunded" },
        { status: 400 }
      );
    }

    if (originalTransaction.type === TransactionType.REFUND) {
      return NextResponse.json(
        { error: "Cannot refund a refund transaction" },
        { status: 400 }
      );
    }

    // Não permitir estorno de transferências (tipo SEND)
    if (originalTransaction.type === TransactionType.SEND) {
      return NextResponse.json(
        { error: "Transfers cannot be refunded. Use cancellation for pending/processing transfers instead." },
        { status: 400 }
      );
    }

    // Check if amount is valid
    if (amount > originalTransaction.amount) {
      return NextResponse.json(
        { error: "Refund amount cannot be greater than original transaction amount" },
        { status: 400 }
      );
    }

    // Check for recent refund attempts to prevent rapid duplicates
    const recentRefundAttempts = await db.transaction.findMany({
      where: {
        id: transactionId,
        status: TransactionStatus.REFUNDED,
        updatedAt: {
          gte: new Date(Date.now() - 30 * 1000) // Within last 30 seconds
        }
      }
    });

    if (recentRefundAttempts.length > 0) {
      return NextResponse.json(
        { error: "A refund request was recently processed. Please wait before trying again." },
        { status: 429 }
      );
    }

    // Check if this is a PIX transaction that needs external refund processing
    let pixRefundResult = null;
    if (originalTransaction.endToEndId && originalTransaction.gatewayName === "FLOW2PAY") {
      // Validate required environment variables
      if (!process.env.PIX_API_URL || !process.env.PIX_API_KEY) {
        console.warn("PIX API environment variables not configured, skipping external refund", {
          hasPixApiUrl: !!process.env.PIX_API_URL,
          hasPixApiKey: !!process.env.PIX_API_KEY,
          transactionId: originalTransaction.id
        });
      } else {
        try {
          console.log("Initiating PIX refund via PIX API", {
            transactionId: originalTransaction.id,
            endToEndId: originalTransaction.endToEndId,
            amount,
            reason
          });

          // Call PIX API to initiate the actual refund with Flow2Pay
          const pixApiResponse = await fetch(`${process.env.PIX_API_URL}/refund`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-API-Key": process.env.PIX_API_KEY || "",
            },
            body: JSON.stringify({
              end_to_end_id: originalTransaction.endToEndId,
              amount: amount,
              description: `Admin refund: ${reason}`,
            }),
          });

          if (!pixApiResponse.ok) {
            const errorText = await pixApiResponse.text();
            throw new Error(`PIX API refund failed: ${pixApiResponse.status} - ${errorText}`);
          }

          pixRefundResult = await pixApiResponse.json();
          console.log("PIX API refund successful", {
            transactionCode: pixRefundResult.transaction_code,
            originalTransactionId: originalTransaction.id
          });

        } catch (pixError) {
          console.error("PIX API refund failed:", {
            error: pixError instanceof Error ? pixError.message : String(pixError),
            transactionId: originalTransaction.id,
            endToEndId: originalTransaction.endToEndId
          });

          return NextResponse.json(
            { error: `PIX refund failed: ${pixError instanceof Error ? pixError.message : 'Unknown error'}` },
            { status: 500 }
          );
        }
      }
    }

    try {
      // FIXED: Start a database transaction with improved logic - only update the original transaction
      const result = await db.$transaction(async (prisma) => {
        // FIXED: Instead of creating a new refund transaction, just update the original transaction status
        const updateData: any = {
          status: TransactionStatus.REFUNDED,
          updatedAt: new Date(),
          reason: reason.trim(), // Store the refund reason
          processedAt: new Date(), // Mark when the refund was processed
        };

        // If this is a PIX refund, store the transaction_code for webhook matching
        if (pixRefundResult) {
          const currentMetadata = (originalTransaction.metadata as any) || {};
          updateData.metadata = {
            ...currentMetadata,
            // Array of pending refunds for webhook matching
            pendingRefunds: [
              ...(currentMetadata.pendingRefunds || []),
              {
                transactionCode: pixRefundResult.transaction_code, // CRITICAL: Key for webhook matching
                idEnvio: pixRefundResult.transaction_code, // Compatibility field
                amount: amount,
                description: `Admin refund: ${reason}`,
                initiatedAt: pixRefundResult.transaction_datetime,
                status: 'pending',
                provider: 'PLUGGOU_PIX',
                refundResponse: pixRefundResult,
                adminInitiated: true
              }
            ],
            // Direct fields for quick access
            lastRefundOperationId: pixRefundResult.transaction_code,
            lastRefundTransactionCode: pixRefundResult.transaction_code,
            lastRefundInitiatedAt: pixRefundResult.transaction_datetime,
            lastRefundResponse: pixRefundResult,
            // CRITICAL: Mark that balance was debited by admin to prevent duplicate debit by webhook
            adminRefundBalanceDebited: true,
            adminRefundDebitedAt: new Date().toISOString(),
            adminRefundDebitedAmount: amount,
            // Store admin refund details
            adminRefund: {
              processedAt: new Date(),
              adminUserId: session.user.id,
              adminUserEmail: session.user.email,
              reason: reason.trim(),
              amount: amount
            }
          };
        } else {
          // For non-PIX refunds, also mark balance as debited and store admin refund details
          const currentMetadata = (originalTransaction.metadata as any) || {};
          updateData.metadata = {
            ...currentMetadata,
            adminRefundBalanceDebited: true,
            adminRefundDebitedAt: new Date().toISOString(),
            adminRefundDebitedAmount: amount,
            // Store admin refund details
            adminRefund: {
              processedAt: new Date(),
              adminUserId: session.user.id,
              adminUserEmail: session.user.email,
              reason: reason.trim(),
              amount: amount
            }
          };
        }

        // FIXED: Update the original transaction instead of creating a new one
        const updatedTransaction = await prisma.transaction.update({
          where: { id: transactionId },
          data: updateData,
        });

        return updatedTransaction;
      });

      // CRITICAL FIX: Always update organization balance for admin refunds
      // This ensures the refund transaction exists before creating balance_history
      try {
        // Check if this transaction already had a webhook-based refund that debited the balance
        const metadata = (originalTransaction.metadata as any) || {};
        const webhookRefundBalanceDebited = metadata.webhookRefundBalanceDebited;

        if (webhookRefundBalanceDebited) {
          console.log("Balance was already debited by webhook refund, skipping admin balance debit", {
            transactionId: result.id,
            organizationId: originalTransaction.organizationId,
            webhookRefundDebitedAt: metadata.webhookRefundDebitedAt,
            webhookRefundDebitedAmount: metadata.webhookRefundDebitedAmount,
            action: "SKIP_ADMIN_BALANCE_DEBIT"
          });
        } else {
          // No webhook refund processed yet, proceed with admin balance debit
          // Use DEBIT_ALLOW_NEGATIVE for admin refunds to allow negative balance
          await updateOrganizationBalance(
            originalTransaction.organizationId,
            amount,
            BalanceOperationType.DEBIT_ALLOW_NEGATIVE,
            result.id, // Use the original transaction ID
            `Admin refund processed: ${reason}`
          );

          console.log("Organization balance updated successfully for admin refund", {
            transactionId: result.id,
            organizationId: originalTransaction.organizationId,
            amount,
            operation: "ADMIN_REFUND_BALANCE_DEBIT_ALLOW_NEGATIVE"
          });

          // Mark that admin refund debited the balance to prevent webhook duplication
          await db.transaction.update({
            where: { id: transactionId },
            data: {
              metadata: {
                ...(originalTransaction.metadata as any || {}),
                adminRefundBalanceDebited: true,
                adminRefundDebitedAt: new Date().toISOString(),
                adminRefundDebitedAmount: amount
              }
            }
          });
        }
      } catch (balanceError) {
        // CRITICAL: For admin refunds, balance update failure should be treated as a serious error
        const safeBalanceError = balanceError || "Unknown balance error";
        const errorMessage = safeBalanceError instanceof Error
          ? safeBalanceError.message
          : String(safeBalanceError);

        console.error("CRITICAL: Failed to update organization balance for admin refund:", {
          error: errorMessage,
          transactionId: result.id,
          organizationId: originalTransaction.organizationId,
          amount,
          stack: safeBalanceError instanceof Error ? safeBalanceError.stack : undefined,
          severity: "CRITICAL",
          requiresManualReview: true
        });

        // For admin refunds, we should still continue but log this as critical
        // The refund transaction was created, so we need to proceed with webhook
      }

      // FIXED: After successful refund processing, trigger refund webhook with correct data
      try {
        // Create refund webhook payload using the updated original transaction
        const refundWebhookPayload = {
          id: result.id, // Original transaction ID
          externalId: result.externalId,
          referenceCode: result.referenceCode,
          endToEndId: result.endToEndId,
          type: result.type, // Original transaction type (CHARGE, RECEIVE, etc.)
          amount: result.amount,
          status: "REFUNDED", // Updated status
          reason: result.reason, // Refund reason
          processedAt: result.processedAt,
          paymentAt: result.paymentAt,
          customerName: result.customerName,
          customerEmail: result.customerEmail,
          customerPhone: result.customerPhone,
          customerDocument: result.customerDocument,
          customerDocumentType: result.customerDocumentType,
          organizationId: result.organizationId,
          gatewayId: result.gatewayId,
          gatewayName: result.gatewayName,
          // PIX-specific fields
          pixKey: result.pixKey,
          pixKeyType: result.pixKeyType,
          description: result.description,
          // Fee information
          percentFee: result.percentFee,
          fixedFee: result.fixedFee,
          totalFee: result.totalFee,
          netAmount: result.netAmount,
          // Refund metadata
          refund: {
            amount: amount,
            reason: reason.trim(),
            processedAt: result.processedAt,
            adminInitiated: true,
            adminUserId: session.user.id,
            adminUserEmail: session.user.email,
          },
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
        };

        // FIXED: Trigger the correct webhook event type for refunds
        // For PIX transactions, use PIX_IN_REVERSAL_CONFIRMATION, otherwise use TRANSACTION_REFUNDED
        // Enhanced PIX detection: check multiple fields that indicate a PIX transaction
        const isPix = !!(
          result.pixKey ||
          result.pixKeyType ||
          result.endToEndId ||
          result.type === "CHARGE" ||
          result.type === "RECEIVE" ||
          (result.gatewayName && result.gatewayName.toLowerCase().includes('pix')) ||
          (result.description && result.description.toLowerCase().includes('pix'))
        );

        const eventType = isPix
          ? WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION
          : WebhookEventType.TRANSACTION_REFUNDED;

        console.log("DEBUG: Event type selection", {
          transactionId: result.id,
          pixKey: result.pixKey,
          pixKeyType: result.pixKeyType,
          endToEndId: result.endToEndId,
          type: result.type,
          gatewayName: result.gatewayName,
          description: result.description,
          isPix,
          selectedEventType: eventType,
          pixInReversalConfirmation: WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
          transactionRefunded: WebhookEventType.TRANSACTION_REFUNDED,
          detectionCriteria: {
            hasPixKey: !!result.pixKey,
            hasPixKeyType: !!result.pixKeyType,
            hasEndToEndId: !!result.endToEndId,
            isChargeType: result.type === "CHARGE",
            isReceiveType: result.type === "RECEIVE",
            gatewayHasPix: !!(result.gatewayName && result.gatewayName.toLowerCase().includes('pix')),
            descriptionHasPix: !!(result.description && result.description.toLowerCase().includes('pix'))
          }
        });

        await createWebhookEvent({
          type: eventType,
          payload: refundWebhookPayload,
          transactionId: result.id, // Use the original transaction ID
          organizationId: result.organizationId,
        });

        console.log("Refund webhook triggered successfully", {
          transactionId: result.id,
          organizationId: result.organizationId,
          eventType: eventType,
          pixKey: result.pixKey,
          pixKeyType: result.pixKeyType,
          endToEndId: result.endToEndId,
          detectedAsPix: !!(result.pixKey || result.pixKeyType || result.endToEndId),
        });
      } catch (webhookError) {
        // FIXED: Safe error logging to prevent "payload must be object" error
        const safeWebhookError = webhookError || "Unknown webhook error";
        const errorMessage = safeWebhookError instanceof Error
          ? safeWebhookError.message
          : String(safeWebhookError);

        console.error("Failed to trigger refund webhook:", {
          error: errorMessage,
          transactionId: result.id,
          stack: safeWebhookError instanceof Error ? safeWebhookError.stack : undefined
        });
        // Note: We don't return an error here because the refund was successful
        // The webhook failure should not affect the refund operation
      }

      return NextResponse.json({
        success: true,
        message: "Refund processed successfully",
        transaction: {
          id: result.id,
          amount: result.amount,
          status: result.status,
          reason: result.reason,
          processedAt: result.processedAt,
          refundAmount: amount,
        },
      });
    } catch (dbError) {
      // FIXED: Safe error logging to prevent "payload must be object" error
      const safeDbError = dbError || "Unknown database error";
      const errorMessage = safeDbError instanceof Error
        ? safeDbError.message
        : String(safeDbError);

      console.error("Database error during refund:", {
        error: errorMessage,
        transactionId,
        amount,
        reason,
        stack: safeDbError instanceof Error ? safeDbError.stack : undefined
      });

      return NextResponse.json(
        { error: "Failed to process refund due to database error" },
        { status: 500 }
      );
    }
  } catch (error) {
    // FIXED: Safe error logging to prevent "payload must be object" error
    const safeError = error || "Unknown error";
    const errorMessage = safeError instanceof Error
      ? safeError.message
      : String(safeError);

    console.error("Error processing refund:", {
      error: errorMessage,
      transactionId: params?.transactionId,
      stack: safeError instanceof Error ? safeError.stack : undefined
    });

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
