import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { auth } from "@repo/auth";
import { headers } from "next/headers";

import { TransactionStatus } from "@prisma/client";
import { updateTransactionStatus } from "@repo/payments";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let session: any;
  try {
    const headersList = await headers();
    session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Acesso negado. Apenas administradores podem aprovar transações." },
        { status: 403 }
      );
    }

    const { id: transactionId } = await params;
    
    let reason;
    try {
      const body = await request.json();
      reason = body.reason;
    } catch (error) {
      logger.error("Erro ao fazer parse do JSON da requisição", {
        error: error instanceof Error ? error.message : String(error),
        transactionId
      });
      return NextResponse.json(
        { error: "Erro interno do servidor", message: "Dados da requisição inválidos" },
        { status: 400 }
      );
    }

    const transaction = await db.transaction.findUnique({
      where: { id: transactionId },
      include: {
        organization: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { error: "Transação não encontrada" },
        { status: 404 }
      );
    }

    if (transaction.status !== "PENDING") {
      return NextResponse.json(
        { error: `Transação não pode ser aprovada. Status atual: ${transaction.status}. Apenas transações PENDING podem ser aprovadas manualmente.` },
        { status: 400 }
      );
    }

    logger.info("Iniciando aprovação manual de transação", {
      transactionId,
      organizationId: transaction.organizationId,
      amount: transaction.amount,
      type: transaction.type,
      currentStatus: transaction.status,
      adminUserId: session.user.id,
      reason
    });

    await db.transaction.update({
      where: { id: transactionId },
      data: {
        metadata: {
          ...(transaction.metadata as any || {}),
          manuallyApprovedAt: new Date().toISOString(),
          manuallyApprovedBy: session.user.id,
          approvalReason: reason || "Aprovação manual pelo administrador",
          originalStatus: transaction.status,
          adminApproval: true
        }
      }
    });

    const updatedTransaction = await updateTransactionStatus(
      transactionId,
      TransactionStatus.APPROVED,
      new Date()
    );

    logger.info("Transação aprovada manualmente com sucesso", {
      transactionId,
      organizationId: transaction.organizationId,
      amount: transaction.amount,
      type: transaction.type,
      previousStatus: transaction.status,
      newStatus: updatedTransaction.status,
      adminUserId: session.user.id
    });

    return NextResponse.json({
      success: true,
      message: "Transação aprovada com sucesso",
      data: {
        transactionId,
        status: "APPROVED",
        amount: transaction.amount,
        type: transaction.type,
        organizationName: transaction.organization.name,
        approvedAt: updatedTransaction.paymentAt,
        approvedBy: session.user.id
      }
    });

  } catch (error) {
    logger.error("Erro ao aprovar transação manualmente", {
      transactionId: (await params).id,
      error: error instanceof Error ? error.message : String(error),
      adminUserId: session?.user?.id
    });

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: error instanceof Error ? error.message : "Erro desconhecido"
      },
      { status: 500 }
    );
  }
}
