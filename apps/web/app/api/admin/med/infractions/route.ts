import { getSession } from "@saas/auth/lib/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";

export async function GET(req: NextRequest) {
  try {
    // Verificar se o usuário é admin
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autenticado" },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "all";
    const limit = 20;
    const offset = (page - 1) * limit;

    // Construir filtros
    const where: any = {};

    if (status !== "all") {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          organization: {
            name: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          transaction: {
            customerName: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          transaction: {
            customerEmail: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          transaction: {
            endToEndId: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          externalId: {
            contains: search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Buscar infrações com paginação
    const [infractions, totalCount] = await Promise.all([
      db.med_infraction.findMany({
        where,
        include: {
          organization: {
            select: {
              name: true,
              slug: true,
            },
          },
          transaction: {
            select: {
              id: true,
              customerName: true,
              customerEmail: true,
              amount: true,
              endToEndId: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
      }),
      db.med_infraction.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      infractions,
      totalCount,
      totalPages,
      currentPage: page,
      limit,
    });
  } catch (error) {
    console.error("Erro ao buscar infrações MED:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
