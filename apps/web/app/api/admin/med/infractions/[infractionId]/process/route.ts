import {
	approveMedInfraction,
	rejectMedInfraction,
} from "@repo/payments/src/med/med-service";
import { getSession } from "@saas/auth/lib/server";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(
	req: NextRequest,
	{ params }: { params: Promise<{ infractionId: string }> },
) {
	try {
		// Verificar se o usuário é admin
		const session = await getSession();

		if (!session?.user) {
			return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
		}

		if (session.user.role !== "admin") {
			return NextResponse.json({ error: "Não autorizado" }, { status: 403 });
		}

		const { infractionId } = await params;
		const body = await req.json();
		const { action, reason } = body;

		if (!action || !["approve", "reject"].includes(action)) {
			return NextResponse.json(
				{ error: "Ação inválida. Use 'approve' ou 'reject'" },
				{ status: 400 },
			);
		}

		try {
			let result: {
				success: boolean;
				infractionId?: string;
				message?: string;
				error?: string;
			};
			const defaultReason =
				action === "approve"
					? "Devolução via MED aprovada pelo administrador"
					: "Infração rejeitada pelo administrador";

			if (action === "approve") {
				result = await approveMedInfraction(
					infractionId,
					session.user.id,
					reason || defaultReason,
				);
			} else {
				result = await rejectMedInfraction(
					infractionId,
					session.user.id,
					reason || defaultReason,
				);
			}

			if (result.success) {
				return NextResponse.json({
					success: true,
					infractionId: result.infractionId,
					message: result.message,
				});
			}
			return NextResponse.json(
				{ error: result.error || "Erro ao processar infração" },
				{ status: 400 },
			);
		} catch (error) {
			console.error("Erro ao processar infração MED:", error);
			return NextResponse.json(
				{ error: "Erro interno do servidor" },
				{ status: 500 },
			);
		}
	} catch (error) {
		console.error("Erro ao processar infração MED:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 },
		);
	}
}
