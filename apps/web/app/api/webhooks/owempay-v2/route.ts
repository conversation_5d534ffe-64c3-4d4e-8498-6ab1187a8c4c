import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import * as crypto from "crypto";
import { TransactionStatus } from "@prisma/client";
import { processMedInfraction } from "@repo/payments/src/med/med-service";

// Endpoint GET para teste do webhook
export async function GET(_req: NextRequest) {
  logger.info("Owempay v2 webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Owempay v2 webhook endpoint is working" });
}

// Validar assinatura do webhook da Owempay v2
async function validateOwempayV2Webhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Verificar se é um webhook de teste (webhook.site) - permitir sem validação
    const userAgent = req.headers.get('user-agent') || '';
    const origin = req.headers.get('origin') || '';
    const referer = req.headers.get('referer') || '';



    // Verificar assinatura em possíveis headers
    const possibleSignatureHeaders = [
      "x-owempay-signature",
      "x-signature",
      "owempay-signature",
      "signature",
      "x-hub-signature-256",
      "x-hub-signature",
      "authorization" // Owempay v2 envia a assinatura no header authorization
    ];

    let signature = "";
    let signatureHeaderUsed = "";

        // Tentar encontrar assinatura em qualquer um dos headers possíveis
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        // Se for o header authorization, extrair apenas o valor (sem "Basic " ou "Bearer ")
        if (headerName === 'authorization') {
          // A Owempay V2 pode estar enviando a assinatura como "Bearer <signature>" ou apenas a assinatura
          signature = headerValue.replace(/^(Basic|Bearer)\s+/i, '');
        } else {
          signature = headerValue;
        }
        signatureHeaderUsed = headerName;
        logger.info('Found signature in header', {
          headerName,
          originalValue: headerValue,
          extractedSignature: signature
        });
        break;
      }
    }

    // Se não encontrou assinatura em nenhum header
    if (!signature) {
      logger.warn('Missing Owempay v2 webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders,
        allHeaders: Object.fromEntries(req.headers.entries())
      });

      // CRITICAL FIX: Remove bypass in production
      const bypassValidation = process.env.OWEMPAY_V2_BYPASS_SIGNATURE_VALIDATION === 'true' && process.env.NODE_ENV === 'development';
      if (bypassValidation) {
        logger.warn('Bypassing signature validation in development mode only');
        return true;
      }

      return false;
    }

        // Obter o segredo do webhook das variáveis de ambiente
    const webhookSecret = process.env.OWEMPAY_V2_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.error('OWEMPAY_V2_WEBHOOK_SECRET not configured, rejecting webhook for security');
      return false;
    }

    // SIMPLES: Comparar o valor do header authorization com a variável de ambiente
    if (signature === webhookSecret) {
      logger.info('Owempay v2 webhook validated', {
        signatureHeaderUsed,
        signature: signature.substring(0, 10) + '...',
        webhookSecret: webhookSecret.substring(0, 10) + '...'
      });
      return true;
    }

    // Log para debug - mostrar o que está sendo comparado
    logger.warn('Owempay v2 webhook validation failed', {
      receivedSignature: signature,
      expectedSecret: webhookSecret,
      signatureLength: signature.length,
      secretLength: webhookSecret.length,
      match: signature === webhookSecret
    });

        // Rejeitar webhook se a validação falhou
    return false;
  } catch (error) {
    logger.error('Error validating Owempay v2 webhook signature', { error });
    return false;
  }
}

// Função para encontrar transação por múltiplos critérios
async function findTransactionByMultipleCriteria({
  externalId,
  internalId,
  customerEmail,
  amount,
  endToEndId
}: {
  externalId?: string;
  internalId?: string;
  customerEmail?: string;
  amount?: number;
  endToEndId?: string;
}) {
  try {
    logger.info("Searching for transaction with criteria", {
      externalId,
      internalId,
      customerEmail,
      amount,
      endToEndId
    });

    // Primeiro, tentar encontrar por externalId
    if (externalId) {
      const transaction = await db.transaction.findFirst({
        where: { externalId }
      });
      if (transaction) {
        logger.info("Transaction found by externalId", { transactionId: transaction.id });
        return transaction;
      }
    }

    // Se não encontrou, tentar por endToEndId (para transferências PIX)
    if (endToEndId) {
      const transaction = await db.transaction.findFirst({
        where: { endToEndId }
      });
      if (transaction) {
        logger.info("Transaction found by endToEndId", { transactionId: transaction.id, endToEndId });
        return transaction;
      }
    }

    // Se ainda não encontrou, tentar por internalId
    if (internalId) {
      const transaction = await db.transaction.findFirst({
        where: { id: internalId }
      });
      if (transaction) {
        logger.info("Transaction found by internalId", { transactionId: transaction.id });
        return transaction;
      }
    }

    // Se ainda não encontrou, tentar por email e valor
    if (customerEmail && amount) {
      const transaction = await db.transaction.findFirst({
        where: {
          customerEmail,
          amount: Math.round(amount * 100) // Converter para centavos (banco armazena em centavos)
        }
      });
      if (transaction) {
        logger.info("Transaction found by email and amount", { transactionId: transaction.id });
        return transaction;
      }
    }

    logger.warn("Transaction not found with any criteria");
    return null;
  } catch (error) {
    logger.error("Error finding transaction", { error });
    return null;
  }
}

// Endpoint POST para receber webhooks da Owempay v2
export async function POST(req: NextRequest) {
  try {
    logger.info("Owempay v2 webhook received");

    // Parse do corpo da requisição
    const body = await req.text();
    let fullPayload;

    // Verificar se o body está vazio
    if (!body || body.trim() === '') {
      logger.warn("Empty webhook body received", { body });
      return NextResponse.json(
        { error: "Empty payload" },
        { status: 400 }
      );
    }

    try {
      fullPayload = JSON.parse(body);
    } catch (parseError) {
      logger.error("Error parsing Owempay v2 webhook body", { parseError, body });
      return NextResponse.json(
        { error: "Invalid JSON payload" },
        { status: 400 }
      );
    }

    logger.info("Owempay v2 webhook FULL PAYLOAD", {
      fullPayload,
      bodyRaw: body
    });

    // Debug: verificar estrutura do payload
    logger.info("Owempay v2 webhook payload structure debug", {
      hasEvent: !!fullPayload.event,
      hasObject: !!fullPayload.object,
      eventValue: fullPayload.event,
      objectKeys: fullPayload.object ? Object.keys(fullPayload.object) : null,
      objectId: fullPayload.object?.id,
      objectEntryId: fullPayload.object?.entryId, // CORREÇÃO: Mostrar entryId
      objectEndToEndId: fullPayload.object?.endToEndId, // Adicionar endToEndId
      objectExternalReference: fullPayload.object?.externalReference,
      objectStatus: fullPayload.object?.status
    });

    // Validar assinatura do webhook
    const isValidSignature = await validateOwempayV2Webhook(req, body);
    if (!isValidSignature) {
      logger.warn("Invalid Owempay v2 webhook signature");
      return NextResponse.json(
        { error: "Invalid signature" },
        { status: 401 }
      );
    }

    // Extrair dados do webhook baseado na estrutura real do payload
    const { event, object } = fullPayload;

    if (!event || !object) {
      logger.error("Invalid Owempay v2 webhook payload structure", {
        hasEvent: !!event,
        hasObject: !!object,
        payload: fullPayload
      });
      return NextResponse.json(
        { error: "Invalid payload structure" },
        { status: 400 }
      );
    }

    // Verificar se é um evento de transferência (PIX OUT)
    const isTransferEvent = event.startsWith('pix_out:');
    logger.info("Webhook event type", {
      event,
      isTransferEvent,
      eventType: isTransferEvent ? 'TRANSFER' : 'PAYMENT'
    });

    // Extrair informações da transação
    const transactionId = object.id;
    const externalId = object.entryId; // Owempay V2 usa entryId como externalId (mas pode ser null)
    const endToEndId = object.endToEndId; // Para transferências PIX, usar endToEndId
    const status = object.status;
    const amount = object.grossAmount; // CORREÇÃO: Owempay V2 usa grossAmount
    const customerEmail = object.customerEmail;
    const eventType = event;

    logger.info("Owempay v2 webhook data extracted", {
      transactionId,
      externalId,
      endToEndId,
      status,
      amount,
      customerEmail,
      eventType
    });

    // Mapear status da Owempay v2 para status interno
    // Baseado no payload real: status: 'succeeded'
    const statusMap: Record<string, TransactionStatus> = {
      'pending': TransactionStatus.PENDING,
      'paid': TransactionStatus.APPROVED,
      'succeeded': TransactionStatus.APPROVED, // CORREÇÃO: Mapear para APPROVED como outros providers
      'expired': TransactionStatus.EXPIRED,
      'cancelled': TransactionStatus.CANCELLED,
      'refunded': TransactionStatus.REFUNDED,
      'refunded_med': TransactionStatus.REFUNDED_MED,
      'refunding': TransactionStatus.REFUNDING,
      'failed': TransactionStatus.FAILED,
      'processing': TransactionStatus.PROCESSING,
      'completed': TransactionStatus.APPROVED // CORREÇÃO: Mapear para APPROVED também
    };

    const internalStatus = statusMap[status] || TransactionStatus.PENDING;

    // Encontrar a transação no banco de dados
    // Para transferências PIX, usar endToEndId como primary identifier
    const searchExternalId = isTransferEvent ? endToEndId : externalId;

    const transaction = await findTransactionByMultipleCriteria({
      externalId: searchExternalId,
      internalId: transactionId,
      customerEmail,
      amount: amount ? amount : undefined, // Owempay v2 envia em decimal, função converte para centavos
      endToEndId: endToEndId // Adicionar endToEndId como critério adicional
    });

    // Se não encontrou transação e é um evento de transferência, logar e continuar
    if (!transaction && isTransferEvent) {
      logger.info("Transfer event received - no transaction found in database", {
        event,
        transactionId,
        externalId,
        status,
        amount,
        eventType: 'TRANSFER'
      });

      // Para transferências, não precisamos encontrar transação no banco
      // Apenas logar o evento
      return NextResponse.json(
        {
          success: true,
          message: "Transfer event processed",
          eventType: 'TRANSFER',
          transactionId,
          status
        },
        { status: 200 }
      );
    }

    if (!transaction) {
      logger.warn("Transaction not found for Owempay v2 webhook", {
        transactionId,
        externalId,
        customerEmail,
        amount,
        eventType
      });
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }

    // Verificar se é um reembolso MED (refunded_med status)
    if (status === 'refunded_med') {
      logger.info('[OWEMPAY_V2][MED] Detectado status refunded_med, criando infração MED', {
        transactionId: transaction.id,
        externalId,
        status,
        amount: transaction.amount
      });

      try {
        // Usar o novo serviço MED centralizado
        const medResult = await processMedInfraction({
          transactionId: transaction.id,
          externalId: externalId || `owempay_v2_${transaction.id}_${Date.now()}`,
          reportDetails: `Devolução MED via OWEMPAY V2 - Status: ${status}`,
          organizationId: transaction.organizationId,
          gatewayName: 'OWEMPAY_V2',
          autoApprove: true, // Aprovação automática para Owempay v2
          reportedBy: 'DEBITED_PARTICIPANT',
          type: 'REFUND_REQUEST'
        });

        if (medResult.success) {
          logger.info('[OWEMPAY_V2][MED] Processamento de MED concluído com sucesso', {
            infractionId: medResult.infractionId,
            transactionId: transaction.id,
            autoApproved: true
          });
        } else {
          logger.error('[OWEMPAY_V2][MED] Erro ao processar MED', {
            error: medResult.error,
            message: medResult.message,
            transactionId: transaction.id,
          });
          // Não falhar o webhook se o processamento do MED falhar
        }

      } catch (medError) {
        logger.error('[OWEMPAY_V2][MED] Erro ao processar MED', {
          error: medError,
          transactionId: transaction.id,
          externalId,
          status
        });
        // Não falhar o webhook se a criação do MED falhar
      }
    }

    // Atualizar status da transação
    logger.info("Updating transaction status", {
      transactionId: transaction.id,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      owempayStatus: status
    });

    await updateTransactionStatus(
      transaction.id,
      internalStatus,
      (status === "paid" || status === "succeeded" || status === "completed") ? new Date() : undefined
    );

    logger.info("Owempay v2 webhook processed successfully", {
      transactionId: transaction.id,
      externalId,
      owempayTransactionId: transactionId,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    return NextResponse.json(
      {
        success: true,
        message: "Webhook processed successfully",
        transactionId: transaction.id,
        status: internalStatus
      },
      { status: 200 }
    );

  } catch (error) {
    logger.error('Error processing Owempay v2 webhook', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
