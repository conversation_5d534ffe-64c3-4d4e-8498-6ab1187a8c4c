import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { headers } from "next/headers";
import * as webhooksApi from "@repo/api/src/webhooks";
import { db } from "@repo/database";
import { createHash } from "crypto";

export async function GET(req: NextRequest) {
  try {
    // Verificar autenticação (session ou API key)
    const headersList = await headers();
    const apiKey = req.headers.get("X-API-Key");

    let userId: string | null = null;
    let authenticatedOrgId: string | null = null;

    if (apiKey) {
      // Autenticação via API key
      const hash = createHash("sha256").update(apiKey).digest("hex");

      const key = await db.api_key.findFirst({
        where: { hash },
        include: {
          organization: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!key) {
        return NextResponse.json(
          { error: "Invalid API key" },
          { status: 401 }
        );
      }

      // Check if API key has expired (expiresAt is optional)
      // Access expiresAt using bracket notation to avoid TypeScript errors
      const expiresAt = (key as any).expiresAt as Date | null | undefined;
      if (expiresAt && expiresAt < new Date()) {
        return NextResponse.json(
          { error: "API key has expired" },
          { status: 401 }
        );
      }

      // Check if user exists
      if (!key.user) {
        return NextResponse.json(
          { error: "Invalid API key configuration" },
          { status: 401 }
        );
      }

      userId = key.createdById;
      authenticatedOrgId = key.organizationId;

      // Update lastUsedAt
      try {
        await db.api_key.update({
          where: { id: key.id },
          data: { lastUsedAt: new Date() }
        });
      } catch (updateError) {
        console.warn("Failed to update lastUsedAt for API key:", updateError);
      }
    } else {
      // Autenticação via session
      const session = await auth.api.getSession({
        headers: headersList,
      });
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      userId = session.user.id;
    }

    // Obter o organizationId da query
    const url = new URL(req.url);
    const orgSlugOrId = url.searchParams.get("organizationId");

    if (!orgSlugOrId) {
      return NextResponse.json(
        { error: "organizationId is required" },
        { status: 400 }
      );
    }

    // Verificar se o organizationId é um slug ou um ID
    let organizationId = orgSlugOrId;

    try {
      // Se o organizationId não for um UUID válido, assumimos que é um slug ou um ID customizado
      if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(orgSlugOrId)) {
        // Primeiro, tentar buscar pelo slug
        const organizationBySlug = await db.organization.findUnique({
          where: { slug: orgSlugOrId },
          select: { id: true }
        });

        if (organizationBySlug) {
          organizationId = organizationBySlug.id;
        } else {
          // Se não encontrar pelo slug, tentar buscar pelo ID diretamente
          const organizationById = await db.organization.findUnique({
            where: { id: orgSlugOrId },
            select: { id: true }
          });

          if (!organizationById) {
            return NextResponse.json(
              { error: "Organization not found" },
              { status: 404 }
            );
          }

          // Se encontrou pelo ID, manter o ID original
          organizationId = orgSlugOrId;
        }
      }

      // Verificar se a organização existe, mesmo se for um UUID
      const organizationExists = await db.organization.findUnique({
        where: { id: organizationId },
        select: { id: true }
      });

      if (!organizationExists) {
        return NextResponse.json(
          { error: "Organization not found" },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error("Error resolving organization ID:", error);
      return NextResponse.json(
        { error: "Failed to resolve organization ID" },
        { status: 500 }
      );
    }

    // For API key auth, validate that the API key belongs to the requested organization
    if (apiKey && authenticatedOrgId && authenticatedOrgId !== organizationId) {
      return NextResponse.json(
        { error: "API key does not have access to this organization" },
        { status: 403 }
      );
    }

    console.log("Listing webhooks for organization:", organizationId);

    // Chamar a API diretamente
    try {
      const webhooks = await webhooksApi.listWebhooks(organizationId);

      // Log the response for debugging
      console.log("Webhooks API response:", JSON.stringify(webhooks));

      // Ensure we're returning a properly formatted response
      if (!webhooks || !webhooks.data) {
        console.error("Invalid response from webhooksApi.listWebhooks", webhooks);
        return NextResponse.json(
          { error: "Invalid response from webhook service" },
          { status: 500 }
        );
      }

      return NextResponse.json(webhooks);
    } catch (error: any) {
      console.error("Error in listWebhooks:", error);
      if (error.message === "Organization not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Error fetching webhooks", error);
    return NextResponse.json(
      { error: "Failed to fetch webhooks" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Debug: Log environment variables for SVIX
    console.log("SVIX Environment Variables:", {
      SVIX_TOKEN: process.env.SVIX_TOKEN ? "Set (value hidden)" : "Not set",
      SVIX_API_URL: process.env.SVIX_API_URL,
      SVIX_APP_ID: process.env.SVIX_APP_ID,
    });

    // Verificar autenticação (session ou API key)
    const headersList = await headers();
    const apiKey = req.headers.get("X-API-Key");

    let userId: string | null = null;
    let authenticatedOrgId: string | null = null;

    if (apiKey) {
      // Autenticação via API key
      const hash = createHash("sha256").update(apiKey).digest("hex");

      const key = await db.api_key.findFirst({
        where: { hash },
        include: {
          organization: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!key) {
        return NextResponse.json(
          { error: "Invalid API key" },
          { status: 401 }
        );
      }

      // Check if API key has expired (expiresAt is optional)
      // Access expiresAt using bracket notation to avoid TypeScript errors
      const expiresAt = (key as any).expiresAt as Date | null | undefined;
      if (expiresAt && expiresAt < new Date()) {
        return NextResponse.json(
          { error: "API key has expired" },
          { status: 401 }
        );
      }

      // Check if user exists
      if (!key.user) {
        return NextResponse.json(
          { error: "Invalid API key configuration" },
          { status: 401 }
        );
      }

      userId = key.createdById;
      authenticatedOrgId = key.organizationId;

      // Update lastUsedAt
      try {
        await db.api_key.update({
          where: { id: key.id },
          data: { lastUsedAt: new Date() }
        });
      } catch (updateError) {
        console.warn("Failed to update lastUsedAt for API key:", updateError);
      }
    } else {
      // Autenticação via session
      const session = await auth.api.getSession({
        headers: headersList,
      });
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      userId = session.user.id;
    }

    // Obter os dados do corpo da requisição
    const body = await req.json();
    const { url, events, organizationId: orgSlugOrId, isActive, useSvix } = body;

    if (!url || !events || !orgSlugOrId) {
      return NextResponse.json(
        { error: "url, events, and organizationId are required" },
        { status: 400 }
      );
    }

    // Verificar se o organizationId é um slug ou um ID
    let organizationId = orgSlugOrId;

    try {
      // Se o organizationId não for um UUID válido, assumimos que é um slug ou um ID customizado
      if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(orgSlugOrId)) {
        // Primeiro, tentar buscar pelo slug
        const organizationBySlug = await db.organization.findUnique({
          where: { slug: orgSlugOrId },
          select: { id: true }
        });

        if (organizationBySlug) {
          organizationId = organizationBySlug.id;
        } else {
          // Se não encontrar pelo slug, tentar buscar pelo ID diretamente
          const organizationById = await db.organization.findUnique({
            where: { id: orgSlugOrId },
            select: { id: true }
          });

          if (!organizationById) {
            return NextResponse.json(
              { error: "Organization not found" },
              { status: 404 }
            );
          }

          // Se encontrou pelo ID, manter o ID original
          organizationId = orgSlugOrId;
        }
      }

      // Verificar se a organização existe, mesmo se for um UUID
      const organizationExists = await db.organization.findUnique({
        where: { id: organizationId },
        select: { id: true }
      });

      if (!organizationExists) {
        return NextResponse.json(
          { error: "Organization not found" },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error("Error resolving organization ID:", error);
      return NextResponse.json(
        { error: "Failed to resolve organization ID" },
        { status: 500 }
      );
    }

    // For API key auth, validate that the API key belongs to the requested organization
    if (apiKey && authenticatedOrgId && authenticatedOrgId !== organizationId) {
      return NextResponse.json(
        { error: "API key does not have access to this organization" },
        { status: 403 }
      );
    }

    console.log("Creating webhook:", { url, events, organizationId, isActive, useSvix });

    // Chamar a API diretamente
    try {
      console.log("Creating webhook with params:", {
        url,
        eventsCount: events.length,
        organizationId,
        isActive,
        useSvix
      });

      const webhook = await webhooksApi.createWebhook({
        url,
        events,
        organizationId,
        isActive,
        useSvix,
      });

      console.log("Webhook created successfully:", webhook);

      return NextResponse.json(webhook, { status: 201 });
    } catch (error: any) {
      console.error("Error in createWebhook:", error);

      if (error.message === "A webhook with this URL already exists for this organization") {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      if (error.message === "Organization not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }

      // Handle SVIX-specific errors
      if (error.message && error.message.includes("SVIX")) {
        return NextResponse.json(
          {
            error: "Error with SVIX integration",
            details: error.message,
            suggestion: "Try creating the webhook without SVIX integration"
          },
          { status: 500 }
        );
      }

      throw error;
    }
  } catch (error) {
    console.error("Error creating webhook", error);
    return NextResponse.json(
      { error: "Failed to create webhook" },
      { status: 500 }
    );
  }
}
