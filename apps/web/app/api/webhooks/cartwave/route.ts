import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { type NextRequest, NextResponse } from "next/server";

// Test endpoint
export async function GET() {
	return NextResponse.json({ message: "Cartwave webhook endpoint is working" });
}

/**
 * Validate Cartwave webhook signature
 * Note: Cartwave documentation doesn't specify signature validation,
 * so we'll implement basic validation if needed in the future
 */
function validateCartwaveWebhook(payload: string, signature?: string): boolean {
	// For now, we'll skip signature validation as Cartwave docs don't specify it
	// In production, you might want to implement IP whitelist or other security measures

	// Skip validation in development or if explicitly disabled
	if (
		process.env.NODE_ENV === "development" ||
		process.env.SKIP_WEBHOOK_VALIDATION === "true"
	) {
		return true;
	}

	// If signature validation is implemented in the future, add it here
	return true;
}

/**
 * Find transaction by multiple criteria
 */
async function findTransactionByMultipleCriteria(criteria: {
	externalId?: string;
	internalId?: string;
}): Promise<any> {
	const { externalId, internalId } = criteria;

	logger.info("Searching for transaction with multiple criteria", criteria);

	// 1. First try to find by our internal ID
	if (internalId) {
		const transaction = await db.transaction.findUnique({
			where: { id: internalId },
		});

		if (transaction) {
			logger.info("Transaction found by internal ID", {
				transactionId: internalId,
			});
			return { ...transaction, searchMethod: "internal_id" };
		}
	}

	// 2. If not found, try to find by external ID
	if (externalId) {
		const transaction = await db.transaction.findFirst({
			where: { externalId: externalId },
		});

		if (transaction) {
			logger.info("Transaction found by external ID", { externalId });
			return { ...transaction, searchMethod: "external_id" };
		}
	}

	logger.warn("Transaction not found with any criteria", criteria);
	return null;
}

/**
 * Main webhook handler for Cartwave notifications
 */
export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const signature =
			request.headers.get("x-signature") || request.headers.get("signature") || undefined;

		logger.info("Received Cartwave webhook", {
			headers: Object.fromEntries(request.headers.entries()),
			bodyLength: body.length,
			signature: signature ? "present" : "missing",
		});

		// Validate webhook signature (currently skipped)
		if (!validateCartwaveWebhook(body, signature)) {
			logger.warn("Invalid Cartwave webhook signature");
			return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
		}

		// Parse JSON payload
		let payload: any;
		try {
			payload = JSON.parse(body);
		} catch (error) {
			logger.error("Invalid JSON in Cartwave webhook", { error, body });
			return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
		}

		logger.info("Parsed Cartwave webhook payload", { payload });

		// Check if this is a withdrawal webhook
		if (payload.withdrawStatusId) {
			// Handle withdrawal webhook using provider handler
			const { webhookHandler } = await import(
				"@repo/payments/provider/cartwave"
			);
			const result = await webhookHandler(payload);

			if (result.success) {
				logger.info("Successfully processed Cartwave withdrawal webhook", {
					withdrawStatusId: payload.withdrawStatusId,
					externalCode: payload.externalCode,
				});
			} else {
				logger.error("Failed to process Cartwave withdrawal webhook", {
					withdrawStatusId: payload.withdrawStatusId,
					error: result.message,
				});
			}

			return NextResponse.json({ success: true });
		}

		// Extract webhook data for payment webhooks
		const { code, externalCode, orderId, storeId, paymentMethod, status } =
			payload;

		// Basic validation for payment webhooks
		if (!code || !status) {
			logger.warn("Missing required fields in Cartwave payment webhook", { payload });
			return NextResponse.json(
				{ error: "Missing required fields" },
				{ status: 400 },
			);
		}

		// Create deduplication key
		const webhookId = code;
		const deduplicationKey = `cartwave-${webhookId}-${status}`;

		logger.info("Processing CARTWAVE webhook with deduplication", {
			webhookId,
			status,
			deduplicationKey
		});

		// Check for recent webhook processing (last 5 minutes)
		const recentTransaction = await db.transaction.findFirst({
			where: {
				AND: [
					{
						OR: [
							{
								metadata: {
									path: ['lastWebhook', 'webhookId'],
									equals: webhookId
								}
							},
							{
								metadata: {
									path: ['deduplicationKey'],
									equals: deduplicationKey
								}
							}
						]
					},
					{
						updatedAt: {
							gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
						}
					}
				]
			}
		});

		if (recentTransaction) {
			logger.info("CARTWAVE webhook already processed recently, skipping", {
				webhookId,
				deduplicationKey,
				transactionId: recentTransaction.id,
				lastProcessed: recentTransaction.updatedAt,
				timeSinceLastProcessed: Date.now() - recentTransaction.updatedAt.getTime()
			});
			return NextResponse.json({ success: true, message: "Already processed" });
		}

		// Handle creation webhooks (pending status)
		if (status === "pending") {
			logger.info("Processing Cartwave creation webhook", { code, status });

			// Find transaction by external code (our internal transaction ID)
			let transaction = null;
			if (externalCode) {
				transaction = await db.transaction.findUnique({
					where: { id: externalCode },
				});
			}

			// If not found, try to find by external ID
			if (!transaction && code) {
				transaction = await db.transaction.findFirst({
					where: { externalId: code },
				});
			}

			if (transaction) {
				// Update transaction with Cartwave data
				const existingMetadata = transaction.metadata || {};
				const updatedMetadata = {
					...(typeof existingMetadata === "object" ? existingMetadata : {}),
					cartwaveOrderId: orderId,
					cartwaveStoreId: storeId,
					lastWebhook: {
						receivedAt: new Date().toISOString(),
						status,
						webhookId
					},
					deduplicationKey: deduplicationKey,
					cartwaveWebhook: {
						...payload,
						receivedAt: new Date().toISOString(),
					},
				};

				// Update external ID if not set
				const updateData: any = {
					metadata: updatedMetadata,
				};

				if (!transaction.externalId && code) {
					updateData.externalId = code;
				}

				await db.transaction.update({
					where: { id: transaction.id },
					data: updateData,
				});

				// Send SVIX webhook for transaction creation confirmation
				try {
					const { triggerTransactionEvents } = await import(
						"@repo/payments/src/webhooks/events"
					);
					await triggerTransactionEvents(
						transaction,
						transaction.status,
						false,
					);

					logger.info("Sent SVIX webhook for Cartwave transaction creation", {
						transactionId: transaction.id,
						externalId: code,
					});
				} catch (svixError) {
					logger.error("Error sending SVIX webhook for Cartwave creation", {
						error: svixError,
						transactionId: transaction.id,
					});
				}

				logger.info("Updated transaction from Cartwave creation webhook", {
					transactionId: transaction.id,
					externalId: code,
				});
			} else {
				logger.warn("Transaction not found for Cartwave creation webhook", {
					code,
					externalCode,
					orderId,
				});
			}

			return NextResponse.json({ success: true });
		}

		// Handle status update webhooks
		const relevantStatuses = ["paid", "refused", "refunded", "infraction"];
		if (relevantStatuses.includes(status)) {
			logger.info("Processing Cartwave status webhook", { code, status });

			// Find transaction using multiple criteria
			const transaction = await findTransactionByMultipleCriteria({
				externalId: code,
				internalId: externalCode,
			});

			if (!transaction) {
				logger.warn("Transaction not found for Cartwave status webhook", {
					code,
					externalCode,
					status,
				});
				return NextResponse.json({
					success: true,
					message: "Transaction not found",
				});
			}

			logger.info("Found transaction for Cartwave webhook", {
				transactionId: transaction.id,
				searchMethod: transaction.searchMethod,
				currentStatus: transaction.status,
				newStatus: status,
			});

			// Update transaction metadata with webhook information
			const updatedMetadata = {
				...transaction.metadata,
				lastWebhook: {
					receivedAt: new Date().toISOString(),
					status,
					webhookId
				},
				deduplicationKey: deduplicationKey,
				webhookUpdate: {
					provider: "CARTWAVE",
					webhookSource: "cartwave",
					updatedAt: new Date().toISOString(),
					webhookId,
					originalStatus: status
				},
				updatedByWebhook: true,
				cartwaveWebhook: {
					...payload,
					receivedAt: new Date().toISOString(),
				},
			};

			await db.transaction.update({
				where: { id: transaction.id },
				data: { metadata: updatedMetadata },
			});

			// Process the webhook using the provider handler
			const { webhookHandler } = await import(
				"@repo/payments/provider/cartwave"
			);
			const result = await webhookHandler(payload);

			if (result.success) {
				logger.info("Successfully processed Cartwave payment webhook", {
					transactionId: transaction.id,
					status,
				});
			} else {
				logger.error("Failed to process Cartwave payment webhook", {
					transactionId: transaction.id,
					status,
					error: result.message,
				});
			}
		} else {
			logger.info("Ignoring Cartwave webhook with irrelevant status", {
				code,
				status,
			});
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		logger.error("Error processing Cartwave webhook", { error });
		// Return success to prevent Cartwave from retrying
		return NextResponse.json(
			{ success: true, message: "Webhook received with errors" },
			{ status: 200 },
		);
	}
}

/**
 * Map Cartwave status to internal status
 */
function mapCartwaveStatusToInternal(cartwaveStatus: string): string {
	if (!cartwaveStatus) return "PENDING";

	switch (cartwaveStatus.toLowerCase()) {
		case "pending":
			return "PENDING";
		case "paid":
			return "APPROVED";
		case "refused":
			return "REJECTED";
		case "refunded":
			return "REFUNDED";
		case "infraction":
			return "REJECTED";
		default:
			logger.warn("Unknown Cartwave status", { cartwaveStatus });
			return "PENDING";
	}
}
