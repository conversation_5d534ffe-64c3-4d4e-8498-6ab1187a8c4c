import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { webhook<PERSON>andler } from "@repo/payments/provider/ecomovi";
import crypto from "crypto";

// Endpoint GET para teste do webhook
export async function GET(_req: NextRequest) {
  logger.info("Ecomovi webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Ecomovi webhook endpoint is working" });
}

// Validar assinatura do webhook da Ecomovi (se necessário)
async function validateEcomoviWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Obter headers da requisição
    const headersList = Object.fromEntries(req.headers);

    logger.info("Ecomovi webhook headers for signature validation", {
      headers: headersList
    });

    // Verificar assinatura em possíveis headers
    const possibleSignatureHeaders = [
      "x-ecomovi-signature",
      "x-signature",
      "ecomovi-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Tentar encontrar assinatura em qualquer um dos headers possíveis
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        break;
      }
    }

    // Se não encontrou assinatura em nenhum header
    if (!signature) {
      logger.warn('Missing Ecomovi webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders
      });

      // Verificar se validação de assinatura deve ser ignorada
      if (process.env.ECOMOVI_BYPASS_SIGNATURE_VALIDATION === "true") {
        logger.warn('Bypassing signature validation due to ECOMOVI_BYPASS_SIGNATURE_VALIDATION=true');
        return true;
      }

      // Em desenvolvimento, permitir webhooks sem assinatura
      if (process.env.NODE_ENV === 'development') {
        logger.warn('Allowing webhook without signature in development mode');
        return true;
      }

      // Se não há segredo configurado, permitir
      if (!process.env.ECOMOVI_WEBHOOK_SECRET) {
        logger.warn('ECOMOVI_WEBHOOK_SECRET not configured, allowing webhook without signature');
        return true;
      }

      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    // Obter o segredo do webhook das variáveis de ambiente
    const webhookSecret = process.env.ECOMOVI_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('ECOMOVI_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Computar a assinatura esperada
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(body);
    const computedSignature = hmac.digest('hex');

    // Comparar assinaturas - usar comparação segura para prevenir ataques de timing
    let isValid = false;
    try {
      isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (error) {
      // Se os buffers têm tamanhos diferentes, timingSafeEqual lança erro
      // Neste caso, as assinaturas definitivamente não coincidem
      logger.warn('Error in signature comparison, likely different lengths', { error });
      isValid = false;
    }

    if (!isValid) {
      logger.warn('Invalid Ecomovi webhook signature', {
        receivedSignature: signature,
        computedSignature,
        signatureHeaderUsed
      });

      // Verificar se validação de assinatura deve ser ignorada
      if (process.env.ECOMOVI_BYPASS_SIGNATURE_VALIDATION === "true") {
        logger.warn('Bypassing invalid signature due to ECOMOVI_BYPASS_SIGNATURE_VALIDATION=true');
        return true;
      }

      // Em desenvolvimento, permitir webhooks com assinatura inválida
      if (process.env.NODE_ENV === 'development') {
        logger.warn('Allowing webhook with invalid signature in development mode');
        return true;
      }
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating Ecomovi webhook signature', { error });

    // Verificar se validação de assinatura deve ser ignorada
    if (process.env.ECOMOVI_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing signature validation error due to ECOMOVI_BYPASS_SIGNATURE_VALIDATION=true');
      return true;
    }

    // Em desenvolvimento, permitir webhooks mesmo com erro de validação
    if (process.env.NODE_ENV === 'development') {
      logger.warn('Allowing webhook despite signature validation error in development mode');
      return true;
    }

    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Obter o corpo da requisição para validação de assinatura
    const rawBody = await req.text();

    // Log headers para debug
    const headersObj = Object.fromEntries(req.headers);

    // Log informações do ambiente para ajudar com debug
    const isVercel = !!process.env.VERCEL;
    const vercelEnv = process.env.VERCEL_ENV || 'unknown';

    logger.info("Received Ecomovi webhook", {
      headers: headersObj,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        isVercel,
        vercelEnv
      }
    });

    // Verificar se o corpo está vazio
    if (!rawBody || rawBody.trim() === '') {
      logger.error('Empty webhook payload received');
      return NextResponse.json({ error: 'Empty payload' }, { status: 400 });
    }

    // Verificar se validação de webhook deve ser ignorada usando variável de ambiente global
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
    } else if (process.env.ECOMOVI_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing Ecomovi webhook signature validation due to ECOMOVI_BYPASS_SIGNATURE_VALIDATION=true');
    } else if (process.env.NODE_ENV === 'development') {
      logger.warn('Bypassing webhook signature validation in development mode');
    } else {
      // Validar assinatura do webhook apenas em produção e se não estiver configurado para bypass
      if (process.env.NODE_ENV === 'production') {
        try {
          const isValid = await validateEcomoviWebhook(req, rawBody);
          if (!isValid) {
            logger.error('Invalid webhook signature');
            return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
          }
        } catch (signatureError) {
          logger.error('Error during signature validation', { error: signatureError });
          return NextResponse.json({ error: 'Signature validation error' }, { status: 401 });
        }
      }
    }

    logger.info("Ecomovi webhook endpoint called - processing payload");

    // Criar uma nova Request com o corpo já lido
    const newRequest = new Request(req.url, {
      method: req.method,
      headers: req.headers,
      body: rawBody
    });

    // Processar webhook usando o handler do provedor Ecomovi
    const response = await webhookHandler(newRequest);

    // O handler já retorna uma Response, então podemos retorná-la diretamente
    return response;
  } catch (error) {
    logger.error("Error in Ecomovi webhook endpoint:", { error });

    // Sempre retornar 200 OK para evitar reenvios desnecessários
    return NextResponse.json(
      {
        success: true,
        message: "Webhook processed with errors"
      },
      { status: 200 }
    );
  }
}
