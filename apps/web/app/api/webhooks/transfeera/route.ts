import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { syncTransferStatus } from "@repo/payments/provider/transfeera";
import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";
import { TransactionStatus } from "@prisma/client";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { calculateTransactionFees } from "@repo/payments/src/taxes/calculator";

// Secret para validação de assinatura da Transfeera
const TRANSFEERA_SIGNATURE_SECRET = process.env.TRANSFEERA_SIGNATURE_SECRET || "f54f46db2421ae556b8d0054b8e90afa9536cf1a39adcaa4712e4053bf509272466a26d5";

// Função para validar a assinatura do webhook da Transfeera
function validateTransfeeraSignature(payload: string, signature: string | null): boolean {
  if (!signature) {
    logger.warn("Assinatura ausente no webhook da Transfeera");
    return false;
  }

  try {
    // Calcular a assinatura esperada usando HMAC SHA-256
    const hmac = crypto.createHmac("sha256", TRANSFEERA_SIGNATURE_SECRET);
    hmac.update(payload);
    const expectedSignature = hmac.digest("hex");

    // Comparar a assinatura recebida com a esperada usando comparação segura
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );

    if (!isValid) {
      logger.warn("Assinatura inválida no webhook da Transfeera", {
        receivedSignature: signature,
        expectedSignature
      });
    }

    return isValid;
  } catch (error) {
    logger.error("Erro ao validar assinatura da Transfeera", { error });
    return false;
  }
}

// Função para verificar se é uma requisição de teste da Transfeera
function isTransfeeraTestRequest(req: NextRequest, payload: any): boolean {
  try {
    // Verificar headers específicos que podem indicar uma requisição de teste
    const userAgent = req.headers.get("user-agent") || "";
    if (userAgent.toLowerCase().includes("transfeera") || userAgent.toLowerCase().includes("webhook-test")) {
      return true;
    }

    // Verificar se o payload contém indicações de que é um teste
    if (payload.test === true || payload.environment === "test" || payload.sandbox === true) {
      return true;
    }

    // Verificar se é uma requisição de validação
    if (payload.validation === true || payload.ping === true) {
      return true;
    }

    // Verificar se o payload tem apenas um campo "test" ou similar
    const keys = Object.keys(payload);
    if (keys.length === 1 && (keys[0] === "test" || keys[0] === "validation" || keys[0] === "ping")) {
      return true;
    }

    return false;
  } catch (error) {
    // Se ocorrer um erro, assumir que não é um teste
    return false;
  }
}

// Função auxiliar para mapear status da Transfeera para nosso formato interno
function mapTransfeeraStatusToInternal(status: string | undefined): string {
  // Verificar se o status é nulo ou indefinido
  if (!status) {
    return "PENDING";
  }

  const statusMap: Record<string, string> = {
    // Status em inglês
    "pending": "PENDING",
    "processing": "PROCESSING",
    "waiting": "PROCESSING",
    "approved": "APPROVED",
    "paid": "APPROVED",
    "completed": "APPROVED",
    "success": "APPROVED",
    "received": "APPROVED",
    "rejected": "REJECTED",
    "refused": "REJECTED",
    "failed": "REJECTED",
    "cancelled": "CANCELED",
    "canceled": "CANCELED",

    // Status em português (Transfeera)
    "finalizado": "APPROVED",
    "concluido": "APPROVED",
    "concluído": "APPROVED",
    "processando": "PROCESSING",
    "aguardando": "PROCESSING",
    "rejeitado": "REJECTED",
    "falha": "REJECTED",
    "cancelado": "CANCELED",

    // Status específicos da Transfeera baseados na documentação
    "CRIADA": "PROCESSING",
    "PROCESSANDO": "PROCESSING",
    "FINALIZADA": "APPROVED",
    "REJEITADA": "REJECTED",
    "CANCELADA": "CANCELED"
  };

  const lowerStatus = status.toLowerCase();
  const mappedStatus = statusMap[lowerStatus] || "PENDING";

  // Log para depuração de mapeamento de status
  if (!statusMap[lowerStatus]) {
    logger.warn(`🔍 [TRANSFEERA WEBHOOK] Status desconhecido: "${status}" - mapeado para "PENDING"`);
  } else {
    logger.info(`🔍 [TRANSFEERA WEBHOOK] Status mapeado: "${status}" -> "${mappedStatus}"`);
  }

  return mappedStatus;
}

// Função para criar um ID único do webhook baseado no payload
function createWebhookId(payload: any): string {
  try {
    // Tentar usar campos únicos do payload para criar um ID
    const id = payload.id || payload.data?.id || payload.transfer_id || payload.data?.transfer_id;
    const timestamp = payload.timestamp || payload.date || payload.data?.date || Date.now();
    const status = payload.status || payload.data?.status || "unknown";

    // Criar hash baseado nos dados do webhook
    const dataString = `${id}-${timestamp}-${status}`;
    const hash = crypto.createHash('md5').update(dataString).digest('hex').substring(0, 8);

    return `transfeera-${hash}`;
  } catch (error) {
    // Fallback para um ID baseado no timestamp
    return `transfeera-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Gerar ID único para rastreamento de logs
    const requestId = crypto.randomUUID();

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Recebendo webhook`, {
      url: req.url,
      method: req.method,
      headers: Object.fromEntries(req.headers.entries()),
      timestamp: new Date().toISOString()
    });

    // Obter o corpo da requisição como texto
    const rawBody = await req.text();

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Raw body recebido`, {
      rawBodyLength: rawBody.length,
      rawBodyPreview: rawBody.substring(0, 500),
      isEmpty: rawBody.trim() === ""
    });

    // Parse do payload JSON
    let payload;
    try {
      payload = JSON.parse(rawBody);
      logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] JSON parseado com sucesso`, {
        payloadKeys: Object.keys(payload),
        payloadType: typeof payload,
        isArray: Array.isArray(payload),
        hasEvent: !!payload.event,
        hasData: !!payload.data,
        dataKeys: payload.data ? Object.keys(payload.data) : [],
        version: payload.version,
        object: payload.object
      });
    } catch (e) {
      logger.error(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Erro ao parsear JSON`, {
        error: e instanceof Error ? e.message : String(e),
        rawBody: rawBody.substring(0, 1000)
      });

      return NextResponse.json({
        success: false,
        error: "Invalid JSON payload"
      }, { status: 400 });
    }

    // Verificar se é um payload vazio
    if (Object.keys(payload).length === 0) {
      logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Payload vazio detectado`);
      return NextResponse.json({
        success: true,
        message: "Webhook received successfully (empty payload)"
      });
    }

    // Verificar se é uma requisição de teste
    if (isTransfeeraTestRequest(req, payload)) {
      logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Requisição de teste detectada`);
      return NextResponse.json({
        success: true,
        message: "Test webhook received successfully"
      });
    }

    // Extrair dados principais do webhook baseado na documentação da Transfeera
    const webhookData = extractTransfeeraWebhookData(payload, requestId);

    if (!webhookData.isValid) {
      logger.warn(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Webhook inválido ou não suportado`, {
        payload,
        reason: webhookData.reason
      });

      return NextResponse.json({
        success: true,
        message: "Webhook received but not processed: " + webhookData.reason
      });
    }

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Dados extraídos do webhook`, webhookData);

    // Enhanced deduplication using multiple identifiers
    const webhookId = createWebhookId(payload);
    const deduplicationKey = `transfeera-${webhookData.transferId || 'unknown'}-${webhookData.eventType}-${webhookData.status}-${webhookId}`;

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Processando com deduplicação`, {
      webhookId,
      transferId: webhookData.transferId,
      status: webhookData.status,
      eventType: webhookData.eventType,
      deduplicationKey
    });

    // Check if we processed this exact webhook recently (last 5 minutes)
    const recentTransaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              {
                metadata: {
                  path: ['lastWebhookId'],
                  equals: webhookId
                }
              },
              {
                metadata: {
                  path: ['deduplicationKey'],
                  equals: deduplicationKey
                }
              }
            ]
          },
          {
            updatedAt: {
              gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
            }
          }
        ]
      }
    });

    if (recentTransaction) {
      logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Webhook já processado recentemente, ignorando`, {
        webhookId,
        deduplicationKey,
        transactionId: recentTransaction.id,
        lastProcessed: recentTransaction.updatedAt,
        timeSinceLastProcessed: Date.now() - recentTransaction.updatedAt.getTime()
      });
      return NextResponse.json({ success: true, message: "Already processed" });
    }

    // Buscar transação usando estratégia aprimorada
    const transaction = await findTransactionByTransfeeraData(webhookData, requestId);

    if (!transaction) {
      logger.warn(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Transação não encontrada`, {
        transferId: webhookData.transferId,
        integrationId: webhookData.integrationId,
        searchMethods: ['externalId', 'metadata.integrationId', 'metadata.transferId'],
        recommendation: "Verificar se a transação foi criada corretamente"
      });

      // Salvar webhook órfão para análise posterior
      await saveOrphanWebhook(payload, requestId, webhookData);

      return NextResponse.json({
        success: true,
        message: "Transaction not found, webhook saved for analysis"
      });
    }

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Transação encontrada`, {
          transactionId: transaction.id,
      currentStatus: transaction.status,
      newStatus: webhookData.mappedStatus,
      externalId: transaction.externalId,
      organizationId: transaction.organizationId
    });

    // Verificar se a transição de status é válida
    if (!isValidStatusTransition(transaction.status, webhookData.mappedStatus)) {
      logger.warn(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Transição de status inválida bloqueada`, {
              transactionId: transaction.id,
        currentStatus: transaction.status,
        requestedStatus: webhookData.mappedStatus,
        originalStatus: webhookData.status
      });
      return NextResponse.json({ success: true, message: "Invalid status transition blocked" });
    }

              // Processar a transação
     return await processSingleTransaction(
       transaction,
       webhookData.mappedStatus,
       payload,
       webhookData.batchId || undefined,
       deduplicationKey
     );

  } catch (error) {
    const requestId = crypto.randomUUID();
    logger.error(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Erro não tratado`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
          });

          return NextResponse.json({
      success: false,
      error: "Internal server error"
          }, { status: 500 });
        }
      }

// Função para extrair dados do webhook baseado na documentação da Transfeera
function extractTransfeeraWebhookData(payload: any, requestId: string) {
  logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Extraindo dados do webhook`, {
    payloadStructure: {
      hasId: !!payload.id,
      hasData: !!payload.data,
      hasVersion: !!payload.version,
      hasObject: !!payload.object,
      hasEvent: !!payload.event,
      version: payload.version,
      object: payload.object,
      event: payload.event
    }
  });

  // Estrutura baseada na documentação: https://docs.transfeera.dev/reference/eventos-1
  const webhookData = {
    isValid: false,
    reason: "",
    eventId: payload.id,
    version: payload.version,
    object: payload.object,
    eventType: payload.event || payload.object || "unknown",
    date: payload.date,
    transferId: null as string | null,
    integrationId: null as string | null,
    batchId: null as string | null,
    status: null as string | null,
    mappedStatus: "PENDING" as string,
    data: payload.data || {}
  };

  // Extrair dados do campo 'data' se existir
  if (payload.data) {
    webhookData.transferId = payload.data.id || payload.data.transfer_id;
    webhookData.integrationId = payload.data.integration_id;
    webhookData.batchId = payload.data.batch_id;
    webhookData.status = payload.data.status;
  } else {
    // Fallback para estrutura direta
    webhookData.transferId = payload.transfer_id || payload.id;
    webhookData.integrationId = payload.integration_id;
    webhookData.batchId = payload.batch_id;
    webhookData.status = payload.status;
  }

  // Mapear status para formato interno
  if (webhookData.status) {
    webhookData.mappedStatus = mapTransfeeraStatusToInternal(webhookData.status);
  }

  // Validar se temos dados suficientes
  if (!webhookData.transferId && !webhookData.integrationId) {
    webhookData.reason = "Nenhum ID de transferência ou integração encontrado";
    return webhookData;
  }

  if (!webhookData.status) {
    webhookData.reason = "Status não encontrado no webhook";
    return webhookData;
  }

  webhookData.isValid = true;

  logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Dados extraídos com sucesso`, webhookData);

  return webhookData;
}

// Função para buscar transação usando dados da Transfeera
async function findTransactionByTransfeeraData(webhookData: any, requestId: string) {
  const searchCriteria = [];

  // Prioridade 1: Buscar por externalId (transferId)
  if (webhookData.transferId) {
    searchCriteria.push({
      method: "externalId",
      value: webhookData.transferId
    });
  }

  // Prioridade 2: Buscar por integrationId nos metadados
  if (webhookData.integrationId) {
    searchCriteria.push({
      method: "metadata.integrationId",
      value: webhookData.integrationId
    });
  }

  // Prioridade 3: Buscar por transferId nos metadados
  if (webhookData.transferId) {
    searchCriteria.push({
      method: "metadata.transferId",
      value: webhookData.transferId
    });
  }

  logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Buscando transação`, {
    searchCriteria,
    totalMethods: searchCriteria.length
  });

  for (const criteria of searchCriteria) {
    let transaction = null;

    try {
      if (criteria.method === "externalId") {
        transaction = await db.transaction.findFirst({
          where: {
            externalId: criteria.value,
            type: "SEND"
          }
        });
      } else if (criteria.method === "metadata.integrationId") {
        transaction = await db.transaction.findFirst({
          where: {
            type: "SEND",
            metadata: {
              path: ['integrationId'],
              equals: criteria.value
            }
          }
        });
      } else if (criteria.method === "metadata.transferId") {
        transaction = await db.transaction.findFirst({
        where: {
            type: "SEND",
          metadata: {
              path: ['transferId'],
              equals: criteria.value
            }
          }
        });
      }

      if (transaction) {
        logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Transação encontrada via ${criteria.method}`, {
          transactionId: transaction.id,
          method: criteria.method,
          searchValue: criteria.value,
          externalId: transaction.externalId
        });

                 // Atualizar externalId se necessário
         if (criteria.method !== "externalId" && webhookData.transferId && transaction.externalId !== webhookData.transferId) {
           await db.transaction.update({
             where: { id: transaction.id },
             data: { externalId: webhookData.transferId || undefined }
           });

          logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] ExternalId atualizado`, {
            transactionId: transaction.id,
            oldExternalId: transaction.externalId,
            newExternalId: webhookData.transferId
          });
        }

        return transaction;
      }
    } catch (error) {
      logger.error(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Erro na busca via ${criteria.method}`, {
        method: criteria.method,
        value: criteria.value,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  return null;
}

// Função para salvar webhooks órfãos para análise
async function saveOrphanWebhook(payload: any, requestId: string, webhookData: any) {
  try {
    await db.webhook_event.create({
          data: {
        type: "transfeera.orphan",
        organizationId: null,
        transactionId: null,
        payload: {
          ...payload,
          debug: {
            requestId,
            extractedData: webhookData,
            timestamp: new Date().toISOString(),
            reason: "Transaction not found"
          }
        }
      }
    });

    logger.info(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Webhook órfão salvo para análise`);
  } catch (error) {
    logger.error(`🔍 [TRANSFEERA WEBHOOK ${requestId}] Erro ao salvar webhook órfão`, {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Importar a função updateTransactionStatus para disparar eventos de webhook
import { updateTransactionStatus } from "@repo/payments/src/transactions/service";

// Função auxiliar para processar uma única transação
async function processSingleTransaction(transaction: any, internalStatus: string, payload: any, batchId?: string, deduplicationKey?: string) {
  const requestId = crypto.randomUUID();
  logger.info(`[${requestId}] Processing single transaction - DIAGNOSTIC MODE`, {
    transactionId: transaction.id,
    externalId: transaction.externalId,
    currentStatus: transaction.status,
    newStatus: internalStatus,
    batchId,
    transactionType: transaction.type,
    transactionAmount: transaction.amount,
    transactionCreatedAt: transaction.createdAt,
    transactionMetadata: transaction.metadata,
    payloadSummary: JSON.stringify(payload).substring(0, 200)
  });

  // Extrair informações adicionais do payload para logs
  const payloadInfo = payload.version === 'v1' ? {
    version: payload.version,
    object: payload.object,
    dataId: payload.data?.id,
    dataStatus: payload.data?.status,
    integration_id: payload.data?.integration_id
  } : {
    id: payload.id,
    status: payload.status,
    type: payload.type
  };

  // Verificar se a transação já está com status final
  if (transaction.status === "APPROVED" || transaction.status === "REJECTED" || transaction.status === "CANCELED") {
    logger.info(`[${requestId}] Transaction already finalized, ignoring webhook`, {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      requestedStatus: internalStatus,
      payloadInfo,
      payloadSummary: JSON.stringify(payload).substring(0, 200)
    });

    // Mesmo que o status seja final, vamos atualizar os metadados para registrar que recebemos o webhook
    try {
      const metadata = transaction.metadata as any || {};
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          metadata: {
            ...metadata,
            webhookUpdate: {
              processedAt: new Date().toISOString(),
              oldStatus: transaction.status,
              newStatus: transaction.status, // Mantém o mesmo status
              payload: payload,
              batchId: batchId,
              ignored: true,
              reason: "Transaction already finalized"
            }
          }
        }
      });

      logger.info(`[${requestId}] Updated transaction metadata for finalized transaction`, {
        transactionId: transaction.id
      });
    } catch (updateError) {
      logger.error(`[${requestId}] Error updating metadata for finalized transaction`, {
        error: updateError,
        transactionId: transaction.id
      });
    }

    return NextResponse.json({
      success: true,
      message: "Transaction already finalized",
      transaction: {
        id: transaction.id,
        status: transaction.status
      }
    });
  }

  // Se o status for final (APPROVED, REJECTED, CANCELED), processar a atualização de saldo
  if (["APPROVED", "REJECTED", "CANCELED"].includes(internalStatus)) {
    // Recuperar os metadados da transação para obter o valor total (incluindo taxa)
    const metadata = transaction.metadata as any || {};

    // Verificar se temos informações de taxas nos metadados
    let totalAmount, fee;

    if (metadata.fees && metadata.totalAmount) {
      // Usar as taxas calculadas pelo processTransferFees
      fee = metadata.fees.totalFee;
      totalAmount = metadata.totalAmount;

      logger.info("Usando taxas calculadas pelo processTransferFees", {
        transactionId: transaction.id,
        percentFee: metadata.fees.percentFee,
        fixedFee: metadata.fees.fixedFee,
        totalFee: metadata.fees.totalFee,
        totalAmount: metadata.totalAmount
      });
    } else if (metadata.fee !== undefined && metadata.totalAmount !== undefined) {
      // Usar o formato antigo de metadados
      fee = metadata.fee;
      totalAmount = metadata.totalAmount;

      logger.info("Usando formato antigo de metadados para taxas", {
        transactionId: transaction.id,
        fee: metadata.fee,
        totalAmount: metadata.totalAmount
      });
    } else {
      // Se não temos informações de taxas, usar apenas o valor da transação
      fee = 0;
      totalAmount = transaction.amount;

      logger.warn("Nenhuma informação de taxas encontrada nos metadados", {
        transactionId: transaction.id,
        amount: transaction.amount,
        metadata: metadata
      });
    }

    logger.info("Processing Transfeera webhook with fee", {
      transactionId: transaction.id,
      status: internalStatus,
      amount: transaction.amount,
      fee,
      totalAmount
    });

    // Atualizar a transação usando updateTransactionStatus para disparar eventos de webhook
    logger.info(`[${requestId}] Atualizando transação - DIAGNOSTIC MODE`, {
      transactionId: transaction.id,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      willSetPaymentDate: internalStatus === "APPROVED",
      paymentDate: internalStatus === "APPROVED" ? new Date().toISOString() : undefined
    });

    // Store previous status for SVIX event
    const previousStatus = transaction.status;

    try {
      // Update transaction metadata with webhook information and deduplication data (following Pluggou PIX pattern)
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          metadata: {
            ...metadata,
            // Add deduplication tracking
            lastWebhookId: deduplicationKey ? deduplicationKey.split('-')[3] : undefined, // Extract webhook ID from deduplication key
            deduplicationKey: deduplicationKey,
            webhookUpdate: {
              processedAt: new Date().toISOString(),
              oldStatus: transaction.status,
              newStatus: internalStatus,
              payload: payload,
              batchId: batchId,
              webhookType: payload.type,
              originalWebhookStatus: payload.data?.status || payload.status,
              wasFailureWebhookOverridden: payload.type === "pix.out.failure" && internalStatus === "APPROVED",
              pixKeyType: payload.data?.pixKeyType,
              pixKey: payload.data?.pixKey,
              requiresManualReview: payload.type === "pix.out.failure" && (
                payload.data?.pixKeyType === "PHONE" ||
                payload.data?.pixKeyType === "RANDOM"
              ),
              // Add provider information
              provider: "TRANSFEERA"
              // Note: Removed webhookSource to allow proper event triggering
            }
          }
        }
      });

      // Update transaction status and trigger webhook events
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: internalStatus as any,
          paymentAt: internalStatus === "APPROVED" ? new Date() : undefined,
          updatedAt: new Date()
        }
      });

      // Trigger webhook events using the standard service
      const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
      await triggerTransactionEvents(updatedTransaction, previousStatus);

      logger.info(`[${requestId}] Transação atualizada com sucesso e eventos de webhook disparados - DIAGNOSTIC MODE`, {
        transactionId: transaction.id,
        previousStatus,
        newStatus: updatedTransaction.status,
        paymentAt: updatedTransaction.paymentAt
      });

      // Ensure webhook events are sent even if transaction was updated by webhook
      // This is critical for PIX confirmation events
      try {
        await triggerTransactionEvents(updatedTransaction, previousStatus, true); // Force confirmation event

        logger.info(`[${requestId}] Manually triggered webhook events for Transfeera confirmation`, {
          transactionId: transaction.id,
          previousStatus,
          newStatus: internalStatus,
          provider: "TRANSFEERA"
        });
      } catch (webhookError) {
        logger.error(`[${requestId}] Error triggering webhook events for Transfeera`, {
          error: webhookError,
          transactionId: transaction.id
        });
      }
    } catch (updateError) {
      logger.error(`[${requestId}] Erro ao atualizar transação - DIAGNOSTIC MODE`, {
        transactionId: transaction.id,
        error: updateError instanceof Error ? updateError.message : "Unknown error",
        stack: updateError instanceof Error ? updateError.stack : undefined
      });
      throw updateError; // Propagar o erro para ser tratado no catch principal
    }

    // Atualizar o saldo da organização com base no status e tipo de transação
    try {
      const { updateOrganizationBalance, BalanceOperationType } = await import("@repo/payments/src/balance/balance-service");

      // Verificar se é uma transação de cobrança (CHARGE) ou transferência (SEND)
      if (transaction.type === "CHARGE") {
        // Para transações de cobrança (recebimento de PIX)
        if (internalStatus === "APPROVED") {
          // Usar o serviço centralizado para processar as taxas e atualizar o saldo
          const { processApprovedTransactionFees } = await import("@repo/payments/src/taxes/fee-service");

          const result = await processApprovedTransactionFees(transaction);

          logger.info(`[${requestId}] Processed fees for transaction`, {
            transactionId: transaction.id,
            grossAmount: transaction.amount,
            calculatedFees: result.fees.totalFee,
            feeSource: result.fees.source || 'unknown',
            netAmount: result.netAmount,
            metadataFee: fee, // Fee from metadata for comparison
            success: result.success
          });
        }
        // Para CHARGE, não precisamos fazer nada se for REJECTED ou CANCELED
        // pois o dinheiro nunca entrou no sistema
      } else {
        // Para transações de transferência (envio de PIX)
        if (internalStatus === "APPROVED") {
          try {
            // Verificar se já existe uma operação DEBIT_RESERVED para esta transação
            const balanceHistory = await db.balance_history.findFirst({
              where: {
                transactionId: transaction.id,
                operation: "DEBIT_RESERVED"
              }
            });

            if (balanceHistory) {
              logger.info(`[${requestId}] DEBIT_RESERVED operation already exists for this transaction, skipping balance update`, {
                transactionId: transaction.id,
                balanceHistoryId: balanceHistory.id,
                amount: balanceHistory.amount
              });
            } else {
              // Verificar se já existe uma operação DEBIT_RESERVED para esta transação
              const existingDebitReserved = await db.balance_history.findFirst({
                where: {
                  transactionId: transaction.id,
                  operation: "DEBIT_RESERVED"
                }
              });

              if (existingDebitReserved) {
                logger.info(`[${requestId}] DEBIT_RESERVED operation already exists, skipping balance update`, {
                  transactionId: transaction.id,
                  balanceHistoryId: existingDebitReserved.id,
                  amount: existingDebitReserved.amount
                });
              } else {
                // Se aprovada, debitar diretamente do saldo reservado
                // Não precisamos fazer nada com o saldo disponível pois já foi debitado na criação da transferência
                try {
                  await updateOrganizationBalance(
                    transaction.organizationId,
                    totalAmount,
                    BalanceOperationType.DEBIT_RESERVED,
                    transaction.id,
                    `Transferência PIX aprovada via webhook Transfeera: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
                  );

                  // Verificar se o saldo foi atualizado corretamente
                  const updatedBalance = await db.organization_balance.findUnique({
                    where: { organizationId: transaction.organizationId }
                  });

                  if (updatedBalance && updatedBalance.reservedBalance < 0) {
                    logger.error(`[${requestId}] Saldo reservado negativo após DEBIT_RESERVED`, {
                      transactionId: transaction.id,
                      reservedBalance: updatedBalance.reservedBalance
                    });

                    // Corrigir saldo reservado negativo
                    await db.organization_balance.update({
                      where: { organizationId: transaction.organizationId },
                      data: { reservedBalance: 0 }
                    });
                  }
                } catch (error) {
                  logger.error(`[${requestId}] Falha ao atualizar saldo reservado`, {
                    error,
                    transactionId: transaction.id,
                    organizationId: transaction.organizationId
                  });
                  throw error;
                }

                logger.info(`[${requestId}] Balance updated for completed transfer`, {
                  transactionId: transaction.id,
                  organizationId: transaction.organizationId,
                  operation: "DEBIT_RESERVED",
                  amount: totalAmount
                });
              }
            }
          } catch (debitError) {
            logger.error(`[${requestId}] Error during DEBIT_RESERVED operation`, {
              error: debitError,
              transactionId: transaction.id,
              organizationId: transaction.organizationId,
              amount: totalAmount
            });

            // Verificar se o erro é de saldo insuficiente
            if (debitError instanceof Error && debitError.message.includes("Insufficient reserved balance")) {
              logger.warn(`[${requestId}] Insufficient reserved balance detected, attempting to fix...`, {
                transactionId: transaction.id
              });

              try {
                // Verificar o saldo atual da organização
                const currentBalance = await db.organization_balance.findUnique({
                  where: { organizationId: transaction.organizationId }
                });

                logger.info(`[${requestId}] Current organization balance`, {
                  transactionId: transaction.id,
                  organizationId: transaction.organizationId,
                  availableBalance: currentBalance?.availableBalance,
                  reservedBalance: currentBalance?.reservedBalance,
                  pendingBalance: currentBalance?.pendingBalance
                });

                // Verificar se existe uma operação RESERVE para esta transação
                const reserveOperation = await db.balance_history.findFirst({
                  where: {
                    transactionId: transaction.id,
                    operation: "RESERVE"
                  }
                });

                if (reserveOperation) {
                  logger.info(`[${requestId}] Found RESERVE operation`, {
                    transactionId: transaction.id,
                    reserveAmount: reserveOperation.amount,
                    requiredAmount: totalAmount
                  });

                  // Se o valor reservado for diferente do valor total, tentar corrigir
                  if (Math.abs(reserveOperation.amount - totalAmount) > 0.01) {
                    logger.warn(`[${requestId}] Reserved amount (${reserveOperation.amount}) differs from total amount (${totalAmount}), fixing...`, {
                      transactionId: transaction.id
                    });

                    // Verificar se há saldo disponível suficiente para a correção
                    if ((currentBalance?.availableBalance || 0) >= totalAmount - reserveOperation.amount) {
                      // Primeiro, devolver o valor reservado incorretamente
                      await updateOrganizationBalance(
                        transaction.organizationId,
                        reserveOperation.amount,
                        BalanceOperationType.UNRESERVE,
                        transaction.id,
                        `Correção: Devolução de reserva incorreta para transferência PIX: ${transaction.id}`
                      );

                      // Depois, reservar o valor correto
                      await updateOrganizationBalance(
                        transaction.organizationId,
                        totalAmount,
                        BalanceOperationType.RESERVE,
                        transaction.id,
                        `Correção: Reserva correta para transferência PIX: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
                      );

                      // Finalmente, debitar do saldo reservado
                      await updateOrganizationBalance(
                        transaction.organizationId,
                        totalAmount,
                        BalanceOperationType.DEBIT_RESERVED,
                        transaction.id,
                        `Transferência PIX aprovada via webhook Transfeera (após correção): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
                      );

                      logger.info(`[${requestId}] Balance correction successful`, {
                        transactionId: transaction.id,
                        organizationId: transaction.organizationId
                      });
                    } else {
                      // Se não houver saldo disponível suficiente, forçar a operação DEBIT_RESERVED
                      logger.warn(`[${requestId}] Insufficient available balance for correction, forcing DEBIT_RESERVED`, {
                        transactionId: transaction.id,
                        availableBalance: currentBalance?.availableBalance,
                        reservedBalance: currentBalance?.reservedBalance,
                        requiredAmount: totalAmount
                      });

                      // Atualizar diretamente o saldo reservado no banco de dados
                      await db.organization_balance.update({
                        where: { organizationId: transaction.organizationId },
                        data: {
                          reservedBalance: {
                            decrement: totalAmount
                          }
                        }
                      });

                      // Registrar a operação forçada para auditoria
                      await db.balance_history.create({
                        data: {
                          organizationId: transaction.organizationId,
                          transactionId: transaction.id,
                          operation: "DEBIT_RESERVED",
                          amount: totalAmount,
                          description: `Transferência PIX aprovada via webhook Transfeera (forçada): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`,
                          balanceAfterOperation: {
                            available: (currentBalance?.availableBalance || 0),
                            pending: (currentBalance?.pendingBalance || 0),
                            reserved: (currentBalance?.reservedBalance || 0) - totalAmount
                          },
                          balanceId: currentBalance?.id || ""
                        }
                      });

                      logger.info(`[${requestId}] Forced DEBIT_RESERVED operation completed`, {
                        transactionId: transaction.id,
                        organizationId: transaction.organizationId,
                        amount: totalAmount
                      });
                    }
                  } else {
                    // Se o valor reservado for correto, mas DEBIT_RESERVED falhou, forçar a operação
                    logger.warn(`[${requestId}] Reserved amount is correct but DEBIT_RESERVED failed, forcing operation`, {
                      transactionId: transaction.id,
                      reserveAmount: reserveOperation.amount,
                      totalAmount: totalAmount
                    });

                    // Atualizar diretamente o saldo reservado no banco de dados
                    await db.organization_balance.update({
                      where: { organizationId: transaction.organizationId },
                      data: {
                        reservedBalance: {
                          decrement: totalAmount
                        }
                      }
                    });

                    // Registrar a operação forçada para auditoria
                    await db.balance_history.create({
                      data: {
                        organizationId: transaction.organizationId,
                        transactionId: transaction.id,
                        operation: "DEBIT_RESERVED",
                        amount: totalAmount,
                        description: `Transferência PIX aprovada via webhook Transfeera (forçada): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`,
                        balanceAfterOperation: {
                          available: (currentBalance?.availableBalance || 0),
                          pending: (currentBalance?.pendingBalance || 0),
                          reserved: (currentBalance?.reservedBalance || 0) - totalAmount
                        },
                        balanceId: currentBalance?.id || ""
                      }
                    });

                    logger.info(`[${requestId}] Forced DEBIT_RESERVED operation completed`, {
                      transactionId: transaction.id,
                      organizationId: transaction.organizationId,
                      amount: totalAmount
                    });
                  }
                } else {
                  logger.warn(`[${requestId}] No RESERVE operation found, creating one...`, {
                    transactionId: transaction.id
                  });

                  // Verificar se há saldo disponível suficiente
                  if ((currentBalance?.availableBalance || 0) >= totalAmount) {
                    // Se não houver operação RESERVE, criar uma
                    await updateOrganizationBalance(
                      transaction.organizationId,
                      totalAmount,
                      BalanceOperationType.RESERVE,
                      transaction.id,
                      `Correção: Reserva para transferência PIX: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
                    );

                    // Depois, debitar do saldo reservado
                    await updateOrganizationBalance(
                      transaction.organizationId,
                      totalAmount,
                      BalanceOperationType.DEBIT_RESERVED,
                      transaction.id,
                      `Transferência PIX aprovada via webhook Transfeera (após correção): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
                    );

                    logger.info(`[${requestId}] Created missing RESERVE operation and completed DEBIT_RESERVED`, {
                      transactionId: transaction.id,
                      organizationId: transaction.organizationId,
                      amount: totalAmount
                    });
                  } else {
                    // Se não houver saldo disponível suficiente, forçar a operação DEBIT_RESERVED
                    logger.warn(`[${requestId}] Insufficient available balance, forcing DEBIT_RESERVED without RESERVE`, {
                      transactionId: transaction.id,
                      availableBalance: currentBalance?.availableBalance,
                      requiredAmount: totalAmount
                    });

                    // Atualizar diretamente o saldo reservado no banco de dados
                    await db.organization_balance.update({
                      where: { organizationId: transaction.organizationId },
                      data: {
                        reservedBalance: {
                          decrement: totalAmount
                        }
                      }
                    });

                    // Registrar a operação forçada para auditoria
                    await db.balance_history.create({
                      data: {
                        organizationId: transaction.organizationId,
                        transactionId: transaction.id,
                        operation: "DEBIT_RESERVED",
                        amount: totalAmount,
                        description: `Transferência PIX aprovada via webhook Transfeera (forçada sem RESERVE): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`,
                        balanceAfterOperation: {
                          available: (currentBalance?.availableBalance || 0),
                          pending: (currentBalance?.pendingBalance || 0),
                          reserved: (currentBalance?.reservedBalance || 0) - totalAmount
                        },
                        balanceId: currentBalance?.id || ""
                      }
                    });

                    logger.info(`[${requestId}] Forced DEBIT_RESERVED operation completed without RESERVE`, {
                      transactionId: transaction.id,
                      organizationId: transaction.organizationId,
                      amount: totalAmount
                    });
                  }
                }
              } catch (correctionError) {
                logger.error(`[${requestId}] Failed to correct balance issue`, {
                  error: correctionError,
                  transactionId: transaction.id,
                  organizationId: transaction.organizationId
                });
              }
            }
          }
        } else {
          // Se rejeitada ou cancelada, devolver o valor para o saldo disponível
          try {
            await updateOrganizationBalance(
              transaction.organizationId,
              totalAmount,
              BalanceOperationType.UNRESERVE,
              transaction.id,
              `Transferência PIX ${internalStatus.toLowerCase()} via webhook Transfeera: ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
            );

            logger.info(`[${requestId}] Balance returned to available for failed transfer`, {
              transactionId: transaction.id,
              organizationId: transaction.organizationId,
              operation: "UNRESERVE",
              amount: totalAmount
            });
          } catch (unreserveError) {
            logger.error(`[${requestId}] Error during UNRESERVE operation`, {
              error: unreserveError,
              transactionId: transaction.id,
              organizationId: transaction.organizationId,
              amount: totalAmount
            });
          }
        }
      }
    } catch (balanceError) {
      logger.error(`[${requestId}] Error updating organization balance`, {
        error: balanceError,
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: internalStatus,
        amount: totalAmount
      });

      // Mesmo com erro no saldo, continuamos o processamento para atualizar o status da transação
      // e notificar os clientes, mas registramos o erro nos metadados
      const metadata = transaction.metadata as any || {};
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          metadata: {
            ...metadata,
            balanceUpdateError: {
              error: balanceError instanceof Error ? balanceError.message : "Unknown error",
              timestamp: new Date().toISOString()
            }
          }
        }
      });
    }

    // Não precisamos mais criar o evento de webhook manualmente aqui
    // pois a função updateTransactionStatus já dispara os eventos de webhook
    logger.info("Webhook events triggered via updateTransactionStatus", {
      transactionId: transaction.id,
      status: internalStatus
    });

    // Processar as entregas de webhook para garantir que os clientes sejam notificados
    try {
      const { processWebhookDeliveries } = await import("@repo/payments/src/webhooks/service");
      await processWebhookDeliveries();
      logger.info("Webhook deliveries processed for status update", {
        transactionId: transaction.id
      });
    } catch (processError) {
      logger.error("Error processing webhook deliveries", {
        error: processError,
        transactionId: transaction.id
      });
      // Não interromper o fluxo se houver erro no processamento de webhooks
    }
  } else {
    // Se não for um status final, atualizar o status da transação e os metadados
    const metadata = transaction.metadata as any || {};

    // Primeiro atualizar os metadados da transação para incluir informações do webhook
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        metadata: {
          ...metadata,
          webhookUpdate: {
            processedAt: new Date().toISOString(),
            oldStatus: transaction.status,
            newStatus: internalStatus,
            payload: payload,
            batchId: batchId
          }
        }
      }
    });

    // Depois atualizar o status usando updateTransactionStatus para disparar eventos de webhook
    await updateTransactionStatus(
      transaction.id,
      internalStatus as any,
      undefined // Não definir paymentAt para status não finais
    );

    logger.info(`[${requestId}] Transaction status updated for non-final status and webhook events triggered`, {
      transactionId: transaction.id,
      oldStatus: transaction.status,
      newStatus: internalStatus
    });

    // Não precisamos mais criar o evento de webhook manualmente aqui
    // pois a função updateTransactionStatus já dispara os eventos de webhook
    logger.info(`[${requestId}] Webhook events triggered via updateTransactionStatus for non-final status`, {
      transactionId: transaction.id,
      status: internalStatus
    });

    // Processar as entregas de webhook para garantir que os clientes sejam notificados
    try {
      const { processWebhookDeliveries } = await import("@repo/payments/src/webhooks/service");
      await processWebhookDeliveries();
      logger.info(`[${requestId}] Webhook deliveries processed for non-final status update`, {
        transactionId: transaction.id
      });
    } catch (processError) {
      logger.error(`[${requestId}] Error processing webhook deliveries for non-final status`, {
        error: processError,
        transactionId: transaction.id
      });
      // Não interromper o fluxo se houver erro no processamento de webhooks
    }
  }

  logger.info(`[${requestId}] Webhook processing completed successfully`, {
    transactionId: transaction.id,
    externalId: transaction.externalId,
    status: internalStatus,
    processingTime: Date.now() - new Date(transaction.metadata?.webhookUpdate?.processedAt || Date.now()).getTime()
  });

  // Disparar webhooks para clientes cadastrados
  try {
    const { processWebhookDeliveries } = await import("@repo/payments/src/webhooks/service");
    await processWebhookDeliveries();
    logger.info(`[${requestId}] Webhook deliveries processed for client notifications`, {
      transactionId: transaction.id
    });
  } catch (processError) {
    logger.error(`[${requestId}] Error processing client webhook deliveries`, {
      error: processError,
      transactionId: transaction.id
    });
  }

  return NextResponse.json({
    success: true,
    transaction: {
      id: transaction.id,
      externalId: transaction.externalId,
      status: internalStatus,
      processedAt: new Date().toISOString()
    }
  });
}

/**
 * Find transaction by identifiers - Enhanced search strategy
 * Following the same 4-level priority search as Pluggou PIX
 */
async function findTransactionByIdentifiers(identifiers: string[]) {
  if (identifiers.length === 0) {
    logger.warn("No identifiers provided for transaction search");
    return null;
  }

  logger.info("Searching for transaction by identifiers", {
    identifiers,
    identifierCount: identifiers.length
  });

  // Primary strategy: Search by externalId
  let transaction = await db.transaction.findFirst({
    where: {
      externalId: { in: identifiers }
    }
  });

  if (transaction) {
    logger.info("Found transaction by externalId", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      matchedIdentifier: identifiers.find(id => id === transaction?.externalId)
    });
    return transaction;
  }

  logger.info("No transaction found by externalId, trying metadata search");

  // Secondary strategy: Search by integration_id in metadata
  transaction = await db.transaction.findFirst({
    where: {
      OR: identifiers.map(id => ({
        metadata: { path: ['integrationId'], equals: id }
      }))
    }
  });

  if (transaction) {
    logger.info("Found transaction by integrationId in metadata", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      matchedIdentifier: identifiers.find(id => (transaction?.metadata as any)?.integrationId === id)
    });
    return transaction;
  }

  logger.info("No transaction found by integrationId, trying endToEndId");

  // Tertiary strategy: Search by endToEndId
  transaction = await db.transaction.findFirst({
    where: {
      endToEndId: { in: identifiers }
    }
  });

  if (transaction) {
    logger.info("Found transaction by endToEndId", {
      transactionId: transaction.id,
      endToEndId: transaction.endToEndId,
      matchedIdentifier: identifiers.find(id => id === transaction?.endToEndId)
    });
    return transaction;
  }

  logger.info("No transaction found by endToEndId, trying referenceCode");

  // Quaternary strategy: Search by referenceCode
  transaction = await db.transaction.findFirst({
    where: {
      referenceCode: { in: identifiers }
    }
  });

  if (transaction) {
    logger.info("Found transaction by referenceCode", {
      transactionId: transaction.id,
      referenceCode: transaction.referenceCode,
      matchedIdentifier: identifiers.find(id => id === transaction?.referenceCode)
    });
    return transaction;
  }

  logger.warn("No transaction found by any standard identifier", {
    identifiers,
    searchMethods: ['externalId', 'metadata.integrationId', 'endToEndId', 'referenceCode'],
    recommendation: "Ensure transactions are created with proper identifiers"
  });

  return null;
}

/**
 * Enhanced status mapping to handle both Portuguese and English statuses
 * Following the same pattern as Pluggou PIX
 */
function mapTransfeeraStatusToInternalEnhanced(status: string | undefined): TransactionStatus {
  if (!status) return "PENDING";

  const statusLower = status.toLowerCase();

  // Enhanced mapping with more status variations
  const statusMap: Record<string, TransactionStatus> = {
    // English statuses
    "pending": "PENDING",
    "processing": "PROCESSING",
    "waiting": "PROCESSING",
    "approved": "APPROVED",
    "paid": "APPROVED",
    "completed": "APPROVED",
    "success": "APPROVED",
    "received": "APPROVED",
    "finalizado": "APPROVED",
    "concluido": "APPROVED",
    "concluído": "APPROVED",
    "rejected": "REJECTED",
    "refused": "REJECTED",
    "failed": "REJECTED",
    "rejeitado": "REJECTED",
    "falha": "REJECTED",
    "cancelled": "CANCELED",
    "canceled": "CANCELED",
    "cancelado": "CANCELED",
    "refunded": "REFUNDED",
    "refund": "REFUNDED",
    "chargeback": "REFUNDED",
    "estornado": "REFUNDED",
    "devolvido": "REFUNDED"
  };

  const mappedStatus = statusMap[statusLower];

  if (!mappedStatus) {
    logger.warn("Unknown Transfeera status, mapping to PENDING", {
      originalStatus: status,
      statusLower
    });
    return "PENDING";
  }

  return mappedStatus;
}

/**
 * Check for invalid status transitions
 * Following the same pattern as Pluggou PIX
 */
function isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
  // Define invalid transitions
  const invalidTransitions = [
    { from: "REFUNDED", to: "APPROVED" },
    { from: "REFUNDED", to: "PROCESSING" },
    { from: "CANCELED", to: "APPROVED" },
    { from: "CANCELED", to: "PROCESSING" }
  ];

  const isInvalid = invalidTransitions.some(
    transition => transition.from === currentStatus && transition.to === newStatus
  );

  if (isInvalid) {
    logger.warn("Invalid status transition detected", {
      currentStatus,
      newStatus,
      action: "BLOCKED"
    });
  }

  return !isInvalid;
}
