import { NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import type { TransactionStatus } from "@prisma/client";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { calculateTransactionFees } from "@repo/payments/src/taxes/calculator";

/**
 * Webhook handler for Pluggou PIX payments
 * Simplified version that only updates transactions and sends SVIX events
 * NO DATABASE EVENT SAVING
 */
export async function POST(request: Request) {
  try {
    const rawPayload = await request.json();

    // Handle nested payload structure - some webhooks come with a "payload" wrapper
    let payload = rawPayload.payload || rawPayload;

    // If payload is a string, parse it as JSON
    if (typeof payload === 'string') {
      try {
        payload = JSON.parse(payload);
      } catch (parseError) {
        logger.error("Failed to parse payload string as JSON", {
          payload,
          parseError: parseError instanceof Error ? parseError.message : 'Unknown error'
        });
        return NextResponse.json({ error: "Invalid payload format" }, { status: 400 });
      }
    }

    logger.info("Pluggou PIX webhook received", {
      timestamp: new Date().toISOString(),
      rawPayloadStructure: {
        hasNestedPayload: !!rawPayload.payload,
        payloadType: typeof (rawPayload.payload || rawPayload),
        topLevelKeys: Object.keys(rawPayload),
        actualPayloadKeys: typeof payload === 'object' ? Object.keys(payload) : []
      },
      payload: payload
    });

    // Enhanced deduplication using multiple identifiers
    const webhookId = createWebhookId(payload);

    // Extract identifiers from different possible payload structures
    const flow2payId = payload.idEnvio || // Direct Flow2Pay field
                       payload.txid || // Alternative Flow2Pay field
                       payload.flow2pay_id ||
                       payload.data?.idEnvio ||
                       payload.data?.txid ||
                       payload.webhook_payload?.idEnvio ||
                       payload.webhook_payload?.txid ||
                       payload.data?.flow2pay_id ||
                       payload.webhook_payload?.flow2pay_id;

    const endToEndId = payload.endToEndId || // Direct Flow2Pay field
                       payload.end_to_end_id ||
                       payload.data?.endToEndId ||
                       payload.webhook_payload?.endToEndId;

    // Handle different status field locations and values
    const status = payload.status || // Direct Flow2Pay field
                   payload.data?.status ||
                   payload.webhook_payload?.status ||
                   "";

    // Create a more specific deduplication key that includes the status
    // This allows processing different status updates for the same transaction
    const deduplicationKey = `${flow2payId || 'unknown'}-${endToEndId || 'unknown'}-${status}-${webhookId}`;

    logger.info("Processing webhook with deduplication", {
      webhookId,
      flow2payId,
      endToEndId,
      status,
      deduplicationKey,
      payload: {
        event: payload.event,
        event_type: payload.event_type,
        flow2pay_event_type: payload.flow2pay_event_type,
        svix_event_type: payload.svix_event_type,
        status: payload.status,
        data_evento: payload.data?.evento,
        webhook_evento: payload.webhook_payload?.evento
      }
    });

    // Check if we processed this exact webhook recently (last 5 minutes)
    // Use a shorter time window and more specific matching
    const recentTransaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              {
                metadata: {
                  path: ['lastWebhookId'],
                  equals: webhookId
                }
              },
              {
                metadata: {
                  path: ['deduplicationKey'],
                  equals: deduplicationKey
                }
              }
            ]
          },
          {
            updatedAt: {
              gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
            }
          }
        ]
      }
    });

    if (recentTransaction) {
      logger.info("Webhook already processed recently, skipping", {
        webhookId,
        deduplicationKey,
        transactionId: recentTransaction.id,
        lastProcessed: recentTransaction.updatedAt,
        timeSinceLastProcessed: Date.now() - recentTransaction.updatedAt.getTime()
      });
      return NextResponse.json({ success: true, message: "Already processed" });
    }

    // Determine event type
    const eventType = determineEventType(payload);

    if (eventType === 'pixout') {
      await handlePixOutTransfer(payload, webhookId, deduplicationKey);
    } else if (eventType === 'pixin') {
      await handlePixInPayment(payload, webhookId, deduplicationKey);
    } else if (eventType === 'pixinreversal') {
      await handlePixInReversal(payload, webhookId, deduplicationKey);
    } else {
      logger.warn("Unknown event type", { payload });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error processing Pluggou PIX webhook", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handle PixOut transfer webhook - NO DATABASE EVENT SAVING
 */
async function handlePixOutTransfer(payload: any, webhookId: string, deduplicationKey: string) {
  try {
    // Extract data from multiple possible payload structures
    const flow2payId = payload.idEnvio || // Direct Flow2Pay field
                       payload.txid || // Alternative Flow2Pay field
                       payload.flow2pay_id ||
                       payload.data?.idEnvio ||
                       payload.data?.txid ||
                       payload.webhook_payload?.idEnvio ||
                       payload.webhook_payload?.txid ||
                       payload.data?.flow2pay_id ||
                       payload.webhook_payload?.flow2pay_id;

    const status = payload.status || // Direct Flow2Pay field
                   payload.data?.status ||
                   payload.webhook_payload?.status ||
                   "";

    const amount = payload.valor || // Direct Flow2Pay field (note: valor, not amount)
                   payload.amount ||
                   payload.data?.valor ||
                   payload.webhook_payload?.valor;

    const endToEndId = payload.endToEndId || // Direct Flow2Pay field
                       payload.end_to_end_id ||
                       payload.data?.endToEndId ||
                       payload.webhook_payload?.endToEndId;

    logger.info("Processing PixOut webhook", {
      flow2payId,
      status,
      amount,
      endToEndId,
      webhookId,
      payloadStructure: {
        hasData: !!payload.data,
        hasWebhookPayload: !!payload.webhook_payload,
        topLevelFields: Object.keys(payload)
      }
    });

    // Find transaction with enhanced search
    const transaction = await findTransactionByIdentifiers([
      flow2payId,
      endToEndId,
      payload.codigoTransacao, // Direct Flow2Pay field
      payload.data?.codigoTransacao,
      payload.webhook_payload?.codigoTransacao,
      payload.webhook_payload?.idEnvio
    ].filter(Boolean));

    if (!transaction) {
      logger.error("Transaction not found for PixOut webhook", {
        flow2payId,
        endToEndId,
        amount,
        searchIdentifiers: [
          flow2payId,
          endToEndId,
          payload.codigoTransacao,
          payload.data?.codigoTransacao,
          payload.webhook_payload?.codigoTransacao,
          payload.webhook_payload?.idEnvio
        ].filter(Boolean)
      });
      return;
    }

    logger.info("Found transaction for PixOut webhook", {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      transactionType: transaction.type,
      organizationId: transaction.organizationId
    });

    // Enhanced status mapping to handle both Portuguese and English statuses
    let transactionStatus: TransactionStatus = "PENDING";
    const statusLower = status.toLowerCase();

    if (statusLower.includes("sucesso") ||
        statusLower.includes("success") ||
        statusLower.includes("completed") ||
        statusLower.includes("finalizado") ||
        statusLower.includes("aprovado")) {
      transactionStatus = "APPROVED";
    } else if (statusLower.includes("processamento") ||
               statusLower.includes("processing") ||
               statusLower.includes("em_andamento") ||
               statusLower.includes("pendente")) {
      transactionStatus = "PROCESSING";
    } else if (statusLower.includes("falha") ||
               statusLower.includes("failed") ||
               statusLower.includes("erro") ||
               statusLower.includes("rejeitado") ||
               statusLower.includes("rejected")) {
      transactionStatus = "REJECTED";
    }

    logger.info("Mapped webhook status", {
      originalStatus: status,
      mappedStatus: transactionStatus,
      currentTransactionStatus: transaction.status,
      willUpdate: transaction.status !== transactionStatus
    });

    // Only update if status changed
    if (transaction.status !== transactionStatus) {
      const previousStatus = transaction.status;

      // Calculate fees FIRST if transaction is being approved
      let updatedTransactionData: any = {
        status: transactionStatus,
        metadata: {
          ...(transaction.metadata as any || {}),
          lastWebhookId: webhookId,
          lastWebhookAt: new Date().toISOString(),
          endToEndId: endToEndId,
          deduplicationKey: deduplicationKey,
          // Store original webhook payload for debugging
          lastWebhookPayload: JSON.stringify(payload),
          // Flag to indicate this was updated by webhook to avoid recursive events
          updatedByWebhook: true,
          webhookSource: 'pluggou-pix'
        },
        endToEndId: endToEndId,
        ...(transactionStatus === "APPROVED" ? { paymentAt: new Date() } : {}),
        ...(transactionStatus === "REJECTED" ? { processedAt: new Date() } : {})
      };

      // Calculate and apply fees for approved transactions
      if (transactionStatus === "APPROVED") {
        try {
          // Calculate organization taxes for PIX OUT (TRANSFER)
          const fees = await calculateTransactionFees(
            transaction.organizationId,
            transaction.amount,
            'TRANSFER'
          );

          // Calculate total amount (transfer amount + fees)
          const totalAmount = transaction.amount + fees.totalFee;

          // Add fee fields to the update data
          updatedTransactionData = {
            ...updatedTransactionData,
            percentFee: fees.percentFee,
            fixedFee: fees.fixedFee,
            totalFee: fees.totalFee,
            netAmount: transaction.amount, // For transfers, netAmount is the transfer amount (before fees)
            metadata: {
              ...updatedTransactionData.metadata,
              fees: {
                percentFee: fees.percentFee,
                fixedFee: fees.fixedFee,
                totalFee: fees.totalFee,
                source: fees.source || 'organization',
                calculatedAt: new Date().toISOString()
              },
              totalAmount: totalAmount,
              feeProcessed: true,
              feeProcessedAt: new Date().toISOString()
            }
          };

          logger.info("Calculated fees for PIX OUT transaction", {
            transactionId: transaction.id,
            transferAmount: transaction.amount,
            fees: fees.totalFee,
            totalAmount: totalAmount,
            percentFee: fees.percentFee,
            fixedFee: fees.fixedFee
          });
        } catch (feeError) {
          logger.error("Error calculating fees for PIX OUT", {
            error: feeError instanceof Error ? feeError.message : String(feeError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      }

      // Update transaction with all data including fees
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: updatedTransactionData
      });

      logger.info("Transaction status updated", {
        transactionId: transaction.id,
        previousStatus,
        newStatus: transactionStatus,
        webhookId,
        updatedByWebhook: true,
        willSendWebhook: true,
        fees: {
          percentFee: updatedTransaction.percentFee,
          fixedFee: updatedTransaction.fixedFee,
          totalFee: updatedTransaction.totalFee,
          netAmount: updatedTransaction.netAmount
        }
      });

      // Update organization balance for PIX OUT transactions
      if (transactionStatus === "APPROVED") {
        try {
          // Calculate organization taxes for PIX OUT (TRANSFER)
          const fees = await calculateTransactionFees(
            transaction.organizationId,
            transaction.amount,
            'TRANSFER'
          );

          // Calculate total amount (transfer amount + fees)
          const totalAmount = transaction.amount + fees.totalFee;

          // For PIX OUT (SEND), when approved, debit from reserved balance (amount + fees)
          await updateOrganizationBalance(
            transaction.organizationId,
            totalAmount,
            BalanceOperationType.DEBIT_RESERVED,
            transaction.id,
            `Transferência PIX aprovada via webhook Pluggou: ${transaction.id} (valor: ${transaction.amount}, taxas: ${fees.totalFee}, total: ${totalAmount})`
          );

          logger.info("Organization balance debited from reserved for approved PIX OUT with fees", {
            transactionId: transaction.id,
            organizationId: transaction.organizationId,
            transferAmount: transaction.amount,
            fees: fees.totalFee,
            totalAmount: totalAmount,
            operation: BalanceOperationType.DEBIT_RESERVED
          });
        } catch (balanceError) {
          logger.error("Error updating organization balance for approved PIX OUT", {
            error: balanceError instanceof Error ? balanceError.message : String(balanceError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      } else if (transactionStatus === "REJECTED") {
        try {
          // Calculate organization taxes for PIX OUT (TRANSFER) to get the correct total amount
          const fees = await calculateTransactionFees(
            transaction.organizationId,
            transaction.amount,
            'TRANSFER'
          );

          // Calculate total amount (transfer amount + fees)
          const totalAmount = transaction.amount + fees.totalFee;

          // For PIX OUT (SEND), when rejected, return reserved amount to available balance (amount + fees)
          await updateOrganizationBalance(
            transaction.organizationId,
            totalAmount,
            BalanceOperationType.UNRESERVE,
            transaction.id,
            `Transferência PIX rejeitada via webhook Pluggou: ${transaction.id} (valor: ${transaction.amount}, taxas: ${fees.totalFee}, total: ${totalAmount})`
          );

          logger.info("Organization balance unreserved for rejected PIX OUT with fees", {
            transactionId: transaction.id,
            organizationId: transaction.organizationId,
            transferAmount: transaction.amount,
            fees: fees.totalFee,
            totalAmount: totalAmount,
            operation: BalanceOperationType.UNRESERVE
          });
        } catch (balanceError) {
          logger.error("Error updating organization balance for rejected PIX OUT", {
            error: balanceError instanceof Error ? balanceError.message : String(balanceError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      }

      // Send webhook for all status changes
      logger.info("Sending webhook for status change", {
        transactionId: transaction.id,
        status: transactionStatus,
        previousStatus
      });
      await sendWebhookDirectly(updatedTransaction, previousStatus, transactionStatus);
    } else {
      logger.info("Transaction status unchanged, skipping update", {
        transactionId: transaction.id,
        currentStatus: transaction.status,
        webhookStatus: transactionStatus
      });
    }
  } catch (error) {
    logger.error("Error in PixOut handler", { error, webhookId });
  }
}

/**
 * Handle PixIn payment webhook - NO DATABASE EVENT SAVING
 */
async function handlePixInPayment(payload: any, webhookId: string, deduplicationKey: string) {
  try {
    // Extract data from multiple possible payload structures
    const txid = payload.txid || // Direct Flow2Pay field
                 payload.idEnvio || // Alternative Flow2Pay field
                 payload.flow2pay_id ||
                 payload.data?.txid ||
                 payload.webhook_payload?.txid ||
                 payload.data?.idEnvio ||
                 payload.webhook_payload?.idEnvio ||
                 payload.data?.flow2pay_id ||
                 payload.webhook_payload?.flow2pay_id;

    const status = payload.status || // Direct Flow2Pay field
                   payload.data?.status ||
                   payload.webhook_payload?.status ||
                   "";

    const amount = payload.valor || // Direct Flow2Pay field (note: valor, not amount)
                   payload.amount ||
                   payload.data?.valor ||
                   payload.webhook_payload?.valor;

    const endToEndId = payload.endToEndId || // Direct Flow2Pay field
                       payload.end_to_end_id ||
                       payload.data?.endToEndId ||
                       payload.webhook_payload?.endToEndId;

    logger.info("Processing PixIn webhook", {
      txid,
      status,
      amount,
      endToEndId,
      webhookId,
      payloadStructure: {
        hasData: !!payload.data,
        hasWebhookPayload: !!payload.webhook_payload,
        topLevelFields: Object.keys(payload)
      }
    });

    // Find transaction with enhanced search
    let transaction = await findTransactionByIdentifiers([
      txid,
      endToEndId,
      payload.codigoTransacao // Direct Flow2Pay field
    ].filter(Boolean));

    // If not found by ID, try by amount for recent CHARGE transactions
    if (!transaction && amount) {
      transaction = await db.transaction.findFirst({
        where: {
          amount,
          type: "CHARGE",
          status: "PENDING",
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (transaction) {
        logger.info("Found transaction by amount fallback", {
          transactionId: transaction.id,
          amount,
          txid
        });
      }
    }

    if (!transaction) {
      logger.error("Transaction not found for PixIn webhook", {
        txid,
        endToEndId,
        amount,
        searchIdentifiers: [txid, endToEndId].filter(Boolean)
      });
      return;
    }

    logger.info("Found transaction for PixIn webhook", {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      transactionType: transaction.type,
      organizationId: transaction.organizationId
    });

    // Enhanced status mapping to handle both Portuguese and English statuses
    let transactionStatus: TransactionStatus = "PENDING";
    const statusLower = status.toLowerCase();

    if (statusLower.includes("sucesso") ||
        statusLower.includes("success") ||
        statusLower.includes("completed") ||
        statusLower.includes("finalizado") ||
        statusLower.includes("aprovado")) {
      transactionStatus = "APPROVED";
    } else if (statusLower.includes("processamento") ||
               statusLower.includes("processing") ||
               statusLower.includes("em_andamento") ||
               statusLower.includes("pendente")) {
      transactionStatus = "PROCESSING";
    } else if (statusLower.includes("falha") ||
               statusLower.includes("failed") ||
               statusLower.includes("erro") ||
               statusLower.includes("rejeitado") ||
               statusLower.includes("rejected")) {
      transactionStatus = "REJECTED";
    }

    logger.info("Mapped webhook status", {
      originalStatus: status,
      mappedStatus: transactionStatus,
      currentTransactionStatus: transaction.status,
      willUpdate: transaction.status !== transactionStatus
    });

    // Only update if status changed
    if (transaction.status !== transactionStatus) {
      const previousStatus = transaction.status;

      // Calculate fees FIRST if transaction is being approved
      let updatedTransactionData: any = {
        status: transactionStatus,
        metadata: {
          ...(transaction.metadata as any || {}),
          lastWebhookId: webhookId,
          lastWebhookAt: new Date().toISOString(),
          endToEndId: endToEndId,
          deduplicationKey: deduplicationKey,
          // Store original webhook payload for debugging
          lastWebhookPayload: JSON.stringify(payload),
          // Flag to indicate this was updated by webhook to avoid recursive events
          updatedByWebhook: true,
          webhookSource: 'pluggou-pix'
        },
        endToEndId: endToEndId,
        ...(transactionStatus === "APPROVED" ? { paymentAt: new Date() } : {}),
        ...(transactionStatus === "REJECTED" ? { processedAt: new Date() } : {})
      };

      // Calculate and apply fees for approved transactions
      if (transactionStatus === "APPROVED") {
        try {
          // Calculate organization taxes for PIX IN (CHARGE)
          const fees = await calculateTransactionFees(
            transaction.organizationId,
            transaction.amount,
            'CHARGE'
          );

          // Calculate net amount (gross amount minus fees)
          const netAmount = transaction.amount - fees.totalFee;

          // Add fee fields to the update data
          updatedTransactionData = {
            ...updatedTransactionData,
            percentFee: fees.percentFee,
            fixedFee: fees.fixedFee,
            totalFee: fees.totalFee,
            netAmount: netAmount,
            metadata: {
              ...updatedTransactionData.metadata,
              fees: {
                percentFee: fees.percentFee,
                fixedFee: fees.fixedFee,
                totalFee: fees.totalFee,
                source: fees.source || 'organization',
                calculatedAt: new Date().toISOString()
              },
              feeProcessed: true,
              feeProcessedAt: new Date().toISOString()
            }
          };

          logger.info("Calculated fees for PIX IN transaction", {
            transactionId: transaction.id,
            grossAmount: transaction.amount,
            fees: fees.totalFee,
            netAmount: netAmount,
            percentFee: fees.percentFee,
            fixedFee: fees.fixedFee
          });
        } catch (feeError) {
          logger.error("Error calculating fees for PIX IN", {
            error: feeError instanceof Error ? feeError.message : String(feeError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      }

      // Update transaction with all data including fees
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: updatedTransactionData
      });

      logger.info("Transaction status updated", {
        transactionId: transaction.id,
        previousStatus,
        newStatus: transactionStatus,
        webhookId,
        updatedByWebhook: true,
        willSendWebhook: true,
        fees: {
          percentFee: updatedTransaction.percentFee,
          fixedFee: updatedTransaction.fixedFee,
          totalFee: updatedTransaction.totalFee,
          netAmount: updatedTransaction.netAmount
        }
      });

      // Update organization balance for PIX IN transactions
      if (transactionStatus === "APPROVED") {
        try {
          // Use the fees already calculated and stored in the transaction
          const netAmount = updatedTransaction.netAmount || 0;
          const totalFee = updatedTransaction.totalFee || 0;

          // For PIX IN (CHARGE), when approved, credit the net amount (after fees)
          // This follows the standard practice where fees are deducted from received amount
          if (netAmount > 0) {
            await updateOrganizationBalance(
              transaction.organizationId,
              netAmount,
              BalanceOperationType.CREDIT,
              transaction.id,
              `Recebimento PIX aprovado via webhook Pluggou: ${transaction.id} (valor bruto: ${transaction.amount}, taxas: ${totalFee}, valor líquido: ${netAmount})`
            );

            logger.info("Organization balance credited for approved PIX IN with fees", {
              transactionId: transaction.id,
              organizationId: transaction.organizationId,
              grossAmount: transaction.amount,
              fees: totalFee,
              netAmount: netAmount,
              operation: BalanceOperationType.CREDIT
            });
          } else {
            logger.warn("PIX IN net amount is zero or negative after fees, not crediting balance", {
              transactionId: transaction.id,
              organizationId: transaction.organizationId,
              grossAmount: transaction.amount,
              fees: totalFee,
              netAmount: netAmount
            });
          }
        } catch (balanceError) {
          logger.error("Error updating organization balance for PIX IN", {
            error: balanceError instanceof Error ? balanceError.message : String(balanceError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      }

      // Send webhook for all status changes
      logger.info("Sending webhook for status change", {
        transactionId: transaction.id,
        status: transactionStatus,
        previousStatus
      });
      await sendWebhookDirectly(updatedTransaction, previousStatus, transactionStatus);
    } else {
      logger.info("Transaction status unchanged, skipping update", {
        transactionId: transaction.id,
        currentStatus: transaction.status,
        webhookStatus: transactionStatus
      });
    }
  } catch (error) {
    logger.error("Error in PixIn handler", { error, webhookId });
  }
}

/**
 * Handle PixInReversal webhook - Process refunds/reversals
 */
async function handlePixInReversal(payload: any, webhookId: string, deduplicationKey: string) {
  try {
    // Extract data from multiple possible payload structures
    const txid = payload.txid || // Direct Flow2Pay field
                 payload.idEnvio || // Alternative Flow2Pay field
                 payload.flow2pay_id ||
                 payload.data?.txid ||
                 payload.webhook_payload?.txid ||
                 payload.data?.idEnvio ||
                 payload.webhook_payload?.idEnvio ||
                 payload.data?.flow2pay_id ||
                 payload.webhook_payload?.flow2pay_id;

    const status = payload.status || // Direct Flow2Pay field
                   payload.data?.status ||
                   payload.webhook_payload?.status ||
                   "";

    const amount = payload.valor || // Direct Flow2Pay field (note: valor, not amount)
                   payload.amount ||
                   payload.data?.valor ||
                   payload.webhook_payload?.valor;

    const endToEndId = payload.endToEndId || // Direct Flow2Pay field
                       payload.end_to_end_id ||
                       payload.data?.endToEndId ||
                       payload.webhook_payload?.endToEndId;

    // CORREÇÃO: O idEnvio é o campo que vincula a requisição de estorno ao webhook
    // Conforme documentado, este é o transaction_code retornado na requisição de estorno
    const idEnvio = payload.idEnvio ||
                    payload.id_envio ||
                    payload.data?.idEnvio ||
                    payload.webhook_payload?.idEnvio;

    logger.info("Processing PixInReversal webhook", {
      txid,
      status,
      amount,
      endToEndId,
      idEnvio, // Adicionado para debug
      webhookId,
      payloadStructure: {
        hasData: !!payload.data,
        hasWebhookPayload: !!payload.webhook_payload,
        topLevelFields: Object.keys(payload)
      }
    });

    // CORREÇÃO: Buscar a transação original usando o idEnvio primeiro
    // O idEnvio é o transaction_code que vincula a requisição de estorno ao webhook
    let transaction = null;

    // 1. Primeiro, tentar buscar pela transação que foi criada com o idEnvio como externalId
    if (idEnvio) {
      transaction = await db.transaction.findFirst({
        where: {
          externalId: idEnvio,
          type: "CHARGE", // Só transações de cobrança podem ser estornadas
          status: "APPROVED" // Só transações aprovadas podem ser estornadas
        },
        orderBy: { createdAt: 'desc' }
      });

      if (transaction) {
        logger.info("Found original transaction by idEnvio (externalId)", {
          transactionId: transaction.id,
          idEnvio: idEnvio,
          originalEndToEndId: transaction.endToEndId,
          reversalEndToEndId: endToEndId
        });
      }
    }

    // 2. Se não encontrou, buscar por idEnvio nos metadados
    if (!transaction && idEnvio) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            { metadata: { path: ['idEnvio'], equals: idEnvio } },
            { metadata: { path: ['transaction_code'], equals: idEnvio } },
            { metadata: { path: ['id_envio'], equals: idEnvio } },
            { metadata: { path: ['codigoTransacao'], equals: idEnvio } },
            { metadata: { path: ['flow2pay', 'idEnvio'], equals: idEnvio } },
            { metadata: { path: ['flow2pay', 'transaction_code'], equals: idEnvio } }
          ],
          type: "CHARGE",
          status: "APPROVED"
        },
        orderBy: { createdAt: 'desc' }
      });

      if (transaction) {
        logger.info("Found original transaction by idEnvio in metadata", {
          transactionId: transaction.id,
          idEnvio: idEnvio,
          originalEndToEndId: transaction.endToEndId,
          reversalEndToEndId: endToEndId
        });
      }
    }

    // 3. Fallback: usar os identificadores tradicionais (txid, endToEndId) apenas se não encontrou pelo idEnvio
    if (!transaction) {
      logger.warn("Could not find transaction by idEnvio, trying fallback search", {
        idEnvio,
        txid,
        endToEndId
      });

      transaction = await findTransactionByIdentifiers([
        txid,
        payload.codigoTransacao // Direct Flow2Pay field
      ].filter(Boolean));
    }

    // 4. Se ainda não encontrou, tentar buscar pelo endToEndId original (não o de estorno)
    // Nota: Este é um fallback para casos onde o endToEndId no payload seja da transação original
    if (!transaction && endToEndId) {
      transaction = await db.transaction.findFirst({
        where: {
          endToEndId: endToEndId,
          type: "CHARGE",
          status: "APPROVED",
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (transaction) {
        logger.info("Found transaction by endToEndId field for reversal", {
          transactionId: transaction.id,
          endToEndId: endToEndId,
          txid
        });
      }
    }

    // 5. Último fallback: buscar por valor
    if (!transaction && amount) {
      // For reversals, amount is typically negative, so we search for the positive amount
      const searchAmount = Math.abs(amount);

      transaction = await db.transaction.findFirst({
        where: {
          amount: searchAmount,
          type: "CHARGE",
          status: "APPROVED", // Only approved transactions can be reversed
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (transaction) {
        logger.info("Found transaction by amount fallback for reversal", {
          transactionId: transaction.id,
          amount: searchAmount,
          txid
        });
      }
    }

    if (!transaction) {
      logger.error("Original transaction not found for PixInReversal webhook", {
        txid,
        endToEndId,
        idEnvio, // Adicionado para debug
        amount,
        searchIdentifiers: [txid, idEnvio, endToEndId].filter(Boolean),
        searchStrategy: "Tried idEnvio first, then fallback methods"
      });
      return;
    }

    logger.info("Found original transaction for PixInReversal webhook", {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      transactionType: transaction.type,
      organizationId: transaction.organizationId,
      originalAmount: transaction.amount,
      reversalAmount: amount,
      originalEndToEndId: transaction.endToEndId,
      reversalEndToEndId: endToEndId,
      linkingIdEnvio: idEnvio
    });

    // Enhanced status mapping for reversal
    let transactionStatus: TransactionStatus = "REFUNDED";
    const statusLower = status.toLowerCase();

    if (statusLower.includes("sucesso") ||
        statusLower.includes("success") ||
        statusLower.includes("completed") ||
        statusLower.includes("finalizado") ||
        statusLower.includes("aprovado")) {
      transactionStatus = "REFUNDED";
    } else if (statusLower.includes("processamento") ||
               statusLower.includes("processing") ||
               statusLower.includes("em_andamento") ||
               statusLower.includes("pendente")) {
      transactionStatus = "PROCESSING";
    } else if (statusLower.includes("falha") ||
               statusLower.includes("failed") ||
               statusLower.includes("erro") ||
               statusLower.includes("rejeitado") ||
               statusLower.includes("rejected")) {
      transactionStatus = "REJECTED";
    }

    logger.info("Mapped reversal webhook status", {
      originalStatus: status,
      mappedStatus: transactionStatus,
      currentTransactionStatus: transaction.status,
      willUpdate: transaction.status !== transactionStatus
    });

    // Only update if status changed and it's a valid reversal status
    if (transaction.status !== transactionStatus && (transactionStatus === "REFUNDED" || transactionStatus === "PROCESSING")) {
      const previousStatus = transaction.status;

      // Update transaction to reversal status
      const updatedTransactionData: any = {
        status: transactionStatus,
        metadata: {
          ...(transaction.metadata as any || {}),
          lastWebhookId: webhookId,
          lastWebhookAt: new Date().toISOString(),
          endToEndId: endToEndId,
          deduplicationKey: deduplicationKey,
          // Store original webhook payload for debugging
          lastWebhookPayload: JSON.stringify(payload),
          // Flag to indicate this was updated by webhook to avoid recursive events
          updatedByWebhook: true,
          webhookSource: 'pluggou-pix',
          // Reversal specific metadata
          reversalDetails: {
            reversalAmount: Math.abs(amount || 0),
            reversalEndToEndId: endToEndId,
            reversalTxid: txid,
            reversalIdEnvio: idEnvio, // Armazenar o idEnvio que fez a ligação
            reversalTimestamp: new Date().toISOString(),
            reversalStatus: status,
            originalEndToEndId: transaction.endToEndId // Armazenar o endToEndId original
          }
        },
        endToEndId: endToEndId,
        processedAt: transactionStatus === "REFUNDED" ? new Date() : null // Mark as processed only when refunded
      };

      // Update transaction with reversal data
      const updatedTransaction = await db.transaction.update({
        where: { id: transaction.id },
        data: updatedTransactionData
      });

      logger.info("Transaction status updated for reversal", {
        transactionId: transaction.id,
        previousStatus,
        newStatus: transactionStatus,
        webhookId,
        updatedByWebhook: true,
        willSendWebhook: true,
        reversalAmount: Math.abs(amount || 0),
        linkingIdEnvio: idEnvio
      });

      // Update organization balance for PIX IN reversal - only when REFUNDED
      if (transactionStatus === "REFUNDED") {
        try {
          const reversalAmount = Math.abs(amount || transaction.amount);

          // For PIX IN reversal, when approved, debit the amount (reverse the original credit)
          await updateOrganizationBalance(
            transaction.organizationId,
            reversalAmount,
            BalanceOperationType.DEBIT,
            transaction.id,
            `Estorno PIX aprovado via webhook Pluggou: ${transaction.id} (valor estornado: ${reversalAmount})`
          );

          logger.info("Organization balance debited for approved PIX IN reversal", {
            transactionId: transaction.id,
            organizationId: transaction.organizationId,
            reversalAmount: reversalAmount,
            operation: BalanceOperationType.DEBIT
          });
        } catch (balanceError) {
          logger.error("Error updating organization balance for PIX IN reversal", {
            error: balanceError instanceof Error ? balanceError.message : String(balanceError),
            transactionId: transaction.id,
            organizationId: transaction.organizationId
          });
        }
      }

      // Send webhook for reversal - use specific reversal event type
      logger.info("Sending webhook for PIX IN reversal", {
        transactionId: transaction.id,
        status: transactionStatus,
        previousStatus
      });
      await sendWebhookDirectly(updatedTransaction, previousStatus, transactionStatus);
    } else {
      logger.info("Transaction status unchanged or invalid reversal status, skipping update", {
        transactionId: transaction.id,
        currentStatus: transaction.status,
        webhookStatus: transactionStatus,
        isValidReversal: transactionStatus === "REFUNDED" || transactionStatus === "PROCESSING"
      });
    }

  } catch (error) {
    logger.error("Error processing PixInReversal webhook", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      payload,
      webhookId
    });
    throw error;
  }
}

/**
 * Send webhook directly via SVIX - NO DATABASE EVENT SAVING
 */
async function sendWebhookDirectly(transaction: any, previousStatus: string, newStatus: string) {
  try {
    // Check if transaction exists
    if (!transaction) {
      logger.error("Transaction is null or undefined", {
        previousStatus,
        newStatus
      });
      return;
    }

    // Log detailed transaction info for debugging
    logger.info("Preparing to send webhook", {
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
      transactionType: transaction.type,
      previousStatus,
      newStatus,
      hasOrganizationId: !!transaction.organizationId
    });

    // Verify organization exists before sending webhook
    if (transaction.organizationId) {
      const organization = await db.organization.findUnique({
        where: { id: transaction.organizationId },
        select: { id: true, name: true, status: true }
      });

      if (!organization) {
        logger.error("Organization not found for transaction", {
          transactionId: transaction.id,
          organizationId: transaction.organizationId
        });
        return;
      }

      logger.info("Organization verified for webhook", {
        transactionId: transaction.id,
        organizationId: organization.id,
        organizationName: organization.name,
        organizationStatus: organization.status
      });
    } else {
      logger.error("Transaction has no organizationId", {
        transactionId: transaction.id,
        transaction: {
          id: transaction.id,
          type: transaction.type,
          status: transaction.status,
          amount: transaction.amount
        }
      });
      return;
    }

    // Determine event type
    let eventType: string;
    const isConfirmation = newStatus === "APPROVED";
    const isRefund = newStatus === "REFUNDED";

    logger.info("Determining webhook event type", {
      transactionId: transaction.id,
      transactionType: transaction.type,
      newStatus,
      isConfirmation,
      isRefund
    });

    if (isRefund && transaction.type === "CHARGE") {
      // PIX IN Reversal
      eventType = "pix.in.reversal.confirmation";
    } else if (transaction.type === "SEND" && isConfirmation) {
      eventType = "pix.out.confirmation";
    } else if (transaction.type === "CHARGE" && isConfirmation) {
      eventType = "pix.in.confirmation";
    } else if (transaction.type === "SEND" && newStatus === "PROCESSING") {
      eventType = "pix.out.processing";
    } else if (transaction.type === "CHARGE" && newStatus === "PROCESSING") {
      eventType = "pix.in.processing";
    } else if (newStatus === "REJECTED") {
      eventType = transaction.type === "SEND" ? "pix.out.failure" : "transaction.failed";
    } else {
      eventType = "transaction.updated";
    }

    logger.info("Selected webhook event type", {
      transactionId: transaction.id,
      eventType,
      transactionType: transaction.type,
      newStatus,
      isConfirmation,
      isRefund
    });

    logger.info("Sending webhook directly via SVIX", {
      transactionId: transaction.id,
      eventType,
      organizationId: transaction.organizationId
    });

    // Create webhook payload in the SAME format as createWebhookEvent
    const transactionData = {
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      endToEndId: transaction.endToEndId,
      status: transaction.status,
      previousStatus,
      type: transaction.type,
      amount: transaction.amount,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      customerDocument: transaction.customerDocument,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      paymentAt: transaction.paymentAt,
      organizationId: transaction.organizationId,
      pixKey: transaction.pixKey,
      pixKeyType: transaction.pixKeyType,
      description: transaction.description,
      percentFee: transaction.percentFee,
      fixedFee: transaction.fixedFee,
      totalFee: transaction.totalFee,
      netAmount: transaction.netAmount,
    };

    // Use the SAME structured format as createWebhookEvent
    const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");

    await createWebhookEvent({
      type: eventType,
      payload: transactionData,
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
    });

    logger.info("Webhook sent successfully via structured format", {
      transactionId: transaction.id,
      eventType,
      organizationId: transaction.organizationId,
      format: "structured"
    });

  } catch (error) {
    logger.error("Error sending webhook directly", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: transaction?.id || 'unknown',
      organizationId: transaction?.organizationId || 'unknown',
      errorType: error?.constructor?.name
    });
  }
}

/**
 * Find transaction by identifiers
 */
async function findTransactionByIdentifiers(identifiers: string[]) {
  if (identifiers.length === 0) {
    logger.warn("No identifiers provided for transaction search");
    return null;
  }

  logger.info("Searching for transaction by identifiers", {
    identifiers,
    identifierCount: identifiers.length
  });

  // Try externalId first
  let transaction = await db.transaction.findFirst({
    where: {
      externalId: { in: identifiers }
    }
  });

  if (transaction) {
    logger.info("Found transaction by externalId", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      matchedIdentifier: identifiers.find(id => id === transaction?.externalId)
    });
    return transaction;
  }

  logger.info("No transaction found by externalId, trying endToEndId search");

  // Try endToEndId search
  transaction = await db.transaction.findFirst({
    where: {
      endToEndId: { in: identifiers }
    }
  });

  if (transaction) {
    logger.info("Found transaction by endToEndId", {
      transactionId: transaction.id,
      endToEndId: transaction.endToEndId,
      matchedIdentifier: identifiers.find(id => id === transaction?.endToEndId)
    });
    return transaction;
  }

  logger.info("No transaction found by endToEndId, trying metadata search");

  // Enhanced metadata search with more possible paths
  const metadataConditions = identifiers.flatMap(id => [
    { metadata: { path: ['flow2pay_id'], equals: id } },
    { metadata: { path: ['txid'], equals: id } },
    { metadata: { path: ['idEnvio'], equals: id } },
    { metadata: { path: ['id_envio'], equals: id } },
    { metadata: { path: ['codigoTransacao'], equals: id } },
    { metadata: { path: ['endToEndId'], equals: id } },
    // Add paths for pluggou specific metadata
    { metadata: { path: ['pluggou', 'txid'], equals: id } },
    { metadata: { path: ['pluggou', 'flow2pay_id'], equals: id } },
    { metadata: { path: ['pluggou', 'id'], equals: id } },
    { metadata: { path: ['pluggou', 'transaction_id'], equals: id } },
    { metadata: { path: ['pluggou', 'endToEndId'], equals: id } }
  ]);

  logger.info("Searching with enhanced metadata conditions", {
    conditionCount: metadataConditions.length,
    identifiers
  });

  transaction = await db.transaction.findFirst({
    where: { OR: metadataConditions }
  });

  if (transaction) {
    logger.info("Found transaction by metadata search", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      metadata: transaction.metadata
    });
  } else {
    logger.warn("No transaction found by any identifier", {
      identifiers,
      searchMethods: ['externalId', 'endToEndId', 'metadata']
    });
  }

  return transaction;
}

/**
 * Determine event type from payload
 */
function determineEventType(payload: any): 'pixin' | 'pixout' | 'pixinreversal' | 'unknown' {
  // Check multiple possible event fields, including the direct 'evento' field from Flow2Pay
  const eventFields = [
    payload.evento, // Direct Flow2Pay event field
    payload.event,
    payload.event_type,
    payload.flow2pay_event_type,
    payload.svix_event_type,
    payload.webhook_payload?.evento,
    payload.data?.evento
  ].filter(Boolean);

  logger.info("Determining event type from fields", {
    eventFields,
    payload: {
      evento: payload.evento, // Add the direct evento field
      event: payload.event,
      event_type: payload.event_type,
      flow2pay_event_type: payload.flow2pay_event_type,
      svix_event_type: payload.svix_event_type,
      webhook_evento: payload.webhook_payload?.evento,
      data_evento: payload.data?.evento
    }
  });

  for (const event of eventFields) {
    if (!event) continue;

    const eventLower = String(event).toLowerCase();

    // Check for PixOut variations (including new pix.out format and Flow2Pay 'PixOut')
    if (eventLower.includes("pixout") ||
        eventLower.includes("pix_out") ||
        eventLower.includes("pix.out") ||
        eventLower === "pixout" ||
        eventLower.startsWith("pixout.") ||
        eventLower.startsWith("pix.out.")) {
      logger.info("Detected PixOut event", { event, eventLower });
      return 'pixout';
    }

    // Check for PixInReversal FIRST (before PixIn to avoid false matches)
    if (eventLower.includes("pixinreversal")) {
      logger.info("Detected PixInReversal event", { event, eventLower });
      return 'pixinreversal';
    }

    // Check for PixIn variations (including new pix.in format and Flow2Pay 'PixIn')
    if (eventLower.includes("pixin") ||
        eventLower.includes("pix_in") ||
        eventLower.includes("pix.in") ||
        eventLower === "pixin" ||
        eventLower.startsWith("pixin.") ||
        eventLower.startsWith("pix.in.")) {
      logger.info("Detected PixIn event", { event, eventLower });
      return 'pixin';
    }
  }

  logger.warn("Could not determine event type", {
    eventFields,
    payload: JSON.stringify(payload, null, 2)
  });

  return 'unknown';
}

/**
 * Create webhook ID for deduplication
 */
function createWebhookId(payload: any): string {
  const key = `${payload.flow2pay_id || payload.data?.idEnvio || 'unknown'}-${payload.status || 'unknown'}-${payload.timestamp || Date.now()}`;

  let hash = 0;
  for (let i = 0; i < key.length; i++) {
    const char = key.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }

  return Math.abs(hash).toString(16);
}
