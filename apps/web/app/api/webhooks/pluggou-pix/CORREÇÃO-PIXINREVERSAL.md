# Correção do Webhook PixInReversal

## Problema Identificado

O webhook `PixInReversal` não estava reconhecendo a transação original quando chegava o evento de estorno. Isso acontecia porque o sistema estava tentando buscar a transação original usando o `endToEndId` da **nova transação de estorno**, quando deveria usar o `idEnvio` para fazer a vinculação correta.

### Análise do Fluxo

Baseado na análise dos logs fornecidos:

1. **Requisição de Estorno:**
   - EndToEndId da transação original: `E18236120202505281530s02ad8e89af`
   - Flow2Pay retorna um `transaction_code`: `f9502e46-c58f-46d6-a88e-ef24ee051ee7`

2. **Webhook de Resposta:**
   - EndToEndId da nova transação de estorno: `D14483955202505290233I9VE07UZ85K`
   - `idEnvio` (que é o `transaction_code`): `f9502e46-c58f-46d6-a88e-ef24ee051ee7`

### O Problema

O código anterior tentava buscar a transação original usando:
- O `endToEndId` da transação de estorno (`D144...`)
- Outros identificadores secundários

Mas **nunca** usava o `idEnvio` (`f9502e46-c58f-46d6-a88e-ef24ee051ee7`), que é o campo que realmente vincula a requisição de estorno ao webhook de resposta.

## Solução Implementada

### 1. Extração do `idEnvio`

Adicionamos a extração do campo `idEnvio` do payload do webhook:

```typescript
// CORREÇÃO: O idEnvio é o campo que vincula a requisição de estorno ao webhook
const idEnvio = payload.idEnvio ||
                payload.id_envio ||
                payload.data?.idEnvio ||
                payload.webhook_payload?.idEnvio;
```

### 2. Nova Estratégia de Busca

Implementamos uma estratégia de busca em ordem de prioridade:

#### Prioridade 1: Buscar por `idEnvio` como `externalId`
```typescript
if (idEnvio) {
  transaction = await db.transaction.findFirst({
    where: {
      externalId: idEnvio,
      type: "CHARGE",
      status: "APPROVED"
    },
    orderBy: { createdAt: 'desc' }
  });
}
```

#### Prioridade 2: Buscar por `idEnvio` nos metadados
```typescript
if (!transaction && idEnvio) {
  transaction = await db.transaction.findFirst({
    where: {
      OR: [
        { metadata: { path: ['idEnvio'], equals: idEnvio } },
        { metadata: { path: ['transaction_code'], equals: idEnvio } },
        { metadata: { path: ['id_envio'], equals: idEnvio } },
        { metadata: { path: ['codigoTransacao'], equals: idEnvio } },
        { metadata: { path: ['flow2pay', 'idEnvio'], equals: idEnvio } },
        { metadata: { path: ['flow2pay', 'transaction_code'], equals: idEnvio } }
      ],
      type: "CHARGE",
      status: "APPROVED"
    }
  });
}
```

#### Prioridade 3: Fallbacks (métodos anteriores)
- Busca por `txid` e outros identificadores
- Busca por `endToEndId` (caso seja da transação original)
- Busca por valor como último recurso

### 3. Logging Aprimorado

Adicionamos logs detalhados para facilitar o debug:

```typescript
logger.info("Processing PixInReversal webhook", {
  txid,
  status,
  amount,
  endToEndId,
  idEnvio, // Adicionado para debug
  webhookId,
  // ...
});
```

### 4. Metadados Aprimorados

Armazenamos informações detalhadas sobre o estorno:

```typescript
reversalDetails: {
  reversalAmount: Math.abs(amount || 0),
  reversalEndToEndId: endToEndId,
  reversalTxid: txid,
  reversalIdEnvio: idEnvio, // Armazenar o idEnvio que fez a ligação
  reversalTimestamp: new Date().toISOString(),
  reversalStatus: status,
  originalEndToEndId: transaction.endToEndId // Armazenar o endToEndId original
}
```

## Como Testar a Correção

1. **Execute o script de teste:**
   ```bash
   npx tsx test-reversal-fix.ts
   ```

2. **Verifique os logs do webhook** quando um estorno real for processado

3. **Confirme que a transação original é encontrada** usando o `idEnvio`

## Pontos Importantes

### Para Desenvolvedores

1. **Sempre use o `idEnvio`** como identificador principal para estornos
2. **O `endToEndId` no webhook de estorno** é da NOVA transação, não da original
3. **O `transaction_code` retornado na API** deve ser armazenado para vinculação posterior

### Para Operações

1. **Monitore os logs** para verificar se as transações estão sendo encontradas
2. **Verifique se o `idEnvio` está presente** nos webhooks de estorno
3. **Confirme que as transações originais** têm o `transaction_code` armazenado corretamente

## Resultado Esperado

Com esta correção, o webhook `PixInReversal` deve:

1. ✅ Encontrar a transação original usando o `idEnvio`
2. ✅ Atualizar o status para `REFUNDED` corretamente
3. ✅ Processar o estorno e atualizar o saldo da organização
4. ✅ Enviar o webhook de notificação para o cliente

## Arquivos Modificados

- `apps/web/app/api/webhooks/pluggou-pix/route.ts` - Handler principal corrigido
- `test-reversal-fix.ts` - Script de teste criado
- `CORREÇÃO-PIXINREVERSAL.md` - Esta documentação

## Próximos Passos

1. **Testar em ambiente de desenvolvimento**
2. **Validar com dados reais de estorno**
3. **Monitorar logs em produção**
4. **Documentar o processo para a equipe**
