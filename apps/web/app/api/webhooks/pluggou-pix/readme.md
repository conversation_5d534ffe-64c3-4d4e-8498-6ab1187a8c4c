# PIX API Documentation

## Overview
This project implements a set of endpoints for the PIX payment system, allowing users to generate QR codes, transfer funds, check account balances, and manage webhook configurations. The API operates in pure proxy mode, meaning it does not store any data in a database.

## Endpoints

### 1. Generate PIX QR Code
- **Method**: POST
- **Path**: `/qrcode`
- **Description**: Generates a QR code for receiving PIX payments.
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `user_id` (optional): User identifier (string or number).
  - `amount` (required): Amount for the transaction.
  - `description` (optional): Description for the transaction.
  - `expiration_time` (optional): Expiration time for the QR code in seconds (default is 3600).
  - `ip_address` (optional): IP address of the user.
  - `user_agent` (optional): User agent string.
  - `numero_documento` (optional): CPF/CNPJ of the payer without special characters.
- **Returns**: `QRCodeResponse` containing:
  - `txid`: Transaction ID.
  - `qr_code_text`: QR code text.
  - `qr_code_image`: QR code image.
  - `expiration_time`: Expiration time.
  - `amount`: Amount.
  - `description`: Description.
  - `created_at`: Creation timestamp.

### 2. Get QR Code Status
- **Method**: GET
- **Path**: `/qrcode/:txid`
- **Description**: Retrieves the status of a QR code using its transaction ID.
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `txid` (required): Transaction ID.
- **Returns**: `QRCodeResponse` containing:
  - `txid`: Transaction ID.
  - `qr_code_text`: QR code text.
  - `qr_code_image`: QR code image.
  - `expiration_time`: Expiration time.
  - `amount`: Amount.
  - `description`: Description.
  - `created_at`: Creation timestamp.

### 3. Transfer PIX
- **Method**: POST
- **Path**: `/transfer`
- **Description**: Initiates a PIX transfer to another account.
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `user_id` (optional): User identifier.
  - `pix_key` (required): PIX key of the recipient.
  - `amount` (required): Amount for the transfer.
  - `description` (optional): Description for the transfer.
  - `ip_address` (optional): IP address of the user.
  - `user_agent` (optional): User agent string.
- **Returns**: `TransferResponse` containing:
  - `id_envio`: Transfer ID.
  - `status`: Status of the transfer.
  - `message`: Message regarding the transfer.
  - `transaction_id`: Optional transaction ID.

### 4. Get Account Balance
- **Method**: GET
- **Path**: `/balance`
- **Description**: Retrieves the account balance.
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `user_id` (optional): User identifier.
- **Returns**: `BalanceResponse` containing:
  - `balance`: Balance amount.
  - `currency`: Currency type.
  - `updated_at`: Last updated timestamp.

### 5. Get Transaction History
- **Method**: GET
- **Path**: `/transactions`
- **Description**: Retrieves the transaction history (returns a mock response).
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `user_id` (optional): User identifier.
  - `start_date` (optional): Start date for filtering transactions.
  - `end_date` (optional): End date for filtering transactions.
  - `type` (optional): Type of transactions to filter.
  - `page` (optional): Page number for pagination (default is 1).
  - `page_size` (optional): Number of transactions per page (default is 20).
- **Returns**: `TransactionHistoryResponse` containing:
  - `transactions`: Array of transactions.
  - `pagination`: Pagination information.

### 6. Create Webhook Configuration
- **Method**: POST
- **Path**: `/webhook-config`
- **Description**: Creates a webhook configuration (returns a mock response).
- **Parameters**:
  - `api_key` (required): Header for API authentication.
  - `user_id` (optional): User identifier.
  - `url` (required): URL for the webhook.
  - `events` (required): Array of event types for the webhook.
  - `description` (optional): Description for the webhook.
- **Returns**: Mock webhook configuration containing:
  - `id`: Webhook ID.
  - `url`: Webhook URL.
  - `events`: Event types.
  - `description`: Description.
  - `secret_key`: Secret key for webhook signatures.
  - `created_at`: Creation timestamp.

## Setup Instructions
1. Clone the repository.
2. Install dependencies using `npm install`.
3. Configure TypeScript by editing `tsconfig.json` if necessary.
4. Start the server and test the endpoints using a tool like Postman or curl.

## Usage Examples
Refer to the individual endpoint descriptions above for details on how to use each endpoint, including required parameters and expected responses.
