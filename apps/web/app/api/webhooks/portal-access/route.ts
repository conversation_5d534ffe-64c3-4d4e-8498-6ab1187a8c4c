import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { generatePortalAccessToken } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function POST(req: NextRequest) {
  try {
    logger.info("Portal access request received", {
      headers: Object.fromEntries(req.headers.entries()),
      url: req.url
    });

    const session = await auth.api.getSession({ headers: req.headers });
    logger.info("Session check result", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id
    });

    if (!session?.user?.id) {
      logger.warn("Unauthorized access attempt to portal access");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { organizationId } = await req.json();
    logger.info("Request body parsed", { organizationId });

    if (!organizationId) {
      logger.warn("Missing organization ID in request");
      return NextResponse.json({ error: "Organization ID required" }, { status: 400 });
    }

    logger.info("Generating portal access token", {
      userId: session.user.id,
      organizationId
    });

    const portalAccess = await generatePortalAccessToken(organizationId);
    logger.info("Portal access token generated successfully", {
      hasToken: !!portalAccess.token,
      hasUrl: !!portalAccess.url
    });

    return NextResponse.json({
      success: true,
      data: portalAccess
    });
  } catch (error) {
    logger.error("Failed to generate portal access", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { error: "Failed to generate portal access" },
      { status: 500 }
    );
  }
}
