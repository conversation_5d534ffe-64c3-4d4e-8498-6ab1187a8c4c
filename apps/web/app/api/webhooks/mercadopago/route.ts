import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import { TransactionStatus } from "@prisma/client";

// Endpoint GET para testes de webhook
export async function GET(_req: NextRequest) {
  logger.info("MercadoPago webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "MercadoPago webhook endpoint is working" });
}

export async function POST(req: NextRequest) {
  try {
    // Obter o corpo da requisição
    const payload = await req.json();

    logger.info("Received MercadoPago webhook", {
      headers: Object.fromEntries(req.headers),
      payload: JSON.stringify(payload).substring(0, 500) // Limitar o tamanho do log
    });

    // Verificar se o payload é válido
    if (!payload || !payload.data || !payload.type) {
      logger.error("Invalid webhook payload", { payload });
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    // Verificar se é um evento de pagamento
    if (payload.type !== "payment") {
      logger.info("Ignoring non-payment webhook", { type: payload.type });
      return NextResponse.json({ success: true, message: "Non-payment webhook ignored" });
    }

    // Obter o ID do pagamento
    const paymentId = payload.data.id;
    if (!paymentId) {
      logger.error("Payment ID not found in webhook", { payload });
      return NextResponse.json({ error: "Payment ID not found" }, { status: 400 });
    }

    // Buscar a transação pelo externalId
    const transaction = await db.transaction.findFirst({
      where: { externalId: paymentId.toString() }
    });

    if (!transaction) {
      logger.warn("Transaction not found for MercadoPago webhook", { paymentId });
      return NextResponse.json({ success: true, message: "Transaction not found, but webhook received" });
    }

    // Obter detalhes do pagamento da API do Mercado Pago
    // Nota: Em um cenário real, você deve usar o token de acesso da organização
    // Aqui estamos apenas processando o status do webhook
    const status = payload.data.status || "pending";
    
    // Mapear o status do Mercado Pago para o nosso status interno
    const internalStatus = mapMercadoPagoStatusToInternal(status);

    logger.info("Processing MercadoPago webhook", {
      transactionId: transaction.id,
      externalId: paymentId,
      status,
      internalStatus
    });

    // Verificar se é um estorno
    const isRefund = internalStatus === "REFUNDED";

    // Atualizar o status da transação
    await updateTransactionStatus(
      transaction.id,
      internalStatus as any,
      internalStatus === "APPROVED" ? new Date() : undefined
    );

    // Se for um estorno, criar um registro de estorno se não existir
    if (isRefund) {
      logger.info("Processing refund webhook", { transactionId: transaction.id, externalId: paymentId });
      
      // Verificar se a transação é uma transferência (SEND)
      if (transaction.type === "SEND") {
        logger.warn("Cannot refund transfer transaction via webhook", {
          transactionId: transaction.id,
          type: transaction.type,
          externalId: paymentId
        });
        return NextResponse.json({ 
          success: true, 
          message: "Transfer transactions cannot be refunded",
          transactionId: transaction.id
        });
      }
      
      // Verificar se já existe um estorno para esta transação
      const existingRefund = await db.refund.findFirst({
        where: { 
          transactionId: transaction.id,
          status: "APPROVED"
        }
      });
      
      if (!existingRefund) {
        try {
          // Criar um registro de estorno
          const refund = await db.refund.create({
            data: {
              externalId: `webhook_refund_${paymentId}`,
              amount: transaction.amount, // Estorno total por padrão
              reason: "Estorno processado via webhook do Mercado Pago",
              status: "APPROVED",
              transactionId: transaction.id,
              processedAt: new Date(),
            },
            include: {
              transaction: true,
            },
          });
          
          logger.info("Created refund record from MercadoPago webhook", {
            refundId: refund.id,
            transactionId: transaction.id,
            externalId: paymentId
          });
          
          // Atualizar o saldo da organização
          try {
            // Importar a função handler
            const { handleRefundApproved } = require("@repo/payments/src/transactions/listeners");
            
            // Processar a atualização de saldo
            await handleRefundApproved(refund, refund.transaction);
            
            logger.info("Organization balance updated for MercadoPago webhook refund", {
              refundId: refund.id,
              transactionId: transaction.id,
              organizationId: transaction.organizationId
            });
          } catch (error) {
            logger.error("Error updating organization balance for MercadoPago webhook refund", {
              error,
              refundId: refund.id,
              transactionId: transaction.id,
              organizationId: transaction.organizationId
            });
          }
        } catch (error) {
          logger.error("Error creating refund record from MercadoPago webhook", {
            error,
            transactionId: transaction.id,
            externalId: paymentId
          });
        }
      } else {
        logger.info("Refund already exists for this transaction", {
          existingRefundId: existingRefund.id,
          transactionId: transaction.id
        });
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: "Webhook processed successfully",
      transactionId: transaction.id,
      status: internalStatus
    });
  } catch (error) {
    logger.error("Error processing MercadoPago webhook", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Função auxiliar para mapear o status do Mercado Pago para o nosso status interno
function mapMercadoPagoStatusToInternal(mpStatus: string): string {
  switch (mpStatus.toLowerCase()) {
    case "pending":
      return "PENDING";
    case "approved":
      return "APPROVED";
    case "rejected":
      return "REJECTED";
    case "cancelled":
    case "canceled":
      return "CANCELED";
    case "in_process":
    case "in_mediation":
      return "PROCESSING";
    case "refunded":
    case "charged_back":
      return "REFUNDED";
    default:
      return "PENDING";
  }
}
