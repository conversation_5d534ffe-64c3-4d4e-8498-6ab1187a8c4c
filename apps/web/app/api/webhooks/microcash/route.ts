import { logger } from "@repo/logs";
import microcash from "@repo/payments/provider/microcash";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

interface WebhookResponse {
	success: boolean;
	message: string;
	transactionId?: string;
	provider?: string;
}

export async function processMicrocashWebhook(
	req: NextRequest,
): Promise<{ status: number; body: WebhookResponse }> {
	const startTime = Date.now();
	
	try {
		logger.info("Processing Microcash webhook request", {
			timestamp: new Date().toISOString(),
			userAgent: req.headers.get('user-agent'),
			contentType: req.headers.get('content-type'),
			contentLength: req.headers.get('content-length'),
		});

		// Get the raw body
		const rawBody = await req.text();

		if (!rawBody) {
			logger.error("Empty webhook payload received from Microcash", {
				timestamp: new Date().toISOString(),
				duration: `${Date.now() - startTime}ms`,
			});
			return {
				status: 400,
				body: {
					success: false,
					message: "Empty payload",
					provider: "microcash",
				},
			};
		}

		// Parse the JSON payload
		let payload: any;
		try {
			payload = JSON.parse(rawBody);
		} catch (parseError) {
			logger.error("Error parsing Microcash webhook payload", {
				parseError,
				rawBody: rawBody.substring(0, 500), // Limit log size
				timestamp: new Date().toISOString(),
				duration: `${Date.now() - startTime}ms`,
			});
			return {
				status: 400,
				body: {
					success: false,
					message: "Invalid JSON payload",
					provider: "microcash",
				},
			};
		}

		// Extract headers
		const headers: Record<string, string> = {};
		req.headers.forEach((value, key) => {
			headers[key] = value;
		});

		// Extract key webhook data for logging
		const txId = payload.txId || payload.TxId;
		const tid = payload.tid || payload.Tid;
		const status = payload.status || payload.Status;
		const endToEndId = payload.endToEndId || payload.EndToEndId;

		logger.info("Microcash webhook payload received", {
			payloadKeys: Object.keys(payload),
			payloadSize: rawBody.length,
			headers: Object.keys(headers),
			txId,
			tid,
			status,
			endToEndId,
			timestamp: new Date().toISOString(),
		});

		// Process the webhook using the provider's webhook handler
		const result = await microcash.webhookHandler({
			body: payload,
			headers,
			organizationId: undefined, // Will be determined from transaction lookup
		});

		const duration = Date.now() - startTime;

		logger.info("Microcash webhook processed successfully", { 
			result,
			txId,
			tid,
			status,
			endToEndId,
			transactionId: result.data?.transactionId,
			webhookProcessed: result.data?.webhookProcessed,
			duration: `${duration}ms`,
			timestamp: new Date().toISOString(),
		});

		return {
			status: 200,
			body: {
				success: true,
				message: "Webhook processed successfully",
				transactionId: result.data?.transactionId,
				provider: "microcash",
			},
		};
	} catch (error) {
		const duration = Date.now() - startTime;
		
		logger.error("Error processing Microcash webhook", {
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
			duration: `${duration}ms`,
			timestamp: new Date().toISOString(),
		});

		return {
			status: 500,
			body: {
				success: false,
				message:
					error instanceof Error ? error.message : "Internal server error",
				provider: "microcash",
			},
		};
	}
}

export async function POST(request: NextRequest) {
	try {
		const result = await processMicrocashWebhook(request);
		return NextResponse.json(result.body, { status: result.status });
	} catch (error) {
		logger.error("Unexpected error in Microcash webhook endpoint", { error });
		return NextResponse.json(
			{
				success: false,
				message: "Internal server error",
				provider: "microcash",
			},
			{ status: 500 },
		);
	}
}

// Health check endpoint
export async function GET() {
	return NextResponse.json({
		success: true,
		message: "Microcash webhook endpoint is healthy",
		provider: "microcash",
		timestamp: new Date().toISOString(),
	});
}
