import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";
import { TransactionStatus } from "@prisma/client";

// Endpoint GET for webhook testing
export async function GET(_req: NextRequest) {
  logger.info("MediusPag webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "MediusPag webhook endpoint is working" });
}

// Validate MediusPag webhook signature
async function validateMediusPagWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Get headers from the request object directly
    const headersList = Object.fromEntries(req.headers);

    logger.info("MediusPag webhook headers for signature validation", {
      headers: headersList
    });

    // Check for signature in multiple possible header formats
    // Some gateways use different header names in production vs documentation
    const possibleSignatureHeaders = [
      "x-mediuspag-signature",
      "x-signature",
      "mediuspag-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Try to find a signature in any of the possible headers
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        break;
      }
    }

    // Check for signature in the 'forwarded' header (MediusPag specific format)
    if (!signature) {
      const forwarded = req.headers.get("forwarded");
      if (forwarded) {
        logger.info('Found forwarded header, checking for signature', { forwarded });

        // Extract the sig parameter from the forwarded header
        // Format example: for=*******;host=example.com;proto=https;sig=0QmVhcmVyIDE0MjlmMDcwYzYxZjU2NmZiNDAwM2M2YzcxNzU5Njk2M2M3NjA3MWM2MDA2YjUwZWRkYzZkMzc2ZWI0OGZhZWM=;exp=1745632862
        const sigMatch = forwarded.match(/sig=([^;]+)/);
        if (sigMatch && sigMatch[1]) {
          signature = sigMatch[1];
          signatureHeaderUsed = "forwarded:sig";

          // If the signature starts with '0QmVhcmVyIA', it's likely Base64 encoded
          // and might need to be decoded first
          if (signature.startsWith('0QmVhcmVyIA')) {
            try {
              // Try to decode from Base64
              const decoded = Buffer.from(signature, 'base64').toString();
              // If it starts with "Bearer ", extract the token part
              if (decoded.startsWith('Bearer ')) {
                signature = decoded.substring(7); // Remove "Bearer " prefix
                logger.info('Extracted Bearer token from Base64 encoded signature', {
                  original: sigMatch[1],
                  decoded,
                  signature
                });
              }
            } catch (error) {
              logger.warn('Failed to decode Base64 signature', { error, signature });
              // Continue with the original signature
            }
          }
        }
      }
    }

    // If no signature found in any header
    if (!signature) {
      logger.warn('Missing MediusPag webhook signature in all possible headers', {
        checkedHeaders: [...possibleSignatureHeaders, "forwarded:sig"]
      });

      // Check if signature validation should be bypassed in production
      if (process.env.MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION === "true") {
        logger.warn('Bypassing signature validation due to MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION=true');
        return true;
      }

      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    // Get the webhook secret from environment variables
    const webhookSecret = process.env.MEDIUSPAG_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('MEDIUSPAG_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Compute the expected signature
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(body);
    const computedSignature = hmac.digest('hex');

    // Compare signatures - use a safe comparison to prevent timing attacks
    let isValid = false;
    try {
      isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (error) {
      // If the buffers are different lengths, timingSafeEqual throws an error
      // In this case, the signatures definitely don't match
      logger.warn('Error in signature comparison, likely different lengths', { error });
      isValid = false;
    }

    if (!isValid) {
      logger.warn('Invalid MediusPag webhook signature', {
        receivedSignature: signature,
        computedSignature,
        signatureHeaderUsed
      });

      // Check if signature validation should be bypassed in production
      if (process.env.MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION === "true") {
        logger.warn('Bypassing invalid signature due to MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION=true');
        return true;
      }
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating MediusPag webhook signature', { error });

    // Check if signature validation should be bypassed in production
    if (process.env.MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing signature validation error due to MEDIUSPAG_BYPASS_SIGNATURE_VALIDATION=true');
      return true;
    }

    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the raw request body for signature validation
    const rawBody = await req.text();

    // Log headers for debugging
    const headersObj = Object.fromEntries(req.headers);

    // Log environment information to help with debugging
    const isVercel = !!process.env.VERCEL;
    const vercelEnv = process.env.VERCEL_ENV || 'unknown';

    logger.info("Received MediusPag webhook", {
      headers: headersObj,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        isVercel,
        vercelEnv
      }
    });

    // Check if the body is empty
    if (!rawBody || rawBody.trim() === '') {
      logger.error('Empty webhook payload received');
      return NextResponse.json({ error: 'Empty payload' }, { status: 400 });
    }

    // Check if webhook validation should be bypassed using the global environment variable
    if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION === "true") {
      logger.warn('Bypassing webhook signature validation due to WEBHOOKS_BYPASS_SIGNATURE_VALIDATION=true');
    } else {
      // Validate webhook signature in production
      if (process.env.NODE_ENV === 'production') {
        try {
          const isValid = await validateMediusPagWebhook(req, rawBody);
          if (!isValid) {
            logger.error('Invalid webhook signature');
            return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
          }
        } catch (signatureError) {
          logger.error('Error during signature validation', { error: signatureError });
          return NextResponse.json({ error: 'Signature validation error' }, { status: 401 });
        }
      }
    }

    // Parse the webhook payload
    let payload;
    try {
      payload = JSON.parse(rawBody);

      // Check if payload is null or not an object
      if (payload === null || typeof payload !== 'object') {
        logger.error('Webhook payload is null or not an object', { rawBody });
        return NextResponse.json({ success: true, message: 'Ignoring invalid payload format' }, { status: 200 });
      }
    } catch (error) {
      logger.error('Error parsing webhook payload', { error, rawBody });
      return NextResponse.json({ success: true, message: 'Ignoring invalid JSON payload' }, { status: 200 });
    }

    // Log the webhook payload for debugging
    const payloadString = JSON.stringify(payload);
    const truncatedPayload = payloadString.substring(0, 1000) + (payloadString.length > 1000 ? '...' : '');

    logger.info("Processing MediusPag webhook payload", {
      payloadSize: payloadString.length,
      payload: truncatedPayload,
      webhookId: payload.id,
      objectId: payload.objectId,
      type: payload.type,
      status: payload.data?.status
    });

    // Enhanced deduplication using webhook ID and status
    const webhookId = payload.id;
    const objectId = payload.objectId; // This is the MediusPag transaction ID
    const status = payload.data?.status || "";

    // Create a deduplication key that includes the status
    const deduplicationKey = `mediuspag-${webhookId || 'unknown'}-${objectId || 'unknown'}-${status}`;

    logger.info("Processing MEDIUSPAG webhook with deduplication", {
      webhookId,
      objectId,
      status,
      deduplicationKey
    });

    // Check if we processed this exact webhook recently (last 5 minutes)
    const recentTransaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              {
                metadata: {
                  path: ['lastWebhook', 'webhookId'],
                  equals: webhookId
                }
              },
              {
                metadata: {
                  path: ['deduplicationKey'],
                  equals: deduplicationKey
                }
              }
            ]
          },
          {
            updatedAt: {
              gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
            }
          }
        ]
      }
    });

    if (recentTransaction) {
      logger.info("MEDIUSPAG webhook already processed recently, skipping", {
        webhookId,
        deduplicationKey,
        transactionId: recentTransaction.id,
        lastProcessed: recentTransaction.updatedAt,
        timeSinceLastProcessed: Date.now() - recentTransaction.updatedAt.getTime()
      });
      return NextResponse.json({ success: true, message: "Already processed" });
    }

    // Check if this is a test webhook
    if (payload.test === true) {
      logger.info("Received test webhook from MediusPag");
      return NextResponse.json({ success: true, message: "Test webhook received" });
    }

    // MediusPag webhook format: { id: 'WEBHOOK_ID', type: 'transaction', objectId: 'TRANSACTION_ID', data: { ... } }
    // Extract the transaction data (variables already declared above for deduplication)
    const transactionData = payload.data || {};

    // Para webhooks de criação de transação, não ignoramos mais
    // Vamos processar para garantir que a transação seja atualizada corretamente
    if (status === "waiting_payment" || status === "pending") {
      logger.info("Processando webhook de criação de transação", { webhookId, objectId, status });

      // Extrair o transactionId do metadata se disponível
      let internalTransactionId = null;
      try {
        if (transactionData.metadata) {
          const metadata = typeof transactionData.metadata === 'string'
            ? JSON.parse(transactionData.metadata)
            : transactionData.metadata;

          internalTransactionId = metadata.transactionId;

          logger.info("Metadata extraído do webhook", {
            metadata: JSON.stringify(metadata),
            internalTransactionId
          });
        }
      } catch (error) {
        logger.warn("Erro ao extrair metadata do webhook", { error });
      }

      // IMPROVED TRANSACTION SEARCH LOGIC
      let existingTransaction = await findTransactionByMultipleCriteria({
        externalId: objectId,
        internalId: internalTransactionId,
        customerEmail: transactionData.customer?.email,
        amount: transactionData.amount ? transactionData.amount / 100 : undefined
      });

      if (existingTransaction) {
        logger.info("Transação encontrada, atualizando metadata", {
          webhookId,
          objectId,
          existingTransactionId: existingTransaction.id,
          searchMethod: existingTransaction.searchMethod
        });

        // Se encontrou pelo ID interno mas não tem externalId, atualizar
        if (!existingTransaction.externalId && objectId) {
          await db.transaction.update({
            where: { id: existingTransaction.id },
            data: { externalId: objectId }
          });
          logger.info("Updated transaction with external ID", {
            transactionId: existingTransaction.id,
            externalId: objectId
          });
        }

        // Extrair dados do PIX se disponíveis
        const pixData = transactionData.pix || {};
        const pixCode = pixData.qrcode || pixData.qr_code || pixData.qrCodeText;
        const pixQrCode = pixData.qrcode_image || pixData.qr_code_image || pixData.encodedImage;
        const pixExpiresAt = pixData.expiration_date || pixData.expirationDate;

        // Atualizar a transação com os dados do webhook
        const updatedTransaction = await db.transaction.update({
          where: { id: existingTransaction.id },
          data: {
            metadata: {
              ...(typeof existingTransaction.metadata === 'object' ? existingTransaction.metadata : {}),
              lastWebhook: {
                receivedAt: new Date().toISOString(),
                status,
                webhookId
              },
              deduplicationKey: deduplicationKey,
              ...(pixCode && { pixCode }),
              ...(pixQrCode && { pixQrCode }),
              ...(pixExpiresAt && { pixExpiresAt })
            }
          }
        });

        // Send SVIX webhook event for transaction creation/update using standard service
        try {
          logger.info("Sending SVIX webhook for MEDIUSPAG transaction creation", {
            transactionId: existingTransaction.id,
            status,
            provider: "MEDIUSPAG"
          });

          const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
          await triggerTransactionEvents(updatedTransaction, existingTransaction.status);
        } catch (webhookError) {
          logger.error("Error sending SVIX webhook for transaction creation", {
            error: webhookError,
            transactionId: existingTransaction.id
          });
        }
      } else {
        logger.info("Transação não encontrada para webhook de criação", {
          webhookId,
          objectId,
          internalTransactionId,
          customerEmail: transactionData.customer?.email,
          amount: transactionData.amount ? transactionData.amount / 100 : null
        });

        // Não criamos uma nova transação aqui, pois isso deve ser feito pelo fluxo normal
        // de criação de transação, não pelo webhook
      }

      return NextResponse.json({ success: true, message: "Transaction creation webhook processed" });
    }

    // Only process webhooks for paid or refunded transactions
    const relevantStatuses = ["approved", "completed", "paid", "refunded", "chargeback"];
    const isRelevantStatus = status && relevantStatuses.includes(status.toLowerCase());

    if (!isRelevantStatus) {
      logger.info("Ignoring webhook with non-relevant status", { webhookId, objectId, status });
      return NextResponse.json({ success: true, message: "Non-relevant status webhook received" });
    }

    // Safely parse metadata if it exists and is a string
    let metadata = {};
    if (transactionData.metadata) {
      try {
        if (typeof transactionData.metadata === 'string') {
          metadata = JSON.parse(transactionData.metadata);
        } else if (typeof transactionData.metadata === 'object') {
          metadata = transactionData.metadata;
        }
      } catch (error) {
        logger.warn('Error parsing transaction metadata', {
          error,
          metadata: transactionData.metadata
        });
      }
    }

    // Extrair o ID da nossa transação interna do metadata do webhook
    let transactionId: string | undefined;

    // Tentar extrair o metadata do webhook
    try {
      let webhookMetadata: any = {};

      if (typeof transactionData.metadata === 'string') {
        webhookMetadata = JSON.parse(transactionData.metadata);
      } else if (typeof transactionData.metadata === 'object') {
        webhookMetadata = transactionData.metadata;
      }

      transactionId = webhookMetadata?.transactionId;
    } catch (error) {
      logger.warn("Erro ao analisar metadata do webhook para extrair transactionId", { error });
    }

    logger.info("Extracted data from MediusPag webhook", {
      webhookId,
      objectId,
      status,
      transactionId,
      metadata
    });

    if (!objectId && !transactionId) {
      logger.error("Missing transaction identifiers in webhook", { payload });
      return NextResponse.json({ error: "Missing transaction identifiers" }, { status: 400 });
    }

    // Map MediusPag status to our internal status
    const internalStatus = mapMediusPagStatusToInternal(status);
    logger.info("Mapped status", { mediuspagStatus: status, internalStatus });

    // IMPROVED TRANSACTION SEARCH FOR PAYMENT CONFIRMATION
    let transaction = await findTransactionByMultipleCriteria({
      externalId: objectId,
      internalId: transactionId,
      customerEmail: transactionData.customer?.email,
      amount: transactionData.amount ? transactionData.amount / 100 : undefined
    });

    if (!transaction) {
      // Log detailed information for debugging
      logger.warn("Transaction not found for MediusPag webhook", {
        webhookId,
        objectId,
        transactionId,
        customerEmail: transactionData.customer?.email,
        amount: transactionData.amount ? transactionData.amount / 100 : null,
        payload: JSON.stringify(payload)
      });

      // Return 200 instead of 404 to prevent MediusPag from retrying
      // This is a common practice for webhooks to avoid unnecessary retries
      return NextResponse.json({
        success: false,
        message: "Transaction not found, but webhook received"
      }, { status: 200 });
    }

    logger.info("Transaction found for webhook processing", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      currentStatus: transaction.status,
      newStatus: internalStatus,
      searchMethod: transaction.searchMethod
    });

    // Update metadata with additional information from the webhook
    const updatedMetadata: Record<string, any> = {
      ...(typeof transaction.metadata === 'object' ? transaction.metadata : {})
    };

    // Add webhook data to metadata with proper provider identification
    updatedMetadata.lastWebhook = {
      receivedAt: new Date().toISOString(),
      webhookId,
      status,
      internalStatus
    };

    // Add webhook update info to identify this as a provider webhook
    updatedMetadata.webhookUpdate = {
      provider: "MEDIUSPAG",
      webhookSource: "mediuspag",
      updatedAt: new Date().toISOString(),
      webhookId,
      originalStatus: status
    };

    // Mark as updated by webhook for filtering logic
    updatedMetadata.updatedByWebhook = true;

    // Add deduplication key to metadata
    updatedMetadata.deduplicationKey = deduplicationKey;

    // If there's payment information, add it to metadata
    if (transactionData.paidAt) {
      updatedMetadata.paidAt = transactionData.paidAt;
    }

    // If there's PIX information, add it to metadata
    if (transactionData.pix) {
      updatedMetadata.pixInfo = transactionData.pix;
    }

    // If we have the MediusPag transaction ID and it's not already set, update it
    if (objectId && transaction.externalId !== objectId) {
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          externalId: objectId,
          metadata: updatedMetadata
        },
      });
    } else {
      // Just update the metadata
      await db.transaction.update({
        where: { id: transaction.id },
        data: { metadata: updatedMetadata },
      });
    }

    try {
      const previousStatus = transaction.status;

      // Note: Removed duplicate balance update here
      // The balance will be updated by updateTransactionStatus -> handleTransactionStatusChange -> processTransactionApproval
      // This prevents the balance duplication issue where the same transaction was credited twice

      // Update the transaction status
      const result = await updateTransactionStatus(
        transaction.id,
        internalStatus as TransactionStatus,
        internalStatus === "APPROVED" ? new Date() : undefined
      );

      logger.info("Updated transaction status from MediusPag webhook", {
        transactionId: transaction.id,
        oldStatus: transaction.status,
        newStatus: internalStatus,
        result
      });

      // Trigger webhook events manually to ensure SVIX events are sent
      // This is needed because updateTransactionStatus might not trigger them for webhook-updated transactions
      try {
        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        const updatedTransaction = await db.transaction.findUnique({
          where: { id: transaction.id }
        });

        if (updatedTransaction) {
          await triggerTransactionEvents(updatedTransaction, previousStatus, true); // Force confirmation event

          logger.info("Manually triggered webhook events for MediusPag confirmation", {
            transactionId: transaction.id,
            previousStatus,
            newStatus: internalStatus,
            provider: "MEDIUSPAG"
          });
        }
      } catch (webhookError) {
        logger.error("Error triggering webhook events for MediusPag", {
          error: webhookError,
          transactionId: transaction.id
        });
      }
    } catch (error) {
      logger.error("Error updating transaction status", {
        error,
        transactionId: transaction.id,
        status: internalStatus
      });
      // Não interromper o fluxo se houver erro na atualização de status
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error processing MediusPag webhook", { error });
    // Return success even on error to prevent MediusPag from retrying
    return NextResponse.json({ success: true, message: "Webhook received with errors" }, { status: 200 });
  }
}

// IMPROVED FUNCTION TO FIND TRANSACTIONS BY MULTIPLE CRITERIA
async function findTransactionByMultipleCriteria(criteria: {
  externalId?: string;
  internalId?: string;
  customerEmail?: string;
  amount?: number;
}): Promise<any> {
  const { externalId, internalId, customerEmail, amount } = criteria;

  logger.info("Searching for transaction with multiple criteria", criteria);

  // 1. First try to find by our internal ID from metadata
  if (internalId) {
    const transaction = await db.transaction.findUnique({
      where: { id: internalId }
    });

    if (transaction) {
      logger.info("Transaction found by internal ID", { transactionId: internalId });
      return { ...transaction, searchMethod: "internal_id" };
    }
  }

  // 2. If not found, try to find by external ID
  if (externalId) {
    const transaction = await db.transaction.findFirst({
      where: { externalId: externalId }
    });

    if (transaction) {
      logger.info("Transaction found by external ID", { externalId });
      return { ...transaction, searchMethod: "external_id" };
    }
  }

  // 3. Try to find by matching customer data for recent transactions
  if (customerEmail && amount && amount > 0) {
    // Buscar transações recentes com os mesmos dados
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const transaction = await db.transaction.findFirst({
      where: {
        customerEmail,
        amount,
        status: { in: ["PENDING", "PROCESSING"] },
        type: "CHARGE",
        createdAt: {
          gte: fiveMinutesAgo
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    if (transaction) {
      logger.info("Transaction found by matching customer data", {
        transactionId: transaction.id,
        customerEmail,
        amount
      });
      return { ...transaction, searchMethod: "customer_data_match" };
    }
  }

  // 4. Last resort: find any recent transaction from the same customer
  if (customerEmail) {
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);

    const transaction = await db.transaction.findFirst({
      where: {
        customerEmail,
        status: { in: ["PENDING", "PROCESSING"] },
        type: "CHARGE",
        createdAt: {
          gte: tenMinutesAgo
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    if (transaction) {
      logger.info("Transaction found by customer email (last resort)", {
        transactionId: transaction.id,
        customerEmail
      });
      return { ...transaction, searchMethod: "customer_email_fallback" };
    }
  }

  logger.warn("Transaction not found with any criteria", criteria);
  return null;
}

// Helper function to map MediusPag status to our internal status
function mapMediusPagStatusToInternal(mediuspagStatus: string): string {
  if (!mediuspagStatus) return "PENDING";

  switch (mediuspagStatus.toLowerCase()) {
    case "pending":
    case "waiting_payment":
      return "PENDING";
    case "approved":
    case "completed":
    case "paid":
      return "APPROVED";
    case "rejected":
    case "failed":
    case "refused":
      return "REJECTED";
    case "canceled":
    case "cancelled":
      return "CANCELED";
    case "processing":
      return "PROCESSING";
    case "refunded":
    case "chargeback":
      return "REFUNDED";
    default:
      logger.warn("Unknown MediusPag status", { mediuspagStatus });
      return "PENDING";
  }
}


