import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { TransactionStatus } from "@prisma/client";
import { updateTransactionStatus } from "@repo/payments";
import { updateOrganizationBalance, BalanceOperationType } from "@repo/payments/src/balance/balance-service";
import { calculateTransactionFees } from "@repo/payments/src/taxes/calculator";
import * as crypto from "crypto";

export async function GET(_req: NextRequest) {
  return NextResponse.json({
    message: "XDPAG webhook endpoint is working"
  });
}

// Validar assinatura do webhook da XDPAG
async function validateXdpagWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Log todos os headers disponíveis para debug
    const allHeaders: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      allHeaders[key] = value;
    });

    logger.info('XDPAG webhook signature validation - checking headers', {
      allHeaders,
      bodyLength: body.length,
      bodyPreview: body.substring(0, 100) + '...'
    });

    // Verificar assinatura em possíveis headers
    const possibleSignatureHeaders = [
      "x-xdpag-signature",
      "x-signature",
      "xdpag-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Tentar encontrar assinatura em qualquer um dos headers possíveis
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        break;
      }
    }

    // Se não encontrou assinatura em nenhum header
    if (!signature) {
      logger.warn('Missing XDPAG webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders,
        allAvailableHeaders: Array.from(req.headers.keys()),
        debugInfo: 'XDPAG may not send signature headers - check webhook documentation'
      });
      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    // Obter o segredo do webhook das variáveis de ambiente
    const webhookSecret = process.env.XDPAG_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('XDPAG_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Computar a assinatura esperada usando HMAC-SHA256
    const computedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');

    // Comparar assinaturas - usar comparação segura para prevenir ataques de timing
    let isValid = false;
    try {
      isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (timingError) {
      logger.error('Error comparing signatures', { timingError });
      return false;
    }

    if (!isValid) {
      logger.warn('Invalid XDPAG webhook signature', {
        receivedSignature: signature,
        computedSignature,
        signatureHeaderUsed
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating XDPAG webhook signature', { error });
    return false;
  }
}

// Função para encontrar transação por múltiplos critérios
async function findTransactionByMultipleCriteria({
  externalId,
  internalId,
  endToEndId,
  amount
}: {
  externalId?: string;
  internalId?: string;
  endToEndId?: string;
  amount?: number;
}) {
  const criteria = [];

  if (externalId) {
    criteria.push({ externalId });
  }

  if (internalId) {
    criteria.push({ id: internalId });
  }

  if (endToEndId) {
    criteria.push({
      metadata: {
        path: ['endToEndId'],
        equals: endToEndId
      }
    });
  }

  if (amount) {
    criteria.push({ amount: amount });
  }

  if (criteria.length === 0) {
    return null;
  }

  try {
    // Buscar transações mais recentes primeiro (últimas 24 horas)
    // para evitar encontrar transações antigas com critérios similares
    const transaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: criteria
          },
          {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
            }
          }
        ]
      },
      orderBy: {
        createdAt: 'desc' // Mais recente primeiro
      }
    });

    if (transaction) {
      logger.info('Transaction found by multiple criteria', {
        transactionId: transaction.id,
        externalId: transaction.externalId,
        status: transaction.status,
        amount: transaction.amount,
        createdAt: transaction.createdAt,
        criteriaUsed: {
          hasExternalId: !!externalId,
          hasInternalId: !!internalId,
          hasEndToEndId: !!endToEndId,
          hasAmount: !!amount
        }
      });
    }

    return transaction;
  } catch (error) {
    logger.error('Error finding transaction by multiple criteria', { error, criteria });
    return null;
  }
}

// Mapear status da XDPAG para status interno
function mapXdpagStatusToInternal(xdpagStatus: string): TransactionStatus {
  const statusMap: Record<string, TransactionStatus> = {
    'CREATED': TransactionStatus.PENDING,
    'PROCESSING': TransactionStatus.PROCESSING,
    'FINISHED': TransactionStatus.APPROVED,
    'CANCELLED': TransactionStatus.CANCELED,
    'REVERSED': TransactionStatus.REFUNDED,
    'PARTIALLY_REVERSED': TransactionStatus.REFUNDED, // Map to REFUNDED since PARTIALLY_REFUNDED doesn't exist
    'TIMEOUT': TransactionStatus.REJECTED // Map to REJECTED since EXPIRED doesn't exist
  };

  return statusMap[xdpagStatus] || TransactionStatus.PENDING;
}

// Endpoint POST para receber webhooks da XDPAG
export async function POST(req: NextRequest) {
  try {
    logger.info("XDPAG webhook received");

    // Parse do corpo da requisição
    const body = await req.text();
    let fullPayload;

    try {
      fullPayload = JSON.parse(body);
    } catch (parseError) {
      logger.error("Error parsing XDPAG webhook body", { parseError, body });
      return NextResponse.json(
        { error: "Invalid JSON payload" },
        { status: 400 }
      );
    }

    logger.info("XDPAG webhook FULL PAYLOAD", {
      fullPayload,
      bodyRaw: body
    });

    // Log detalhado dos headers recebidos
    const allHeaders: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      allHeaders[key] = value;
    });

    logger.info("XDPAG webhook HEADERS DEBUG", {
      headers: allHeaders,
      headersCount: Object.keys(allHeaders).length,
      possibleSignatureHeaders: {
        'x-xdpag-signature': req.headers.get('x-xdpag-signature'),
        'x-signature': req.headers.get('x-signature'),
        'xdpag-signature': req.headers.get('xdpag-signature'),
        'signature': req.headers.get('signature'),
        'authorization': req.headers.get('authorization'),
        'x-webhook-signature': req.headers.get('x-webhook-signature'),
        'x-hub-signature': req.headers.get('x-hub-signature')
      }
    });

    // Debug: verificar estrutura do payload
    logger.info("XDPAG webhook payload structure debug", {
      hasType: !!fullPayload.type,
      hasData: !!fullPayload.data,
      typeValue: fullPayload.type,
      dataKeys: fullPayload.data ? Object.keys(fullPayload.data) : null,
      dataId: fullPayload.data?.id,
      dataExternalId: fullPayload.data?.externalId,
      dataStatus: fullPayload.data?.status
    });

    // SEMPRE bypassar validação por enquanto, mas logar headers para análise
    logger.info("XDPAG webhook signature validation BYPASSED - logging headers for analysis", {
      reason: "Temporarily bypassing validation to analyze XDPAG webhook headers",
      bypassValidation: true
    });

    // Extrair dados do webhook baseado na estrutura real do payload
    const { type, data } = fullPayload;

    if (!type || !data) {
      logger.error("Invalid XDPAG webhook payload structure", {
        hasType: !!type,
        hasData: !!data,
        payload: fullPayload
      });
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Mapear os campos corretamente baseado no payload real
    const eventType = type; // 'PAYIN' or 'PAYOUT'
    const transactionId = data?.id; // ID do XDPAG
    const externalId = data?.externalId; // Nosso ID interno (invertido no webhook)
    const status = data?.status;
    const amount = parseFloat(data?.amount || data?.original_amount || '0');
    const endToEndId = data?.endToEndId;
    const fee = parseFloat(data?.fee || '0');

    // CORREÇÃO: O webhook XDPAG está invertendo os campos
    // externalId no webhook = nosso ID interno
    // id no webhook = ID do XDPAG
    // Vamos corrigir isso para manter consistência
    const correctedTransactionId = externalId; // Nosso ID interno
    const correctedExternalId = transactionId; // ID do XDPAG

    logger.info("XDPAG webhook data extracted", {
      eventType,
      originalTransactionId: transactionId,
      originalExternalId: externalId,
      correctedTransactionId,
      correctedExternalId,
      status,
      amount,
      endToEndId,
      fee
    });

    // Buscar a transação no banco de dados usando os campos corrigidos
    logger.info('XDPAG webhook - Buscando transação no banco de dados...');
    logger.info('Critérios de busca (CORRIGIDOS):', {
      transactionId: correctedTransactionId,
      externalId: correctedExternalId,
      endToEndId,
      amount,
      eventType
    });

    // SEMPRE priorizar o ID interno (correctedTransactionId) - é o mais confiável
    let transaction = null;

    if (correctedTransactionId) {
      logger.info('Buscando por ID interno da transação', { correctedTransactionId });
      transaction = await db.transaction.findFirst({
        where: { id: correctedTransactionId }
      });

      if (transaction) {
        logger.info('Transação encontrada por ID interno', {
          transactionId: transaction.id,
          externalId: transaction.externalId,
          status: transaction.status,
          amount: transaction.amount
        });
      } else {
        logger.warn('Nenhuma transação encontrada por ID interno', { correctedTransactionId });
      }
    }

    // Se não encontrou pelo ID interno, tenta pelo externalId (ID do XDPAG)
    if (!transaction && correctedExternalId) {
      logger.info('Buscando por externalId (ID do XDPAG)', { correctedExternalId });
      transaction = await db.transaction.findFirst({
        where: { externalId: correctedExternalId }
      });

      if (transaction) {
        logger.info('Transação encontrada por externalId', {
          transactionId: transaction.id,
          externalId: transaction.externalId,
          status: transaction.status,
          amount: transaction.amount
        });
      } else {
        logger.warn('Nenhuma transação encontrada por externalId', { correctedExternalId });
      }
    }

    // Se ainda não encontrou, usar critérios menos específicos como fallback
    if (!transaction) {
      logger.warn('Usando critérios de fallback para busca de transação');
      transaction = await findTransactionByMultipleCriteria({
        externalId: correctedExternalId,
        internalId: correctedTransactionId,
        endToEndId,
        amount
      });
    }

    if (!transaction) {
      logger.warn("Transaction not found for XDPAG webhook", {
        correctedTransactionId,
        correctedExternalId,
        endToEndId,
        amount,
        eventType
      });
      return NextResponse.json(
        { success: false, message: "Transaction not found" },
        { status: 404 }
      );
    }

    logger.info("Transaction found for XDPAG webhook", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      currentStatus: transaction.status,
      newStatus: status,
      eventType
    });

    // Mapear status da XDPAG para status interno
    const internalStatus = mapXdpagStatusToInternal(status);

    // Atualizar status da transação usando updateTransactionStatus (como outros providers)
    logger.info("Updating transaction status using updateTransactionStatus", {
      transactionId: transaction.id,
      currentStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    const updatedTransaction = await updateTransactionStatus(
      transaction.id,
      internalStatus,
      internalStatus === TransactionStatus.APPROVED ? new Date() : undefined
    );

    // Atualizar metadata específica da XDPAG
    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        metadata: {
          ...(transaction.metadata as Record<string, any>),
          lastWebhookEvent: eventType,
          lastWebhookReceived: new Date().toISOString(),
          endToEndId: endToEndId,
          fee: fee,
          xdpag: {
            originalStatus: status,
            eventType: eventType,
            transactionId: transactionId,
            endToEndId: endToEndId
          }
        }
      }
    });

    logger.info("Transaction updated successfully", {
      transactionId: transaction.id,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    // A função updateTransactionStatus já cuida da atualização de saldo automaticamente
    // Não precisamos fazer isso manualmente aqui

    return NextResponse.json({
      success: true,
      message: "XDPAG webhook processed successfully",
      data: {
        transactionId: transaction.id,
        externalId: externalId,
        status: internalStatus,
        eventType: eventType
      }
    });

  } catch (error) {
    logger.error("Error processing XDPAG webhook", { error });
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
