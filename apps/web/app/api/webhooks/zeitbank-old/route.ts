import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { updateTransactionStatus } from "@repo/payments";
import zeitbankProvider from "@repo/payments/provider/zeitbank";
import crypto from "crypto";
import { TransactionStatus } from "@prisma/client";

// Endpoint GET para teste do webhook
export async function GET(_req: NextRequest) {
  logger.info("ZeitBank webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "ZeitBank webhook endpoint is working" });
}

// Validar assinatura do webhook da ZeitBank
async function validateZeitbankWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    const possibleSignatureHeaders = [
      "x-zeitbank-signature",
      "x-signature",
      "zeitbank-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Log all available headers for debugging
    const allHeaders: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      allHeaders[key] = value;
    });

    logger.info('Checking for signature headers. All available headers:', { allHeaders });

    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      logger.info(`Checking header "${headerName}":`, { value: headerValue });
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        logger.info(`Found signature in header "${headerName}"`);
        break;
      }
    }

    if (!signature) {
      logger.warn('Missing ZeitBank webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders,
        availableHeaders: Object.keys(allHeaders)
      });
      
      // If no signature is present and no webhook secret is configured, allow the webhook
      const webhookSecret = process.env.ZEITBANK_WEBHOOK_SECRET;
      if (!webhookSecret) {
        logger.info('No signature found and no webhook secret configured - allowing webhook');
        return true;
      }
      
      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    const webhookSecret = process.env.ZEITBANK_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('ZEITBANK_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // ZeitBank sends the secret key directly in the header
    const receivedSignature = signature.replace('sha256=', '');
    
    let isValid = false;
    try {
      // ZeitBank sends the webhook secret directly, not an HMAC signature
      isValid = receivedSignature === webhookSecret;
      
      if (isValid) {
        logger.info('ZeitBank webhook signature validated successfully (direct secret match)');
      } else {
        // Try HMAC validation as fallback
        const computedSignature = crypto
          .createHmac('sha256', webhookSecret)
          .update(body)
          .digest('hex');
          
        isValid = crypto.timingSafeEqual(
          Buffer.from(computedSignature, 'hex'),
          Buffer.from(receivedSignature, 'hex')
        );
        
        if (isValid) {
          logger.info('ZeitBank webhook signature validated successfully (HMAC)');
        }
      }
    } catch (error) {
      logger.warn('Error in signature comparison', { error });
      isValid = false;
    }

    if (!isValid) {
      logger.warn('Invalid ZeitBank webhook signature', {
        receivedSignature: receivedSignature,
        expectedSecret: webhookSecret,
        signatureHeaderUsed,
        originalSignature: signature
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating ZeitBank webhook signature', { error });
    return false;
  }
}

// Webhook handler is now delegated to the provider

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    
    if (!body) {
      logger.warn("Empty webhook body received");
      return NextResponse.json({ success: true }, { status: 200 });
    }

    let webhookData;
    try {
      webhookData = JSON.parse(body);
    } catch (parseError) {
      logger.error("Failed to parse webhook JSON", { error: parseError });
      return NextResponse.json({ success: true }, { status: 200 });
    }

    const headers = Object.fromEntries(request.headers.entries());
    
    const result = await zeitbankProvider.webhookHandler({
      body: webhookData,
      headers
    });
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    logger.error("Error in webhook endpoint", { error });
    return NextResponse.json({ success: true }, { status: 200 });
  }
}
