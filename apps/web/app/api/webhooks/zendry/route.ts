import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import * as crypto from "crypto";
import { TransactionStatus } from "@prisma/client";

// Endpoint GET para teste do webhook
export async function GET(_req: NextRequest) {
  logger.info("Zendry webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Zendry webhook endpoint is working" });
}

// Validar assinatura do webhook da Zendry
async function validateZendryWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Verificar assinatura em possíveis headers
    const possibleSignatureHeaders = [
      "x-zendry-signature",
      "x-signature",
      "zendry-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Tentar encontrar assinatura em qualquer um dos headers possíveis
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        break;
      }
    }

    // Verificar no header forwarded para parâmetro sig
    if (!signature) {
      const forwarded = req.headers.get("forwarded");
      if (forwarded) {
        const sigMatch = forwarded.match(/sig=([^;,]+)/);
        if (sigMatch) {
          signature = sigMatch[1];
          signatureHeaderUsed = "forwarded.sig";
          // Tentar decodificar se parecer base64
          try {
            signature = Buffer.from(signature, "base64").toString();
          } catch {
            // Manter original se não for base64
          }
        }
      }
    }

    // Se não encontrou assinatura em nenhum header
    if (!signature) {
      logger.warn('Missing Zendry webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders
      });
      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    // Obter o segredo do webhook das variáveis de ambiente
    const webhookSecret = process.env.ZENDRY_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('ZENDRY_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Computar a assinatura esperada usando MD5 (conforme documentação da Zendry)
    const computedSignature = crypto.createHash('md5').update(body + webhookSecret).digest('hex');

    // Comparar assinaturas - usar comparação segura para prevenir ataques de timing
    let isValid = false;
    try {
      isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (error) {
      // Se os buffers têm tamanhos diferentes, timingSafeEqual lança erro
      // Neste caso, as assinaturas definitivamente não coincidem
      logger.warn('Error in signature comparison, likely different lengths', { error });
      isValid = false;
    }

    if (!isValid) {
      logger.warn('Invalid Zendry webhook signature', {
        receivedSignature: signature,
        computedSignature,
        signatureHeaderUsed
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating Zendry webhook signature', { error });
    return false;
  }
}

// Função para mapear status da Zendry para status interno
function mapZendryStatusToInternal(zendryStatus: string): TransactionStatus {
  switch (zendryStatus?.toLowerCase()) {
    case "paid":
    case "completed":
    case "confirmed":
      return "APPROVED";
    case "pending":
    case "waiting_payment":
      return "PENDING";
    case "cancelled":
    case "canceled":
      return "CANCELED";
    case "failed":
    case "error":
      return "REJECTED";
    case "refunded":
      return "REFUNDED";
    default:
      logger.warn("Unknown Zendry status", { zendryStatus });
      return "PENDING";
  }
}

// Função para buscar transação usando múltiplos critérios
async function findTransactionByMultipleCriteria({
  externalId,
  internalId,
  customerEmail,
  amount
}: {
  externalId?: string;
  internalId?: string;
  customerEmail?: string;
  amount?: number;
}) {
  // 1. Primeiro tentar encontrar pelo ID interno do metadata
  if (internalId) {
    const transaction = await db.transaction.findUnique({
      where: { id: internalId }
    });

    if (transaction) {
      logger.info("Transaction found by internal ID", { internalId });
      return { ...transaction, searchMethod: "internal_id" };
    }
  }

  // 2. Se não encontrou, tentar encontrar pelo ID externo da Zendry
  if (externalId) {
    const transaction = await db.transaction.findFirst({
      where: { externalId }
    });

    if (transaction) {
      logger.info("Transaction found by external ID", { externalId });
      return { ...transaction, searchMethod: "external_id" };
    }
  }

  // 3. Se ainda não encontrou, tentar encontrar por dados do cliente para transações recentes
  if (customerEmail && amount && amount > 0) {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const transaction = await db.transaction.findFirst({
      where: {
        customerEmail,
        amount,
        status: "PENDING",
        type: "CHARGE",
        createdAt: {
          gte: fiveMinutesAgo
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    if (transaction) {
      logger.info("Transaction found by customer data", { customerEmail, amount });
      return { ...transaction, searchMethod: "customer_data" };
    }
  }

  return null;
}

export async function POST(req: NextRequest) {
  try {
    // Obter o corpo da requisição para validação de assinatura
    const rawBody = await req.text();

    // Log headers para debug
    const headersObj = Object.fromEntries(req.headers.entries());

    // Log informações do ambiente para ajudar com debug
    const isVercel = !!process.env.VERCEL;
    const vercelEnv = process.env.VERCEL_ENV || 'unknown';

    logger.info("Received Zendry webhook", {
      headers: headersObj,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        isVercel,
        vercelEnv
      }
    });

    // Verificar se o corpo está vazio
    if (!rawBody || rawBody.trim() === '') {
      logger.error('Empty webhook payload received');
      return NextResponse.json({ error: 'Empty payload' }, { status: 400 });
    }

    // Validar assinatura do webhook apenas em produção e se não estiver configurado para bypass
    if (process.env.NODE_ENV === 'production') {
      if (process.env.WEBHOOKS_BYPASS_SIGNATURE_VALIDATION !== "true" &&
          process.env.ZENDRY_BYPASS_SIGNATURE_VALIDATION !== "true") {
        try {
          const isValid = await validateZendryWebhook(req, rawBody);
          if (!isValid) {
            logger.error('Invalid webhook signature');
            return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
          }
        } catch (signatureError) {
          logger.error('Error during signature validation', { error: signatureError });
          return NextResponse.json({ error: 'Signature validation error' }, { status: 401 });
        }
      }
    }

    // Parse do payload do webhook
    let payload;
    try {
      payload = JSON.parse(rawBody);

      // Verificar se payload é null ou não é um objeto
      if (payload === null || typeof payload !== 'object') {
        logger.error('Webhook payload is null or not an object', { rawBody });
        return NextResponse.json({ success: true, message: 'Ignoring invalid payload format' }, { status: 200 });
      }
    } catch (error) {
      logger.error('Error parsing webhook payload', { error, rawBody });
      return NextResponse.json({ success: true, message: 'Ignoring invalid JSON payload' }, { status: 200 });
    }

    // Log do payload do webhook para debug
    const payloadString = JSON.stringify(payload);
    const truncatedPayload = payloadString.substring(0, 1000) + (payloadString.length > 1000 ? '...' : '');

    // Estrutura real da Zendry: { notification_type, message: { ... } }
    const notificationType = payload.notification_type;
    const transactionData = payload.message || {};
    const status = transactionData.status;
    const referenceCode = transactionData.reference_code;
    const externalReference = transactionData.external_reference;

    logger.info("Processing Zendry webhook payload", {
      payloadSize: payloadString.length,
      payload: truncatedPayload,
      notificationType,
      referenceCode,
      externalReference,
      status
    });

    // Deduplicação aprimorada usando reference_code e status
    const webhookId = referenceCode; // Usar reference_code como webhookId
    const objectId = referenceCode; // Usar reference_code como objectId

    // Criar uma chave de deduplicação que inclui o status
    const deduplicationKey = `zendry-${webhookId || 'unknown'}-${objectId || 'unknown'}-${status}`;

    logger.info("Processing ZENDRY webhook with deduplication", {
      webhookId,
      objectId,
      status,
      deduplicationKey
    });

    // Verificar se processamos este webhook exato recentemente (últimos 5 minutos)
    const recentTransaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              {
                metadata: {
                  path: ['lastWebhook', 'webhookId'],
                  equals: webhookId
                }
              },
              {
                metadata: {
                  path: ['deduplicationKey'],
                  equals: deduplicationKey
                }
              }
            ]
          },
          {
            updatedAt: {
              gte: new Date(Date.now() - 5 * 60 * 1000) // Últimos 5 minutos
            }
          }
        ]
      }
    });

    if (recentTransaction) {
      logger.info("ZENDRY webhook already processed recently, skipping", {
        webhookId,
        deduplicationKey,
        transactionId: recentTransaction.id,
        lastProcessed: recentTransaction.updatedAt,
        timeSinceLastProcessed: Date.now() - recentTransaction.updatedAt.getTime()
      });
      return NextResponse.json({ success: true, message: "Already processed" });
    }

    // Verificar se é um webhook de teste
    if (payload.test === true) {
      logger.info("Received test webhook from Zendry");
      return NextResponse.json({ success: true, message: "Test webhook received" });
    }

    // Extrair o ID da nossa transação interna do external_reference
    let transactionId: string | undefined = externalReference;

    logger.info("Extracted data from Zendry webhook", {
      webhookId,
      objectId,
      status,
      transactionId,
      referenceCode,
      externalReference
    });

    if (!referenceCode) {
      logger.error("Missing reference_code in webhook", { payload });
      return NextResponse.json({ error: "Missing reference_code" }, { status: 400 });
    }

    // Para webhooks de criação de QR code, processar para atualizar dados da transação
    if (status === "waiting_payment" || status === "pending") {
      logger.info("Processando webhook de criação/atualização de QR code", { webhookId, objectId, status });

      // BUSCA APRIMORADA DE TRANSAÇÃO
      let existingTransaction = await findTransactionByMultipleCriteria({
        externalId: referenceCode,
        internalId: externalReference, // external_reference pode ser nosso ID interno
        customerEmail: transactionData.payer_email || transactionData.customer?.email,
        amount: transactionData.value_cents ? transactionData.value_cents / 100 : undefined
      });

      if (existingTransaction) {
        logger.info("Transação encontrada, atualizando metadata", {
          webhookId,
          objectId,
          existingTransactionId: existingTransaction.id,
          searchMethod: existingTransaction.searchMethod
        });

        // Se encontrou pelo ID interno mas não tem externalId, atualizar
        if (!existingTransaction.externalId && objectId) {
          await db.transaction.update({
            where: { id: existingTransaction.id },
            data: { externalId: objectId }
          });
          logger.info("Updated transaction with external ID", {
            transactionId: existingTransaction.id,
            externalId: objectId
          });
        }

        // Extrair dados do PIX se disponíveis
        const pixCode = transactionData.content;
        const pixQrCode = transactionData.qr_code_image || transactionData.image_base64;
        const pixExpiresAt = transactionData.expiration_date || transactionData.expires_at;

        // Atualizar a transação com os dados do webhook
        const updatedTransaction = await db.transaction.update({
          where: { id: existingTransaction.id },
          data: {
            metadata: {
              ...(typeof existingTransaction.metadata === 'object' ? existingTransaction.metadata : {}),
              lastWebhook: {
                receivedAt: new Date().toISOString(),
                status,
                webhookId
              },
              deduplicationKey: deduplicationKey,
              ...(pixCode && { pixCode }),
              ...(pixQrCode && { pixQrCode }),
              ...(pixExpiresAt && { pixExpiresAt })
            }
          }
        });

        // Enviar evento SVIX para criação/atualização de transação usando serviço padrão
        try {
          logger.info("Sending SVIX webhook for ZENDRY transaction creation", {
            transactionId: existingTransaction.id,
            status,
            provider: "ZENDRY"
          });

          const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
          await triggerTransactionEvents(updatedTransaction, existingTransaction.status);
        } catch (webhookError) {
          logger.error("Error sending SVIX webhook for transaction creation", {
            error: webhookError,
            transactionId: existingTransaction.id
          });
        }
      } else {
        logger.info("Transação não encontrada para webhook de criação", {
          webhookId,
          objectId,
          transactionId: externalReference,
          customerEmail: transactionData.payer_email || transactionData.customer?.email,
          amount: transactionData.value_cents ? transactionData.value_cents / 100 : null
        });

        // Não criamos uma nova transação aqui, pois isso deve ser feito pelo fluxo normal
        // de criação de transação, não pelo webhook
      }

      return NextResponse.json({ success: true, message: "QR code webhook processed" });
    }

    // Apenas processar webhooks para transações pagas ou reembolsadas
    const relevantStatuses = ["paid", "completed", "confirmed", "refunded", "cancelled", "failed"];
    const isRelevantStatus = status && relevantStatuses.includes(status.toLowerCase());

    if (!isRelevantStatus) {
      logger.info("Ignoring webhook with non-relevant status", { webhookId, objectId, status });
      return NextResponse.json({ success: true, message: "Non-relevant status webhook received" });
    }

    // Mapear status da Zendry para nosso status interno
    let internalStatus = mapZendryStatusToInternal(status);

    // Forçar status APPROVED para paid
    if (status === "paid") {
      internalStatus = "APPROVED";
    }

    logger.info("Mapped status", { zendryStatus: status, internalStatus });

    // BUSCA APRIMORADA DE TRANSAÇÃO PARA CONFIRMAÇÃO DE PAGAMENTO
    let transaction = await findTransactionByMultipleCriteria({
      externalId: referenceCode,
      internalId: externalReference, // external_reference pode ser nosso ID interno
      customerEmail: transactionData.payer_email || transactionData.customer?.email,
      amount: transactionData.value_cents ? transactionData.value_cents / 100 : undefined
    });

    if (!transaction) {
      // Log informações detalhadas para debug
      logger.warn("Transaction not found for Zendry webhook", {
        webhookId,
        objectId,
        transactionId,
        customerEmail: transactionData.customer?.email,
        amount: transactionData.amount ? transactionData.amount / 100 : null,
        payload: JSON.stringify(payload)
      });

      // Retornar 200 em vez de 404 para evitar que a Zendry reenvie
      // Esta é uma prática comum para webhooks para evitar reenvios desnecessários
      return NextResponse.json({
        success: false,
        message: "Transaction not found, but webhook received"
      }, { status: 200 });
    }

    logger.info("Transaction found for webhook processing", {
      transactionId: transaction.id,
      externalId: transaction.externalId,
      currentStatus: transaction.status,
      newStatus: internalStatus,
      searchMethod: transaction.searchMethod
    });

    // Atualizar metadata com informações adicionais do webhook
    const updatedMetadata: Record<string, any> = {
      ...(typeof transaction.metadata === 'object' ? transaction.metadata : {})
    };

    // Adicionar dados do webhook ao metadata com identificação adequada do provedor
    updatedMetadata.lastWebhook = {
      receivedAt: new Date().toISOString(),
      webhookId,
      status,
      internalStatus
    };

    // Adicionar informações de atualização do webhook para identificar como webhook do provedor
    updatedMetadata.webhookUpdate = {
      provider: "ZENDRY",
      webhookSource: "zendry",
      updatedAt: new Date().toISOString(),
      webhookId,
      originalStatus: status
    };

    // Marcar como atualizado por webhook para lógica de filtragem
    updatedMetadata.updatedByWebhook = true;

    // Adicionar chave de deduplicação ao metadata
    updatedMetadata.deduplicationKey = deduplicationKey;

    // Se há informações de pagamento, adicionar ao metadata
    if (transactionData.payment_date) {
      updatedMetadata.paidAt = transactionData.payment_date;
    }

    // Se há informações de PIX, adicionar ao metadata
    if (transactionData.content) {
      updatedMetadata.pixInfo = {
        content: transactionData.content,
        end_to_end: transactionData.end_to_end,
        payer_name: transactionData.payer_name,
        payer_document: transactionData.payer_document
      };
    }

    // Se temos o reference_code da Zendry e ainda não está definido, atualizar
    if (referenceCode && transaction.externalId !== referenceCode) {
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          externalId: referenceCode,
          metadata: updatedMetadata
        },
      });
    } else {
      // Apenas atualizar o metadata
      await db.transaction.update({
        where: { id: transaction.id },
        data: { metadata: updatedMetadata },
      });
    }

    try {
      const previousStatus = transaction.status;

      // Atualizar o status da transação
      const result = await updateTransactionStatus(
        transaction.id,
        internalStatus as TransactionStatus,
        internalStatus === "APPROVED" ? new Date() : undefined
      );

      logger.info("Updated transaction status from Zendry webhook", {
        transactionId: transaction.id,
        oldStatus: transaction.status,
        newStatus: internalStatus,
        isPaid: status === "paid",
        forceApproved: status === "paid" && internalStatus === "APPROVED",
        result
      });

      // Disparar eventos de webhook manualmente para garantir que eventos SVIX sejam enviados
      // Isso é necessário porque updateTransactionStatus pode não disparar para transações atualizadas por webhook
      try {
        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        const updatedTransaction = await db.transaction.findUnique({
          where: { id: transaction.id }
        });

        if (updatedTransaction) {
          // Forçar evento de confirmação para status paid
          const forceConfirmation = status === "paid" || internalStatus === "APPROVED";
          await triggerTransactionEvents(updatedTransaction, previousStatus, forceConfirmation);

          logger.info("Manually triggered webhook events for Zendry confirmation", {
            transactionId: transaction.id,
            previousStatus,
            newStatus: internalStatus,
            provider: "ZENDRY"
          });
        }
      } catch (webhookError) {
        logger.error("Error triggering webhook events for Zendry", {
          error: webhookError,
          transactionId: transaction.id
        });
      }
    } catch (error) {
      logger.error("Error updating transaction status", {
        error,
        transactionId: transaction.id,
        webhookId,
        status
      });

      // Ainda retornar sucesso para evitar reenvios
      return NextResponse.json({
        success: true,
        message: "Webhook received but error updating transaction"
      }, { status: 200 });
    }

    return NextResponse.json({ success: true, message: "Webhook processed successfully" });
  } catch (error) {
    logger.error("Error in Zendry webhook endpoint:", { error });

    // Sempre retornar 200 OK para evitar reenvios desnecessários
    return NextResponse.json(
      {
        success: true,
        message: "Webhook processed with errors"
      },
      { status: 200 }
    );
  }
}
