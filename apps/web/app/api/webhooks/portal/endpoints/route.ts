import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { getOrganizationEndpoints } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const organizationSlug = searchParams.get('organizationId'); // Keep param name for compatibility

    if (!organizationSlug) {
      return NextResponse.json({ error: "Organization ID required" }, { status: 400 });
    }

    logger.info("Fetching organization endpoints", {
      userId: session.user.id,
      organizationSlug
    });

    const endpoints = await getOrganizationEndpoints(organizationSlug);

    return NextResponse.json({
      success: true,
      data: endpoints
    });
  } catch (error) {
    logger.error("Failed to fetch organization endpoints", {
      error: error instanceof Error ? error.message : String(error)
    });

    return NextResponse.json(
      { error: "Failed to fetch organization endpoints" },
      { status: 500 }
    );
  }
}
