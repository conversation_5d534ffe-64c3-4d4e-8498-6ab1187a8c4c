import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth/auth";
import { getWebhookEvents } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(
  request: NextRequest,
  { params }: { params: { transactionId: string } }
) {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({ headers: request.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const transactionId = params.transactionId;
    if (!transactionId) {
      return NextResponse.json({ error: "Transaction ID required" }, { status: 400 });
    }

    // Obter o organizationId da URL
    const { searchParams } = new URL(request.url);
    const organizationSlug = searchParams.get('organizationId'); // Manter nome do parâmetro para compatibilidade

    if (!organizationSlug) {
      logger.warn("Missing organization slug in request");
      return NextResponse.json({ error: "Organization ID required" }, { status: 400 });
    }

    logger.info("Fetching webhook events for transaction", {
      userId: session.user.id,
      organizationSlug,
      transactionId
    });

    // Usar o serviço existente para buscar eventos relacionados à transação
    const events = await getWebhookEvents(organizationSlug, {
      transactionId: transactionId,
      limit: 100
    });

    return NextResponse.json({
      data: events.messages
    });
  } catch (error) {
    logger.error("Failed to fetch webhook events for transaction", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId
    });

    return NextResponse.json(
      { error: "Failed to fetch webhook events for transaction" },
      { status: 500 }
    );
  }
}
