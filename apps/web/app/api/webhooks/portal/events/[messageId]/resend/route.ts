import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth/auth";
import { resendWebhook } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function POST(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    // Verificar autenticação
    const session = await auth.api.getSession({ headers: request.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const messageId = params.messageId;
    if (!messageId) {
      return NextResponse.json({ error: "ID da mensagem não fornecido" }, { status: 400 });
    }

    // Obter os dados do corpo da requisição
    const body = await request.json();
    const { endpointId } = body;

    if (!endpointId) {
      return NextResponse.json({ error: "ID do endpoint não fornecido" }, { status: 400 });
    }

    logger.info("Resending webhook", {
      userId: session.user.id,
      messageId,
      endpointId
    });

    await resendWebhook(messageId, endpointId);

    return NextResponse.json({
      success: true,
      message: "Webhook resend scheduled"
    });
  } catch (error) {
    // Usar a variável messageId já extraída anteriormente em vez de params.messageId
    const msgId = params.messageId || "unknown";

    logger.error("Failed to resend webhook", {
      error: error instanceof Error ? error.message : String(error),
      messageId: msgId
    });

    return NextResponse.json(
      { error: "Failed to resend webhook" },
      { status: 500 }
    );
  }
}
