import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { getWebhookEvents } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const organizationSlug = searchParams.get('organizationId'); // Keep param name for compatibility
    const limit = parseInt(searchParams.get('limit') || '50');
    const iterator = searchParams.get('iterator');
    const eventType = searchParams.get('eventType');
    const transactionId = searchParams.get('transactionId');
    const before = searchParams.get('before');
    const after = searchParams.get('after');

    if (!organizationSlug) {
      return NextResponse.json({ error: "Organization ID required" }, { status: 400 });
    }

    logger.info("Fetching webhook events", {
      userId: session.user.id,
      organizationSlug,
      limit,
      eventType,
      transactionId
    });

    const events = await getWebhookEvents(organizationSlug, {
      limit,
      iterator: iterator || undefined,
      eventType: eventType || undefined,
      transactionId: transactionId || undefined,
      before: before || undefined,
      after: after || undefined
    });

    return NextResponse.json({
      success: true,
      data: events
    });
  } catch (error) {
    logger.error("Failed to fetch webhook events", {
      error: error instanceof Error ? error.message : String(error)
    });

    return NextResponse.json(
      { error: "Failed to fetch webhook events" },
      { status: 500 }
    );
  }
}
