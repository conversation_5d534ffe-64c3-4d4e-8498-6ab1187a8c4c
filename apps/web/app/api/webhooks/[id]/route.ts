import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { headers } from "next/headers";
import * as webhooksApi from "@repo/api/src/webhooks";
import { db } from "@repo/database";
import { createHash } from "crypto";

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    // Verificar autenticação (session ou API key)
    const headersList = await headers();
    const apiKey = req.headers.get("X-API-Key");

    let userId: string | null = null;
    let authenticatedOrgId: string | null = null;

    if (apiKey) {
      // API key authentication
      const hash = createHash("sha256").update(apiKey).digest("hex");

      const key = await db.api_key.findFirst({
        where: { hash },
        include: {
          organization: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!key) {
        return NextResponse.json(
          { error: "Invalid API key" },
          { status: 401 }
        );
      }

      // Check if API key has expired
      const expiresAt = (key as any).expiresAt as Date | null | undefined;
      if (expiresAt && expiresAt < new Date()) {
        return NextResponse.json(
          { error: "API key has expired" },
          { status: 401 }
        );
      }

      // Check if user exists
      if (!key.user) {
        return NextResponse.json(
          { error: "Invalid API key configuration" },
          { status: 401 }
        );
      }

      userId = key.createdById;
      authenticatedOrgId = key.organizationId;

      // Update lastUsedAt
      try {
        await db.api_key.update({
          where: { id: key.id },
          data: { lastUsedAt: new Date() }
        });
      } catch (updateError) {
        console.warn("Failed to update lastUsedAt for API key:", updateError);
      }
    } else {
      // Session authentication
      const session = await auth.api.getSession({
        headers: headersList,
      });
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      userId = session.user.id;
    }

    // Garantir que params seja resolvido se for uma Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    if (!id) {
      return NextResponse.json(
        { error: "Missing id parameter" },
        { status: 400 }
      );
    }

    // Obter os dados do corpo da requisição
    const body = await req.json();
    const { url, events, isActive, regenerateSecret } = body;
    // Always use SVIX
    const useSvix = true;

    console.log(`Updating webhook ${id}:`, { url, events, isActive, regenerateSecret, useSvix: true });

    // Chamar a API diretamente
    try {
      const webhook = await webhooksApi.updateWebhook(id, {
        url,
        events,
        isActive,
        regenerateSecret,
        useSvix,
      });

      return NextResponse.json(webhook);
    } catch (error: any) {
      if (error.message === "Webhook not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message === "A webhook with this URL already exists for this organization") {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Error updating webhook", error);
    return NextResponse.json(
      { error: "Failed to update webhook" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> | { id: string } }
) {
  try {
    // Verificar autenticação (session ou API key)
    const headersList = await headers();
    const apiKey = req.headers.get("X-API-Key");

    let userId: string | null = null;
    let authenticatedOrgId: string | null = null;

    if (apiKey) {
      // API key authentication
      const hash = createHash("sha256").update(apiKey).digest("hex");

      const key = await db.api_key.findFirst({
        where: { hash },
        include: {
          organization: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!key) {
        return NextResponse.json(
          { error: "Invalid API key" },
          { status: 401 }
        );
      }

      // Check if API key has expired
      const expiresAt = (key as any).expiresAt as Date | null | undefined;
      if (expiresAt && expiresAt < new Date()) {
        return NextResponse.json(
          { error: "API key has expired" },
          { status: 401 }
        );
      }

      // Check if user exists
      if (!key.user) {
        return NextResponse.json(
          { error: "Invalid API key configuration" },
          { status: 401 }
        );
      }

      userId = key.createdById;
      authenticatedOrgId = key.organizationId;

      // Update lastUsedAt
      try {
        await db.api_key.update({
          where: { id: key.id },
          data: { lastUsedAt: new Date() }
        });
      } catch (updateError) {
        console.warn("Failed to update lastUsedAt for API key:", updateError);
      }
    } else {
      // Session authentication
      const session = await auth.api.getSession({
        headers: headersList,
      });
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      userId = session.user.id;
    }

    // Garantir que params seja resolvido se for uma Promise
    const resolvedParams = params instanceof Promise ? await params : params;
    const { id } = resolvedParams;

    if (!id) {
      return NextResponse.json(
        { error: "Missing id parameter" },
        { status: 400 }
      );
    }

    console.log(`Deleting webhook ${id}`);

    // Chamar a API diretamente
    try {
      const result = await webhooksApi.deleteWebhook(id);
      return NextResponse.json(result);
    } catch (error: any) {
      if (error.message === "Webhook not found") {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Error deleting webhook", error);
    return NextResponse.json(
      { error: "Failed to delete webhook" },
      { status: 500 }
    );
  }
}
