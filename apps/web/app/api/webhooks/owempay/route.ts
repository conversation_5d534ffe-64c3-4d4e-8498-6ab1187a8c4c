import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import * as crypto from "crypto";
import { TransactionStatus } from "@prisma/client";
import { processMedInfraction } from "@repo/payments/src/med/med-service";

// Endpoint GET para teste do webhook
export async function GET(_req: NextRequest) {
  logger.info("Owempay webhook test endpoint accessed");
  return NextResponse.json({ success: true, message: "Owempay webhook endpoint is working" });
}

// Validar assinatura do webhook da Owempay
async function validateOwempayWebhook(req: NextRequest, body: string): Promise<boolean> {
  try {
    // Verificar assinatura em possíveis headers
    const possibleSignatureHeaders = [
      "x-owempay-signature",
      "x-signature",
      "owempay-signature",
      "signature"
    ];

    let signature = "";
    let signatureHeaderUsed = "";

    // Tentar encontrar assinatura em qualquer um dos headers possíveis
    for (const headerName of possibleSignatureHeaders) {
      const headerValue = req.headers.get(headerName);
      if (headerValue) {
        signature = headerValue;
        signatureHeaderUsed = headerName;
        break;
      }
    }

    // Se não encontrou assinatura em nenhum header
    if (!signature) {
      logger.warn('Missing Owempay webhook signature in all possible headers', {
        checkedHeaders: possibleSignatureHeaders
      });
      return false;
    }

    logger.info('Found signature in header', { headerName: signatureHeaderUsed });

    // Obter o segredo do webhook das variáveis de ambiente
    const webhookSecret = process.env.OWEMPAY_WEBHOOK_SECRET;

    if (!webhookSecret) {
      logger.warn('OWEMPAY_WEBHOOK_SECRET not configured, skipping signature validation');
      return true;
    }

    // Computar a assinatura esperada usando HMAC-SHA256 (padrão mais comum)
    const computedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');

    // Comparar assinaturas - usar comparação segura para prevenir ataques de timing
    let isValid = false;
    try {
      isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (error) {
      // Se os buffers têm tamanhos diferentes, timingSafeEqual lança erro
      // Neste caso, as assinaturas definitivamente não coincidem
      logger.warn('Error in signature comparison, likely different lengths', { error });
      isValid = false;
    }

    if (!isValid) {
      logger.warn('Invalid Owempay webhook signature', {
        receivedSignature: signature,
        computedSignature,
        signatureHeaderUsed
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error validating Owempay webhook signature', { error });
    return false;
  }
}

// Mapear status da Owempay para status interno
function mapOwempayStatusToInternal(owempayStatus: string): TransactionStatus {
  const statusMap: Record<string, TransactionStatus> = {
    "pending": TransactionStatus.PENDING,
    "paid": TransactionStatus.APPROVED,
    "approved": TransactionStatus.APPROVED,
    "refused": TransactionStatus.REJECTED,
    "rejected": TransactionStatus.REJECTED,
    "canceled": TransactionStatus.CANCELED,
    "cancelled": TransactionStatus.CANCELED,
    "refunded": TransactionStatus.REFUNDED,
    "refunded_med": TransactionStatus.REFUNDED,
    "processing": TransactionStatus.PROCESSING,
    // Eventos de cashout (transferência)
    "cashout_completed": TransactionStatus.APPROVED,
    "cashout_failed": TransactionStatus.REJECTED,
    "transfer_completed": TransactionStatus.APPROVED,
    "transfer_failed": TransactionStatus.REJECTED,
  };

  return statusMap[owempayStatus] || TransactionStatus.PENDING;
}

// Função para encontrar transação por múltiplos critérios
async function findTransactionByMultipleCriteria({
  externalId,
  internalId,
  customerEmail,
  amount
}: {
  externalId?: string;
  internalId?: string;
  customerEmail?: string;
  amount?: number;
}) {
  try {
    // Primeiro, tentar encontrar por externalId (usado para PIX IN)
    if (externalId) {
      const transaction = await db.transaction.findFirst({
        where: { externalId }
      });
      if (transaction) {
        logger.info('Transaction found by externalId', { externalId, transactionId: transaction.id });
        return transaction;
      }
    }

    // Segundo, tentar encontrar por referenceCode (usado para PIX OUT/cashout)
    if (externalId) {
      const transaction = await db.transaction.findFirst({
        where: { referenceCode: externalId }
      });
      if (transaction) {
        logger.info('Transaction found by referenceCode', { referenceCode: externalId, transactionId: transaction.id });
        return transaction;
      }
    }

    // Terceiro, tentar encontrar por internalId
    if (internalId) {
      const transaction = await db.transaction.findFirst({
        where: { id: internalId }
      });
      if (transaction) {
        logger.info('Transaction found by internalId', { internalId, transactionId: transaction.id });
        return transaction;
      }
    }

    // Quarto, tentar encontrar por email do cliente e valor (menos preciso)
    if (customerEmail && amount) {
      const transaction = await db.transaction.findFirst({
        where: {
          customerEmail,
          amount,
          status: {
            in: [TransactionStatus.PENDING, TransactionStatus.APPROVED]
          }
        },
        orderBy: { createdAt: 'desc' }
      });
      if (transaction) {
        logger.info('Transaction found by customerEmail and amount', {
          customerEmail,
          amount,
          transactionId: transaction.id
        });
        return transaction;
      }
    }

    logger.warn('Transaction not found by any criteria', {
      externalId,
      internalId,
      customerEmail,
      amount
    });
    return null;
  } catch (error) {
    logger.error('Error finding transaction by multiple criteria', { error });
    return null;
  }
}

// Endpoint POST para receber webhooks da Owempay
export async function POST(req: NextRequest) {
  try {
    logger.info("Owempay webhook received");

    // Parse do corpo da requisição
    const body = await req.text();
    let fullPayload;

    try {
      fullPayload = JSON.parse(body);
    } catch (parseError) {
      logger.error("Error parsing Owempay webhook body", { parseError, body });
      return NextResponse.json(
        { error: "Invalid JSON payload" },
        { status: 400 }
      );
    }

    logger.info("Owempay webhook FULL PAYLOAD", {
      fullPayload,
      bodyRaw: body
    });

    // Debug: verificar estrutura do payload
    logger.info("Owempay webhook payload structure debug", {
      hasEvent: !!fullPayload.event,
      hasObject: !!fullPayload.object,
      eventValue: fullPayload.event,
      objectKeys: fullPayload.object ? Object.keys(fullPayload.object) : null,
      objectId: fullPayload.object?.id,
      objectExternalReference: fullPayload.object?.externalReference,
      objectStatus: fullPayload.object?.status
    });

    // Temporariamente ignorando validação de assinatura para debug
    console.log('🔍 OWEMPAY WEBHOOK - Ignorando validação de assinatura temporariamente');
    console.log('📋 OWEMPAY WEBHOOK - Headers completos:', JSON.stringify(Object.fromEntries(req.headers.entries()), null, 2));
    console.log('📦 OWEMPAY WEBHOOK - Body completo:', JSON.stringify(fullPayload, null, 2));
    console.log('🔑 OWEMPAY WEBHOOK - Signature recebida:', req.headers.get("x-owempay-signature") || req.headers.get("owempay-signature") || req.headers.get("signature"));

    // const isValidSignature = await validateOwempayWebhook(req, body);
    // if (!isValidSignature) {
    //   logger.warn("Invalid Owempay webhook signature");
    //   return NextResponse.json(
    //     { error: "Invalid signature" },
    //     { status: 401 }
    //   );
    // }

    // Extrair dados do webhook baseado na estrutura real do payload
    const { event, object } = fullPayload;

    if (!event || !object) {
      logger.error("Missing required fields in Owempay webhook", { fullPayload });
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Mapear os campos corretamente baseado no payload real
    const eventType = event || fullPayload.event; // 'cashout_completed'
    const transactionId = object?.id || fullPayload.object?.id; // 'B22acCOlAWoJl0N30ZAL'
    const externalId = object?.externalReference || fullPayload.object?.externalReference; // 'cmdsvttbu0001l9045tc9p03v'

    // Para eventos de cashout, o status é o próprio evento
    let status = object?.status || fullPayload.object?.status;
    if (!status && (eventType === 'cashout_completed' || eventType === 'cashout_failed')) {
      status = eventType; // Usar o evento como status para cashout
    }

    const originalEvent = event || fullPayload.event;

    console.log('🔍 OWEMPAY WEBHOOK - Dados extraídos:');
    console.log('  📝 Event Type:', eventType);
    console.log('  🆔 Transaction ID:', transactionId);
    console.log('  🔗 External ID:', externalId);
    console.log('  📊 Status:', status);
    console.log('  🆔 Job ID:', object?.jobId);
    console.log('  📦 Raw Data completo:', JSON.stringify({ event, object }, null, 2));

    const webhookData = {
      eventType,
      transactionId,
      externalId,
      status,
      originalEvent
    };

    logger.info("Owempay webhook data parsed", webhookData);
    console.log("📊 Owempay webhook data extracted:", JSON.stringify(webhookData, null, 2));

    // Buscar a transação no banco de dados
    console.log('🔍 OWEMPAY WEBHOOK - Buscando transação no banco de dados...');
    console.log('  🔍 Critérios de busca:', {
      transactionId,
      externalId,
      jobId: object?.jobId,
      eventType
    });

    // Primeiro tenta pelo externalReference (nosso ID interno)
    let transaction = await db.transaction.findFirst({
      where: {
        OR: [
          { id: externalId },
          { referenceCode: externalId },
          { externalId: transactionId },
          // Para eventos de cashout, também buscar pelo jobId
          ...(object?.jobId ? [{ externalId: object.jobId }] : []),
          ...(object?.jobId ? [{ referenceCode: object.jobId }] : [])
        ]
      }
    });

    if (!transaction) {
      console.warn('❌ OWEMPAY WEBHOOK - Transação não encontrada:', {
        transactionId,
        externalId,
        eventType
      });
      logger.warn("Transaction not found for Owempay webhook - ALLOWING FOR ANALYSIS", {
        eventType,
        transactionId,
        externalId,
        status,
        searchCriteria: {
          id: externalId,
          referenceCode: externalId,
          externalId: transactionId
        }
      });

      return NextResponse.json(
        { success: true, message: "Transaction not found but webhook processed" },
        { status: 200 }
      );
    }

    console.log('✅ OWEMPAY WEBHOOK - Transação encontrada:', {
      id: transaction.id,
      status: transaction.status,
      type: transaction.type,
      amount: transaction.amount
    });

    // Verificar se é um evento MED (refunded_med)
    if (eventType === 'transaction_refunded_med' || status === 'refunded_med') {
      console.log('🚨 OWEMPAY WEBHOOK - Detectado evento MED, processando infração...');
      
      try {
        const medResult = await processMedInfraction({
           transactionId: transaction.id,
           externalId: transactionId,
           reportDetails: `MED refund reported by OWEMPAY for transaction ${transactionId}`,
           organizationId: transaction.organizationId,
           gatewayName: 'OWEMPAY',
           reportedBy: 'OWEMPAY',
           type: 'REFUND_REQUEST',
           autoApprove: true
         });
         
         console.log('✅ OWEMPAY WEBHOOK - Infração MED processada:', medResult);
         logger.info('[OWEMPAY][MED] Infração MED criada com sucesso', {
           infractionId: medResult.infractionId,
           transactionId: transaction.id,
           externalId: transactionId
         });
        
      } catch (medError) {
        console.error('❌ OWEMPAY WEBHOOK - Erro ao processar MED:', medError);
        logger.error('[OWEMPAY][MED] Erro ao processar infração MED', {
          error: medError,
          transactionId: transaction.id,
          externalId: transactionId,
          eventType
        });
        // Não falhar o webhook se o processamento do MED falhar
      }
    }

    // Mapear status da Owempay para status interno
    console.log('🔄 OWEMPAY WEBHOOK - Mapeando status...');
    console.log('  📊 Status original da Owempay:', status);

    const internalStatus = mapOwempayStatusToInternal(status);

    console.log('  ✅ Status mapeado:', internalStatus);
    console.log('🔄 Status mapping completo:', {
      originalStatus: status,
      mappedStatus: internalStatus,
      transactionId: transaction.id,
      currentTransactionStatus: transaction.status
    });

    // Atualizar status da transação
    console.log('💾 OWEMPAY WEBHOOK - Atualizando status da transação...');
    console.log('  🆔 Transaction ID:', transaction.id);
    console.log('  📊 Novo status:', internalStatus);

    await updateTransactionStatus(
      transaction.id,
      internalStatus,
      status === "paid" ? new Date() : undefined
    );

    console.log('✅ OWEMPAY WEBHOOK - Status da transação atualizado com sucesso!');
    logger.info("Owempay webhook processed successfully", {
      transactionId: transaction.id,
      externalId,
      owempayTransactionId: transactionId,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    return NextResponse.json(
      {
        success: true,
        message: "Webhook processed successfully",
        transactionId: transaction.id,
        status: internalStatus
      },
      { status: 200 }
    );

    console.log('🎉 OWEMPAY WEBHOOK - Processamento concluído com sucesso!');
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ OWEMPAY WEBHOOK - Erro durante processamento:', error);
    console.error('❌ OWEMPAY WEBHOOK - Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('❌ OWEMPAY WEBHOOK - Dados do erro:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      name: error instanceof Error ? error.name : 'Unknown',
      cause: error instanceof Error ? error.cause : undefined
    });
    logger.error('Error processing Owempay webhook', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
