import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { getWebhookStats } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(req: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    logger.info("Session check", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id
    });

    if (!session?.user?.id) {
      logger.warn("Unauthorized access attempt to webhook stats");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

        const { searchParams } = new URL(req.url);
    const organizationSlug = searchParams.get('organizationId'); // Keep param name for compatibility

    if (!organizationSlug) {
      logger.warn("Missing organization slug in request");
      return NextResponse.json({ error: "Organization ID required" }, { status: 400 });
    }

    logger.info("Fetching webhook stats", {
      userId: session.user.id,
      organizationSlug
    });

    logger.info("Calling getWebhookStats", { organizationSlug });
    const stats = await getWebhookStats(organizationSlug);
    logger.info("getWebhookStats result", { stats });

    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error("Failed to fetch webhook stats", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { error: "Failed to fetch webhook stats" },
      { status: 500 }
    );
  }
}
