import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Ensure params is properly awaited in Next.js route handlers
  const { id } = await params;
  const invitationId = id;

  try {
    console.log("API pública: Buscando convite para ID:", invitationId);
    
    // Buscar o convite diretamente do banco de dados
    const invitation = await db.invitation.findUnique({
      where: { id: invitationId },
      include: { organization: true },
    });

    if (!invitation) {
      console.log("API pública: Convite não encontrado");
      return NextResponse.json(
        { error: "Invitation not found" },
        { status: 404 }
      );
    }

    console.log("API pública: Convite encontrado:", invitation.id, invitation.email, invitation.role);

    // Retornar apenas as informações necessárias
    return NextResponse.json({
      id: invitation.id,
      email: invitation.email,
      role: invitation.role,
      status: invitation.status,
      organizationId: invitation.organizationId,
      organizationName: invitation.organization.name,
      organizationSlug: invitation.organization.slug,
      logoUrl: invitation.organization.logo,
    });
  } catch (error) {
    console.error("API pública: Erro ao buscar convite:", error);
    return NextResponse.json(
      { error: "Failed to fetch invitation details" },
      { status: 500 }
    );
  }
}
