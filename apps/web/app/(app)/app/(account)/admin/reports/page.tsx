import { PageHeader } from "@saas/shared/components/PageHeader";
import { OrganizationsRevenueReport } from "@saas/admin/component/OrganizationsRevenueReport";

export async function generateMetadata() {
  return {
    title: "Relatórios - Admin",
  };
}

export default async function AdminReportsPage() {
  return (
    <>
      <PageHeader
        title="Relatório de Receita por Organização"
        subtitle="Análises detalhadas e métricas de negócio da plataforma"
      />

      <OrganizationsRevenueReport />
    </>
  );
}
