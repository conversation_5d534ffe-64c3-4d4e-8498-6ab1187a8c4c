import { PageHeader } from "@saas/shared/components/PageHeader";
import { AdminTransactionsContent } from "@saas/transactions/components/admin/AdminTransactionsContent";
import { AdminTransactionSummaryCards } from "@saas/transactions/components/admin/AdminTransactionSummaryCards";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: `${t("app.menu.transfers") || "Transferências"} - Admin`,
  };
}

export default async function AdminTransfersPage() {
  const t = await getTranslations();

  return (
    <>
      <PageHeader
        title={t("app.menu.transfers") || "Transferências"}
        subtitle="Gerencie transferências PIX de todas as organizações"
      />

      <div className="space-y-6">
        <AdminTransactionSummaryCards typeFilter="SEND" />
        <AdminTransactionsContent defaultTypeFilter="SEND" />
      </div>
    </>
  );
}