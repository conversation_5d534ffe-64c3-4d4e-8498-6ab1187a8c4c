import { auth } from "@repo/auth";
import { OrganizationForm } from "@saas/admin/component/organizations/OrganizationForm";
import { getAdminPath } from "@saas/admin/lib/links";
import { fullOrganizationQueryKey } from "@saas/organizations/lib/api";
import { getQueryClient } from "@shared/lib/server";
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { headers } from "next/headers";
import Link from "next/link";

export default async function OrganizationAdminPage({ params }: { params: { id: string } }) {
	const organizationId = params.id;
	const t = await getTranslations();
	const queryClient = getQueryClient();

	// Prefetch organization data
	try {
		const headersList = await headers();
		const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/organizations/${organizationId}/full`, {
			headers: headersList,
		});

		if (response.ok) {
			const data = await response.json();
			queryClient.setQueryData(fullOrganizationQueryKey(organizationId), data);
		} else {
			// Fallback para o método original
			await queryClient.prefetchQuery({
				queryKey: fullOrganizationQueryKey(organizationId),
				queryFn: async () =>
					await auth.api.getFullOrganization({
						query: {
							organizationId: organizationId,
						},
						headers: headersList,
					}),
			});
		}
	} catch (error) {
		console.error("Error prefetching organization data:", error);
		// Fallback para o método original
		await queryClient.prefetchQuery({
			queryKey: fullOrganizationQueryKey(organizationId),
			queryFn: async () =>
				await auth.api.getFullOrganization({
					query: {
						organizationId: organizationId,
					},
					headers: await headers(),
				}),
		});
	}

	return (
		<HydrationBoundary state={dehydrate(queryClient)}>
			<div>
				<div className="mb-2 flex justify-start">
					<Button variant="link" size="sm" asChild className="px-0">
						<Link href={getAdminPath("/organizations")}>
							<ArrowLeftIcon className="mr-1.5 size-4" />
							{t("admin.organizations.backToList")}
						</Link>
					</Button>
				</div>
				
				{/* ✅ Manter apenas o componente existente que já funciona */}
				<OrganizationForm organizationId={organizationId} />
			</div>
		</HydrationBoundary>
	);
}
