"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Label } from "@ui/components/label";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { AlertCircle, Info, Loader2 } from "lucide-react";

// Schema simples apenas para webhooks
const webhookControlSchema = z.object({
  webhooksEnabled: z.boolean(),
});

type WebhookControlData = z.infer<typeof webhookControlSchema>;

interface WebhookControlModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gatewayId: string;
  gatewayName: string;
  gatewayType: string;
  currentWebhookStatus: boolean;
  onSuccess?: () => void;
}

export const WebhookControlModal = ({
  open,
  onOpenChange,
  gatewayId,
  gatewayName,
  gatewayType,
  currentWebhookStatus,
  onSuccess
}: WebhookControlModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  // Debug: Log dos props recebidos
  console.log("🔍 WebhookControlModal props:", {
    gatewayId,
    gatewayName,
    gatewayType,
    currentWebhookStatus,
    currentWebhookStatusType: typeof currentWebhookStatus
  });

  const form = useForm<WebhookControlData>({
    resolver: zodResolver(webhookControlSchema),
    defaultValues: {
      webhooksEnabled: currentWebhookStatus,
    },
  });

  // Atualizar o formulário quando o status atual mudar
  React.useEffect(() => {
    console.log("🔄 useEffect - Resetando formulário com status:", {
      currentWebhookStatus,
      currentWebhookStatusType: typeof currentWebhookStatus
    });
    
    form.reset({
      webhooksEnabled: currentWebhookStatus,
    });
  }, [currentWebhookStatus, form]);

  const onSubmit = async (data: WebhookControlData) => {
    setIsSubmitting(true);

    try {
      console.log("🔄 Atualizando status de webhook:", {
        gatewayId,
        gatewayType,
        webhooksEnabled: data.webhooksEnabled,
        currentStatus: currentWebhookStatus
      });

      const response = await fetch(`/api/admin/gateways/${gatewayId}/webhook-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          webhooksEnabled: data.webhooksEnabled,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao atualizar status de webhook');
      }

      const result = await response.json();
      console.log("✅ Webhook status atualizado:", result);

      // Invalidate queries para atualizar a UI
      queryClient.invalidateQueries({ queryKey: ['gateways'] });
      queryClient.invalidateQueries({ queryKey: ['admin-gateways'] });

      // Fechar modal
      onOpenChange(false);

      // Notificar sucesso
      toast.success(
        `Webhooks ${data.webhooksEnabled ? 'ativados' : 'desativados'} com sucesso para ${gatewayName}`
      );

      // Callback de sucesso
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error("❌ Erro ao atualizar webhook status:", error);
      toast.error(
        error instanceof Error ? error.message : 'Erro ao atualizar status de webhook'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Resetar para o valor original
    form.reset({
      webhooksEnabled: currentWebhookStatus,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Controle de Webhooks
          </DialogTitle>
          <DialogDescription>
            Configure o processamento de webhooks para {gatewayName}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status dos Webhooks</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Importante:</strong> Quando desabilitado, webhooks retornam sucesso mas não processam transações.
                  Use esta opção para manutenção ou debugging.
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/20">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">Processar Webhooks</Label>
                  <p className="text-xs text-muted-foreground">
                    {form.watch('webhooksEnabled') ? 'Webhooks estão ativos' : 'Webhooks estão desativados'}
                  </p>
                </div>
                <Switch
                  checked={(() => {
                    const value = form.watch('webhooksEnabled');
                    console.log("🔧 Switch value:", {
                      value,
                      type: typeof value,
                      currentWebhookStatus,
                      currentWebhookStatusType: typeof currentWebhookStatus
                    });
                    return value;
                  })()}
                  onCheckedChange={(checked) => {
                    console.log("🔧 Webhook switch changed to:", checked);
                    form.setValue('webhooksEnabled', checked);
                  }}
                  disabled={isSubmitting}
                />
              </div>

              {/* Status atual */}
              <div className="text-xs text-muted-foreground">
                Status atual: {currentWebhookStatus ? 'Ativado' : 'Desativado'}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || form.watch('webhooksEnabled') === currentWebhookStatus}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting ? 'Salvando...' : 'Salvar'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
