"use client";

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog';
import { Button } from '@ui/components/button';
import { Card, CardContent } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Loader2, DollarSign, RefreshCw, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@ui/components/alert';

interface BalanceData {
  accountId: string;
  balance: number;
  requestId: string;
}

interface BalanceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gatewayId: string;
  gatewayName: string;
  organizationId: string;
}

export function BalanceModal({
  open,
  onOpenChange,
  gatewayId,
  gatewayName,
  organizationId,
}: BalanceModalProps) {
  const [balance, setBalance] = useState<BalanceData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBalance = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/gateways/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gatewayId,
          organizationId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar saldo');
      }

      setBalance(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchBalance();
    }
  }, [open]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Saldo da Conta
          </DialogTitle>
          <DialogDescription>
            Saldo atual da conta {gatewayName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Buscando saldo...</span>
            </div>
          )}

          {error && (
            <Alert variant="error">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {balance && !loading && (
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {formatCurrency(balance.balance)}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Saldo disponível
                    </p>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">ID da Conta:</span>
                      <Badge>{balance.accountId}</Badge>
                    </div>

                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={fetchBalance}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
            <Button onClick={() => onOpenChange(false)}>
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
