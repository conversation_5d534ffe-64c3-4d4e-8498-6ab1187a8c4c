"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { useToast } from "@ui/hooks/use-toast";
import { Building2, Search, Loader2, AlertTriangle, CheckCircle, Ban, Shield, ChevronLeft, ChevronRight } from "lucide-react";
import { Alert, AlertDescription } from "@ui/components/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog";

type Organization = {
  id: string;
  name: string;
  withdrawalBlocked: boolean;
  withdrawalBlockedReason?: string;
  withdrawalBlockedAt?: string;
  withdrawalBlockedBy?: string;
};

type OrganizationDialogData = {
  organization: Organization;
  withdrawalBlocked: boolean;
  withdrawalBlockedReason: string;
};

export function OrganizationWithdrawalControl() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrg, setSelectedOrg] = useState<OrganizationDialogData | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrganizations, setTotalOrganizations] = useState(0);
  const itemsPerPage = 10;

  const { toast } = useToast();

  useEffect(() => {
    loadOrganizations();
  }, [currentPage, searchTerm]);

    const loadOrganizations = async () => {
    try {
      setIsLoading(true);
      const offset = (currentPage - 1) * itemsPerPage;
      const params = new URLSearchParams({
        offset: offset.toString(),
        limit: itemsPerPage.toString(),
        ...(searchTerm && { query: searchTerm })
      });

      const response = await fetch(`/api/admin/organizations?${params}`);

      if (response.ok) {
        const result = await response.json();
        if (result.organizations) {
          setOrganizations(result.organizations);
          setFilteredOrganizations(result.organizations);
          setTotalOrganizations(result.total || 0);
          setTotalPages(Math.ceil((result.total || 0) / itemsPerPage));
        }
      } else {
        console.error("Erro ao carregar organizações:", response.status);
      }
    } catch (error) {
      console.error("Erro ao carregar organizações:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as organizações",
        variant: "error"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset para primeira página ao buscar
  };

  const handleEditOrganization = (organization: Organization) => {
    setSelectedOrg({
      organization,
      withdrawalBlocked: organization.withdrawalBlocked,
      withdrawalBlockedReason: organization.withdrawalBlockedReason || ""
    });
    setIsDialogOpen(true);
  };

  const handleSaveOrganizationSettings = async () => {
    if (!selectedOrg) return;

    try {
      setIsSaving(true);
      const response = await fetch(`/api/admin/organizations/${selectedOrg.organization.id}/withdrawal-block`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          withdrawalBlocked: selectedOrg.withdrawalBlocked,
          withdrawalBlockedReason: selectedOrg.withdrawalBlockedReason
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Atualizar a lista de organizações
          setOrganizations(prev =>
            prev.map(org =>
              org.id === selectedOrg.organization.id
                ? { ...org, ...result.data }
                : org
            )
          );
          setFilteredOrganizations(prev =>
            prev.map(org =>
              org.id === selectedOrg.organization.id
                ? { ...org, ...result.data }
                : org
            )
          );

          toast({
            title: "Sucesso",
            description: result.message,
            variant: "success"
          });

          setIsDialogOpen(false);
          setSelectedOrg(null);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao salvar configurações");
      }
    } catch (error) {
      console.error("Erro ao salvar configurações:", error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Não foi possível salvar as configurações",
        variant: "error"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando organizações...</span>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Controle por Organização
        </CardTitle>
        <CardDescription>
          Gerencie bloqueios de saque específicos para cada organização.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4" />
            <Input
              placeholder="Buscar organização..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <div className="text-sm text-muted-foreground">
            {totalOrganizations} organizações encontradas
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organização</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Motivo do Bloqueio</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrganizations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                    {searchTerm ? "Nenhuma organização encontrada" : "Nenhuma organização cadastrada"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredOrganizations.map((org) => (
                  <TableRow key={org.id}>
                    <TableCell className="font-medium">{org.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {org.withdrawalBlocked ? (
                          <>
                            <Ban className="h-4 w-4 text-red-500" />
                            <span className="text-red-600 font-medium">Bloqueado</span>
                          </>
                        ) : (
                          <>
                            <Shield className="h-4 w-4 text-green-500" />
                            <span className="text-green-600 font-medium">Liberado</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {org.withdrawalBlocked ? (
                        <span className="text-sm text-muted-foreground">
                          {org.withdrawalBlockedReason || "Não informado"}
                        </span>
                      ) : (
                        <span className="text-sm text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Dialog open={isDialogOpen && selectedOrg?.organization.id === org.id} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditOrganization(org)}
                          >
                            Gerenciar
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                          <DialogHeader>
                            <DialogTitle>Controle de Saques</DialogTitle>
                            <DialogDescription>
                              Configure o bloqueio de saques para {org.name}
                            </DialogDescription>
                          </DialogHeader>

                          {selectedOrg && (
                            <div className="space-y-4 py-4">
                              <div className="flex items-center space-x-2">
                                <Switch
                                  id="org-withdrawal-blocked"
                                  checked={selectedOrg.withdrawalBlocked}
                                  onCheckedChange={(checked) =>
                                    setSelectedOrg(prev => prev ? { ...prev, withdrawalBlocked: checked } : null)
                                  }
                                />
                                <Label htmlFor="org-withdrawal-blocked" className="font-medium">
                                  {selectedOrg.withdrawalBlocked ? "Saques Bloqueados" : "Saques Liberados"}
                                </Label>
                              </div>

                              {selectedOrg.withdrawalBlocked && (
                                <div className="space-y-2">
                                  <Label htmlFor="org-withdrawal-reason">Motivo do Bloqueio</Label>
                                  <Textarea
                                    id="org-withdrawal-reason"
                                    placeholder="Digite o motivo do bloqueio..."
                                    value={selectedOrg.withdrawalBlockedReason}
                                    onChange={(e) =>
                                      setSelectedOrg(prev =>
                                        prev ? { ...prev, withdrawalBlockedReason: e.target.value } : null
                                      )
                                    }
                                    className="min-h-[80px]"
                                  />
                                </div>
                              )}

                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="outline"
                                  onClick={() => setIsDialogOpen(false)}
                                  disabled={isSaving}
                                >
                                  Cancelar
                                </Button>
                                <Button
                                  onClick={handleSaveOrganizationSettings}
                                  disabled={isSaving}
                                >
                                  {isSaving ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Salvando...
                                    </>
                                  ) : (
                                    "Salvar"
                                  )}
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Paginação */}
        {totalPages > 1 && (
          <div className="flex flex-col items-center gap-4 justify-between">
             <div className="text-sm text-muted-foreground">
              Página {currentPage} de {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Anterior
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNumber}
                      variant={currentPage === pageNumber ? "primary" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNumber)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNumber}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Próxima
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

           


          </div>
        )}
      </CardContent>
    </Card>
  );
}
