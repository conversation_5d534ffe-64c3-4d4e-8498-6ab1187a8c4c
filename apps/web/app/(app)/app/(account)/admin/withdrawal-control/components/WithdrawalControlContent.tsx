"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { useToast } from "@ui/hooks/use-toast";
import { Ban, Globe, Building2, Loader2, AlertTriangle, CheckCircle, Shield } from "lucide-react";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { OrganizationWithdrawalControl } from "./OrganizationWithdrawalControl";
import { MedAutoApprovalSection } from "@saas/admin/component/system-settings/MedAutoApprovalSection";

type SystemSettings = {
  globalWithdrawalBlocked: boolean;
  globalWithdrawalMessage?: string;
  globalWithdrawalBlockedAt?: string;
  globalWithdrawalBlockedBy?: string;
};

export function WithdrawalControlContent() {
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    globalWithdrawalBlocked: false,
    globalWithdrawalMessage: "Saques temporariamente indisponíveis. Para dúvidas, entre em contato com o suporte."
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Carregar configurações do sistema
  useEffect(() => {
    loadSystemSettings();
  }, []);

  const loadSystemSettings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/system-settings");

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSystemSettings(result.data);
        }
      } else {
        console.error("Erro ao carregar configurações:", response.status);
      }
    } catch (error) {
      console.error("Erro ao carregar configurações:", error);
              toast({
          title: "Erro",
          description: "Não foi possível carregar as configurações",
          variant: "error"
        });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSystemSettings = async () => {
    try {
      setIsSaving(true);
      const response = await fetch("/api/admin/system-settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          globalWithdrawalBlocked: systemSettings.globalWithdrawalBlocked,
          globalWithdrawalMessage: systemSettings.globalWithdrawalMessage
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSystemSettings(result.data);
          toast({
            title: "Sucesso",
            description: result.message,
            variant: "default"
          });
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao salvar configurações");
      }
    } catch (error) {
      console.error("Erro ao salvar configurações:", error);
              toast({
          title: "Erro",
          description: error instanceof Error ? error.message : "Não foi possível salvar as configurações",
          variant: "error"
        });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando configurações...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="global" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="global" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Configurações Globais
          </TabsTrigger>
          <TabsTrigger value="organizations" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Por Organização
          </TabsTrigger>
          <TabsTrigger value="med" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Aprovação Automática de MED
          </TabsTrigger>
        </TabsList>

        <TabsContent value="global" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Ban className="h-5 w-5" />
                Bloqueio Global de Saques
              </CardTitle>
              <CardDescription>
                Configure o bloqueio de saques para toda a plataforma. Quando ativado, nenhuma organização poderá realizar saques.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemSettings.globalWithdrawalBlocked && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Atenção:</strong> Os saques estão atualmente bloqueados para toda a plataforma.
                    {systemSettings.globalWithdrawalBlockedAt && (
                      <span className="block mt-1 text-sm">
                        Bloqueado em: {new Date(systemSettings.globalWithdrawalBlockedAt).toLocaleString('pt-BR')}
                      </span>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="global-withdrawal-blocked"
                  checked={systemSettings.globalWithdrawalBlocked}
                  onCheckedChange={(checked) =>
                    setSystemSettings(prev => ({ ...prev, globalWithdrawalBlocked: checked }))
                  }
                />
                <Label htmlFor="global-withdrawal-blocked" className="font-medium">
                  {systemSettings.globalWithdrawalBlocked ? "Saques Bloqueados" : "Saques Liberados"}
                </Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="withdrawal-message">Mensagem de Bloqueio</Label>
                <Textarea
                  id="withdrawal-message"
                  placeholder="Digite a mensagem que será exibida aos usuários quando tentarem fazer um saque..."
                  value={systemSettings.globalWithdrawalMessage || ""}
                  onChange={(e) =>
                    setSystemSettings(prev => ({ ...prev, globalWithdrawalMessage: e.target.value }))
                  }
                  className="min-h-[100px]"
                />
                <p className="text-sm text-muted-foreground">
                  Esta mensagem será exibida para os usuários quando tentarem realizar um saque e o bloqueio estiver ativo.
                </p>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={handleSaveSystemSettings}
                  disabled={isSaving}
                  className="min-w-[120px]"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Salvar
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organizations" className="space-y-6">
          <OrganizationWithdrawalControl />
        </TabsContent>

        <TabsContent value="med" className="space-y-6">
          <MedAutoApprovalSection />
        </TabsContent>
      </Tabs>
    </div>
  );
}
