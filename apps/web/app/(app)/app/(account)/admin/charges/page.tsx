import { PageHeader } from "@saas/shared/components/PageHeader";
import { AdminTransactionsContent } from "@saas/transactions/components/admin/AdminTransactionsContent";
import { AdminTransactionSummaryCards } from "@saas/transactions/components/admin/AdminTransactionSummaryCards";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: `${t("app.menu.charges") || "Cobranças"} - Admin`,
  };
}

export default async function AdminChargesPage() {
  const t = await getTranslations();

  return (
    <>
      <PageHeader
        title={t("app.menu.charges") || "Pagamentos"}
        subtitle="Gerencie pagamentos PIX de todas as organizações"
      />

      <div className="space-y-6">
        <AdminTransactionSummaryCards typeFilter="CHARGE" />
        <AdminTransactionsContent defaultTypeFilter="CHARGE" />
      </div>
    </>
  );
}
