"use client";

import { use<PERSON>ara<PERSON> } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { Input } from "@ui/components/input";
import { CopyIcon, ExternalLinkIcon, PlusIcon, RefreshCwIcon, Trash2Icon } from "lucide-react";
import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@ui/components/dialog";
import { Label } from "@ui/components/label";
import Link from "next/link";
import { ApiKeyDisplay } from "../../../../../../../components/api-key-display";
// Import the ApiKeyDisplay component
// import { ApiKeyDisplay } from "@/components/api-key-display";
// Import apiClient if needed later
// import { apiClient } from "@shared/lib/api-client";

// Define the types for API keys
interface ApiKeyPermissions {
  transactions: {
    read: boolean;
    write: boolean;
  };
  customers: {
    read: boolean;
    write: boolean;
  };
}

interface ApiKey {
  id: string;
  name: string;
  type: "production" | "test";
  key?: string;
  prefix: string;
  permissions: ApiKeyPermissions;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  lastUsedAt: string | null;
  createdById?: string;
}

interface Organization {
  id: string;
  slug: string;
  name: string;
}

async function fetchOrganizationId(slug: string): Promise<string> {
  try {
    console.log(`Fetching organization with slug: ${slug}`);
    // Use the Next.js API route directly instead of the Hono client
    const response = await fetch(`/api/organizations/by-slug/${slug}`, {
      credentials: 'include'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Organization fetch failed: ${response.status}`, errorText);
      throw new Error(`Failed to fetch organization: ${response.status} ${response.statusText}`);
    }

    const organization = await response.json() as Organization;
    console.log(`Found organization:`, organization);
    return organization.id;
  } catch (error) {
    console.error("Error fetching organization:", error);
    throw error;
  }
}

export default function ApiIntegrationPage() {
  const params = useParams();
  const organizationSlug = params.organizationSlug as string;
  const queryClient = useQueryClient();
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [newApiKey, setNewApiKey] = useState<ApiKey | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  // Fetch organization ID from slug
  useEffect(() => {
    if (organizationSlug) {
      fetchOrganizationId(organizationSlug)
        .then(id => {
          setOrganizationId(id);
        })
        .catch(error => {
          console.error("Failed to fetch organization ID:", error);
          setFetchError("Failed to fetch organization data. Please try again.");
        });
    }
  }, [organizationSlug]);

  const { data: apiKeys = [], isLoading, refetch } = useQuery({
    queryKey: ["apiKeys", organizationId],
    queryFn: async () => {
      try {
        if (!organizationId) throw new Error("Organization ID not available");

        console.log(`Fetching API keys for organization: ${organizationId}`);
        const response = await fetch(`/api/api-keys?organizationId=${organizationId}`, {
          credentials: 'include'
        });

        const responseText = await response.text();
        console.log(`API keys fetch response: ${response.status}`, responseText);

        if (!response.ok) {
          let errorMessage = "Failed to fetch API keys";
          try {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.error || errorMessage;
          } catch (e) {
            errorMessage = responseText || errorMessage;
          }
          throw new Error(errorMessage);
        }

        try {
          const data = JSON.parse(responseText);
          console.log("API keys fetched successfully:", data);
          return data;
        } catch (e) {
          console.error("Error parsing API keys response:", e);
          throw new Error("Invalid response format from server");
        }
      } catch (error) {
        console.error("Error in fetchApiKeys function:", error);
        setFetchError(error instanceof Error ? error.message : String(error));
        throw error;
      }
    },
    enabled: !!organizationId,
    retry: 1
  });

  // Create API key mutation
  const createMutation = useMutation({
    mutationFn: async (data: {
      name: string;
      type: "production" | "test";
      permissions: ApiKeyPermissions;
      organizationId: string;
    }) => {
      console.log("Creating API key with data:", data);
      const response = await fetch('/api/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data),
        credentials: 'include'
      });

      const responseText = await response.text();
      console.log(`API key creation response: ${response.status}`, responseText);

      if (!response.ok) {
        let errorMessage = `Failed to create API key: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = responseText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      try {
        const data = JSON.parse(responseText) as ApiKey;
        return data;
      } catch (e) {
        console.error("Error parsing API key response:", e);
        throw new Error("Invalid response format from server");
      }
    },
    onSuccess: (data: ApiKey) => {
      queryClient.invalidateQueries({ queryKey: ["apiKeys"] });
      toast.success("Chave de API criada com sucesso");
      setNewApiKey(data);
    },
    onError: (err: Error) => {
      console.error("Error creating API key:", err);
      if (err.message.includes("Headers are required")) {
        toast.error("Erro de autenticação. Por favor, atualize a página e tente novamente.");
      } else {
        toast.error(`Falha ao criar chave: ${err.message}`);
      }
    },
  });

  // Delete API key mutation
  const deleteMutation = useMutation({
    mutationFn: async ({ id, organizationId }: { id: string; organizationId: string }) => {
      console.log(`Deleting API key: ${id} for organization: ${organizationId}`);
      const response = await fetch(`/api/api-keys?id=${id}&organizationId=${organizationId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const responseText = await response.text();
      console.log(`API key deletion response: ${response.status}`, responseText);

      if (!response.ok) {
        let errorMessage = `Failed to delete API key: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = responseText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      try {
        // If the response is empty, return a success object
        if (!responseText.trim()) {
          return { success: true };
        }
        return JSON.parse(responseText);
      } catch (e) {
        console.error("Error parsing API key deletion response:", e);
        // Return success anyway since the request was successful
        return { success: true };
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["apiKeys"] });
      toast.success("Chave de API excluída com sucesso");
    },
    onError: (err: Error) => {
      console.error("Error deleting API key:", err);
      if (err.message.includes("Headers are required")) {
        toast.error("Erro de autenticação. Por favor, atualize a página e tente novamente.");
      } else {
        toast.error(`Falha ao excluir chave: ${err.message}`);
      }
    },
  });

  const handleRetry = () => {
    setFetchError(null);
    refetch();
  };

  return (
    <>
      <div className="mb-8">
        <PageHeader
          title="API"
          subtitle="Gerencie suas credenciais de API e explore nossa documentação."
        />
      </div>

      {newApiKey && (
        <div className="mb-4">
          <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg mb-4 border border-green-200 dark:border-green-800">
            <h3 className="font-medium text-green-800 dark:text-green-400 mb-2">Nova chave de API criada com sucesso</h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              Esta é a única vez que a chave completa será exibida. Copie-a agora para uso futuro.
            </p>
          </div>

          <ApiKeyDisplay
            apiKey={newApiKey.key || ""}
            organizationId={organizationId || ""}
            description="Use estas credenciais para autenticar suas requisições à API. A chave de API deve ser enviada no cabeçalho X-API-Key e o Organization ID deve ser incluído nos parâmetros da requisição."
          />

          <div className="flex justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setNewApiKey(null)}
              className="0"
            >
              <Trash2Icon className="h-4 w-4 mr-2" />
              Fechar
            </Button>
          </div>
        </div>
      )}

      <Tabs defaultValue="keys" className="space-y-4">
        <TabsList>
          <TabsTrigger value="keys">Chaves de API</TabsTrigger>
          <TabsTrigger value="docs">Documentação</TabsTrigger>
        </TabsList>

        <TabsContent value="keys" className="space-y-4">
          {!organizationId ? (
            <Card className=" ">
              <CardContent className="py-8">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground dark:text-gray-400">
                    Carregando dados da organização...
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : isLoading ? (
            <div className="dark:text-gray-300">Carregando...</div>
          ) : fetchError ? (
            <Card className=" ">
              <CardContent className="py-8">
                <div className="text-center">
                  <p className="text-sm text-red-500 dark:text-red-400 mb-4">
                    {fetchError}
                  </p>
                  <Button onClick={handleRetry} className="dark:bg-primary dark:text-white dark:hover:bg-primary/90">
                    <RefreshCwIcon className="mr-2 h-4 w-4" />
                    Tentar novamente
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : apiKeys.length === 0 ? (
            <Card className=" ">
              <CardContent className="py-8">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground dark:text-gray-400">
                    Você ainda não tem nenhuma chave de API.
                  </p>
                  <CreateApiKeyDialog
                    isLoading={createMutation.isPending}
                    onSubmit={(data) =>
                      createMutation.mutate({
                        ...data,
                        organizationId: organizationId,
                      })
                    }
                  >
                    <Button className="mt-4  ">
                      <PlusIcon className="mr-2 h-4 w-4" />
                      Criar chave de API
                    </Button>
                  </CreateApiKeyDialog>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="flex justify-end mb-4">
                <CreateApiKeyDialog
                  isLoading={createMutation.isPending}
                  onSubmit={(data) =>
                    createMutation.mutate({
                      ...data,
                      organizationId: organizationId,
                    })
                  }
                >
                  <Button  variant={"primary"} >
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Criar chave de API
                  </Button>
                </CreateApiKeyDialog>
              </div>
              {apiKeys.map((apiKey: ApiKey) => (
                <ApiKeyCard
                  key={apiKey.id}
                  apiKey={apiKey}
                  onDelete={() =>
                    deleteMutation.mutate({
                      id: apiKey.id,
                      organizationId: organizationId,
                    })
                  }
                />
              ))}
            </>
          )}
        </TabsContent>

        <TabsContent value="docs">
          <Card className=" ">
            <CardHeader>
              <CardTitle className="dark:text-white">Documentação da API</CardTitle>
              <CardDescription className="dark:text-gray-400">
                Consulte nossa documentação completa para integrar seu sistema com nossa plataforma.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground dark:text-gray-400">
                Nossa API RESTful permite que você integre facilmente sua aplicação com nossa plataforma de pagamentos.
                Consulte nossa documentação completa para mais detalhes sobre endpoints disponíveis, exemplos de código e guias de integração.
              </p>
              <Link href="/api/docs" target="_blank">
                <Button className="mt-10  ">
                  Acessar documentação
                  <ExternalLinkIcon className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  );
}

function ApiKeyCard({
  apiKey,
  onDelete,
}: {
  apiKey: ApiKey;
  onDelete: () => void;
}) {
  return (
    <Card className=" ">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="dark:text-white">{apiKey.name}</CardTitle>
            <CardDescription className="mt-1 dark:text-gray-400">
              Criada em {new Intl.DateTimeFormat("pt-BR").format(new Date(apiKey.createdAt))}
            </CardDescription>
          </div>
          <div className={`px-2 py-1 text-xs font-medium rounded-full ${
            apiKey.type === "production"
              ? "bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-400"
              : "bg-amber-50 text-amber-700 dark:bg-amber-900 dark:text-amber-400"
          }`}>
            {apiKey.type === "production" ? "Produção" : "Teste"}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor={`api-key-${apiKey.id}`} className="dark:text-gray-300">Chave de API</Label>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Input
                id={`api-key-${apiKey.id}`}
                readOnly
                value={apiKey.prefix}
                type="text"
                className="font-mono pr-10    overflow-x-auto"
                onClick={(e) => (e.target as HTMLInputElement).select()}
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                navigator.clipboard.writeText(apiKey.prefix);
                toast.success("Prefixo da chave copiado para a área de transferência");
              }}
              title="Copiar prefixo da chave"
              className="0"
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`org-id-${apiKey.id}`} className="dark:text-gray-300">ID da Organização</Label>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Input
                id={`org-id-${apiKey.id}`}
                readOnly
                value={apiKey.organizationId}
                type="text"
                className="font-mono pr-10     overflow-x-auto"
                onClick={(e) => (e.target as HTMLInputElement).select()}
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                navigator.clipboard.writeText(apiKey.organizationId);
                toast.success("ID da Organização copiado para a área de transferência");
              }}
              title="Copiar ID da Organização"
              className="  "
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="text-destructive  "
              onClick={onDelete}
              title="Excluir chave de API"
            >
              <Trash2Icon className="h-4 w-4" />
            </Button>
          </div>
        </div>


      </CardContent>
    </Card>
  );
}

function CreateApiKeyDialog({
  children,
  onSubmit,
  isLoading = false
}: {
  children?: React.ReactNode;
  onSubmit: (data: {
    name: string;
    type: "production" | "test";
    permissions: ApiKeyPermissions;
  }) => void;
  isLoading?: boolean;
}) {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [type, setType] = useState<"production" | "test">("production");
  // Definir permissões padrão (todas com leitura ativada)
  const permissions: ApiKeyPermissions = {
    transactions: { read: true, write: true },
    customers: { read: true, write: true },
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ name, type, permissions });
    // Close the dialog on submit
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen);
      if (!isOpen) {
        // Reset form when dialog is closed
        setName("");
        setType("test");
      }
    }}>
      <DialogTrigger asChild>
        {children || (
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            Criar chave de API
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className=" ">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Criar nova chave de API</DialogTitle>
            <DialogDescription className="dark:text-gray-400">
              Crie uma nova chave de API para integrar sua aplicação.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="dark:text-gray-300">Nome</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Ex: Integração com Gateway"
                required
                disabled={isLoading}
                className="dark:bg-gray-800 dark:border-gray-700 dark:text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="dark:text-gray-300">Tipo</Label>
              <div className="flex space-x-4">

              <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="production"
                    checked={type === "production"}
                    onChange={(e) => setType(e.target.value as "test" | "production")}
                    disabled={isLoading}
                    className="dark:bg-gray-800 dark:border-gray-700"
                  />
                  <span className="dark:text-gray-300">Produção</span>
                </label>
               <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value="test"
                    checked={type === "test"}
                    onChange={(e) => setType(e.target.value as "test" | "production")}
                    disabled={isLoading}
                    className="dark:bg-gray-800 dark:border-gray-700"
                  />
                  <span className="dark:text-gray-300">Teste</span>
                </label>

              </div>
              <div className="mt-4 p-3 rounded-md border ">
                <p className="text-sm dark:text-gray-300">
                  <strong>Nota:</strong> Esta chave terá permissões completas para leitura e escrita de transações e transferências.
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}  variant={"primary"}>
              {isLoading ? "Criando..." : "Criar chave"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
