"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Switch } from "@ui/components/switch";
import { BanknoteIcon, AlertCircle } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { useToast } from "@ui/hooks/use-toast";

interface GatewayCardProps {
  id: string;
  name: string;
  description: string;
  logo: string;
  features: {
    pixReceive: boolean;
    pixSend: boolean;
  };
  highlight: boolean;
  isConfigured: boolean;
  isActive: boolean;
  isDefault: boolean;
  organizationSlug: string;
  onUpdateStatus: (id: string, isActive: boolean) => Promise<void>;
}

export function GatewayCard({
  id,
  name,
  description,
  logo,
  features,
  highlight,
  isConfigured,
  isActive,
  isDefault,
  organizationSlug,
  onUpdateStatus,
}: GatewayCardProps) {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<boolean | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  const handleStatusToggle = (newStatus: boolean) => {
    setPendingStatus(newStatus);
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmStatusChange = async () => {
    if (pendingStatus === null) return;

    setIsUpdating(true);
    try {
      await onUpdateStatus(id, pendingStatus);
      toast({
        title: "Status atualizado",
        description: `Gateway ${pendingStatus ? "ativado" : "desativado"} com sucesso.`,
      });
    } catch (error) {
      toast({
        title: "Erro ao atualizar status",
        description: "Não foi possível atualizar o status do gateway.",
        variant: "error",
      });
    } finally {
      setIsUpdating(false);
      setIsConfirmDialogOpen(false);
      setPendingStatus(null);
    }
  };



  return (
    <>
      <Card className={highlight ? "border-primary/50" : ""}>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-muted">
              <BanknoteIcon className="h-6 w-6 text-primary" />
            </div>
            <div className="flex flex-col items-end gap-2">
              {isConfigured && (
                <div className="flex items-center gap-2">
                  <Switch
                    checked={isActive}
                    onCheckedChange={handleStatusToggle}
                    disabled={isUpdating}
                  />
                  <Badge
                    className={isActive
                      ? "bg-green-50 text-green-700 border-green-200"
                      : "bg-red-50 text-red-700 border-red-200"}
                  >
                    {isActive ? "Ativo" : "Inativo"}
                  </Badge>
                </div>
              )}
              {isDefault && (
                <Badge className="border-primary/50 text-primary">
                  Padrão
                </Badge>
              )}
            </div>
          </div>
          <CardTitle className="mt-4">{name}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {features.pixReceive && (
              <Badge key="pixreceive" className="font-normal">
                Recebimento PIX
              </Badge>
            )}
            {features.pixSend && (
              <Badge key="pixsend" className="font-normal">
                Envio PIX
              </Badge>
            )}
          </div>

        </CardContent>
        <CardFooter>
          <Button
            variant={isConfigured ? "outline" : "primary"}
            className="w-full"
            asChild
          >
            <a href={`/app/${organizationSlug}/integrations/gateways/${id}`}>
              {isConfigured ? "Gerenciar" : "Configurar"}
            </a>
          </Button>
        </CardFooter>
      </Card>

      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar alteração de status</DialogTitle>
            <DialogDescription>
              {pendingStatus
                ? "Tem certeza que deseja ativar este gateway de pagamento? Isso permitirá que ele processe transações."
                : "Tem certeza que deseja desativar este gateway de pagamento? Isso impedirá que ele processe novas transações."}
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-2 rounded-lg border p-3">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <p className="text-sm">
              {pendingStatus
                ? "Ao ativar, certifique-se de que as credenciais estão configuradas corretamente."
                : "Ao desativar, todas as transações pendentes serão concluídas, mas novas transações não serão aceitas."}
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
              disabled={isUpdating}
            >
              Cancelar
            </Button>
            <Button
              variant={pendingStatus ? "primary" : "error"}
              onClick={handleConfirmStatusChange}
              disabled={isUpdating}
            >
              {isUpdating ? "Atualizando..." : pendingStatus ? "Ativar" : "Desativar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
