"use client";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@ui/components/table";
import { Button } from "@ui/components/button";
import { useParams, useRouter } from "next/navigation";
// import { useToast } from "@ui/components/use-toast";
// import { apiClient } from "@saas/shared/lib/api-client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "@ui/components/skeleton";
import { Badge } from "@ui/components/badge";
import { AlertCircleIcon, ArrowLeftIcon, RefreshCwIcon, Loader2Icon, EyeIcon } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Sheet, SheetContent, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@ui/components/sheet";
// import { ScrollArea } from "@ui/components/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
// import { Pagination } from "@ui/components/pagination";
import { useState } from "react";
import { apiClient } from "@shared/lib/api-client";
import { useToast } from "@ui/hooks/use-toast";
import { Pagination } from "@shared/components/Pagination";
import { ScrollArea } from "@ui/components/scroll-area";

// Types
interface WebhookEvent {
  id: string;
  type: string;
  payload: any;
  transactionId: string | null;
  transaction: any;
  organizationId: string;
  createdAt: string;
}

interface WebhookDelivery {
  id: string;
  webhookId: string;
  eventId: string;
  status: "PENDING" | "SUCCESS" | "FAILED" | "RETRYING";
  attempts: number;
  maxAttempts: number;
  nextAttemptAt: string | null;
  lastAttemptAt: string | null;
  response: any;
  error: string | null;
  createdAt: string;
  updatedAt: string;
  webhook: {
    id: string;
    url: string;
    events: string[];
    isActive: boolean;
  };
}

export default function WebhookEventsPage() {
  const params = useParams<{ organizationSlug: string }>();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Pagination state
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [selectedEventType, setSelectedEventType] = useState<string | null>(null);

  // Fetch webhook events
  const { data: eventsData, isLoading: isLoadingEvents } = useQuery({
    queryKey: ["webhook-events", params.organizationSlug, page, limit, selectedEventType],
    queryFn: async () => {
      const queryParams = new URLSearchParams({
        organizationId: params.organizationSlug as string,
        limit: limit.toString(),
        offset: ((page - 1) * limit).toString(),
      });

      if (selectedEventType) {
        queryParams.append("type", selectedEventType);
      }

      const response = await apiClient.get(`/webhooks/events?${queryParams.toString()}`);
      return response.data;
    }
  });

  // Fetch event types for filtering
  const { data: eventTypes } = useQuery({
    queryKey: ["webhook-event-types"],
    queryFn: async () => {
      const response = await apiClient.get("/webhooks/event-types");
      return response.data.data;
    }
  });

  // Retry webhook delivery mutation
  const retryDeliveryMutation = useMutation({
    mutationFn: async (deliveryId: string) => {
      return apiClient.post(`/webhooks/deliveries/${deliveryId}/retry`);
    },
    onSuccess: (_, deliveryId) => {
      queryClient.invalidateQueries({ queryKey: ["webhook-deliveries"] });
      toast({
        title: "Reenvio agendado",
        description: "O webhook será reenviado em breve.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao reenviar webhook",
        description: error.response?.data?.message || "Ocorreu um erro ao reenviar o webhook.",
        variant: "destructive",
      });
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm:ss", { locale: ptBR });
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Sucesso</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Falha</Badge>;
      case "PENDING":
        return <Badge variant="outline" className="text-orange-600 border-orange-300 bg-orange-50">Pendente</Badge>;
      case "RETRYING":
        return <Badge variant="secondary" className="text-blue-600 bg-blue-100 hover:bg-blue-100">Reenvio</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Handle retry delivery
  const handleRetryDelivery = (deliveryId: string) => {
    retryDeliveryMutation.mutate(deliveryId);
  };

  // Handle filter change
  const handleFilterChange = (type: string | null) => {
    setSelectedEventType(type);
    setPage(1);
  };

  return (
    <>
      <PageHeader
        title="Eventos de Webhook"
        subtitle="Histórico de eventos e entregas de webhooks."
        actions={
          <Button variant="outline" onClick={() => router.push(`/app/${params.organizationSlug}/integrations/webhooks`)}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Voltar para Webhooks
          </Button>
        }
      />

      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Filtros</h3>
          <Button
            variant="outline"
            onClick={() => queryClient.invalidateQueries({ queryKey: ["webhook-events"] })}
          >
            <RefreshCwIcon className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedEventType === null ? "default" : "outline"}
            onClick={() => handleFilterChange(null)}
            className="text-sm"
          >
            Todos
          </Button>

          {eventTypes?.map((eventType: any) => (
            <Button
              key={eventType.type}
              variant={selectedEventType === eventType.type ? "default" : "outline"}
              onClick={() => handleFilterChange(eventType.type)}
              className="text-sm"
            >
              {eventType.type}
            </Button>
          ))}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Eventos</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isLoadingEvents ? (
            <div className="p-6 space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : eventsData?.data.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <AlertCircleIcon className="h-10 w-10 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Nenhum evento encontrado</h3>
              <p className="text-sm text-muted-foreground mt-2 max-w-md">
                Não há eventos de webhook registrados para esta organização.
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Evento</TableHead>
                    <TableHead>Transação</TableHead>
                    <TableHead>Data</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {eventsData?.data.map((event: WebhookEvent) => (
                    <TableRow key={event.id}>
                      <TableCell>
                        <Badge variant="secondary" className="mb-1">
                          {event.type}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          ID: {event.id}
                        </div>
                      </TableCell>
                      <TableCell>
                        {event.transaction ? (
                          <div>
                            <div className="font-medium">{event.transaction.referenceCode || event.transaction.externalId}</div>
                            <div className="text-xs text-muted-foreground">
                              {event.transaction.status} • R$ {(event.transaction.amount / 100).toFixed(2)}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">N/A</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {formatDate(event.createdAt)}
                      </TableCell>
                      <TableCell className="text-right">
                        <EventDetailsSheet event={event} />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex items-center justify-between p-4">
                <div className="text-sm text-muted-foreground">
                  Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, eventsData?.meta.total)} de {eventsData?.meta.total} eventos
                </div>

                <Pagination>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                  >
                    Anterior
                  </Button>

                  <div className="flex items-center mx-2">
                    <span className="text-sm">Página {page} de {Math.ceil(eventsData?.meta.total / limit)}</span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={page >= Math.ceil(eventsData?.meta.total / limit)}
                  >
                    Próxima
                  </Button>
                </Pagination>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </>
  );
}

// Event Details Sheet Component
function EventDetailsSheet({ event }: { event: WebhookEvent }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch webhook deliveries for this event
  const { data: deliveries, isLoading: isLoadingDeliveries } = useQuery({
    queryKey: ["webhook-deliveries", event.id],
    queryFn: async () => {
      const response = await apiClient.get(`/webhooks/events/${event.id}/deliveries`);
      return response.data.data as WebhookDelivery[];
    },
    enabled: false, // Only load when sheet is opened
  });

  // Retry webhook delivery mutation
  const retryDeliveryMutation = useMutation({
    mutationFn: async (deliveryId: string) => {
      return apiClient.post(`/webhooks/deliveries/${deliveryId}/retry`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["webhook-deliveries", event.id] });
      toast({
        title: "Reenvio agendado",
        description: "O webhook será reenviado em breve.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao reenviar webhook",
        description: error.response?.data?.message || "Ocorreu um erro ao reenviar o webhook.",
        variant: "error",
      });
    },
  });

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return format(new Date(dateString), "dd/MM/yyyy HH:mm:ss", { locale: ptBR });
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Sucesso</Badge>;
      case "FAILED":
        return <Badge variant="error">Falha</Badge>;
      case "PENDING":
        return <Badge variant="outline" className="text-orange-600 border-orange-300 bg-orange-50">Pendente</Badge>;
      case "RETRYING":
        return <Badge variant="secondary" className="text-blue-600 bg-blue-100 hover:bg-blue-100">Reenvio</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Sheet onOpenChange={(open) => {
      if (open) {
        queryClient.fetchQuery({ queryKey: ["webhook-deliveries", event.id] });
      }
    }}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm">
          <EyeIcon className="h-4 w-4 mr-1" />
          Detalhes
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-xl md:max-w-2xl">
        <SheetHeader>
          <SheetTitle>Detalhes do Evento</SheetTitle>
          <SheetDescription>
            Informações detalhadas sobre o evento e suas entregas.
          </SheetDescription>
        </SheetHeader>

        <Tabs defaultValue="event" className="mt-6">
          <TabsList className="mb-4">
            <TabsTrigger value="event">Evento</TabsTrigger>
            <TabsTrigger value="deliveries">Entregas ({deliveries?.length || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="event">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-1">Tipo de Evento</h3>
                <Badge variant="secondary">{event.type}</Badge>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">ID do Evento</h3>
                <p className="text-sm">{event.id}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Data de Criação</h3>
                <p className="text-sm">{formatDate(event.createdAt)}</p>
              </div>

              {event.transaction && (
                <div>
                  <h3 className="text-sm font-medium mb-1">Transação</h3>
                  <div className="text-sm">
                    <p><strong>ID:</strong> {event.transaction.id}</p>
                    <p><strong>Referência:</strong> {event.transaction.referenceCode || "N/A"}</p>
                    <p><strong>Status:</strong> {event.transaction.status}</p>
                    <p><strong>Valor:</strong> R$ {(event.transaction.amount / 100).toFixed(2)}</p>
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium mb-1">Payload</h3>
                <div className="bg-muted rounded-md p-3 overflow-auto max-h-60">
                  <pre className="text-xs whitespace-pre-wrap">
                    {JSON.stringify(event.payload, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="deliveries">
            {isLoadingDeliveries ? (
              <div className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            ) : deliveries?.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircleIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Nenhuma entrega encontrada para este evento.</p>
              </div>
            ) : (
              <ScrollArea className="h-[500px]">
                <div className="space-y-4">
                  {deliveries?.map((delivery) => (
                    <Card key={delivery.id} className="overflow-hidden">
                      <CardHeader className="p-4 pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusBadge(delivery.status)}
                            <span className="text-sm font-medium">
                              Tentativa {delivery.attempts} de {delivery.maxAttempts}
                            </span>
                          </div>

                          {(delivery.status === "FAILED" || delivery.status === "RETRYING") && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => retryDeliveryMutation.mutate(delivery.id)}
                              disabled={retryDeliveryMutation.isPending}
                            >
                              {retryDeliveryMutation.isPending && <Loader2Icon className="mr-2 h-3 w-3 animate-spin" />}
                              Reenviar
                            </Button>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-2">
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">URL:</span>
                            <span className="font-medium truncate max-w-[250px]">{delivery.webhook.url}</span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Criado em:</span>
                            <span>{formatDate(delivery.createdAt)}</span>
                          </div>

                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Última tentativa:</span>
                            <span>{formatDate(delivery.lastAttemptAt)}</span>
                          </div>

                          {delivery.nextAttemptAt && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Próxima tentativa:</span>
                              <span>{formatDate(delivery.nextAttemptAt)}</span>
                            </div>
                          )}

                          {delivery.error && (
                            <div className="mt-2">
                              <span className="text-muted-foreground">Erro:</span>
                              <div className="bg-red-50 text-red-800 p-2 rounded mt-1 text-xs">
                                {delivery.error}
                              </div>
                            </div>
                          )}

                          {delivery.response && (
                            <div className="mt-2">
                              <span className="text-muted-foreground">Resposta:</span>
                              <div className="bg-muted p-2 rounded mt-1 text-xs overflow-auto max-h-20">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(delivery.response, null, 2)}
                                </pre>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}
