import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent } from "@ui/components/card";
import { db } from "@repo/database";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { GatewayCardWrapper } from "./components/GatewayCardWrapper";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { AlertCircle, Info } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";

export const metadata = {
  title: "Integrações de Pagamento",
};

// Type for database payment gateway records
interface PaymentGatewayRecord {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  isDefault: boolean;
  isGlobal: boolean;
  adminConfigured: boolean;
  credentials: any;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
  priority?: number;
}

interface AcquirerInfo {
  id: string;
  name: string;
  description: string;
  logo: string;
  features: {
    pixReceive: boolean;
    pixSend: boolean;
  };
  highlight: boolean;
}

// Define acquirer information - only include gateways that have actual implementations
const availableAcquirers: AcquirerInfo[] = [
  {
    id: "flow2pay",
    name: "Flow2Pay",
    description: "Integração direta com Flow2Pay para PIX com performance otimizada e menor latência.",
    logo: "/images/gateways/flow2pay.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "mercadopago",
    name: "Mercado Pago",
    description: "Plataforma de pagamentos do Mercado Livre com suporte completo a Pix.",
    logo: "/images/gateways/mercado-pago.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "asaas",
    name: "Asaas",
    description: "Plataforma completa de pagamentos com foco em Pix e cobranças recorrentes.",
    logo: "/images/gateways/asaas.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "reflowpay",
    name: "ReflowPay",
    description: "Processador de pagamentos com foco em Pix e métodos alternativos.",
    logo: "/images/gateways/reflowpay.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "primepag",
    name: "PrimePag",
    description: "Adquirente de pagamentos completo para e-commerce e marketplaces.",
    logo: "/images/gateways/primepag.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "pixium",
    name: "Pixium",
    description: "Plataforma especializada em pagamentos Pix para empresas de todos os portes.",
    logo: "/images/gateways/pixium.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "transfeera",
    name: "Transfeera",
    description: "Solução completa para transferências e pagamentos via Pix.",
    logo: "/images/gateways/transfeera.png",
    features: {
      pixReceive: false,
      pixSend: true
    },
    highlight: false
  },
  {
    id: "pagarme",
    name: "Pagar.me",
    description: "Plataforma completa de pagamentos com suporte a Pix e diversos métodos de pagamento.",
    logo: "/images/gateways/pagarme.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  }
];

export default async function AcquirersPage({
  params,
}: {
  params: { organizationSlug: string };
}) {
  const { organizationSlug } = params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return <div>Organização não encontrada</div>;
  }

  try {
    // Get configured acquirers from database (organization specific) with proper instance handling
    const organizationAcquirers = await db.organization_gateway.findMany({
      where: {
        organizationId: organization.id
      },
      include: {
        payment_gateway: {
          select: {
            id: true,
            name: true,
            displayName: true,
            type: true,
            instanceNumber: true,
            isActive: true,
            isDefault: true,
            isGlobal: true,
            priority: true,
            canReceive: true,
            canSend: true,
            credentials: true // Include credentials to show client ID
          }
        }
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    // Get global acquirers configured by admin
    const globalAcquirers = await db.payment_gateway.findMany({
      where: {
        isGlobal: true,
        isActive: true
      },
      select: {
        id: true,
        name: true,
        displayName: true,
        type: true,
        instanceNumber: true,
        isActive: true,
        isDefault: true,
        isGlobal: true,
        priority: true,
        canReceive: true,
        canSend: true
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    // Process organization-specific gateways with instance information
    const processedOrgGateways = organizationAcquirers.map(orgGateway => ({
      ...orgGateway.payment_gateway,
      // Override with organization-specific settings
      isActive: orgGateway.isActive,
      isDefault: orgGateway.isDefault,
      priority: orgGateway.priority,
      relationId: orgGateway.id,
      isOrganizationSpecific: true,
      // Create a unique display name with instance info
      displayName: orgGateway.payment_gateway.displayName ||
        `${orgGateway.payment_gateway.name}${orgGateway.payment_gateway.instanceNumber ? ` (Instância ${orgGateway.payment_gateway.instanceNumber})` : ''}`
    }));

    // Process global gateways
    const processedGlobalGateways = globalAcquirers.map(gateway => ({
      ...gateway,
      isOrganizationSpecific: false,
      relationId: null,
      displayName: gateway.displayName ||
        `${gateway.name}${gateway.instanceNumber ? ` (Instância ${gateway.instanceNumber})` : ''}`,
      credentials: null // Global gateways don't have credentials in this context
    }));

    // Combine both lists
    const allConfiguredGateways = [...processedOrgGateways, ...processedGlobalGateways];

    // Create a map for quick lookup by gateway type and instance
    const configuredGatewayMap = new Map<string, any>();
    allConfiguredGateways.forEach(gateway => {
      const key = `${gateway.type.toLowerCase()}_${gateway.instanceNumber || 1}`;
      configuredGatewayMap.set(key, gateway);
    });

    // Group gateways by type for counting instances
    const gatewayTypeGroups = allConfiguredGateways.reduce((acc: Record<string, any[]>, gateway) => {
      const type = gateway.type.toLowerCase();
      if (!acc[type]) acc[type] = [];
      acc[type].push(gateway);
      return acc;
    }, {});

    // Separate gateways into categories
    const activeConfiguredGateways = allConfiguredGateways.filter(gateway => gateway.isActive);
    const inactiveConfiguredGateways = allConfiguredGateways.filter(gateway => !gateway.isActive);

    // Find unconfigured gateway types (types that have no instances configured)
    const configuredTypes = new Set(allConfiguredGateways.map(g => g.type.toLowerCase()));
    const unconfiguredAcquirers = availableAcquirers.filter(acquirer =>
      !configuredTypes.has(acquirer.id.toLowerCase())
    );

    // Check if there's a default gateway
    const hasDefaultGateway = allConfiguredGateways.some(gateway => gateway.isDefault && gateway.isActive);
    const activeGatewaysCount = activeConfiguredGateways.length;
    const inactiveGatewaysCount = inactiveConfiguredGateways.length;

    // Count global gateways for display
    const globalGatewaysCount = processedGlobalGateways.length;

    // Total configured gateways (including all instances)
    const totalConfiguredCount = allConfiguredGateways.length;

    return (
      <div className="space-y-6">
        <PageHeader
          title="Integrações de Pagamento"
          subtitle="Configure os adquirentes de pagamento para receber e enviar pagamentos via Pix"
        />

        {!hasDefaultGateway && activeGatewaysCount > 0 && (
          <Alert className="bg-amber-50 border-amber-200">
            <AlertCircle className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">Nenhum gateway padrão definido</AlertTitle>
            <AlertDescription className="text-amber-700">
              Você tem gateways ativos, mas nenhum está definido como padrão. Defina um gateway padrão para garantir o processamento correto das transações.
            </AlertDescription>
          </Alert>
        )}

        {activeGatewaysCount === 0 && globalGatewaysCount === 0 && (
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">Configure um gateway de pagamento</AlertTitle>
            <AlertDescription className="text-blue-700">
              Para processar pagamentos, você precisa configurar e ativar pelo menos um gateway de pagamento.
            </AlertDescription>
          </Alert>
        )}

        {/* Seção de Gateways Globais */}
        {globalGatewaysCount > 0 && (
          <div className="mb-8">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-medium mb-4">Gateways Globais Disponíveis</h3>
                <p className="text-muted-foreground mb-6">
                  Os seguintes gateways foram configurados pelo administrador e estão disponíveis para sua organização.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {processedGlobalGateways.map((gateway) => {
                    // Find acquirer info from available acquirers
                    const acquirerInfo = availableAcquirers.find(a =>
                      a.id.toLowerCase() === gateway.type.toLowerCase()
                    ) || {
                      id: gateway.type.toLowerCase(),
                      name: gateway.name,
                      description: "Gateway de pagamento configurado pelo administrador",
                      logo: "/images/gateways/default.png",
                      features: {
                        pixReceive: gateway.canReceive || true,
                        pixSend: gateway.canSend || false
                      },
                      highlight: false
                    };

                    // Count instances of this gateway type
                    const instanceCount = gatewayTypeGroups[gateway.type.toLowerCase()]?.length || 1;

                    return (
                      <Card key={gateway.id} className="border-2 border-blue-100 bg-blue-50">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center">
                              <div className="h-10 w-10 rounded-md bg-white p-1 mr-3">
                                <img
                                  src={acquirerInfo.logo}
                                  alt={acquirerInfo.name}
                                  className="h-full w-full object-contain"
                                />
                              </div>
                              <div>
                                <h3 className="font-medium">{gateway.displayName}</h3>
                                <p className="text-xs text-muted-foreground">Configurado pelo administrador</p>
                              </div>
                            </div>
                            {gateway.isDefault && (
                              <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                Padrão
                              </div>
                            )}
                          </div>

                          <p className="text-sm text-muted-foreground mb-4">{acquirerInfo.description}</p>

                          <div className="flex gap-2 mb-4">
                            {gateway.canReceive && (
                              <div className="bg-green-50 border border-green-200 text-green-700 text-xs px-2 py-1 rounded-full">
                                Recebimento PIX
                              </div>
                            )}
                            {gateway.canSend && (
                              <div className="bg-blue-50 border border-blue-200 text-blue-700 text-xs px-2 py-1 rounded-full">
                                Envio PIX
                              </div>
                            )}
                          </div>


                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="all">Todos ({totalConfiguredCount + unconfiguredAcquirers.length})</TabsTrigger>
            <TabsTrigger value="active">Ativos ({activeGatewaysCount})</TabsTrigger>
            <TabsTrigger value="inactive">Inativos ({inactiveGatewaysCount})</TabsTrigger>
            <TabsTrigger value="available">Disponíveis ({unconfiguredAcquirers.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            {activeConfiguredGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Ativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {activeConfiguredGateways.map((gateway) => {
                     const acquirerInfo = availableAcquirers.find(a => a.id.toLowerCase() === gateway.type.toLowerCase());
                     return (
                       <GatewayCardWrapper
                         key={gateway.id}
                         id={gateway.type.toLowerCase()}
                         name={acquirerInfo?.name || gateway.name}
                         displayName={gateway.displayName}
                         description={acquirerInfo?.description || "Gateway de pagamento"}
                         logo={acquirerInfo?.logo || "/images/gateways/default.png"}
                         features={{
                           pixReceive: gateway.canReceive || true,
                           pixSend: gateway.canSend || false
                         }}
                         highlight={acquirerInfo?.highlight || false}
                         isConfigured={true}
                         isActive={true}
                         isDefault={gateway.isDefault}
                         priority={gateway.priority || 999}
                         instanceNumber={gateway.instanceNumber}
                         organizationSlug={organizationSlug}
                         organizationId={organization.id}
                         credentials={gateway.credentials}
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {inactiveConfiguredGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Inativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {inactiveConfiguredGateways.map((gateway) => {
                     const acquirerInfo = availableAcquirers.find(a => a.id.toLowerCase() === gateway.type.toLowerCase());
                     return (
                       <GatewayCardWrapper
                         key={gateway.id}
                         id={gateway.type.toLowerCase()}
                         name={acquirerInfo?.name || gateway.name}
                         displayName={gateway.displayName}
                         description={acquirerInfo?.description || "Gateway de pagamento"}
                         logo={acquirerInfo?.logo || "/images/gateways/default.png"}
                         features={{
                           pixReceive: gateway.canReceive || true,
                           pixSend: gateway.canSend || false
                         }}
                         highlight={acquirerInfo?.highlight || false}
                         isConfigured={true}
                         isActive={false}
                         isDefault={gateway.isDefault}
                         priority={gateway.priority || 999}
                         instanceNumber={gateway.instanceNumber}
                         organizationSlug={organizationSlug}
                         organizationId={organization.id}
                         credentials={gateway.credentials}
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {unconfiguredAcquirers.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Disponíveis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {unconfiguredAcquirers.map((acquirer) => (
                    <GatewayCardWrapper
                      key={acquirer.id}
                      {...acquirer}
                      isConfigured={false}
                      isActive={false}
                      isDefault={false}
                      priority={999}
                      organizationSlug={organizationSlug}
                      organizationId={organization.id}
                    />
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="active">
            {activeConfiguredGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeConfiguredGateways.map((gateway) => {
                   const acquirerInfo = availableAcquirers.find(a => a.id.toLowerCase() === gateway.type.toLowerCase());
                   return (
                     <GatewayCardWrapper
                       key={gateway.id}
                       id={gateway.type.toLowerCase()}
                       name={acquirerInfo?.name || gateway.name}
                       displayName={gateway.displayName}
                       description={acquirerInfo?.description || "Gateway de pagamento"}
                       logo={acquirerInfo?.logo || "/images/gateways/default.png"}
                       features={{
                         pixReceive: gateway.canReceive || true,
                         pixSend: gateway.canSend || false
                       }}
                       highlight={acquirerInfo?.highlight || false}
                       isConfigured={true}
                       isActive={true}
                       isDefault={gateway.isDefault}
                       priority={gateway.priority || 999}
                       instanceNumber={gateway.instanceNumber}
                       organizationSlug={organizationSlug}
                       organizationId={organization.id}
                       credentials={gateway.credentials}
                    />
                  );
                })}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Nenhum gateway ativo configurado.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="inactive">
            {inactiveConfiguredGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {inactiveConfiguredGateways.map((gateway) => {
                   const acquirerInfo = availableAcquirers.find(a => a.id.toLowerCase() === gateway.type.toLowerCase());
                   return (
                     <GatewayCardWrapper
                       key={gateway.id}
                       id={gateway.type.toLowerCase()}
                       name={acquirerInfo?.name || gateway.name}
                       displayName={gateway.displayName}
                       description={acquirerInfo?.description || "Gateway de pagamento"}
                       logo={acquirerInfo?.logo || "/images/gateways/default.png"}
                       features={{
                         pixReceive: gateway.canReceive || true,
                         pixSend: gateway.canSend || false
                       }}
                       highlight={acquirerInfo?.highlight || false}
                       isConfigured={true}
                       isActive={false}
                       isDefault={gateway.isDefault}
                       priority={gateway.priority || 999}
                       instanceNumber={gateway.instanceNumber}
                       organizationSlug={organizationSlug}
                       organizationId={organization.id}
                       credentials={gateway.credentials}
                    />
                  );
                })}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Nenhum gateway inativo configurado.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="available">
            {unconfiguredAcquirers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {unconfiguredAcquirers.map((acquirer) => (
                  <GatewayCardWrapper
                    key={acquirer.id}
                    {...acquirer}
                    isConfigured={false}
                    isActive={false}
                    isDefault={false}
                    priority={999}
                    organizationSlug={organizationSlug}
                    organizationId={organization.id}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Todos os gateways disponíveis já foram configurados.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching payment acquirers:", error);
    return (
      <div className="space-y-6">
        <PageHeader
          title="Integrações de Pagamento"
          subtitle="Configure os adquirentes de pagamento para receber e enviar pagamentos via Pix"
        />
        <Card className="p-6">
          <div className="flex h-64 items-center justify-center text-foreground/60">
            Erro ao carregar as integrações. Por favor, tente novamente mais tarde.
          </div>
        </Card>
      </div>
    );
  }
}
