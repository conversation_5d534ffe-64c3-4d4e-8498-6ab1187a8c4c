"use client";

import { useState } from "react";
import { updateGatewayStatus, updateGatewayDefault } from "../actions";
import { useToast } from "@ui/hooks/use-toast";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@ui/components/card";
import Image from "next/image";
import { Switch } from "@ui/components/switch";
// Badge component removed as we're using custom styled divs
import { Button } from "@ui/components/button";
import { useRouter } from "next/navigation";
import { Star, Cog } from "lucide-react";

interface GatewayCardWrapperProps {
  id: string;
  name: string;
  description: string;
  logo: string;
  features: {
    pixReceive: boolean;
    pixSend: boolean;
  };
  highlight: boolean;
  isConfigured: boolean;
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  organizationSlug: string;
  organizationId: string;
  isGlobal?: boolean;
  adminConfigured?: boolean;
  adminMode?: boolean;
  instanceCount?: number;
  displayName?: string;
  instanceNumber?: number;
  credentials?: any; // Adicionando credenciais para extrair client ID
}

// Função para extrair o client ID das credenciais
function getClientIdFromCredentials(credentials: any): string | null {
  if (!credentials) return null;

  // Priorizar clientId, depois apiKey, depois outros identificadores
  return credentials.clientId ||
         credentials.apiKey ||
         credentials.client_id ||
         credentials.api_key ||
         credentials.companyId ||
         credentials.company_id ||
         null;
}

// Função para formatar o client ID para exibição
function formatClientId(clientId: string): string {
  if (!clientId) return 'N/A';

  // Se for muito longo, truncar e mostrar apenas os primeiros caracteres
  if (clientId.length > 20) {
    return `${clientId.substring(0, 12)}...`;
  }

  return clientId;
}

export function GatewayCardWrapper(props: GatewayCardWrapperProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isUpdating, setIsUpdating] = useState(false);

  // Extrair o client ID das credenciais
  const clientId = props.credentials ? getClientIdFromCredentials(props.credentials) : null;
  const displayClientId = formatClientId(clientId || '');

  const handleStatusChange = async (checked: boolean) => {
    setIsUpdating(true);
    try {
      await updateGatewayStatus(props.organizationId, props.id, checked);
      toast({
        title: checked ? "Gateway ativado" : "Gateway desativado",
        description: checked
          ? `${props.name} foi ativado com sucesso.`
          : `${props.name} foi desativado com sucesso.`,
      });
      router.refresh();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status do gateway.",
        variant: "error",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSetDefault = async () => {
    setIsUpdating(true);
    try {
      await updateGatewayDefault(props.organizationId, props.id);
      toast({
        title: "Gateway padrão definido",
        description: `${props.name} foi definido como gateway padrão.`,
      });
      router.refresh();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível definir o gateway padrão.",
        variant: "error",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const navigateToConfig = () => {
    router.push(`/app/${props.organizationSlug}/integrations/gateways/${props.id.toLowerCase()}`);
  };

  return (
    <Card className={`${props.isDefault ? "border-primary" : ""} ${props.isGlobal ? "border-blue-300 bg-blue-50/30" : ""}`}>
      <CardHeader className="space-y-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
             <div >
             <Image className="h-8 w-auto" src={props.logo} alt={props.displayName || props.name} width={120} height={40} />
             <div className="flex items-center gap-2 mt-2">
               <h3>{props.displayName || props.name}</h3>
               {props.isGlobal && (
                 <div className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-md">
                   Global
                 </div>
               )}
               {props.adminConfigured && (
                 <div className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-md">
                   Admin
                 </div>
               )}
               {props.instanceNumber && (
                 <div className="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-md">
                   Instância #{props.instanceNumber}
                 </div>
               )}
               {props.adminMode && props.instanceCount && props.instanceCount > 1 && (
                 <div className="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-md">
                   {props.instanceCount} instâncias
                 </div>
               )}
             </div>
             </div>

          </div>
          <div className="flex items-center gap-2">

          {props.isDefault && (
              <div className="ml-2 bg-primary/10 text-primary text-xs px-2 py-1 rounded-md flex items-center">
                <Star className="h-3 w-3 mr-1" /> Padrão
              </div>
            )}
            {props.isConfigured && !props.isGlobal && (
              <div className="flex items-center gap-2 ml-3">
                <span className="text-xs text-muted-foreground">
                  {props.isActive ? "Ativo" : "Inativo"}
                </span>
                <Switch
                  checked={props.isActive}
                  onCheckedChange={handleStatusChange}
                  disabled={isUpdating}
                />
              </div>
            )}
            {props.isGlobal && (
              <div className="flex items-center gap-2 ml-3">
                <span className="text-xs text-muted-foreground">
                  {props.isActive ? "Ativo" : "Inativo"}
                </span>
              </div>
            )}

          </div>
        </div>
        {/* Exibir o client ID abaixo do nome */}
        <div className="text-sm text-muted-foreground">
          {clientId ? displayClientId : props.id.toUpperCase()}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">{props.description}</p>
        <div className="mt-4 flex flex-wrap gap-2">
          {props.features.pixReceive && (
            <div className="text-xs border rounded-md px-2 py-1 bg-green-50 border-green-200 text-green-700">RECEBIMENTO PIX</div>
          )}
          {props.features.pixSend && (
            <div className="text-xs border rounded-md px-2 py-1 bg-blue-50 border-blue-200 text-blue-700 font-medium">ENVIO PIX</div>
          )}


        </div>
      </CardContent>
      <CardFooter>
        {props.isGlobal ? (
          // Gateway global - não pode ser gerenciado pela organização
          <div className="w-full text-center text-sm text-muted-foreground">
            Este gateway é gerenciado pelo administrador do sistema
          </div>
        ) : props.isConfigured ? (
          // Gateway configurado da organização
          <div className="flex w-full gap-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={navigateToConfig}
              disabled={isUpdating}
            >
              <Cog className="h-4 w-4 mr-2" />
              Configurar gateway
            </Button>
            {!props.isDefault && props.isActive && (
              <Button
                variant="secondary"
                className="flex-1"
                onClick={handleSetDefault}
                disabled={isUpdating}
              >
                Definir como Padrão
              </Button>
            )}
          </div>
        ) : (
          // Gateway não configurado
          <Button
            className="w-full"
            onClick={navigateToConfig}
            disabled={isUpdating}
          >
            <Cog className="h-4 w-4 mr-2" />
            Configurar gateway
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
