import { PageHeader } from "@saas/shared/components/PageHeader";
import { getTranslations } from "next-intl/server";
import { OrganizationTaxesView } from "@saas/organizations/components/OrganizationTaxesView";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("organizations.taxes.title") || "Taxas",
  };
}

interface TaxesPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

export default async function TaxesPage({ params }: TaxesPageProps) {
  const { organizationSlug } = await params;
  const t = await getTranslations();

  // Get the organization to ensure user has access and get the ID
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <>
      <PageHeader
        title={t("organizations.taxes.title") || "Taxas"}
        subtitle={t("organizations.taxes.subtitle") || "Visualize as taxas aplicadas às suas transações PIX"}
      />

      <div className="space-y-6">
        <div className="text-sm text-muted-foreground">
          {t("organizations.taxes.description") || "Aqui você pode visualizar todas as taxas e tarifas aplicadas às transações da sua organização."}
        </div>

        <OrganizationTaxesView organizationId={organization.id} />
      </div>
    </>
  );
}
