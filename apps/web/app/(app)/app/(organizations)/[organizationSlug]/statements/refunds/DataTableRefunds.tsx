"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Button } from "@ui/components/button";
import { Eye } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Spinner } from "@shared/components/Spinner";
import { Pagination } from "@shared/components/Pagination";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useTransactions, type Transaction } from "@saas/transactions/hooks/use-transactions";

// Estendendo a interface Transaction para campos adicionais que podem vir do backend
interface RefundTransaction extends Transaction {
  description?: string;
  reason?: string;
  originalTransactionId?: string;
}

export function DataTableRefunds() {
  const [selectedRefund, setSelectedRefund] = useState<RefundTransaction | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchId, setSearchId] = useState("");
  const [searchTransaction, setSearchTransaction] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const { activeOrganization } = useActiveOrganization();

  // Convert UI status filter to API status
  const getApiStatus = (): string | undefined => {
    if (statusFilter === "all") return undefined;
    return statusFilter;
  };

  const { data, isLoading, error } = useTransactions({
    status: 'REFUNDED',
    page,
    limit: pageSize,
    searchId: searchId || undefined,
    searchClient: searchTransaction || undefined,
    enabled: !!activeOrganization?.id
  });

  // Log para debug
  useEffect(() => {
    console.log("Dados de estorno retornados:", data);
  }, [data]);

  const handleRowClick = (refund: RefundTransaction) => {
    setSelectedRefund(refund);
    setIsDetailsOpen(true);
  };

  const statusBadge = (status: string) => {
    switch (status) {
      case "APPROVED":
        return (
          <span className={cn(badge({ className: "bg-purple-500 hover:bg-purple-600 text-white" }))}>
            Estorno Aprovado
          </span>
        );
      case "REFUNDED":
        return (
          <span className={cn(badge({ status: "refunded" }))}>
            Estornado
          </span>
        );
      case "PENDING":
        return (
          <span className={cn(badge({ status: "warning" }))}>
            Pendente
          </span>
        );
      case "PROCESSING":
        return (
          <span className={cn(badge({ status: "info" }))}>
            Processando
          </span>
        );
      case "REJECTED":
        return (
          <span className={cn(badge({ status: "error" }))}>
            Rejeitado
          </span>
        );
      default:
        return (
          <span className={cn(badge())}>
            {status}
          </span>
        );
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy", { locale: ptBR });
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <p className="text-red-500">Erro ao carregar estornos: {error.message}</p>
      </div>
    );
  }

  const refunds = data?.data as RefundTransaction[] || [];
  const pagination = data?.pagination;

  return (
    <>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
            <Input
              placeholder="Buscar por ID"
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
            />
            <Input
              placeholder="Buscar por transação"
              value={searchTransaction}
              onChange={(e) => setSearchTransaction(e.target.value)}
            />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="APPROVED">Aprovado</SelectItem>
                <SelectItem value="PENDING">Pendente</SelectItem>
                <SelectItem value="PROCESSING">Processando</SelectItem>
                <SelectItem value="REJECTED">Rejeitado</SelectItem>
                <SelectItem value="REFUNDED">Estornado</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-800 text-left">
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  ID
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Data
                </th>

                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Motivo
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm text-left">
                  Valor
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Status
                </th>
                <th className="pb-4 font-medium text-muted-foreground text-sm">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody>
              {refunds.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-4 text-center text-muted-foreground">
                    Nenhum estorno encontrado
                  </td>
                </tr>
              ) : (
                refunds.map((refund) => (
                  <tr
                    key={refund.id}
                    className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors relative group"
                    onClick={() => handleRowClick(refund)}
                  >
                    <td className="py-4 text-sm font-medium">{refund.id}</td>
                    <td className="py-4 text-sm">{formatDate(refund.createdAt)}</td>
                    <td className="py-4 text-sm">{refund.reason || refund.description || '-'}</td>
                    <td className="py-4 text-sm">{formatCurrency(refund.amount)}</td>
                    <td className="py-4 text-sm">{statusBadge(refund.status)}</td>
                    <td className="py-4 text-sm">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-3 text-xs hover:bg-emerald-500/10 hover:text-emerald-500 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRowClick(refund);
                        }}
                      >
                        <Eye className="h-3.5 w-3.5" />
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {pagination && pagination.total > 0 && (
          <div className="mt-6 flex justify-center">
            <Pagination
              currentPage={page}
              totalPages={pagination.pages || Math.ceil(pagination.total / pagination.limit) || 1}
              onPageChange={setPage}
            />
          </div>
        )}

        {/* Debug info */}
        <div className="text-xs text-muted-foreground text-center mt-2">
          {pagination && (
            <span>Total: {pagination.total} | Página: {pagination.page} de {pagination.pages} | Itens por página: {pagination.limit}</span>
          )}
        </div>
      </div>

      {/* Details Sheet */}
      <Sheet open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Detalhes do Estorno</SheetTitle>
          </SheetHeader>

          {selectedRefund && (
            <div className="mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">ID</p>
                  <p className="font-medium">{selectedRefund.id}</p>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <div className="mt-1">{statusBadge(selectedRefund.status)}</div>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground">Valor</p>
                  <p className="font-medium">{formatCurrency(selectedRefund.amount)}</p>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground">Data</p>
                  <p className="font-medium">{formatDate(selectedRefund.createdAt)}</p>
                </div>

                <div className="col-span-2">
                  <p className="text-sm text-muted-foreground">Transação Original</p>
                  <p className="font-medium">{selectedRefund.originalTransactionId || selectedRefund.referenceCode || 'N/A'}</p>
                </div>

                <div className="col-span-2">
                  <p className="text-sm text-muted-foreground">Motivo</p>
                  <p className="font-medium">{selectedRefund.reason || selectedRefund.description || 'Não informado'}</p>
                </div>

                <div className="col-span-2">
                  <p className="text-sm text-muted-foreground">Cliente</p>
                  <p className="font-medium">{selectedRefund.customerName}</p>
                  <p className="text-sm text-muted-foreground">{selectedRefund.customerEmail}</p>
                </div>

                {selectedRefund.paymentAt && (
                  <div className="col-span-2">
                    <p className="text-sm text-muted-foreground">Concluído em</p>
                    <p className="font-medium">{formatDate(selectedRefund.paymentAt)}</p>
                  </div>
                )}

                {selectedRefund.externalId && (
                  <div className="col-span-2">
                    <p className="text-sm text-muted-foreground">ID Externo</p>
                    <p className="font-medium">{selectedRefund.externalId}</p>
                  </div>
                )}
              </div>

              {selectedRefund.status === "PENDING" && (
                <div className="mt-6 flex space-x-2">
                  <Button
                    variant="outline"
                    className="w-full bg-green-500 hover:bg-green-600 text-white"
                    onClick={async () => {
                      try {
                        const response = await fetch(`/api/transactions/process-refund/${selectedRefund.id}`, {
                          method: "POST",
                          headers: {
                            "Content-Type": "application/json",
                          },
                          body: JSON.stringify({
                            action: "approve",
                            organizationId: activeOrganization?.id,
                          }),
                        });

                        if (!response.ok) {
                          throw new Error("Failed to approve refund");
                        }

                        // Close modal and refresh data
                        setIsDetailsOpen(false);
                        // Reload data
                      } catch (error) {
                        console.error("Error approving refund:", error);
                        // Show error message
                      }
                    }}
                  >
                    Aprovar
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full bg-red-500 hover:bg-red-600 text-white"
                    onClick={async () => {
                      try {
                        const response = await fetch(`/api/transactions/process-refund/${selectedRefund.id}`, {
                          method: "POST",
                          headers: {
                            "Content-Type": "application/json",
                          },
                          body: JSON.stringify({
                            action: "reject",
                            organizationId: activeOrganization?.id,
                          }),
                        });

                        if (!response.ok) {
                          throw new Error("Failed to reject refund");
                        }

                        // Close modal and refresh data
                        setIsDetailsOpen(false);
                        // Reload data
                      } catch (error) {
                        console.error("Error rejecting refund:", error);
                        // Show error message
                      }
                    }}
                  >
                    Rejeitar
                  </Button>
                </div>
              )}
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
}


