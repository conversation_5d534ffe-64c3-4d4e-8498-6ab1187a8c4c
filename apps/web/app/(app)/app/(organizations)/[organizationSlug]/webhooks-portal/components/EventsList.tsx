"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@ui/components/table";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { SearchIcon, RefreshCwIcon } from "lucide-react";
import { useState } from "react";
import { EventDetails } from "./EventDetails";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@ui/components/sheet";
import { Pagination } from "../../../../../../../modules/shared/components/Pagination";

interface Event {
  id: string;
  eventType: string;
  payload: any;
  timestamp: string;
  channels: string[];
}

interface EventsListProps {
  organizationId: string;
}

export function EventsList({ organizationId }: EventsListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [eventTypeFilter, setEventTypeFilter] = useState("");
  const [page, setPage] = useState(1);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  const { data: eventsData, isLoading } = useQuery({
    queryKey: ["webhook-events", organizationId, page, eventTypeFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        organizationId,
        limit: "20",
        page: page.toString()
      });

      if (eventTypeFilter && eventTypeFilter !== "all") {
        params.append("eventType", eventTypeFilter);
      }

      const response = await fetch(`/api/webhooks/portal/events?${params}`);
      const data = await response.json();
      return data.data;
    }
  });

  const filteredEvents = eventsData?.messages?.filter((event: Event) =>
    event.payload?.data?.id?.includes(searchTerm) ||
    event.payload?.data?.referenceCode?.includes(searchTerm) ||
    event.eventType.includes(searchTerm)
  ) || [];

  const openEventDetails = (event: Event) => {
    setSelectedEvent(event);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleEventTypeFilterChange = (value: string) => {
    setEventTypeFilter(value);
    setPage(1); // Reset to first page when filter changes
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when search changes
  };

  // Calculate total pages based on total count from API
  const totalPages = Math.max(1, eventsData?.totalPages || 1);

  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle className="mb-4">Eventos de Webhook Recentes</CardTitle>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por ID ou referência..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={eventTypeFilter} onValueChange={handleEventTypeFilterChange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrar por tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                <SelectItem value="PixIn.Processing">PIX In - Processando</SelectItem>
                <SelectItem value="PixIn.Confirmation">PIX In - Confirmado</SelectItem>
                <SelectItem value="PixOut.Processing">PIX Out - Processando</SelectItem>
                <SelectItem value="PixOut.Confirmation">PIX Out - Confirmado</SelectItem>
                <SelectItem value="PixOut.Failure">PIX Out - Falha</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCwIcon className="h-6 w-6 animate-spin" />
              <span className="ml-2">Carregando eventos...</span>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Transação</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Endpoint</TableHead>
                    <TableHead>Data</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvents.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        Nenhum evento encontrado
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredEvents.map((event: Event) => (
                      <TableRow
                        key={event.id}
                        className="cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => openEventDetails(event)}
                      >
                        <TableCell>
                          <Badge>
                            {event.eventType}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {event.payload?.data?.referenceCode || event.payload?.data?.id || "N/A"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {event.payload?.data?.amount ?
                                `R$ ${(event.payload.data.amount).toFixed(2)}` :
                                "Valor não disponível"
                              }
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge>
                            {event.payload?.data?.status || "N/A"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div className="font-medium text-muted-foreground">
                              {event.channels?.length || 0} endpoint(s)
                            </div>
                            <div className="text-muted-foreground">
                              Ver detalhes →
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {format(new Date(event.timestamp), "dd/MM/yyyy HH:mm", { locale: ptBR })}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Paginação */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-muted-foreground">
                    Página {page} de {totalPages} • {filteredEvents.length} eventos
                  </div>
                  <Pagination
                    currentPage={page}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Sheet para detalhes do evento */}
      <Sheet open={!!selectedEvent} onOpenChange={(open) => !open && setSelectedEvent(null)}>
        <SheetContent className="w-full sm:max-w-[500px] md:max-w-[600px] lg:max-w-[700px]">
          <SheetHeader>
            <SheetTitle>Detalhes do Evento</SheetTitle>
            <SheetDescription>
              Informações detalhadas sobre o evento e suas entregas.
            </SheetDescription>
          </SheetHeader>
          {selectedEvent ? (
            <EventDetails
              messageId={selectedEvent.id}
              organizationId={organizationId}
            />
          ) : null}
        </SheetContent>
      </Sheet>
    </div>
  );
}
