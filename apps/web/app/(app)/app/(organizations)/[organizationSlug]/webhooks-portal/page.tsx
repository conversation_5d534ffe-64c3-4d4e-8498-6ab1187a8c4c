"use client";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { useParams } from "next/navigation";
import { EventsList } from "./components/EventsList";

export default function WebhooksPortalPage() {
  const params = useParams<{ organizationSlug: string }>();

  return (
    <>
      <PageHeader
        title="Portal de Webhooks"
        subtitle="Visualize e monitore todos os eventos de webhook da sua organização"
      />

            <div className="grid gap-6">
        {/* Lista de Eventos Recentes */}
        <EventsList organizationId={params.organizationSlug as string} />
      </div>
    </>
  );
}
