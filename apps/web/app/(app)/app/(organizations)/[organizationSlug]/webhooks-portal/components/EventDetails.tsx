"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { RefreshCwIcon, CheckCircleIcon, XCircleIcon, ClockIcon, AlertCircleIcon } from "lucide-react";

import { useToast } from "@ui/hooks/use-toast";

interface EventDetailsProps {
  messageId: string;
  organizationId: string;
}

export function EventDetails({ messageId, organizationId }: EventDetailsProps) {
  const { toast } = useToast();

  const { data: eventData, isLoading } = useQuery({
    queryKey: ["webhook-event-details", messageId],
    queryFn: async () => {
      const response = await fetch(`/api/webhooks/portal/events/${messageId}`);
      const data = await response.json();
      return data.data;
    }
  });

  // Helper function to determine attempt status
  const getAttemptStatus = (attempt: any) => {
    const isSuccess = attempt.responseStatusCode >= 200 && attempt.responseStatusCode < 300;
    const isPending = attempt.status === 0 && !attempt.responseStatusCode;
    const isFailed = attempt.status === 2 || (attempt.responseStatusCode >= 400);
    const isRetrying = attempt.status === 3;

    if (isSuccess) return { status: 'success', label: 'Sucesso', icon: 'success' };
    if (isPending) return { status: 'pending', label: 'Pendente', icon: 'pending' };
    if (isFailed) return { status: 'failed', label: 'Falha', icon: 'failed' };
    if (isRetrying) return { status: 'retrying', label: 'Reenviando', icon: 'retrying' };
    return { status: 'pending', label: 'Pendente', icon: 'pending' };
  };

  const resendWebhook = async (endpointId: string) => {
    try {
      await fetch(`/api/webhooks/portal/events/${messageId}/resend`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ endpointId })
      });

      toast({
        title: "Webhook reenviado",
        description: "O webhook foi agendado para reenvio.",
      });
    } catch (error: any) {
      toast({
        title: "Erro ao reenviar",
        description: "Não foi possível reenviar o webhook.",
        variant: "error",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCwIcon className="h-6 w-6 animate-spin" />
        <span className="ml-2">Carregando detalhes...</span>
      </div>
    );
  }

  if (!eventData) {
    return (
      <div className="flex items-center justify-center py-8 text-muted-foreground">
        <AlertCircleIcon className="h-6 w-6 mr-2" />
        Nenhum dado encontrado
      </div>
    );
  }

  const { message, attempts } = eventData;

  return (
    <div className="mt-6 h-full overflow-y-auto">
      <Tabs defaultValue="event" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="event">Evento</TabsTrigger>
          <TabsTrigger value="attempts">Tentativas ({attempts?.length || 0})</TabsTrigger>
        </TabsList>

        <TabsContent value="event" className="space-y-4">
          <div className="grid gap-4">
            <div>
              <h3 className="text-sm font-medium mb-1">Tipo de Evento</h3>
              <Badge>{message.eventType}</Badge>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">ID da Mensagem</h3>
              <p className="text-sm font-mono bg-muted p-2 rounded">{message.id}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-1">Data de Criação</h3>
              <p className="text-sm">
                {format(new Date(message.timestamp), "dd/MM/yyyy HH:mm:ss", { locale: ptBR })}
              </p>
            </div>

            {message.payload?.data && (
              <div>
                <h3 className="text-sm font-medium mb-1">Dados da Transação</h3>
                <div className="text-sm space-y-1">
                  <p><strong>ID:</strong> {message.payload.data.id || "N/A"}</p>
                  <p><strong>Referência:</strong> {message.payload.data.referenceCode || "N/A"}</p>
                  <p><strong>Status:</strong> {message.payload.data.status || "N/A"}</p>
                  <p><strong>Valor:</strong> {message.payload.data.amount ?
                    `R$ ${(message.payload.data.amount).toFixed(2)}` : "N/A"}</p>
                  <p><strong>Cliente:</strong> {message.payload.data.customerName || "N/A"}</p>
                  <p><strong>Email:</strong> {message.payload.data.customerEmail || "N/A"}</p>
                </div>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium mb-1">Payload Completo</h3>
              <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-96">
                {JSON.stringify(message.payload, null, 2)}
              </pre>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="attempts" className="space-y-4">
          {attempts && attempts.length > 0 ? (
            attempts.map((attempt: any) => (
              <Card key={attempt.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {(() => {
                        const attemptStatus = getAttemptStatus(attempt);

                        switch (attemptStatus.icon) {
                          case 'success':
                            return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
                          case 'pending':
                            return <ClockIcon className="h-4 w-4 text-yellow-500" />;
                          case 'failed':
                            return <XCircleIcon className="h-4 w-4 text-red-500" />;
                          case 'retrying':
                            return <RefreshCwIcon className="h-4 w-4 text-blue-500" />;
                          default:
                            return <ClockIcon className="h-4 w-4 text-yellow-500" />;
                        }
                      })()}

                      <Badge>
                        {getAttemptStatus(attempt).label}
                      </Badge>
                    </div>

                    <div className="text-sm text-muted-foreground">
                      {format(new Date(attempt.timestamp), "dd/MM/yyyy HH:mm:ss", { locale: ptBR })}
                    </div>
                  </div>

                  {/* Endpoint Information */}
                  {attempt.endpoint && (
                    <div className="mb-3 p-3 bg-muted/50 rounded-lg">
                      <h4 className="text-sm font-medium mb-2">Endpoint</h4>
                      <div className="text-sm space-y-1">
                        <p><strong>URL:</strong>
                          <span className="font-mono text-xs break-all ml-1">
                            {attempt.endpoint.url}
                          </span>
                        </p>
                        {attempt.endpoint.description && (
                          <p><strong>Descrição:</strong> {attempt.endpoint.description}</p>
                        )}
                        <p><strong>Status:</strong>
                          <Badge className={`ml-1 ${attempt.endpoint.disabled ? 'bg-red-500 text-white' : 'bg-green-500 text-white'}`}>
                            {attempt.endpoint.disabled ? "Desabilitado" : "Ativo"}
                          </Badge>
                        </p>
                      </div>
                    </div>
                  )}

                  {attempt.responseStatusCode && (
                    <div className="mb-3">
                      <p className="text-sm">
                        <strong>Status HTTP:</strong> {attempt.responseStatusCode}
                      </p>
                      {attempt.response && (
                        <div className="mt-2">
                          <p className="text-sm font-medium mb-1">Resposta:</p>
                          <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                            {attempt.response}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Mostrar botão de reenviar para todos os eventos, independente do status */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => resendWebhook(attempt.endpointId)}
                    className="flex items-center gap-2"
                  >
                    <RefreshCwIcon className="h-4 w-4" />
                    Reenviar
                  </Button>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircleIcon className="h-8 w-8 mx-auto mb-2" />
              <p>Nenhuma tentativa de entrega encontrada</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
