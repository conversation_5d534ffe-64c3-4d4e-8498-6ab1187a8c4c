import { NextRequest, NextResponse } from "next/server";

// Interface para configuração do rate limiting
interface RateLimitConfig {
  windowMs: number; // janela de tempo em ms
  maxRequests: number; // máximo de requests por janela
  message?: string; // mensagem de erro customizada
  skipSuccessfulRequests?: boolean; // não contar requests bem-sucedidos
}

// Store em memória para rate limiting (em produção, usar Redis)
const requestCounts = new Map<string, { count: number; resetTime: number }>();

/**
 * Middleware de rate limiting para proteger endpoints sensíveis
 */
export function withRateLimit(config: RateLimitConfig) {
  return function(handler: Function) {
    return async (req: NextRequest, ...args: any[]) => {
      // Obter identificador do cliente (IP + User-Agent para maior precisão)
      const clientId = getClientIdentifier(req);
      const now = Date.now();
      
      // Limpar entradas expiradas
      cleanupExpiredEntries(now);
      
      // Verificar rate limit
      const clientData = requestCounts.get(clientId);
      
      if (!clientData || now > clientData.resetTime) {
        // Primeira requisição ou janela expirada
        requestCounts.set(clientId, {
          count: 1,
          resetTime: now + config.windowMs
        });
      } else if (clientData.count >= config.maxRequests) {
        // Rate limit excedido
        const resetIn = Math.ceil((clientData.resetTime - now) / 1000);
        
        return NextResponse.json(
          {
            error: config.message || "Too Many Requests",
            retryAfter: resetIn
          },
          {
            status: 429,
            headers: {
              'Retry-After': resetIn.toString(),
              'X-RateLimit-Limit': config.maxRequests.toString(),
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': new Date(clientData.resetTime).toISOString()
            }
          }
        );
      } else {
        // Incrementar contador
        clientData.count++;
      }
      
      // Executar handler original
      const response = await handler(req, ...args);
      
      // Adicionar headers de rate limit na resposta
      if (response instanceof NextResponse) {
        const clientData = requestCounts.get(clientId);
        if (clientData) {
          response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
          response.headers.set('X-RateLimit-Remaining', Math.max(0, config.maxRequests - clientData.count).toString());
          response.headers.set('X-RateLimit-Reset', new Date(clientData.resetTime).toISOString());
        }
      }
      
      return response;
    };
  };
}

/**
 * Obter identificador único do cliente
 */
function getClientIdentifier(req: NextRequest): string {
  // Tentar obter IP real através de headers de proxy
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const ip = forwarded?.split(',')[0] || realIp || req.ip || 'unknown';
  
  // Combinar IP com User-Agent para identificação mais precisa
  const userAgent = req.headers.get('user-agent') || 'unknown';
  
  return `${ip}:${userAgent.slice(0, 50)}`; // Limitar tamanho do User-Agent
}

/**
 * Limpar entradas expiradas do cache
 */
function cleanupExpiredEntries(now: number) {
  for (const [key, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(key);
    }
  }
}

// Configurações pré-definidas para diferentes tipos de endpoints
export const rateLimitConfigs = {
  // Para endpoints de API keys (muito restritivo)
  apiKeys: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 10, // 10 requests por 15 minutos
    message: "Too many API key requests. Please try again later."
  },
  
  // Para endpoints de autenticação
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: 5, // 5 tentativas por 15 minutos
    message: "Too many authentication attempts. Please try again later."
  },
  
  // Para endpoints gerais de API
  general: {
    windowMs: 1 * 60 * 1000, // 1 minuto
    maxRequests: 100, // 100 requests por minuto
    message: "Rate limit exceeded. Please slow down your requests."
  },
  
  // Para endpoints de pagamento (muito restritivo)
  payments: {
    windowMs: 5 * 60 * 1000, // 5 minutos
    maxRequests: 20, // 20 requests por 5 minutos
    message: "Too many payment requests. Please try again later."
  }
};

/**
 * Middleware específico para endpoints de API keys
 */
export const withApiKeyRateLimit = withRateLimit(rateLimitConfigs.apiKeys);

/**
 * Middleware específico para endpoints de autenticação
 */
export const withAuthRateLimit = withRateLimit(rateLimitConfigs.auth);

/**
 * Middleware específico para endpoints de pagamento
 */
export const withPaymentRateLimit = withRateLimit(rateLimitConfigs.payments);