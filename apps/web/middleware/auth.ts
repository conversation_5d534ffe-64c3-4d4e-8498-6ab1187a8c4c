import { auth, getOrganizationMembership } from "@repo/auth";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

/**
 * Middleware para verificar autenticação do usuário
 */
export function withAuth(handler: Function) {
  return async (req: NextRequest, context?: any) => {
    const headersList = headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    return handler(req, context, session);
  };
}

/**
 * Middleware para verificar autenticação e autorização de organização
 */
export function withOrgAuth(handler: Function) {
  return withAuth(async (req: NextRequest, context: any, session: any) => {
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId') || context?.params?.organizationId;
    
    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }
    
    const membership = await getOrganizationMembership(session.user.id, organizationId);
    if (!membership) {
      console.error(`User ${session.user.id} attempted to access organization ${organizationId} without permission`);
      return NextResponse.json(
        { error: "Forbidden: You don't have access to this organization" },
        { status: 403 }
      );
    }
    
    return handler(req, context, session, membership);
  });
}

/**
 * Função utilitária para validar membership de organização
 */
export async function validateOrgMembership(userId: string, organizationId: string) {
  return await getOrganizationMembership(userId, organizationId);
}