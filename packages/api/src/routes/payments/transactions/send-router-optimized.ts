import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { z } from 'zod';
import { logger } from '@repo/logs';
import { SendTransactionService } from '@repo/utils/src/send-transaction-service';

const app = new Hono();

// Schema de validação para criação de transação SEND
const createSendSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  pixKey: z.string().min(1, 'PIX key is required'),
  pixKeyType: z.enum(['CPF', 'CNPJ', 'EMAIL', 'PHONE', 'RANDOM'], {
    errorMap: () => ({ message: 'Invalid PIX key type' })
  }),
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Invalid email format'),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  organizationId: z.string().min(1, 'Organization ID is required')
});

/**
 * POST /api/payments/transactions/send
 * Cria uma transação SEND (transferência PIX) com proteção rigorosa contra duplicatas
 * Bloqueia duplicatas para evitar perda de dinheiro
 */
app.post(
  '/',
  async (c) => {
    const timings = {
      start: Date.now(),
      validationComplete: 0,
      duplicateCheck: 0,
      transactionCreated: 0,
      total: 0
    };

    try {
      // 1. Validar dados de entrada
      const body = await c.req.json();
      const validatedData = createSendSchema.parse(body);
      timings.validationComplete = Date.now();

      const {
        amount,
        pixKey,
        pixKeyType,
        customerName,
        customerEmail,
        description,
        metadata,
        organizationId
      } = validatedData;

      // 2. Verificar permissões (simplificado para exemplo)
      const session = c.get('session');
      if (!session?.userId) {
        throw new HTTPException(401, { message: 'Authentication required' });
      }

      // 3. Verificação prévia de duplicatas (opcional, para feedback mais rápido)
      logger.info('Checking for duplicate SEND transaction', {
        customerEmail,
        amount,
        pixKey: pixKey.substring(0, 4) + '***',
        pixKeyType,
        organizationId
      });

      const duplicateCheck = await SendTransactionService.checkDuplicateTransfer({
        customerEmail,
        amount,
        organizationId,
        pixKey,
        pixKeyType
      });

      if (duplicateCheck.exists) {
        logger.warn('Duplicate SEND transaction detected in pre-check', {
          existingTransactionId: duplicateCheck.transactionId,
          status: duplicateCheck.status,
          customerEmail,
          amount,
          pixKey: pixKey.substring(0, 4) + '***'
        });

        throw new HTTPException(409, {
          message: `Duplicate transfer transaction detected. Transaction ID: ${duplicateCheck.transactionId}`,
          details: {
            existingTransactionId: duplicateCheck.transactionId,
            status: duplicateCheck.status,
            conflictType: 'duplicate_transfer'
          }
        });
      }

      timings.duplicateCheck = Date.now();

      // 4. Criar transação SEND usando serviço com proteção rigorosa
      logger.info('Creating SEND transaction with strict duplicate prevention', {
        customerEmail,
        amount,
        pixKey: pixKey.substring(0, 4) + '***',
        pixKeyType,
        organizationId,
        userId: session.userId
      });

      const transactionResult = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName,
        amount,
        organizationId,
        pixKey,
        pixKeyType,
        description,
        metadata: {
          ...metadata,
          createdBy: session.userId,
          source: 'api_optimized'
        }
      });

      timings.transactionCreated = Date.now();

      // 5. Aqui você processaria a transferência real com o gateway
      // const transferResult = await processTransferWithGateway(transactionResult);

      timings.total = Date.now();

      logger.info('SEND transaction created successfully', {
        transactionId: transactionResult.id,
        isNew: transactionResult.isNew,
        timings: {
          validation: timings.validationComplete - timings.start,
          duplicateCheck: timings.duplicateCheck - timings.validationComplete,
          transaction: timings.transactionCreated - timings.duplicateCheck,
          total: timings.total - timings.start
        }
      });

      // 6. Retornar resposta
      return c.json({
        id: transactionResult.id,
        referenceCode: transactionResult.referenceCode,
        status: transactionResult.status,
        amount: transactionResult.amount,
        customerEmail: transactionResult.customerEmail,
        customerName: transactionResult.customerName,
        pixKey: transactionResult.pixKey,
        pixKeyType: transactionResult.pixKeyType,
        createdAt: transactionResult.createdAt,
        message: 'PIX transfer created successfully',
        isNew: transactionResult.isNew,
        duplicatePrevention: {
          enabled: true,
          strictMode: true
        }
      }, 201);

    } catch (error) {
      timings.total = Date.now();

      logger.error('Error creating SEND transaction', {
        error: error instanceof Error ? error.message : error,
        timings: {
          total: timings.total - timings.start,
          validation: timings.validationComplete - timings.start,
          duplicateCheck: timings.duplicateCheck - timings.validationComplete
        }
      });

      if (error instanceof z.ZodError) {
        return c.json({
          error: 'Validation error',
          details: error.errors
        }, 400);
      }

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, {
        message: 'Internal server error creating PIX transfer'
      });
    }
  }
);

/**
 * GET /api/payments/transactions/send/duplicates
 * Lista transferências duplicadas para análise
 */
app.get(
  '/duplicates',
  async (c) => {
    try {
      const organizationId = c.req.query('organizationId');
      const limit = parseInt(c.req.query('limit') || '10');

      if (!organizationId) {
        throw new HTTPException(400, { message: 'Organization ID is required' });
      }

      const duplicates = await SendTransactionService.findDuplicateTransfers(
        organizationId,
        limit
      );

      return c.json({
        duplicates,
        count: duplicates.length,
        message: duplicates.length > 0
          ? 'Duplicate transfers found'
          : 'No duplicate transfers found'
      });

    } catch (error) {
      logger.error('Error fetching duplicate transfers', {
        error: error instanceof Error ? error.message : error
      });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, {
        message: 'Internal server error fetching duplicate transfers'
      });
    }
  }
);

export default app;
