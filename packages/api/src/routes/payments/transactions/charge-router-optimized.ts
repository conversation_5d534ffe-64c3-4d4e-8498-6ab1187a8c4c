import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { z } from 'zod';
import { logger } from '@repo/logs';
import { ChargeTransactionService } from '@repo/utils/src/charge-transaction-service';

const app = new Hono();

// Schema de validação para criação de transação CHARGE
const createChargeSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Invalid email format'),
  customerPhone: z.string().optional(),
  customerDocument: z.string().optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  organizationId: z.string().min(1, 'Organization ID is required'),
  allowMultiple: z.boolean().optional().default(true) // Permitir múltiplas transações por padrão
});

/**
 * POST /api/payments/transactions/charge
 * Cria uma transação CHARGE (recebimento PIX) otimizada
 * Permite múltiplas transações para gerar QR codes únicos
 */
app.post(
  '/',
  async (c) => {
    const timings = {
      start: Date.now(),
      validationComplete: 0,
      transactionCreated: 0,
      pixGenerated: 0,
      total: 0
    };

    try {
      // 1. Validar dados de entrada
      const body = await c.req.json();
      const validatedData = createChargeSchema.parse(body);
      timings.validationComplete = Date.now();

      const {
        amount,
        customerName,
        customerEmail,
        customerPhone,
        customerDocument,
        description,
        metadata,
        organizationId,
        allowMultiple
      } = validatedData;

      // 2. Verificar permissões (simplificado para exemplo)
      const session = c.get('session');
      if (!session?.userId) {
        throw new HTTPException(401, { message: 'Authentication required' });
      }

      // 3. Criar transação CHARGE usando serviço otimizado
      logger.info('Creating CHARGE transaction with optimized service', {
        customerEmail,
        amount,
        organizationId,
        allowMultiple,
        userId: session.userId
      });

      const transactionResult = await ChargeTransactionService.createChargeTransaction({
        customerEmail,
        customerName,
        customerPhone,
        customerDocument,
        amount,
        organizationId,
        description,
        metadata,
        allowMultiple
      });

      timings.transactionCreated = Date.now();

      // 4. Se for nova transação, gerar PIX QR Code
      let pixData = null;
      if (transactionResult.isNew) {
        try {
          // Aqui você chamaria o provider para gerar o PIX
          // const pixResult = await generatePixQRCode(transactionResult);

          // Simular geração de PIX (substitua pela chamada real ao provider)
          pixData = {
            pixCode: `pix_${transactionResult.id}_${Date.now()}`,
            pixQrCode: `data:image/png;base64,${Buffer.from('fake_qr_code').toString('base64')}`,
            pixExpiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
          };

          // Atualizar transação com dados PIX
          await ChargeTransactionService.updateChargeWithPixData(
            transactionResult.id,
            pixData
          );

          timings.pixGenerated = Date.now();
        } catch (pixError) {
          logger.error('Error generating PIX QR Code', {
            error: pixError,
            transactionId: transactionResult.id
          });
          // Não falhar a transação se PIX falhar - pode ser gerado depois
        }
      } else {
        // Retornar dados PIX existentes
        pixData = {
          pixCode: transactionResult.metadata.pixCode,
          pixQrCode: transactionResult.metadata.pixQrCode,
          pixExpiresAt: transactionResult.metadata.pixExpiresAt
        };
      }

      timings.total = Date.now();

      logger.info('CHARGE transaction created successfully', {
        transactionId: transactionResult.id,
        isNew: transactionResult.isNew,
        hasPixData: !!pixData?.pixCode,
        timings: {
          validation: timings.validationComplete - timings.start,
          transaction: timings.transactionCreated - timings.validationComplete,
          pix: timings.pixGenerated - timings.transactionCreated,
          total: timings.total - timings.start
        }
      });

      // 5. Retornar resposta otimizada
      return c.json({
        id: transactionResult.id,
        referenceCode: transactionResult.referenceCode,
        status: transactionResult.status,
        amount: transactionResult.amount,
        customerEmail: transactionResult.customerEmail,
        customerName: transactionResult.customerName,
        createdAt: transactionResult.createdAt,
        pix: pixData ? {
          qrCode: {
            emv: pixData.pixCode,
            imagem: pixData.pixQrCode
          },
          expirationDate: pixData.pixExpiresAt
        } : null,
        message: transactionResult.isNew
          ? 'PIX charge created successfully'
          : 'Existing PIX charge returned',
        allowMultiple,
        isNew: transactionResult.isNew
      }, 200);

    } catch (error) {
      timings.total = Date.now();

      logger.error('Error creating CHARGE transaction', {
        error: error instanceof Error ? error.message : error,
        timings: {
          total: timings.total - timings.start,
          validation: timings.validationComplete - timings.start,
          transaction: timings.transactionCreated - timings.validationComplete
        }
      });

      if (error instanceof z.ZodError) {
        return c.json({
          error: 'Validation error',
          details: error.errors
        }, 400);
      }

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, {
        message: 'Internal server error creating PIX charge'
      });
    }
  }
);

export default app;
