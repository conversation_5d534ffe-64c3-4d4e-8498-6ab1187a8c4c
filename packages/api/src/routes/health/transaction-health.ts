/**
 * Transaction health check endpoint
 * Provides comprehensive health status for PIX transaction system
 */

import { Hono } from 'hono';
import { getTransactionHealthStatus } from '@repo/utils/src/transaction-monitoring';
import { circuitBreakers } from '@repo/utils/src/circuit-breaker';
import { getFeatureFlags } from '@repo/utils/src/feature-flags';
import { logger } from '@repo/logs';

const app = new Hono();

/**
 * GET /health/transactions
 * Returns comprehensive health status of the transaction system
 */
app.get('/', async (c) => {
  try {
    const startTime = Date.now();
    
    // Get comprehensive health report
    const healthReport = await getTransactionHealthStatus();
    
    // Get circuit breaker status
    const circuitBreakerStatus = {
      transactionService: circuitBreakers.transactionService.getMetrics(),
      databaseOperations: circuitBreakers.databaseOperations.getMetrics(),
      externalApi: circuitBreakers.externalApi.getMetrics()
    };
    
    // Get feature flag status
    const featureFlags = getFeatureFlags();
    
    const response = {
      status: healthReport.status,
      timestamp: new Date().toISOString(),
      version: '2.0',
      uptime: process.uptime(),
      responseTime: Date.now() - startTime,
      
      // Core metrics
      metrics: healthReport.metrics,
      
      // Database health
      database: healthReport.databaseHealth,
      
      // Circuit breaker status
      circuitBreakers: circuitBreakerStatus,
      
      // Feature flags
      featureFlags,
      
      // Alerts and recommendations
      alerts: healthReport.alerts,
      recommendations: healthReport.recommendations,
      
      // System info
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: process.memoryUsage(),
        pid: process.pid
      }
    };
    
    // Set appropriate HTTP status based on health
    const httpStatus = healthReport.status === 'HEALTHY' ? 200 
                     : healthReport.status === 'WARNING' ? 200 
                     : 503; // Service Unavailable for CRITICAL
    
    // Log health check
    logger.info('Transaction health check completed', {
      status: healthReport.status,
      responseTime: response.responseTime,
      alertCount: healthReport.alerts.length
    });
    
    return c.json(response, httpStatus);
    
  } catch (error) {
    logger.error('Transaction health check failed', {
      error: error instanceof Error ? error.message : error
    });
    
    return c.json({
      status: 'CRITICAL',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      details: error instanceof Error ? error.message : String(error)
    }, 503);
  }
});

/**
 * GET /health/transactions/metrics
 * Returns just the metrics without full health report
 */
app.get('/metrics', async (c) => {
  try {
    const { transactionMonitor } = await import('@repo/utils/src/transaction-monitoring');
    
    const metrics = await transactionMonitor.getCurrentMetrics();
    const databaseHealth = await transactionMonitor.getDatabaseHealthMetrics();
    
    return c.json({
      timestamp: new Date().toISOString(),
      metrics,
      database: databaseHealth
    });
    
  } catch (error) {
    logger.error('Transaction metrics check failed', {
      error: error instanceof Error ? error.message : error
    });
    
    return c.json({
      error: 'Metrics check failed',
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

/**
 * GET /health/transactions/circuit-breakers
 * Returns circuit breaker status
 */
app.get('/circuit-breakers', async (c) => {
  try {
    const status = {
      transactionService: circuitBreakers.transactionService.getMetrics(),
      databaseOperations: circuitBreakers.databaseOperations.getMetrics(),
      externalApi: circuitBreakers.externalApi.getMetrics()
    };
    
    return c.json({
      timestamp: new Date().toISOString(),
      circuitBreakers: status
    });
    
  } catch (error) {
    logger.error('Circuit breaker status check failed', {
      error: error instanceof Error ? error.message : error
    });
    
    return c.json({
      error: 'Circuit breaker status check failed',
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

/**
 * POST /health/transactions/reset-circuit-breakers
 * Reset all circuit breakers (admin endpoint)
 */
app.post('/reset-circuit-breakers', async (c) => {
  try {
    // Reset all circuit breakers
    circuitBreakers.transactionService.reset();
    circuitBreakers.databaseOperations.reset();
    circuitBreakers.externalApi.reset();
    
    logger.info('All circuit breakers reset via health endpoint');
    
    return c.json({
      message: 'All circuit breakers reset successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Circuit breaker reset failed', {
      error: error instanceof Error ? error.message : error
    });
    
    return c.json({
      error: 'Circuit breaker reset failed',
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

/**
 * GET /health/transactions/ready
 * Simple readiness check for load balancers
 */
app.get('/ready', async (c) => {
  try {
    const healthReport = await getTransactionHealthStatus();
    
    if (healthReport.status === 'CRITICAL') {
      return c.json({ ready: false, status: healthReport.status }, 503);
    }
    
    return c.json({ 
      ready: true, 
      status: healthReport.status,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    return c.json({ 
      ready: false, 
      error: 'Readiness check failed' 
    }, 503);
  }
});

/**
 * GET /health/transactions/live
 * Simple liveness check for Kubernetes
 */
app.get('/live', async (c) => {
  return c.json({ 
    alive: true, 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

export default app;
