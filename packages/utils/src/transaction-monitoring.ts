/**
 * Transaction monitoring and alerting system
 * Provides comprehensive monitoring for PIX transaction operations
 */

import { logger } from '@repo/logs';
import { db } from '@repo/database';

export interface TransactionMetrics {
  totalTransactions: number;
  chargeTransactions: number;
  sendTransactions: number;
  duplicatesBlocked: number;
  averageProcessingTime: number;
  errorRate: number;
  constraintViolations: number;
}

export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number; // Percentage
    slowTransactionMs: number;
    duplicateRatePerHour: number;
    constraintViolationsPerHour: number;
  };
  channels: {
    email?: string[];
    slack?: string;
    webhook?: string;
  };
}

export class TransactionMonitor {
  private static instance: TransactionMonitor;
  private metrics: Map<string, any> = new Map();
  private alertConfig: AlertConfig;

  constructor(config?: Partial<AlertConfig>) {
    this.alertConfig = {
      enabled: true,
      thresholds: {
        errorRate: 5, // 5% error rate
        slowTransactionMs: 1000, // 1 second
        duplicateRatePerHour: 100, // 100 duplicates per hour
        constraintViolationsPerHour: 10 // 10 violations per hour
      },
      channels: {},
      ...config
    };
  }

  static getInstance(config?: Partial<AlertConfig>): TransactionMonitor {
    if (!TransactionMonitor.instance) {
      TransactionMonitor.instance = new TransactionMonitor(config);
    }
    return TransactionMonitor.instance;
  }

  /**
   * Record transaction creation event
   */
  recordTransactionCreation(data: {
    type: 'CHARGE' | 'SEND';
    organizationId: string;
    duration: number;
    success: boolean;
    isDuplicate?: boolean;
    error?: string;
  }): void {
    const timestamp = Date.now();
    const key = `transaction_${timestamp}`;

    this.metrics.set(key, {
      ...data,
      timestamp
    });

    // Log the event
    logger.info('Transaction creation recorded', {
      type: data.type,
      organizationId: data.organizationId,
      duration: data.duration,
      success: data.success,
      isDuplicate: data.isDuplicate || false
    });

    // Check for alerts
    this.checkAlerts(data);

    // Clean old metrics (keep last hour)
    this.cleanOldMetrics();
  }

  /**
   * Record constraint violation
   */
  recordConstraintViolation(data: {
    constraintType: string;
    organizationId: string;
    details: any;
  }): void {
    const timestamp = Date.now();
    const key = `constraint_violation_${timestamp}`;

    this.metrics.set(key, {
      ...data,
      timestamp,
      type: 'constraint_violation'
    });

    logger.warn('Database constraint violation detected', {
      constraintType: data.constraintType,
      organizationId: data.organizationId,
      details: data.details
    });

    // Immediate alert for constraint violations
    this.sendAlert('constraint_violation', {
      message: `Database constraint violation: ${data.constraintType}`,
      organizationId: data.organizationId,
      details: data.details,
      severity: 'HIGH'
    });
  }

  /**
   * Get current metrics
   */
  async getCurrentMetrics(): Promise<TransactionMetrics> {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentMetrics = Array.from(this.metrics.values())
      .filter(m => m.timestamp > oneHourAgo);

    const transactions = recentMetrics.filter(m => m.type !== 'constraint_violation');
    const violations = recentMetrics.filter(m => m.type === 'constraint_violation');

    const totalTransactions = transactions.length;
    const chargeTransactions = transactions.filter(t => t.type === 'CHARGE').length;
    const sendTransactions = transactions.filter(t => t.type === 'SEND').length;
    const duplicatesBlocked = transactions.filter(t => t.isDuplicate).length;
    const errors = transactions.filter(t => !t.success).length;
    const durations = transactions.map(t => t.duration).filter(d => d > 0);

    return {
      totalTransactions,
      chargeTransactions,
      sendTransactions,
      duplicatesBlocked,
      averageProcessingTime: durations.length > 0 
        ? durations.reduce((a, b) => a + b, 0) / durations.length 
        : 0,
      errorRate: totalTransactions > 0 ? (errors / totalTransactions) * 100 : 0,
      constraintViolations: violations.length
    };
  }

  /**
   * Get database health metrics
   */
  async getDatabaseHealthMetrics(): Promise<{
    totalTransactions: number;
    recentTransactions: number;
    duplicateReferenceCodeCount: number;
    duplicateExternalIdCount: number;
    indexHealth: any;
  }> {
    try {
      // Get total transaction count
      const totalTransactions = await db.transaction.count();

      // Get recent transactions (last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentTransactions = await db.transaction.count({
        where: {
          createdAt: { gte: oneHourAgo }
        }
      });

      // Check for duplicate reference codes
      const duplicateRefCodes = await db.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count FROM (
          SELECT "referenceCode", COUNT(*)
          FROM "transaction"
          WHERE "referenceCode" IS NOT NULL
          GROUP BY "referenceCode"
          HAVING COUNT(*) > 1
        ) duplicates
      `;

      // Check for duplicate external IDs
      const duplicateExtIds = await db.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count FROM (
          SELECT "externalId", COUNT(*)
          FROM "transaction"
          WHERE "externalId" IS NOT NULL
          GROUP BY "externalId"
          HAVING COUNT(*) > 1
        ) duplicates
      `;

      // Check index usage (simplified)
      const indexHealth = await db.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE tablename = 'transaction'
        ORDER BY idx_tup_read DESC
        LIMIT 10
      `;

      return {
        totalTransactions,
        recentTransactions,
        duplicateReferenceCodeCount: duplicateRefCodes[0]?.count || 0,
        duplicateExternalIdCount: duplicateExtIds[0]?.count || 0,
        indexHealth
      };

    } catch (error) {
      logger.error('Failed to get database health metrics', {
        error: error instanceof Error ? error.message : error
      });
      
      return {
        totalTransactions: 0,
        recentTransactions: 0,
        duplicateReferenceCodeCount: 0,
        duplicateExternalIdCount: 0,
        indexHealth: []
      };
    }
  }

  /**
   * Generate health report
   */
  async generateHealthReport(): Promise<{
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    metrics: TransactionMetrics;
    databaseHealth: any;
    alerts: string[];
    recommendations: string[];
  }> {
    const metrics = await this.getCurrentMetrics();
    const databaseHealth = await this.getDatabaseHealthMetrics();
    const alerts: string[] = [];
    const recommendations: string[] = [];

    let status: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';

    // Check error rate
    if (metrics.errorRate > this.alertConfig.thresholds.errorRate) {
      status = 'CRITICAL';
      alerts.push(`High error rate: ${metrics.errorRate.toFixed(2)}%`);
      recommendations.push('Investigate transaction failures and fix underlying issues');
    }

    // Check processing time
    if (metrics.averageProcessingTime > this.alertConfig.thresholds.slowTransactionMs) {
      if (status === 'HEALTHY') status = 'WARNING';
      alerts.push(`Slow transaction processing: ${metrics.averageProcessingTime.toFixed(0)}ms average`);
      recommendations.push('Optimize database queries and consider scaling resources');
    }

    // Check constraint violations
    if (metrics.constraintViolations > this.alertConfig.thresholds.constraintViolationsPerHour) {
      status = 'CRITICAL';
      alerts.push(`High constraint violations: ${metrics.constraintViolations} in last hour`);
      recommendations.push('Review data integrity and fix constraint violations');
    }

    // Check database duplicates
    if (databaseHealth.duplicateExternalIdCount > 0) {
      status = 'CRITICAL';
      alerts.push(`Database has ${databaseHealth.duplicateExternalIdCount} duplicate external IDs`);
      recommendations.push('Clean up duplicate external IDs before applying constraints');
    }

    return {
      status,
      metrics,
      databaseHealth,
      alerts,
      recommendations
    };
  }

  /**
   * Check for alert conditions
   */
  private checkAlerts(data: any): void {
    if (!this.alertConfig.enabled) return;

    // Check for slow transactions
    if (data.duration > this.alertConfig.thresholds.slowTransactionMs) {
      this.sendAlert('slow_transaction', {
        message: `Slow transaction detected: ${data.duration}ms`,
        organizationId: data.organizationId,
        duration: data.duration,
        severity: 'MEDIUM'
      });
    }

    // Check error rate (every 10 transactions)
    const recentTransactions = Array.from(this.metrics.values())
      .filter(m => m.timestamp > Date.now() - 10 * 60 * 1000) // Last 10 minutes
      .filter(m => m.type !== 'constraint_violation');

    if (recentTransactions.length >= 10 && recentTransactions.length % 10 === 0) {
      const errors = recentTransactions.filter(t => !t.success).length;
      const errorRate = (errors / recentTransactions.length) * 100;

      if (errorRate > this.alertConfig.thresholds.errorRate) {
        this.sendAlert('high_error_rate', {
          message: `High error rate detected: ${errorRate.toFixed(2)}%`,
          errorRate,
          recentTransactions: recentTransactions.length,
          errors,
          severity: 'HIGH'
        });
      }
    }
  }

  /**
   * Send alert
   */
  private sendAlert(type: string, data: any): void {
    logger.warn(`ALERT: ${type}`, data);

    // In a real implementation, you would send to configured channels
    // For now, we just log the alert
    console.warn(`🚨 ALERT [${type.toUpperCase()}]: ${data.message}`);
    
    if (data.severity === 'HIGH' || data.severity === 'CRITICAL') {
      console.warn('   This requires immediate attention!');
    }
  }

  /**
   * Clean old metrics to prevent memory leaks
   */
  private cleanOldMetrics(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    for (const [key, value] of this.metrics.entries()) {
      if (value.timestamp < oneHourAgo) {
        this.metrics.delete(key);
      }
    }
  }
}

// Export singleton instance
export const transactionMonitor = TransactionMonitor.getInstance();

// Health check endpoint helper
export async function getTransactionHealthStatus() {
  return await transactionMonitor.generateHealthReport();
}
