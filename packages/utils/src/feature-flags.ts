/**
 * Feature flags for PIX transaction duplication prevention
 * Allows gradual rollout and safe testing in production
 */

export interface FeatureFlags {
  // Core transaction features
  enableNewTransactionServices: boolean;
  enableStrictDuplicatePrevention: boolean;
  enableEnhancedIdempotency: boolean;
  
  // Database features
  enableNewConstraints: boolean;
  enableOptimizedQueries: boolean;
  
  // Monitoring and safety
  enableCircuitBreaker: boolean;
  enableDetailedLogging: boolean;
  enablePerformanceMonitoring: boolean;
  
  // Rollout controls
  rolloutPercentage: number;
  enabledOrganizations: string[];
  disabledOrganizations: string[];
}

// Default feature flags - conservative approach for production safety
const DEFAULT_FLAGS: FeatureFlags = {
  enableNewTransactionServices: false,
  enableStrictDuplicatePrevention: false,
  enableEnhancedIdempotency: false,
  enableNewConstraints: false,
  enableOptimizedQueries: false,
  enableCircuitBreaker: true,
  enableDetailedLogging: true,
  enablePerformanceMonitoring: true,
  rolloutPercentage: 0,
  enabledOrganizations: [],
  disabledOrganizations: []
};

// Environment-based feature flags
const FEATURE_FLAGS: FeatureFlags = {
  ...DEFAULT_FLAGS,
  // Override with environment variables
  enableNewTransactionServices: process.env.ENABLE_NEW_TRANSACTION_SERVICES === 'true',
  enableStrictDuplicatePrevention: process.env.ENABLE_STRICT_DUPLICATE_PREVENTION === 'true',
  enableEnhancedIdempotency: process.env.ENABLE_ENHANCED_IDEMPOTENCY === 'true',
  enableNewConstraints: process.env.ENABLE_NEW_CONSTRAINTS === 'true',
  enableOptimizedQueries: process.env.ENABLE_OPTIMIZED_QUERIES === 'true',
  enableCircuitBreaker: process.env.ENABLE_CIRCUIT_BREAKER !== 'false',
  enableDetailedLogging: process.env.ENABLE_DETAILED_LOGGING !== 'false',
  enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING !== 'false',
  rolloutPercentage: parseInt(process.env.ROLLOUT_PERCENTAGE || '0', 10),
  enabledOrganizations: process.env.ENABLED_ORGANIZATIONS?.split(',') || [],
  disabledOrganizations: process.env.DISABLED_ORGANIZATIONS?.split(',') || []
};

/**
 * Check if a feature is enabled for a specific organization
 */
export function isFeatureEnabled(
  feature: keyof FeatureFlags,
  organizationId?: string
): boolean {
  const flag = FEATURE_FLAGS[feature];
  
  // Handle boolean flags
  if (typeof flag === 'boolean') {
    // Check organization-specific overrides
    if (organizationId) {
      if (FEATURE_FLAGS.disabledOrganizations.includes(organizationId)) {
        return false;
      }
      if (FEATURE_FLAGS.enabledOrganizations.includes(organizationId)) {
        return true;
      }
    }
    
    return flag;
  }
  
  // Handle other types (numbers, arrays)
  return Boolean(flag);
}

/**
 * Check if organization is in rollout percentage
 */
export function isInRollout(organizationId: string): boolean {
  if (FEATURE_FLAGS.rolloutPercentage === 0) return false;
  if (FEATURE_FLAGS.rolloutPercentage === 100) return true;
  
  // Use organization ID hash for consistent rollout
  const hash = organizationId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  const percentage = Math.abs(hash) % 100;
  return percentage < FEATURE_FLAGS.rolloutPercentage;
}

/**
 * Get all feature flags for debugging
 */
export function getFeatureFlags(): FeatureFlags {
  return { ...FEATURE_FLAGS };
}

/**
 * Check if new transaction services should be used
 */
export function shouldUseNewTransactionServices(organizationId?: string): boolean {
  return isFeatureEnabled('enableNewTransactionServices', organizationId) &&
         (organizationId ? isInRollout(organizationId) : false);
}

/**
 * Check if strict duplicate prevention should be used
 */
export function shouldUseStrictDuplicatePrevention(organizationId?: string): boolean {
  return isFeatureEnabled('enableStrictDuplicatePrevention', organizationId) &&
         (organizationId ? isInRollout(organizationId) : false);
}
