import { createHash } from 'crypto';
import { createId } from '@paralleldrive/cuid2';



/**
 * Gera um ID único e atômico para transações PIX
 * MANTÉM COMPATIBILIDADE TOTAL com formato anterior para evitar breaking changes
 * Formato: tx_${timestamp}_${random} - EXATAMENTE como o código original
 * @deprecated Use generateSecureTransactionId() for new implementations
 */
export function generateUniqueTransactionId(): string {
  // MANTÉM O FORMATO ORIGINAL EXATO para compatibilidade total
  return `tx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
}

/**
 * Gera um ID de transação seguro usando CUID (NOVO PADRÃO)
 * Formato: tx_${cuid} - Mais seguro e evita colisões
 * Similar ao padrão de End-to-End ID do PIX
 * CUID é mais eficiente que UUID e garante unicidade global
 */
export function generateSecureTransactionId(): string {
  const id = createId(); // CUID já é otimizado e único
  return `tx_${id}`;
}

/**
 * Gera um End-to-End ID no padrão PIX
 * Formato: E${cuid} - Compatível com padrão PIX e mais seguro
 */
export function generateEndToEndId(): string {
  const id = createId(); // Usar CUID para maior segurança
  return `E${id}`;
}

/**
 * Gera um ID único mais robusto para novos casos de uso
 * Usa CUID para máxima segurança e unicidade
 * @deprecated Use generateSecureTransactionId() instead
 */
export function generateEnhancedTransactionId(): string {
  // Usar CUID em vez de timestamp + random para maior segurança
  const id = createId();
  return `tx_${id}`;
}

/**
 * Gera uma chave de idempotência robusta baseada nos parâmetros da transação
 */
export function generateIdempotencyKey(params: {
  customerEmail: string;
  amount: number;
  organizationId: string;
  customerDocument?: string;
  description?: string;
  timestamp?: number; // Adicionar timestamp opcional para controle externo
}): string {
  const { customerEmail, amount, organizationId, customerDocument, description, timestamp } = params;

  // Criar um hash dos parâmetros principais para idempotência
  const keyData = {
    email: customerEmail.toLowerCase().trim(),
    amount: Math.round(amount * 100), // Converter para centavos para evitar problemas de precisão
    org: organizationId,
    doc: customerDocument?.replace(/\D/g, '') || '', // Apenas números do documento
    desc: description?.trim() || ''
  };

  // Usar timestamp com granularidade de 5 minutos para permitir retry em caso de falha
  // Aumentado de 1 para 5 minutos para ser mais permissivo com retries
  const timeWindow = Math.floor((timestamp || Date.now()) / (5 * 60 * 1000)); // 5 minutos

  const hash = createHash('sha256')
    .update(JSON.stringify({ ...keyData, timeWindow }))
    .digest('hex')
    .substring(0, 16);

  return `idem_${hash}_${timeWindow}`;
}

/**
 * Valida se um ID de transação é válido
 * Aceita tanto o formato antigo quanto o novo formato com CUID
 */
export function isValidTransactionId(id: string): boolean {
  // Formato antigo: tx_${timestamp}_${random}
  const oldFormat = /^tx_\d+_\d+$/.test(id);

  // Formato novo: tx_${cuid} (CUID tem formato específico)
  const newFormat = /^tx_[a-z0-9]{24}$/.test(id); // CUID tem 24 caracteres

  return oldFormat || newFormat;
}

/**
 * Valida se um End-to-End ID é válido
 * Aceita tanto o formato antigo quanto o novo formato com CUID
 */
export function isValidEndToEndId(id: string): boolean {
  // Formato antigo: E${timestamp}${random}
  const oldFormat = /^E\d{13}\d{6}$/.test(id);

  // Formato novo: E${cuid}
  const newFormat = /^E[a-z0-9]{24}$/.test(id);

  return oldFormat || newFormat;
}
