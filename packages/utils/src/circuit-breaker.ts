/**
 * Circuit breaker implementation for PIX transaction services
 * Provides automatic fallback to legacy code in case of issues
 */

import { logger } from '@repo/logs';

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Circuit is open, failing fast
  HALF_OPEN = 'HALF_OPEN' // Testing if service is back
}

export interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures before opening
  recoveryTimeout: number;     // Time to wait before trying again (ms)
  monitoringWindow: number;    // Time window for failure counting (ms)
  successThreshold: number;    // Successes needed to close circuit
}

export interface CircuitBreakerMetrics {
  state: CircuitState;
  failures: number;
  successes: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  totalRequests: number;
  totalFailures: number;
  totalSuccesses: number;
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failures: number = 0;
  private successes: number = 0;
  private lastFailureTime: number = 0;
  private lastSuccessTime: number = 0;
  private totalRequests: number = 0;
  private totalFailures: number = 0;
  private totalSuccesses: number = 0;
  private failureTimestamps: number[] = [];

  constructor(
    private name: string,
    private config: CircuitBreakerConfig
  ) {}

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    operation: () => Promise<T>,
    fallback?: () => Promise<T>
  ): Promise<T> {
    this.totalRequests++;

    // Check if circuit is open
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        logger.info(`Circuit breaker ${this.name} moving to HALF_OPEN state`);
      } else {
        logger.warn(`Circuit breaker ${this.name} is OPEN, using fallback`);
        if (fallback) {
          return await fallback();
        }
        throw new Error(`Circuit breaker ${this.name} is OPEN and no fallback provided`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      
      // If we have a fallback and circuit is open, use it
      if (this.state === CircuitState.OPEN && fallback) {
        logger.warn(`Circuit breaker ${this.name} failed, using fallback`, { error });
        return await fallback();
      }
      
      throw error;
    }
  }

  private onSuccess(): void {
    this.successes++;
    this.totalSuccesses++;
    this.lastSuccessTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.successes >= this.config.successThreshold) {
        this.reset();
        logger.info(`Circuit breaker ${this.name} reset to CLOSED state`);
      }
    }
  }

  private onFailure(): void {
    this.failures++;
    this.totalFailures++;
    this.lastFailureTime = Date.now();
    this.failureTimestamps.push(this.lastFailureTime);

    // Clean old failure timestamps
    const cutoff = Date.now() - this.config.monitoringWindow;
    this.failureTimestamps = this.failureTimestamps.filter(ts => ts > cutoff);

    // Check if we should open the circuit
    if (this.failureTimestamps.length >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      logger.error(`Circuit breaker ${this.name} opened due to failures`, {
        failures: this.failures,
        threshold: this.config.failureThreshold,
        window: this.config.monitoringWindow
      });
    }
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout;
  }

  private reset(): void {
    this.state = CircuitState.CLOSED;
    this.failures = 0;
    this.successes = 0;
    this.failureTimestamps = [];
  }

  /**
   * Get current metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    return {
      state: this.state,
      failures: this.failures,
      successes: this.successes,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      totalRequests: this.totalRequests,
      totalFailures: this.totalFailures,
      totalSuccesses: this.totalSuccesses
    };
  }

  /**
   * Force circuit state (for testing/emergency)
   */
  forceState(state: CircuitState): void {
    logger.warn(`Circuit breaker ${this.name} state forced to ${state}`);
    this.state = state;
    if (state === CircuitState.CLOSED) {
      this.reset();
    }
  }
}

// Default configurations for different services
export const CIRCUIT_BREAKER_CONFIGS = {
  TRANSACTION_SERVICE: {
    failureThreshold: 5,
    recoveryTimeout: 60000, // 1 minute
    monitoringWindow: 300000, // 5 minutes
    successThreshold: 3
  },
  DATABASE_OPERATIONS: {
    failureThreshold: 3,
    recoveryTimeout: 30000, // 30 seconds
    monitoringWindow: 120000, // 2 minutes
    successThreshold: 2
  },
  EXTERNAL_API: {
    failureThreshold: 10,
    recoveryTimeout: 120000, // 2 minutes
    monitoringWindow: 600000, // 10 minutes
    successThreshold: 5
  }
};

// Global circuit breakers
export const circuitBreakers = {
  transactionService: new CircuitBreaker('TransactionService', CIRCUIT_BREAKER_CONFIGS.TRANSACTION_SERVICE),
  databaseOperations: new CircuitBreaker('DatabaseOperations', CIRCUIT_BREAKER_CONFIGS.DATABASE_OPERATIONS),
  externalApi: new CircuitBreaker('ExternalAPI', CIRCUIT_BREAKER_CONFIGS.EXTERNAL_API)
};
