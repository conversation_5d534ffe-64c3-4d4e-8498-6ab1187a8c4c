import { db } from '@repo/database';
import { generateSecureTransactionId, generateIdempotencyKey } from './transaction-id';
import { logger } from '@repo/logs';

export interface CreateTransactionParams {
  customerEmail: string;
  customerName: string;
  customerPhone?: string;
  customerDocument?: string;
  amount: number;
  organizationId: string;
  description?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
  type: 'CHARGE' | 'SEND';
  pixKey?: string;
  pixKeyType?: string;
}

export interface TransactionResult {
  id: string;
  referenceCode: string;
  status: string;
  amount: number;
  customerEmail: string;
  customerName: string;
  createdAt: Date;
  metadata: Record<string, any>;
  isNew: boolean;
}

/**
 * Cria uma transação de forma atômica, evitando duplicações
 * Usa transação de banco de dados para garantir atomicidade
 *
 * Estratégia diferenciada:
 * - CHARGE: Permite múltiplas transações (cada uma gera QR único)
 * - SEND: Bloqueia duplicatas rigorosamente (dinheiro real envolvido)
 */
export async function createTransactionAtomically(
  params: CreateTransactionParams
): Promise<TransactionResult> {
  const {
    customerEmail,
    customerName,
    customerPhone,
    customerDocument,
    amount,
    organizationId,
    description,
    metadata = {},
    idempotencyKey,
    type,
    pixKey,
    pixKeyType
  } = params;

  // Gerar chave de idempotência se não fornecida
  const finalIdempotencyKey = idempotencyKey || generateIdempotencyKey({
    customerEmail,
    amount,
    organizationId,
    customerDocument,
    description
  });

  logger.info('Creating transaction atomically', {
    customerEmail,
    amount,
    organizationId,
    type,
    idempotencyKey: finalIdempotencyKey
  });

  return await db.$transaction(async (tx) => {
    // 1. Verificar se já existe uma transação com a mesma chave de idempotência
    const existingByKey = await tx.transaction.findFirst({
      where: {
        metadata: {
          path: ['idempotencyKey'],
          equals: finalIdempotencyKey
        },
        organizationId,
        type
      },
      orderBy: { createdAt: 'desc' }
    });

    if (existingByKey) {
      logger.info('Found existing transaction by idempotency key', {
        transactionId: existingByKey.id,
        idempotencyKey: finalIdempotencyKey,
        type
      });

      return {
        id: existingByKey.id,
        referenceCode: existingByKey.referenceCode,
        status: existingByKey.status,
        amount: existingByKey.amount,
        customerEmail: existingByKey.customerEmail,
        customerName: existingByKey.customerName,
        createdAt: existingByKey.createdAt,
        metadata: existingByKey.metadata as Record<string, any>,
        isNew: false
      };
    }

    // 2. Estratégia diferenciada por tipo de transação
    if (type === 'SEND') {
      // Para SEND: Verificação rigorosa de duplicatas
      const existingTransfer = await tx.transaction.findFirst({
        where: {
          customerEmail: customerEmail.toLowerCase().trim(),
          amount,
          organizationId,
          type: 'SEND',
          pixKey,
          pixKeyType,
          status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (existingTransfer) {
        logger.warn('Duplicate SEND transaction detected - blocking creation', {
          existingTransactionId: existingTransfer.id,
          customerEmail,
          amount,
          pixKey: pixKey?.substring(0, 4) + '***',
          pixKeyType
        });

        throw new Error(`Duplicate transfer transaction detected. Transaction ID: ${existingTransfer.id}`);
      }
    } else if (type === 'CHARGE') {
      // Para CHARGE: Verificação mais permissiva (permite múltiplas transações)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
      const existingCharge = await tx.transaction.findFirst({
        where: {
          customerEmail: customerEmail.toLowerCase().trim(),
          amount,
          organizationId,
          type: 'CHARGE',
          status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] },
          createdAt: { gte: tenMinutesAgo },
          // Verificar se tem dados PIX válidos
          metadata: {
            path: ['pixCode'],
            not: null
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (existingCharge) {
        logger.info('Found existing CHARGE transaction with PIX data - returning existing', {
          transactionId: existingCharge.id,
          customerEmail,
          amount,
          hasPixCode: !!(existingCharge.metadata as any)?.pixCode
        });

        return {
          id: existingCharge.id,
          referenceCode: existingCharge.referenceCode,
          status: existingCharge.status,
          amount: existingCharge.amount,
          customerEmail: existingCharge.customerEmail,
          customerName: existingCharge.customerName,
          createdAt: existingCharge.createdAt,
          metadata: existingCharge.metadata as Record<string, any>,
          isNew: false
        };
      }
    }

    // 3. Gerar ID único para nova transação usando CUID
    const referenceCode = generateSecureTransactionId();

    // 4. Criar nova transação
    const newTransaction = await tx.transaction.create({
      data: {
        referenceCode,
        customerName,
        customerEmail: customerEmail.toLowerCase().trim(),
        customerPhone: customerPhone || '',
        customerDocument: customerDocument || '',
        amount,
        status: 'PENDING',
        type,
        description: description || (type === 'CHARGE' ? 'Pagamento via PIX' : 'Transferência PIX'),
        pixKey: pixKey || null,
        pixKeyType: pixKeyType || null,
        metadata: {
          ...metadata,
          idempotencyKey: finalIdempotencyKey,
          createdAt: new Date().toISOString()
        },
        organizationId
      }
    });

    logger.info('Created new transaction atomically', {
      transactionId: newTransaction.id,
      referenceCode: newTransaction.referenceCode,
      idempotencyKey: finalIdempotencyKey
    });

    return {
      id: newTransaction.id,
      referenceCode: newTransaction.referenceCode,
      status: newTransaction.status,
      amount: newTransaction.amount,
      customerEmail: newTransaction.customerEmail,
      customerName: newTransaction.customerName,
      createdAt: newTransaction.createdAt,
      metadata: newTransaction.metadata as Record<string, any>,
      isNew: true
    };
  });
}

/**
 * Atualiza uma transação existente com dados PIX de forma atômica
 */
export async function updateTransactionWithPixData(
  transactionId: string,
  pixData: {
    externalId?: string;
    pixCode?: string;
    pixQrCode?: string;
    pixExpiresAt?: string;
    providerMetadata?: Record<string, any>;
  }
): Promise<void> {
  await db.$transaction(async (tx) => {
    const existingTransaction = await tx.transaction.findUnique({
      where: { id: transactionId }
    });

    if (!existingTransaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    // Atualizar apenas se ainda não tiver dados PIX
    if (!(existingTransaction.metadata as any)?.pixCode) {
      await tx.transaction.update({
        where: { id: transactionId },
        data: {
          externalId: pixData.externalId,
          metadata: {
            ...(existingTransaction.metadata as Record<string, any> || {}),
            pixCode: pixData.pixCode,
            pixQrCode: pixData.pixQrCode,
            pixExpiresAt: pixData.pixExpiresAt,
            ...pixData.providerMetadata,
            updatedAt: new Date().toISOString()
          }
        }
      });

      logger.info('Updated transaction with PIX data', {
        transactionId,
        hasPixCode: !!pixData.pixCode,
        hasPixQrCode: !!pixData.pixQrCode
      });
    } else {
      logger.info('Transaction already has PIX data, skipping update', {
        transactionId,
        existingPixCode: !!(existingTransaction.metadata as any)?.pixCode
      });
    }
  });
}
