/**
 * Production-safe wrapper for transaction services
 * Provides fallback to legacy code and gradual rollout capabilities
 */

import { db } from '@repo/database';
import { logger } from '@repo/logs';
import { ChargeTransactionService } from './charge-transaction-service';
import { SendTransactionService } from './send-transaction-service';
import { generateUniqueTransactionId, generateIdempotencyKey } from './transaction-id';
import { shouldUseNewTransactionServices, shouldUseStrictDuplicatePrevention } from './feature-flags';
import { circuitBreakers } from './circuit-breaker';

export interface CreateTransactionParams {
  customerEmail: string;
  customerName: string;
  customerPhone?: string;
  customerDocument?: string;
  amount: number;
  organizationId: string;
  description?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
  type: 'CHARGE' | 'SEND';
  pixKey?: string;
  pixKeyType?: string;
  allowMultiple?: boolean;
}

export interface TransactionResult {
  id: string;
  referenceCode: string;
  status: string;
  amount: number;
  customerEmail: string;
  customerName: string;
  createdAt: Date;
  metadata: Record<string, any>;
  isNew: boolean;
  pixKey?: string;
  pixKeyType?: string;
  usedNewService?: boolean;
  fallbackReason?: string;
}

/**
 * Create a transaction with automatic fallback to legacy code
 */
export async function createTransactionSafely(
  params: CreateTransactionParams
): Promise<TransactionResult> {
  const { organizationId, type } = params;
  
  // Check if we should use new services
  const useNewServices = shouldUseNewTransactionServices(organizationId);
  
  if (!useNewServices) {
    logger.info('Using legacy transaction creation', { organizationId, type });
    return await createTransactionLegacy(params);
  }

  // Try new service with circuit breaker protection
  try {
    return await circuitBreakers.transactionService.execute(
      async () => {
        if (type === 'CHARGE') {
          const result = await ChargeTransactionService.createChargeTransaction({
            customerEmail: params.customerEmail,
            customerName: params.customerName,
            customerPhone: params.customerPhone,
            customerDocument: params.customerDocument,
            amount: params.amount,
            organizationId: params.organizationId,
            description: params.description,
            metadata: params.metadata,
            idempotencyKey: params.idempotencyKey,
            allowMultiple: params.allowMultiple ?? true
          });
          
          return {
            ...result,
            usedNewService: true
          };
        } else {
          const result = await SendTransactionService.createSendTransaction({
            customerEmail: params.customerEmail,
            customerName: params.customerName,
            amount: params.amount,
            organizationId: params.organizationId,
            pixKey: params.pixKey!,
            pixKeyType: params.pixKeyType!,
            description: params.description,
            metadata: params.metadata,
            idempotencyKey: params.idempotencyKey
          });
          
          return {
            ...result,
            usedNewService: true
          };
        }
      },
      // Fallback to legacy code
      async () => {
        logger.warn('Circuit breaker triggered, falling back to legacy transaction creation', {
          organizationId,
          type
        });
        
        const result = await createTransactionLegacy(params);
        return {
          ...result,
          usedNewService: false,
          fallbackReason: 'circuit_breaker'
        };
      }
    );
  } catch (error) {
    logger.error('New transaction service failed, falling back to legacy', {
      error: error instanceof Error ? error.message : error,
      organizationId,
      type
    });
    
    const result = await createTransactionLegacy(params);
    return {
      ...result,
      usedNewService: false,
      fallbackReason: 'service_error'
    };
  }
}

/**
 * Legacy transaction creation logic (maintains exact compatibility)
 */
async function createTransactionLegacy(
  params: CreateTransactionParams
): Promise<TransactionResult> {
  const {
    customerEmail,
    customerName,
    customerPhone,
    customerDocument,
    amount,
    organizationId,
    description,
    metadata = {},
    type,
    pixKey,
    pixKeyType
  } = params;

  // Use original referenceCode format for compatibility
  const referenceCode = generateUniqueTransactionId();
  
  // Simple duplicate checking for CHARGE transactions (legacy behavior)
  if (type === 'CHARGE') {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const existingTransaction = await db.transaction.findFirst({
      where: {
        customerEmail,
        amount,
        organizationId,
        type: 'CHARGE',
        status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] },
        createdAt: { gte: fiveMinutesAgo }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (existingTransaction) {
      logger.info('Found existing CHARGE transaction (legacy)', {
        transactionId: existingTransaction.id
      });

      return {
        id: existingTransaction.id,
        referenceCode: existingTransaction.referenceCode || '',
        status: existingTransaction.status,
        amount: existingTransaction.amount,
        customerEmail: existingTransaction.customerEmail,
        customerName: existingTransaction.customerName,
        createdAt: existingTransaction.createdAt,
        metadata: existingTransaction.metadata as Record<string, any> || {},
        isNew: false,
        usedNewService: false
      };
    }
  }

  // Create new transaction (legacy format)
  const newTransaction = await db.transaction.create({
    data: {
      referenceCode,
      customerName,
      customerEmail: customerEmail.toLowerCase().trim(),
      customerPhone: customerPhone || '',
      customerDocument: customerDocument || '',
      amount,
      status: 'PENDING',
      type,
      description: description || (type === 'CHARGE' ? 'Pagamento via PIX' : 'Transferência PIX'),
      pixKey: pixKey || null,
      pixKeyType: pixKeyType || null,
      metadata: {
        ...metadata,
        createdAt: new Date().toISOString(),
        legacyService: true
      },
      organizationId
    }
  });

  logger.info('Created transaction using legacy service', {
    transactionId: newTransaction.id,
    referenceCode: newTransaction.referenceCode,
    type
  });

  return {
    id: newTransaction.id,
    referenceCode: newTransaction.referenceCode || '',
    status: newTransaction.status,
    amount: newTransaction.amount,
    customerEmail: newTransaction.customerEmail,
    customerName: newTransaction.customerName,
    createdAt: newTransaction.createdAt,
    metadata: newTransaction.metadata as Record<string, any> || {},
    isNew: true,
    pixKey: newTransaction.pixKey || undefined,
    pixKeyType: newTransaction.pixKeyType || undefined,
    usedNewService: false
  };
}
