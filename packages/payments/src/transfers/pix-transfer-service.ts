import { createId } from "@paralleldrive/cuid2";
import type { PixKeyType } from "@prisma/client";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { checkWithdrawalBlocking } from "@repo/utils/src/withdrawal-blocking";
import { getPaymentProvider } from "../../provider/factory";
// import { getPaymentProvider } from "@repo/payments/provider/factory";
import { calculateTransactionFees } from "../taxes/calculator";

export interface PixTransferRequest {
	amount: number;
	pixKey: string;
	pixKeyType: PixKeyType;
	organizationId: string;
	description?: string;
	gatewayType?: string;
	metadata?: Record<string, any>;
	// Internal fields
	userId?: string;
	requiresTwoFactor?: boolean;
	uniqueId?: string;
}

export interface PixTransferResult {
	id: string;
	status: string;
	amount: number;
	pixKey: string;
	pixKeyType: PixKeyType;
	gatewayType: string;
	totalFee: number;
	totalAmount: number;
	externalId?: string;
	message?: string;
}

/**
 * Shared PIX transfer service that handles the complete transfer flow
 * Used by both API and web app endpoints to ensure consistency
 */
export namespace PixTransferService {
	/**
	 * Process a PIX transfer request
	 */
	export async function processTransfer(
		request: PixTransferRequest,
	): Promise<PixTransferResult> {
		const {
			amount,
			pixKey,
			pixKeyType,
			organizationId,
			description,
			gatewayType,
			metadata = {},
			userId,
			requiresTwoFactor = false,
			uniqueId,
		} = request;

		logger.info("Processing PIX transfer request", {
			organizationId,
			amount,
			pixKey: pixKey.substring(0, 4) + "***", // Mask sensitive data
			pixKeyType,
			requiresTwoFactor,
			userId,
			gatewayType,
			uniqueId,
			requestTimestamp: new Date().toISOString(),
		});

		// Check for unique ID duplication if provided
		if (uniqueId) {
			try {
				const existingTransactionWithUniqueId = await db.transaction.findFirst({
					where: {
						organizationId,
						metadata: {
							path: ["uniqueId"],
							equals: uniqueId,
						},
					},
				});

				if (existingTransactionWithUniqueId) {
					logger.error("Transaction with unique ID already exists", {
						uniqueId,
						existingTransactionId: existingTransactionWithUniqueId.id,
						organizationId,
					});
					throw new Error(
						`Transaction with unique ID '${uniqueId}' already exists`,
					);
				}
			} catch (checkError) {
				if (
					checkError instanceof Error &&
					checkError.message.includes("already exists")
				) {
					throw checkError;
				}
				logger.warn(
					"Error checking for unique ID duplication, proceeding with creation",
					{
						error: checkError,
						uniqueId,
						organizationId,
					},
				);
			}
		}

		// Check for duplicate transactions in the last 5 minutes
		try {
			const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
			const existingTransaction = await db.transaction.findFirst({
				where: {
					organizationId,
					amount,
					pixKey,
					pixKeyType,
					type: "SEND",
					status: { in: ["PENDING", "PROCESSING", "APPROVED"] },
					createdAt: { gte: fiveMinutesAgo },
				},
				orderBy: { createdAt: "desc" },
			});

			if (existingTransaction) {
				// Only return existing transaction if it has the same external characteristics
				// This prevents returning transactions that might have different gateway IDs
				const shouldReturnExisting =
					existingTransaction.externalId &&
					existingTransaction.status !== "REJECTED" &&
					existingTransaction.status !== "CANCELED";

				if (shouldReturnExisting) {
					logger.info("Returning existing transaction to avoid duplication", {
						existingTransactionId: existingTransaction.id,
						existingExternalId: existingTransaction.externalId,
						organizationId,
						amount,
						pixKey,
						status: existingTransaction.status,
					});

					return {
						id: existingTransaction.id,
						status: existingTransaction.status,
						amount: existingTransaction.amount,
						pixKey: existingTransaction.pixKey || "",
						pixKeyType: existingTransaction.pixKeyType || "CPF",
						gatewayType: existingTransaction.gatewayName || "unknown",
						totalFee: existingTransaction.totalFee || 0,
						totalAmount:
							existingTransaction.amount + (existingTransaction.totalFee || 0),
						externalId: existingTransaction.externalId || undefined,
						message: "Existing transaction returned",
					};
				} else {
					logger.warn(
						"Found similar transaction but allowing new one due to missing externalId or failed status",
						{
							existingTransactionId: existingTransaction.id,
							existingExternalId: existingTransaction.externalId,
							existingStatus: existingTransaction.status,
							organizationId,
							amount,
							pixKey,
						},
					);
				}
			}
		} catch (checkError) {
			logger.warn(
				"Error checking for duplicate transactions, proceeding with creation",
				{
					error: checkError,
					organizationId,
					amount,
					pixKey,
				},
			);
		}

		// Validate organization access and status
		await validateOrganization(organizationId);

		// Check withdrawal blocking before proceeding
		await validateWithdrawalBlocking(organizationId);

		// Get the appropriate gateway
		const gateway = await getTransferGateway(organizationId, gatewayType);

		// Calculate fees using centralized system
		const feeCalculation = await calculateTransactionFees(
			organizationId,
			amount,
			"TRANSFER",
			gateway.id,
		);

		const totalAmount = amount + feeCalculation.totalFee;

		logger.info("Fee calculation for PIX transfer", {
			organizationId,
			amount,
			feeCalculation,
			totalAmount,
			gatewayId: gateway.id,
		});

		// Validate balance
		await validateBalance(organizationId, totalAmount);

		// Create transaction and reserve balance
		const transaction = await createTransactionAndReserveBalance({
			amount,
			pixKey,
			pixKeyType,
			organizationId,
			description:
				description || `Transferência PIX para ${pixKeyType} ${pixKey}`,
			gateway,
			feeCalculation,
			totalAmount,
			metadata: {
				...metadata,
				createdBy: userId,
				requiresTwoFactor,
				gatewayType: gateway.type,
				uniqueId,
				feeCalculation: {
					percentFee: feeCalculation.percentFee,
					fixedFee: feeCalculation.fixedFee,
					totalFee: feeCalculation.totalFee,
					transferAmount: amount,
					totalReserved: totalAmount,
					source: feeCalculation.source,
				},
			},
		});

		// Process the transfer with the payment provider
		try {
			const transferResult = await processWithProvider(transaction, gateway, {
				amount,
				pixKey,
				pixKeyType,
				organizationId,
				description:
					description || `Transferência PIX para ${pixKeyType} ${pixKey}`,
			});

			return {
				id: transaction.id,
				status: transferResult.status || "PROCESSING",
				amount,
				pixKey,
				pixKeyType,
				gatewayType: gateway.type,
				totalFee: feeCalculation.totalFee,
				totalAmount,
				externalId: transferResult.externalId,
				message: transferResult.message || "Transfer processed successfully",
			};
		} catch (error) {
			// Log detailed error information for debugging
			logger.error("Transfer processing failed", {
				transactionId: transaction.id,
				organizationId,
				pixKey,
				pixKeyType,
				amount,
				error: error instanceof Error ? error.message : String(error),
				errorType:
					error instanceof Error ? error.constructor.name : typeof error,
			});

			// Handle transfer failure - unreserve balance and update transaction status
			await handleTransferFailure(
				transaction.id,
				totalAmount,
				organizationId,
				error,
			);
			throw error;
		}
	}

	/**
	 * Validate organization exists and is approved
	 */
	async function validateOrganization(organizationId: string): Promise<void> {
		const organization = await db.organization.findUnique({
			where: { id: organizationId },
			select: { status: true, name: true },
		});

		if (!organization) {
			throw new Error("Organization not found");
		}

		if (organization.status !== "APPROVED") {
			throw new Error(`Organization is not approved: ${organization.status}`);
		}
	}

	/**
	 * Validate that withdrawals are not blocked for this organization
	 */
	async function validateWithdrawalBlocking(
		organizationId: string,
	): Promise<void> {
		const blockingResult = await checkWithdrawalBlocking(organizationId);

		if (blockingResult.blocked) {
			logger.warn("Transfer blocked for organization", {
				organizationId,
				blockingType: blockingResult.type,
				message: blockingResult.message,
			});

			throw new Error(
				blockingResult.message ||
					"As transferências estão temporariamente indisponíveis. Entre em contato com o suporte.",
			);
		}
	}

	/**
	 * Get the appropriate gateway for transfers
	 * Uses organization-specific gateways with fallback to environment variables
	 */
	async function getTransferGateway(
		organizationId: string,
		preferredType?: string,
	) {
		try {
			// Try to get organization-specific gateway first
			const paymentProvider = await getPaymentProvider(organizationId, {
				action: "withdrawal",
			});

			// Extract gateway type from provider - use the type property if available
			let gatewayType = "UNKNOWN";
			
			// First, try to get the type from the provider's type property
			if (paymentProvider && typeof paymentProvider === 'object' && 'type' in paymentProvider) {
				gatewayType = (paymentProvider as any).type;
				logger.info("Detected provider type from type property", {
					gatewayType,
					organizationId,
					providerType: paymentProvider.constructor.name
				});
			} else {
				// Fallback: try to detect by constructor name (for providers that don't have type property)
				const providerName = paymentProvider.constructor.name;
				
				// Map provider class names to gateway types
				if (providerName.includes("OwempayV2") || providerName.includes("owempayv2"))
					gatewayType = "OWEMPAY_V2";
				else if (providerName.includes("Owempay") || providerName.includes("owempay"))
					gatewayType = "OWEMPAY";
				else if (providerName.includes("Cartwave") || providerName.includes("cartwave"))
					gatewayType = "CARTWAVE";
				else if (providerName.includes("Transfeera") || providerName.includes("transfeera"))
					gatewayType = "TRANSFEERA";
				else if (providerName.includes("Ecomovi") || providerName.includes("ecomovi"))
					gatewayType = "ECOMOVI";
				else if (providerName.includes("Zendry") || providerName.includes("zendry"))
					gatewayType = "ZENDRY";
				else if (providerName.includes("Mocksim") || providerName.includes("mocksim"))
					gatewayType = "MOCKSIM";
				else if (providerName.includes("Pagarme") || providerName.includes("pagarme"))
					gatewayType = "PAGARME";
				else if (providerName.includes("Zeitbank") || providerName.includes("zeitbank"))
					gatewayType = "ZEITBANK";
				else if (providerName.includes("Microcash") || providerName.includes("microcash"))
					gatewayType = "MICROCASH";
				else if (providerName.includes("Xdpag") || providerName.includes("xdpag"))
					gatewayType = "XDPAG";
				else if (providerName.includes("Pixium") || providerName.includes("pixium"))
					gatewayType = "PIXIUM";
				else if (providerName.includes("Reflowpay") || providerName.includes("reflowpay"))
					gatewayType = "REFLOWPAY";
				else if (providerName.includes("Primepag") || providerName.includes("primepag"))
					gatewayType = "PRIMEPAG";
				else if (providerName.includes("Mediuspag") || providerName.includes("mediuspag"))
					gatewayType = "MEDIUSPAG";
				else if (providerName.includes("PluggouPix") || providerName.includes("pluggouPix"))
					gatewayType = "PLUGGOU_PIX";
				else if (providerName.includes("Flow2pay") || providerName.includes("flow2pay"))
					gatewayType = "FLOW2PAY";
				
				logger.info("Detected provider type from constructor name", {
					gatewayType,
					providerName,
					organizationId
				});
			}

			// If we still couldn't detect the provider type, throw an error
			if (gatewayType === "UNKNOWN") {
				logger.error("Could not detect provider type", {
					providerName: paymentProvider.constructor.name,
					organizationId,
					hasTypeProperty: 'type' in paymentProvider,
					providerKeys: Object.keys(paymentProvider),
					availableMethods: Object.getOwnPropertyNames(paymentProvider.constructor.prototype)
				});
				throw new Error(`Unknown provider type. Please check provider configuration.`);
			}

			// Additional check: if provider has createExternalTransfer function, it's likely OWEMPAY_V2
			if (
				typeof (paymentProvider as any).createExternalTransfer === "function" &&
				gatewayType === "OWEMPAY"
			) {
				gatewayType = "OWEMPAY_V2";
				logger.info(
					"🔍 Detected OWEMPAY_V2 based on createExternalTransfer function",
				);
			}

			// Debug logging to understand what we're getting
			logger.info("🔍 Provider analysis", {
				providerType: paymentProvider.constructor.name,
				gatewayType,
				hasCreateExternalTransfer:
					typeof (paymentProvider as any).createExternalTransfer === "function",
				hasProcessPixWithdrawal:
					typeof paymentProvider.processPixWithdrawal === "function",
			});

			logger.info(`🔍 Using gateway from provider factory: ${gatewayType}`, {
				organizationId,
				providerType: paymentProvider.constructor.name,
				gatewayType,
				source: "Organization-specific or environment fallback",
			});

			// Return gateway object
			const gateway = {
				id: `provider-${gatewayType.toLowerCase()}`,
				name: `${gatewayType} Gateway`,
				type: gatewayType,
				isActive: true,
				canSend: true,
				isGlobal: true,
				priority: 1,
			};

			logger.info(`✅ Using ${gatewayType} gateway for PIX transfer`, {
				gatewayId: gateway.id,
				organizationId,
				reason: `Provider factory selection - ${gatewayType}`,
			});

			return gateway;
		} catch (error) {
			// Fallback to environment variable if provider factory fails
			const envGatewayType =
				process.env.DEFAULT_GATEWAY_TRANSFER?.toUpperCase() || "MICROCASH";

			logger.warn(
				`Failed to get gateway from provider factory, using environment fallback: ${envGatewayType}`,
				{
					organizationId,
					error: error instanceof Error ? error.message : String(error),
					envGatewayType,
					source: "DEFAULT_GATEWAY_TRANSFER environment variable (fallback)",
					availableEnvVars: {
						DEFAULT_GATEWAY_TRANSFER: process.env.DEFAULT_GATEWAY_TRANSFER,
						DEFAULT_GATEWAY_CHARGE: process.env.DEFAULT_GATEWAY_CHARGE,
					}
				},
			);

			// Return a mock gateway object based on environment variable
			const gateway = {
				id: `env-${envGatewayType.toLowerCase()}`,
				name: `${envGatewayType} Gateway`,
				type: envGatewayType,
				isActive: true,
				canSend: true,
				isGlobal: true,
				priority: 1,
			};

			return gateway;
		}
	}

	/**
	 * Validate organization has sufficient balance
	 */
	async function validateBalance(
		organizationId: string,
		requiredAmount: number,
	): Promise<void> {
		const balance = await db.organization_balance.findUnique({
			where: { organizationId },
		});

		if (!balance) {
			// Create zero balance record
			await db.organization_balance.create({
				data: {
					id: createId(),
					organizationId,
					availableBalance: 0,
					pendingBalance: 0,
					reservedBalance: 0,
					updatedAt: new Date(),
				},
			});

			throw new Error("Insufficient balance for this transfer");
		}

		if (Number(balance.availableBalance) < requiredAmount) {
			throw new Error(
				`Insufficient balance: available ${balance.availableBalance}, required ${requiredAmount}`,
			);
		}
	}

	/**
	 * Create transaction record and reserve balance atomically
	 */
	async function createTransactionAndReserveBalance(params: {
		amount: number;
		pixKey: string;
		pixKeyType: PixKeyType;
		organizationId: string;
		description: string;
		gateway: any;
		feeCalculation: any;
		totalAmount: number;
		metadata: Record<string, any>;
	}) {
		const {
			amount,
			pixKey,
			pixKeyType,
			organizationId,
			description,
			gateway,
			feeCalculation,
			totalAmount,
			metadata,
		} = params;

		return await db.$transaction(async (tx) => {
			// Create the transaction record
			const transaction = await tx.transaction.create({
				data: {
					amount,
					status: "PENDING",
					type: "SEND",
					description,
					pixKey,
					pixKeyType,
					customerName: "Transferência PIX",
					customerEmail: metadata.createdBy
						? `user-${metadata.createdBy}@pluggou.io`
						: "<EMAIL>",
					organizationId,
					gatewayName: gateway.name,
					// Store fee information in dedicated fields
					percentFee: feeCalculation.percentFee,
					fixedFee: feeCalculation.fixedFee,
					totalFee: feeCalculation.totalFee,
					netAmount: amount, // For transfers, netAmount is the transfer amount
					metadata,
				},
			});

			// Reserve balance (deduct from available, add to reserved)
			const updatedBalance = await tx.organization_balance.update({
				where: { organizationId },
				data: {
					availableBalance: { decrement: totalAmount },
					reservedBalance: { increment: totalAmount },
				},
			});

			// Record balance history
			await tx.balance_history.create({
				data: {
					id: createId(),
					organizationId,
					transactionId: transaction.id,
					operation: "RESERVE",
					amount: totalAmount,
					description: `Reserva para transferência PIX: ${transaction.id} (valor: ${amount}, taxa: ${feeCalculation.totalFee})`,
					balanceAfterOperation: {
						available: updatedBalance.availableBalance,
						pending: updatedBalance.pendingBalance,
						reserved: updatedBalance.reservedBalance,
					},
					balanceId: updatedBalance.id,
				},
			});

			logger.info("Transaction created and balance reserved", {
				transactionId: transaction.id,
				organizationId,
				amount,
				totalFee: feeCalculation.totalFee,
				totalReserved: totalAmount,
				availableBalance: updatedBalance.availableBalance,
			});

			return transaction;
		});
	}

	/**
	 * Process transfer with payment provider
	 */
	async function processWithProvider(
		transaction: any,
		gateway: any,
		transferData: {
			amount: number;
			pixKey: string;
			pixKeyType: PixKeyType;
			organizationId: string;
			description: string;
		},
	) {
		// Get payment provider
		logger.info("Getting payment provider", {
			organizationId: transferData.organizationId,
			action: "withdrawal",
			gatewayType: gateway.type,
		});

		const paymentProvider = await getPaymentProvider(
			transferData.organizationId,
			{
				action: "withdrawal",
			},
		);

		logger.info(`Processing transfer with provider: ${gateway.type}`, {
			gatewayId: gateway.id,
			transactionId: transaction.id,
			providerType: paymentProvider.constructor.name,
			hasProcessPixWithdrawal:
				typeof paymentProvider.processPixWithdrawal === "function",
		});

		// Update transaction status to PROCESSING
		await db.transaction.update({
			where: { id: transaction.id },
			data: {
				status: "PROCESSING",
				metadata: {
					...((transaction.metadata as any) || {}),
					processingStarted: new Date().toISOString(),
				},
			},
		});

		// Process the withdrawal
		const postbackUrl = `${process.env.NEXT_PUBLIC_POSTBACK_URL}/api/webhooks/${gateway.type.toLowerCase()}`;

		// Prepare transfer data with Cartwave-specific adjustments
		const processedTransferData = { ...transferData };

		// Remove '+' from phone numbers for Cartwave provider
		if (
			gateway.type.toLowerCase() === "cartwave" &&
			transferData.pixKeyType === "PHONE" &&
			transferData.pixKey.startsWith("+")
		) {
			processedTransferData.pixKey = transferData.pixKey.substring(1);
			logger.info("Removed + prefix from phone number for Cartwave", {
				original: transferData.pixKey,
				processed: processedTransferData.pixKey,
				transactionId: transaction.id,
			});
		}

		logger.info("Calling processPixWithdrawal", {
			providerType: paymentProvider.constructor.name,
			transferData: {
				amount: processedTransferData.amount,
				pixKey: processedTransferData.pixKey
					? `${processedTransferData.pixKey.substring(0, 4)}***`
					: undefined,
				pixKeyType: processedTransferData.pixKeyType,
				organizationId: processedTransferData.organizationId,
				description: processedTransferData.description,
				postbackUrl,
				transactionId: transaction.id,
			},
		});

		const withdrawResult = await paymentProvider.processPixWithdrawal({
			...processedTransferData,
			postbackUrl,
			transactionId: transaction.id, // Pass transaction ID to provider
		});

		// Determine the best external ID to use (same logic as below)
		const logExternalId =
			withdrawResult?.transactionId ||
			withdrawResult?.id ||
			withdrawResult?.txid ||
			withdrawResult?.id_envio ||
			withdrawResult?.data?.jobId ||
			withdrawResult?.data?.transferId ||
			withdrawResult?.data?.transactionId;

		logger.info("processPixWithdrawal completed", {
			providerType: paymentProvider.constructor.name,
			hasResult: !!withdrawResult,
			resultKeys: withdrawResult ? Object.keys(withdrawResult) : [],
			status: withdrawResult?.status,
			externalId: logExternalId,
		});

		// Determine the best external ID to use
		// Handle different provider response formats
		const externalId =
			withdrawResult.transactionId ||
			withdrawResult.id ||
			withdrawResult.txid ||
			withdrawResult.id_envio ||
			withdrawResult.data?.jobId ||
			withdrawResult.data?.transferId ||
			withdrawResult.data?.transactionId;

		// Validate that this externalId is not already used by another transaction
		if (externalId) {
			const existingTransactionWithSameExternalId =
				await db.transaction.findFirst({
					where: {
						externalId: externalId,
						id: { not: transaction.id }, // Exclude current transaction
						organizationId: transferData.organizationId,
					},
				});

			if (existingTransactionWithSameExternalId) {
				logger.error(
					"Gateway returned externalId that already exists in another transaction",
					{
						currentTransactionId: transaction.id,
						existingTransactionId: existingTransactionWithSameExternalId.id,
						duplicateExternalId: externalId,
						organizationId: transferData.organizationId,
						gatewayType: gateway.type,
					},
				);

				// This is a critical error - the gateway should not return duplicate IDs
				throw new Error(
					`Gateway returned duplicate external ID: ${externalId}. This transaction may have already been processed.`,
				);
			}
		}

		// Get the idempotencyKey from the provider response or existing metadata
		const idempotencyKey =
			withdrawResult.metadata?.ecomovi?.idempotencyKey ||
			withdrawResult.idempotencyKey ||
			(transaction.metadata as any)?.idempotencyKey ||
			(transaction.metadata as any)?.ecomovi?.idempotencyKey;

		// Extract all possible IDs from provider response (generic approach)
		const allProviderIds = extractAllProviderIds(withdrawResult);
		
		// Update transaction with external ID and provider response
		await db.transaction.update({
			where: { id: transaction.id },
			data: {
				externalId: externalId,
				endToEndId: withdrawResult.endToEndId, // ✅ Salvar no campo correto da tabela
				metadata: {
					...((transaction.metadata as any) || {}),
					// Store essential identifiers (COMPATIBILITY - manter para webhooks existentes)
					id_envio: withdrawResult.id_envio,
					txid: withdrawResult.txid,
					id: withdrawResult.id,
					transactionId: withdrawResult.transactionId,
					endToEndId: withdrawResult.endToEndId, // Manter também no metadata para compatibilidade
					idempotencyKey: idempotencyKey, // Store at root level for easier access
					// Store provider response
					providerResponse: withdrawResult.raw || withdrawResult,
					processingCompleted: new Date().toISOString(),
					
					// COMPATIBILITY: Manter dados específicos para webhooks existentes
					...(gateway.type === "OWEMPAY_V2" && {
						// Dados específicos para owempay-v2 (compatibilidade)
						ecomovi: {
							txId: withdrawResult.txid || withdrawResult.transactionId,
							pixKey: transferData.pixKey,
							pixKeyType: transferData.pixKeyType,
							endToEndId: withdrawResult.endToEndId,
							idempotencyKey: idempotencyKey,
							allIds: {
								transactionId: withdrawResult.transactionId,
								txId: withdrawResult.txid,
								id: withdrawResult.id,
								id_envio: withdrawResult.id_envio,
							},
						},
					}),
					
					// NOVA ESTRATÉGIA GENÉRICA: Store provider-specific data for webhook matching
					[gateway.type.toLowerCase()]: {
						provider: gateway.type,
						pixKey: transferData.pixKey,
						pixKeyType: transferData.pixKeyType,
						endToEndId: withdrawResult.endToEndId,
						idempotencyKey: idempotencyKey,
						// Store all possible IDs for webhook matching
						allIds: allProviderIds,
					},
					// NOVA ESTRATÉGIA GENÉRICA: Store all IDs at root level for easier webhook matching
					allProviderIds: allProviderIds,
				},
			},
		});

		// Log the external ID update for debugging
		logger.info("Transaction external ID and metadata updated", {
			transactionId: transaction.id,
			externalId: externalId,
			idempotencyKey: idempotencyKey,
			allIds: allProviderIds,
			provider: gateway.type,
			webhookSearchHints: {
				primaryExternalId: externalId,
				idempotencyKey: idempotencyKey,
				endToEndId: withdrawResult.endToEndId,
				pixKey: transferData.pixKey,
				amount: transferData.amount,
				// All extracted IDs for webhook matching
				...allProviderIds,
			},
		});

		return {
			status: withdrawResult.status || "PROCESSING",
			externalId:
				withdrawResult.id_envio ||
				withdrawResult.id ||
				withdrawResult.txid ||
				withdrawResult.transactionId,
			message: "Transfer processed successfully",
		};
	}

	/**
	 * Extract all possible IDs from provider response (generic approach)
	 * This function works for any provider by extracting all possible ID fields
	 */
	function extractAllProviderIds(providerResponse: any): Record<string, any> {
		const allIds: Record<string, any> = {};
		
		// Common ID fields that most providers use
		const commonIdFields = [
			'transactionId', 'txid', 'id', 'id_envio', 'endToEndId',
			'tid', 'pagamentoIndiretoId', 'referenceCode', 'externalId',
			'jobId', 'transferId', 'paymentId', 'orderId', 'requestId',
			'correlationId', 'idempotencyKey', 'uniqueId'
		];
		
		// Extract common ID fields
		commonIdFields.forEach(field => {
			if (providerResponse[field] !== undefined && providerResponse[field] !== null) {
				allIds[field] = providerResponse[field];
			}
		});
		
		// Extract IDs from nested objects (like data, result, etc.)
		const nestedObjects = ['data', 'result', 'response', 'payload'];
		nestedObjects.forEach(nestedKey => {
			if (providerResponse[nestedKey] && typeof providerResponse[nestedKey] === 'object') {
				commonIdFields.forEach(field => {
					if (providerResponse[nestedKey][field] !== undefined && providerResponse[nestedKey][field] !== null) {
						allIds[field] = providerResponse[nestedKey][field];
					}
				});
			}
		});
		
		// Extract any field that looks like an ID (contains 'id', 'Id', 'ID' in the name)
		Object.keys(providerResponse).forEach(key => {
			if (key.toLowerCase().includes('id') && 
				providerResponse[key] !== undefined && 
				providerResponse[key] !== null &&
				typeof providerResponse[key] === 'string') {
				allIds[key] = providerResponse[key];
			}
		});
		
		// Remove duplicates and null/undefined values
		const cleanIds: Record<string, any> = {};
		Object.keys(allIds).forEach(key => {
			if (allIds[key] !== null && allIds[key] !== undefined && allIds[key] !== '') {
				cleanIds[key] = allIds[key];
			}
		});
		
		logger.info("Extracted provider IDs", {
			providerResponseKeys: Object.keys(providerResponse),
			extractedIds: cleanIds,
			totalIdsFound: Object.keys(cleanIds).length
		});
		
		return cleanIds;
	}

	/**
	 * Handle transfer failure by unreserving balance
	 */
	async function handleTransferFailure(
		transactionId: string,
		totalAmount: number,
		organizationId: string,
		error: any,
	): Promise<void> {
		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";
		const errorType =
			error instanceof Error ? error.constructor.name : typeof error;

		logger.error("Handling transfer failure", {
			transactionId,
			organizationId,
			totalAmount,
			error: errorMessage,
			errorType,
		});

		try {
			// Get current transaction metadata to preserve existing data
			const currentTransaction = await db.transaction.findUnique({
				where: { id: transactionId },
				select: { metadata: true },
			});

			const currentMetadata = (currentTransaction?.metadata as any) || {};

			// Determine if this is a PIX key validation error
			// Check custom error properties first (from Ecomovi provider)
			const isPixKeyError =
				(error as any)?.isPixKeyError === true ||
				errorMessage.toLowerCase().includes("chave") ||
				errorMessage.toLowerCase().includes("key") ||
				errorMessage.toLowerCase().includes("invalid") ||
				errorMessage.toLowerCase().includes("inexistente") ||
				errorMessage.toLowerCase().includes("inválida") ||
				errorMessage.toLowerCase().includes("not found") ||
				(error as any)?.statusCode === 400 ||
				(error as any)?.statusCode === 404 ||
				errorMessage.includes("400") ||
				errorMessage.includes("404");

			// Update transaction status to CANCELED with detailed error information
			await db.transaction.update({
				where: { id: transactionId },
				data: {
					status: "CANCELED",
					metadata: {
						...currentMetadata,
						error: errorMessage,
						errorType: errorType,
						errorAt: new Date().toISOString(),
						status: "CANCELED",
						failureReason: isPixKeyError
							? "INVALID_PIX_KEY"
							: "PROCESSING_ERROR",
						isPixKeyError: isPixKeyError,
						processingFailed: true,
					},
				},
			});

			// Unreserve balance (add back to available, remove from reserved)
			const updatedBalance = await db.organization_balance.update({
				where: { organizationId },
				data: {
					availableBalance: { increment: totalAmount },
					reservedBalance: { decrement: totalAmount },
				},
			});

			// Record balance history
			await db.balance_history.create({
				data: {
					id: createId(),
					organizationId,
					transactionId,
					operation: "UNRESERVE",
					amount: totalAmount,
					description: `Devolução de reserva por falha na transferência: ${transactionId}`,
					balanceAfterOperation: {
						available: updatedBalance.availableBalance,
						pending: updatedBalance.pendingBalance,
						reserved: updatedBalance.reservedBalance,
					},
					balanceId: updatedBalance.id,
				},
			});

			logger.info("Balance unreserved after transfer failure", {
				transactionId,
				organizationId,
				amount: totalAmount,
			});
		} catch (cleanupError) {
			logger.error("Error during transfer failure cleanup", {
				transactionId,
				organizationId,
				originalError: error instanceof Error ? error.message : "Unknown error",
				cleanupError:
					cleanupError instanceof Error
						? cleanupError.message
						: "Unknown cleanup error",
			});
		}
	}
}
