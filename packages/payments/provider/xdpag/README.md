# XDPAG Provider

Este módulo implementa a integração com o gateway de pagamentos XDPAG.

## Funcionalidades

- ✅ **PIX IN (Recebimento)**: Criação de QR codes dinâmicos para recebimento de pagamentos PIX
- ✅ **PIX OUT (Envio)**: Transferências PIX para chaves PIX
- ✅ **Consulta de Saldo**: Verificação do saldo disponível na conta
- ✅ **Status de Transações**: Consulta do status de transações
- ✅ **Webhooks**: Processamento de eventos de pagamento em tempo real
- ❌ **Reembolsos**: Não suportado pela API XDPAG (requer contato com suporte)

## Configuração

### Variáveis de Ambiente

```bash
# XDPAG API Configuration
XDPAG_USERNAME=your_username_here
XDPAG_PASSWORD=your_password_here
XDPAG_ENVIRONMENT=sandbox
XDPAG_WEBHOOK_SECRET=your_webhook_secret_here
XDPAG_BYPASS_SIGNATURE_VALIDATION=false
```

### Credenciais no Banco de Dados

```json
{
  "username": "your_username_here",
  "password": "your_password_here",
  "environment": "sandbox",
  "webhookSecret": "your_webhook_secret_here"
}
```

## API Endpoints

### PIX IN (Recebimento)

**Endpoint**: `POST /api/order/pay-in`

**Payload**:
```json
{
  "amount": "10.00",
  "webhook": "https://your-domain.com/api/webhooks/xdpag",
  "externalId": "your-internal-id",
  "description": "Pagamento PIX",
  "additional_data": [
    {
      "name": "Cliente",
      "value": "Nome do Cliente"
    }
  ]
}
```

**Resposta**:
```json
{
  "data": {
    "id": "transaction-id",
    "status": "CREATED",
    "externalId": "your-internal-id",
    "brcode": "pix-br-code",
    "qrcode": "qr-code-image"
  }
}
```

### PIX OUT (Envio)

**Endpoint**: `POST /api/order/pay-out`

**Payload**:
```json
{
  "amount": 10.00,
  "webhook": "https://your-domain.com/api/webhooks/xdpag",
  "document": "12345678901",
  "pixKey": "pix-key",
  "pixKeyType": "CPF",
  "externalId": "your-internal-id",
  "validate_document": false
}
```

**Resposta**:
```json
{
  "data": {
    "id": "transaction-id",
    "status": "CREATED",
    "externalId": "your-internal-id",
    "events": []
  }
}
```

### Consulta de Saldo

**Endpoint**: `GET /api/order/balance`

**Resposta**:
```json
{
  "data": {
    "balance": 1000.00,
    "balanceProvisioned": 950.00,
    "currency": "BRL"
  }
}
```

## Webhooks

### Estrutura do Webhook

**URL**: `https://your-domain.com/api/webhooks/xdpag`

**Headers**:
- `Content-Type: application/json`
- `x-xdpag-signature: signature` (opcional)

**Payload PAYIN**:
```json
{
  "type": "PAYIN",
  "data": {
    "id": "transaction-id",
    "externalId": "your-internal-id",
    "amount": "10.00",
    "status": "FINISHED",
    "endToEndId": "E12345678901234567890",
    "fee": "0.06"
  }
}
```

**Payload PAYOUT**:
```json
{
  "type": "PAYOUT",
  "data": {
    "id": "transaction-id",
    "externalId": "your-internal-id",
    "amount": "10.00",
    "status": "FINISHED",
    "endToEndId": "E12345678901234567890",
    "fee": "0.00"
  }
}
```

## Status de Transações

| Status XDPAG | Status Interno | Descrição |
|--------------|----------------|-----------|
| CREATED | PENDING | Transação criada |
| PROCESSING | PROCESSING | Processando |
| FINISHED | APPROVED | Concluída com sucesso |
| CANCELLED | CANCELLED | Cancelada |
| REVERSED | REFUNDED | Revertida |
| PARTIALLY_REVERSED | PARTIALLY_REFUNDED | Parcialmente revertida |
| TIMEOUT | EXPIRED | Expirada |

## Limites

- **Valor Mínimo**: R$ 0,01
- **Valor Máximo**: R$ 50.000,00 (configurável)
- **Moeda**: BRL (Real Brasileiro)

## Tipos de Chave PIX Suportados

- **CPF**: CPF
- **CNPJ**: CNPJ
- **EMAIL**: E-mail
- **PHONE**: Telefone
- **EVP**: Chave aleatória

## Tratamento de Erros

### Erros Comuns

- `INVALID_PIX_KEY`: Chave PIX inválida
- `OUT_DISABLED_BY_SECURITY`: Transferência desabilitada por limite de segurança
- `AMOUNT_TOO_LOW`: Valor abaixo do mínimo
- `LIMIT_EXCEEDED_PER_TX`: Valor acima do limite máximo

### Validação de Assinatura

O webhook suporta validação de assinatura HMAC-SHA256. Configure a variável `XDPAG_WEBHOOK_SECRET` para ativar a validação.

## Logs

O provider gera logs detalhados para:
- Criação de transações
- Processamento de webhooks
- Consultas de saldo
- Erros e exceções

## Exemplo de Uso

```typescript
import { getPaymentProvider } from '@repo/payments/provider/factory';

// Criar pagamento PIX
const provider = await getPaymentProvider(organizationId, { action: 'charge' });
const payment = await provider.createPixPayment({
  amount: 100.00,
  customerName: 'João Silva',
  customerEmail: '<EMAIL>',
  description: 'Pagamento de serviço',
  organizationId: 'org-123',
  postbackUrl: 'https://your-domain.com/webhook'
});

// Processar transferência PIX
const transfer = await provider.processPixWithdrawal({
  amount: 50.00,
  pixKey: '11999999999',
  pixKeyType: 'PHONE',
  organizationId: 'org-123',
  description: 'Transferência para cliente'
});
```
