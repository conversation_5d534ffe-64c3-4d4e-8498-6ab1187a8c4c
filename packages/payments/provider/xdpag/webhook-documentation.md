# XDPAG Webhook Documentation

## Visão Geral

O XDPAG envia webhooks para notificar sobre mudanças de status em transações PIX (PAYIN e PAYOUT). Os webhooks são enviados para a URL configurada no momento da criação da transação.

## Configuração do Webhook

### URL do Webhook
```
https://your-domain.com/api/webhooks/xdpag
```

### Headers Esperados
```
Content-Type: application/json
x-xdpag-signature: <signature> (opcional)
```

### Validação de Assinatura
- **Método**: HMAC-SHA256
- **Chave**: Configurada em `XDPAG_WEBHOOK_SECRET`
- **Header**: `x-xdpag-signature`
- **Status**: **DESABILITADA POR PADRÃO** para facilitar deploy inicial
- **Configuração**: `XDPAG_BYPASS_SIGNATURE_VALIDATION=true` (padrão)

## Estrutura dos Webhooks

### PAYIN (Recebimento de PIX)

**Evento**: `PAYIN`

**Payload**:
```json
{
  "type": "PAYIN",
  "data": {
    "id": "f466fef7-bad2-4ff3-a50f-bd95cf2bbbf6",
    "externalId": "f466fef7-bad2-4ff3-a50f-bd95cf2bbbf6",
    "amount": "20.00",
    "document": "",
    "original_amount": "20.00",
    "status": "FINISHED",
    "endToEndId": "E********202412271326s1360618214",
    "receipt": "",
    "fee": "0.06",
    "metadata": [],
    "refunds": []
  }
}
```

**Campos**:
- `id`: ID da transação no XDPAG
- `externalId`: ID externo fornecido na criação
- `amount`: Valor da transação
- `status`: Status da transação
- `endToEndId`: ID de ponta-a-ponta do PIX
- `fee`: Taxa cobrada
- `receipt`: URL do comprovante (se disponível)

### PAYOUT (Envio de PIX)

**Evento**: `PAYOUT`

**Payload**:
```json
{
  "type": "PAYOUT",
  "data": {
    "id": "577b7e1c-248c-4b1e-a6d8-e79e24c4ec8e",
    "externalId": "b909a9b0-91f3-4488-b3d4-e314aeb0b75b",
    "amount": "1.00",
    "document": "***********",
    "original_amount": "1.00",
    "status": "FINISHED",
    "endToEndId": "E5440356320241128165123v3Na7HbBl",
    "receipt": "https://api.xdpag.com/receipt/E5440356320241128165123v3Na7HbBl/payout",
    "fee": "0.00",
    "metadata": {
      "authCode": "577b7e1c-248c-4b1e-a6d8-e79e24c4ec8e",
      "amount": "1.00",
      "paymentDateTime": "2024-11-28T13:53:56.000Z",
      "pixKey": "***********",
      "receiveName": "Lucas Araujo",
      "receiverName": "Lucas Araujo",
      "receiverBankName": "********",
      "receiverDocument": "***********",
      "receiveAgency": "1",
      "receiveAccount": "********",
      "payerName": "VITALCRED MEIOS DE PAGAMENTOS SA",
      "payerAgency": "001",
      "payerAccount": "38805-7",
      "payerDocument": "**************",
      "payerBankName": "BCO ARBI S.A.",
      "createdAt": "2024-11-28T16:53:50.000000Z",
      "endToEnd": "E5440356320241128165123v3Na7HbBl"
    },
    "reason_cancelled": ""
  }
}
```

**Campos**:
- `id`: ID da transação no XDPAG
- `externalId`: ID externo fornecido na criação
- `amount`: Valor da transação
- `status`: Status da transação
- `endToEndId`: ID de ponta-a-ponta do PIX
- `fee`: Taxa cobrada
- `receipt`: URL do comprovante
- `metadata`: Informações detalhadas da transação
- `reason_cancelled`: Motivo do cancelamento (se aplicável)

## Status das Transações

| Status XDPAG | Descrição | Status Interno |
|--------------|-----------|----------------|
| `CREATED` | Transação criada | `PENDING` |
| `PROCESSING` | Processando | `PROCESSING` |
| `FINISHED` | Concluída com sucesso | `APPROVED` |
| `CANCELLED` | Cancelada | `CANCELLED` |
| `REVERSED` | Revertida | `REFUNDED` |
| `PARTIALLY_REVERSED` | Parcialmente revertida | `PARTIALLY_REFUNDED` |
| `TIMEOUT` | Expirada | `EXPIRED` |

## Processamento do Webhook

### 1. Validação de Assinatura
```typescript
const signature = req.headers['x-xdpag-signature'];
const isValid = validateXdpagWebhookSignature(body, signature, webhookSecret);
```

### 2. Extração de Dados
```typescript
const { type, data } = payload;
const eventType = type; // PAYIN ou PAYOUT
const transactionId = data.id;
const externalId = data.externalId;
const status = data.status;
const amount = parseFloat(data.amount);
```

### 3. Busca da Transação
```typescript
let transaction = await db.transaction.findFirst({
  where: { externalId }
});

if (!transaction && transactionId) {
  transaction = await db.transaction.findFirst({
    where: {
      OR: [
        { id: transactionId },
        { externalId: transactionId }
      ]
    }
  });
}
```

### 4. Atualização do Status
```typescript
const internalStatus = mapXdpagStatusToInternal(status);

await db.transaction.update({
  where: { id: transaction.id },
  data: {
    status: internalStatus,
    updatedAt: new Date(),
    metadata: {
      ...transaction.metadata,
      lastWebhookEvent: eventType,
      lastWebhookReceived: new Date().toISOString(),
      endToEndId: data.endToEndId
    }
  }
});
```

### 5. Atualização do Saldo

**Para PAYIN aprovado**:
```typescript
if (internalStatus === TransactionStatus.APPROVED && eventType === 'PAYIN') {
  const fees = await calculateTransactionFees({
    amount: amount,
    transactionType: 'CHARGE',
    organizationId: transaction.organizationId
  });

  const netAmount = amount - fees.totalFee;

  await updateOrganizationBalance(
    transaction.organizationId,
    netAmount,
    BalanceOperationType.CREDIT,
    transaction.id,
    `PIX recebido via XDPAG - ${eventType}`
  );
}
```

**Para PAYOUT aprovado**:
```typescript
if (internalStatus === TransactionStatus.APPROVED && eventType === 'PAYOUT') {
  const fees = await calculateTransactionFees({
    amount: amount,
    transactionType: 'SEND',
    organizationId: transaction.organizationId
  });

  const totalAmount = amount + fees.totalFee;

  await updateOrganizationBalance(
    transaction.organizationId,
    totalAmount,
    BalanceOperationType.DEBIT,
    transaction.id,
    `PIX enviado via XDPAG - ${eventType}`
  );
}
```

## Tratamento de Erros

### Erros Comuns
- **Transação não encontrada**: Retorna 404
- **Assinatura inválida**: Retorna 401
- **Payload inválido**: Retorna 400
- **Erro interno**: Retorna 500

### Logs
Todos os webhooks são logados com informações detalhadas:
- Headers recebidos
- Payload completo
- Dados extraídos
- Resultado do processamento
- Erros (se houver)

## Teste de Webhook

### Usando ngrok (desenvolvimento)
```bash
# Instalar ngrok
npm install -g ngrok

# Expor porta local
ngrok http 3000

# Usar URL do ngrok no webhook
https://abc123.ngrok.io/api/webhooks/xdpag
```

### Usando curl (teste)
```bash
curl -X POST https://your-domain.com/api/webhooks/xdpag \
  -H "Content-Type: application/json" \
  -H "x-xdpag-signature: your-signature" \
  -d '{
    "type": "PAYIN",
    "data": {
      "id": "test-123",
      "externalId": "test-external-123",
      "amount": "10.00",
      "status": "FINISHED",
      "endToEndId": "E123456789"
    }
  }'
```

## Monitoramento

### Métricas Importantes
- Taxa de sucesso dos webhooks
- Tempo de processamento
- Erros de validação de assinatura
- Transações não encontradas

### Alertas Recomendados
- Webhook falhando consecutivamente
- Assinatura inválida frequente
- Transações não encontradas
- Erros de atualização de saldo
