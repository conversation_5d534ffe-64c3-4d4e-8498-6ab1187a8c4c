import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getGatewayCredentials } from "../factory";
import * as crypto from "crypto";

// Base URL for XDPAG API
const XDPAG_API_BASE_URL = {
  production: "https://api.xdpag.com",
  sandbox: "https://api.xdpag.com", // Same URL for both environments
};

// API paths for XDPAG
const XDPAG_API_PATHS = {
  login: "/api/account/login",
  payin: "/api/order/pay-in",
  payout: "/api/order/pay-out",
  getPayin: "/api/order/pay-in/:id",
  getPayout: "/api/order/pay-out/:id",
  balance: "/api/order/balance",
};

// Token cache interface
interface TokenCache {
  token: string;
  expiresAt: number;
  organizationId: string;
}

// In-memory token cache (in production, consider using Redis)
const tokenCache = new Map<string, TokenCache>();

// XDPAG transaction limits configuration
const XDPAG_LIMITS = {
  MIN_AMOUNT: 0.01, // R$ 0.01 - minimum PIX amount
  MAX_AMOUNT: 50000, // R$ 50,000 - conservative maximum limit
  CURRENCY: 'BRL',
} as const;

// Helper function to validate transaction amounts
function validateXdpagAmount(amount: number): { isValid: boolean; error?: Error } {
  if (amount < XDPAG_LIMITS.MIN_AMOUNT) {
    const error = new Error(`Valor mínimo para transação PIX é R$ ${XDPAG_LIMITS.MIN_AMOUNT.toFixed(2)}`);
    (error as any).code = "AMOUNT_TOO_LOW";
    return { isValid: false, error };
  }

  if (amount > XDPAG_LIMITS.MAX_AMOUNT) {
    const error = new Error(`Valor excede o limite máximo permitido por transação (R$ ${XDPAG_LIMITS.MAX_AMOUNT.toLocaleString('pt-BR')}). Por favor, reduza o valor ou entre em contato com o suporte.`);
    (error as any).code = "LIMIT_EXCEEDED_PER_TX";
    return { isValid: false, error };
  }

  return { isValid: true };
}

// Helper function to truncate description to XDPAG limit
function truncateDescription(description: string | undefined, maxLength: number = 100): string {
  if (!description) {
    return "Pagamento PIX";
  }

  if (description.length <= maxLength) {
    return description;
  }

  const truncated = description.substring(0, maxLength - 3) + "...";
  logger.warn("Description truncated for XDPAG API", {
    originalLength: description.length,
    truncatedLength: truncated.length,
    originalDescription: description,
    truncatedDescription: truncated
  });

  return truncated;
}

// Login to XDPAG and get JWT token
export async function loginXdpag(organizationId: string): Promise<string> {
  try {
    logger.info(`Logging in to XDPAG for organization ${organizationId}`);

    // Check if we have a valid cached token
    const cached = tokenCache.get(organizationId);
    if (cached && cached.expiresAt > Date.now()) {
      logger.info(`Using cached XDPAG token for organization ${organizationId}`);
      return cached.token;
    }

    const credentials = await getXdpagCredentials(organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    // Prepare login payload
    const loginPayload = {
      username: credentials.username,
      password: credentials.password
    };

    logger.info("Making login request to XDPAG", {
      url: `${baseUrl}${XDPAG_API_PATHS.login}`,
      username: credentials.username
    });

    const response = await makeHTTPRequest(`${baseUrl}${XDPAG_API_PATHS.login}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify(loginPayload),
      timeout: 15000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("XDPAG login failed", {
        status: response.status,
        body: errorText
      });
      throw new Error(`XDPAG login failed: ${response.status} - ${errorText}`);
    }

    const loginData = await response.json();
    const accessToken = loginData.access_token;
    const expiresIn = loginData.expires_in || 3600; // Default to 1 hour
    const expiresAt = Date.now() + (expiresIn * 1000) - 60000; // Subtract 1 minute for safety

    if (!accessToken) {
      throw new Error("No access token received from XDPAG login");
    }

    // Cache the token
    tokenCache.set(organizationId, {
      token: accessToken,
      expiresAt: expiresAt,
      organizationId: organizationId
    });

    logger.info("XDPAG login successful", {
      organizationId,
      tokenLength: accessToken.length,
      expiresIn: expiresIn,
      expiresAt: new Date(expiresAt).toISOString()
    });

    return accessToken;
  } catch (error) {
    logger.error("Error logging in to XDPAG", { error, organizationId });
    throw error;
  }
}

// Helper to get the XDPAG credentials for an organization
export async function getXdpagCredentials(organizationId: string): Promise<{
  username: string;
  password: string;
  environment: "production" | "sandbox";
}> {
  try {
    logger.info(`Getting XDPAG credentials for organization ${organizationId}`);

    const credentials = await getGatewayCredentials(organizationId, "XDPAG");

    if (!credentials) {
      throw new Error("XDPAG credentials not found");
    }

    const username = credentials.username;
    const password = credentials.password;

    logger.info("XDPAG credentials retrieved", {
      organizationId,
      hasUsername: !!username,
      hasPassword: !!password,
      usernameLength: username?.length || 0,
      passwordLength: password?.length || 0,
      environment: credentials.environment
    });

    if (!username || !password) {
      logger.error("XDPAG credentials incomplete", {
        organizationId,
        username: username || 'MISSING',
        password: password ? 'PRESENT' : 'MISSING',
        credentialsKeys: Object.keys(credentials)
      });
      throw new Error("XDPAG credentials incomplete - missing username or password");
    }

    return {
      username: username as string,
      password: password as string,
      environment: (credentials.environment as "production" | "sandbox") || "sandbox"
    };
  } catch (error) {
    logger.error("Error getting XDPAG credentials", { error, organizationId });
    throw error;
  }
}

// Helper function to make HTTP requests
export async function makeHTTPRequest(url: string, options: {
  method: string;
  headers: Record<string, string>;
  body?: string;
  timeout?: number;
}): Promise<{ ok: boolean; status: number; json: () => Promise<any>; text: () => Promise<string> }> {
  const controller = new AbortController();
  const timeoutId = options.timeout ? setTimeout(() => controller.abort(), options.timeout) : null;

  try {
    const response = await fetch(url, {
      method: options.method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: options.body,
      signal: controller.signal,
    });

    if (timeoutId) clearTimeout(timeoutId);

    return {
      ok: response.ok,
      status: response.status,
      json: () => response.json(),
      text: () => response.text(),
    };
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    throw error;
  }
}

// Test connection to XDPAG API
export async function testXdpagConnection(organizationId: string): Promise<any> {
  try {
    logger.info("Testing connection to XDPAG API");

    // Get JWT token
    const token = await loginXdpag(organizationId);
    const credentials = await getXdpagCredentials(organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    // Test with balance endpoint using Bearer token
    const response = await makeHTTPRequest(`${baseUrl}${XDPAG_API_PATHS.balance}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json"
      },
      timeout: 10000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("XDPAG connection test failed", {
        status: response.status,
        body: errorText
      });
      throw new Error(`XDPAG connection test failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    logger.info("XDPAG connection test successful", { data });
    return data;
  } catch (error) {
    logger.error("Error testing XDPAG connection", { error });
    throw error;
  }
}

// Get account balance from XDPAG API
export async function getXdpagBalance(organizationId: string): Promise<any> {
  try {
    logger.info("Getting XDPAG account balance", { organizationId });

    // Get JWT token
    const token = await loginXdpag(organizationId);
    const credentials = await getXdpagCredentials(organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    const response = await makeHTTPRequest(`${baseUrl}${XDPAG_API_PATHS.balance}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json"
      },
      timeout: 10000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("XDPAG balance request failed", {
        status: response.status,
        body: errorText
      });
      throw new Error(`XDPAG balance request failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    logger.info("XDPAG balance retrieved successfully", {
      balance: data.data?.balance,
      balanceProvisioned: data.data?.balanceProvisioned
    });

    return {
      success: true,
      data: {
        balance: data.data?.balance || 0,
        balanceProvisioned: data.data?.balanceProvisioned || 0,
        currency: data.data?.currency || 'BRL'
      }
    };
  } catch (error) {
    logger.error("Error getting XDPAG balance", { error, organizationId });
    throw error;
  }
}

// Create a Pix payment (charge) - PIX IN
export async function createPixPayment(params: {
  amount: number;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  postbackUrl?: string;
  organizationId: string;
  externalCode?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
}): Promise<any> {
  try {
    logger.info("Creating XDPAG PIX payment", { params: { ...params, amount: params.amount } });

    // Pre-validate amount
    const amountValidation = validateXdpagAmount(params.amount);
    if (!amountValidation.isValid) {
      throw amountValidation.error;
    }

    // Get JWT token
    const token = await loginXdpag(params.organizationId);
    const credentials = await getXdpagCredentials(params.organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    logger.info("XDPAG Bearer token obtained", {
      organizationId: params.organizationId,
      tokenLength: token.length,
      tokenPrefix: token.substring(0, 20)
    });

    // Generate internal transaction ID if not provided
    const internalTransactionId = params.externalCode || `xdpag_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Prepare the request payload according to XDPAG API documentation
    const webhookUrl = 'https://app.pluggou.io/api/webhooks/xdpag';

    const payload = {
      amount: params.amount.toString(), // XDPAG expects string
      webhook: webhookUrl,
      externalId: internalTransactionId,
      description: truncateDescription(params.description),
      additional_data: [
        {
          name: "Cliente",
          value: params.customerName
        },
        ...(params.customerEmail ? [{
          name: "Email",
          value: params.customerEmail
        }] : []),
        ...(params.customerPhone ? [{
          name: "Telefone",
          value: params.customerPhone
        }] : []),
        ...(params.customerDocument ? [{
          name: "Documento",
          value: params.customerDocument
        }] : [])
      ]
    };

    logger.info("Making request to XDPAG", {
      url: `${baseUrl}${XDPAG_API_PATHS.payin}`,
      webhookUrl: webhookUrl,
      payload,
      headers: {
        "Authorization": `Bearer ${token.substring(0, 20)}...`,
        "Content-Type": "application/json"
      }
    });

    const response = await makeHTTPRequest(`${baseUrl}${XDPAG_API_PATHS.payin}`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify(payload),
      timeout: 30000
    });

    logger.info("XDPAG response received", {
      status: response.status
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("XDPAG API error response", {
        status: response.status,
        body: errorText
      });

      // Parse error response to handle specific error codes
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (parseError) {
        throw new Error(`XDPAG API error: ${response.status} - ${errorText}`);
      }

      throw new Error(`XDPAG API error: ${response.status} - ${errorText}`);
    }

    const responseData = await response.json();
    logger.info("XDPAG payment created successfully", {
      responseData
    });

    // Extract transaction data from XDPAG response
    const transactionData = responseData.data;

    // Extract PIX data from response
    const pixCode = transactionData?.brcode; // PIX BR Code
    const qrCode = transactionData?.qrcode; // QR Code image
    const externalId = transactionData?.id; // Transaction ID
    const status = transactionData?.status; // Transaction status

    logger.info("PIX payment created successfully with XDPAG", {
      transactionId: internalTransactionId,
      externalId: externalId,
      pixCode: pixCode ? "present" : "missing",
      qrCode: qrCode ? "present" : "missing",
      status: status
    });

    // Validate PIX data is present
    if (!pixCode) {
      logger.error("Missing PIX data in XDPAG response", {
        hasPixCode: !!pixCode,
        responseData,
        transactionData
      });
      throw new Error("PIX data missing in XDPAG response");
    }

    // Find the gateway ID for this organization
    const gatewayId = (await db.payment_gateway.findFirst({
      where: {
        type: "XDPAG",
        isActive: true,
        canReceive: true,
        organization_gateway: {
          some: {
            organizationId: params.organizationId,
            isActive: true
          }
        }
      },
      orderBy: { priority: 'asc' }
    }))?.id;

    // NOTE: Transaction is already created by the router via ChargeTransactionService
    // We only need to return the PIX data for the existing transaction
    logger.info("Skipping transaction creation - transaction already created by router", {
      externalId: externalId,
      organizationId: params.organizationId,
      amount: params.amount,
      reason: "Router creates transaction via ChargeTransactionService"
    });

    // Return payment data in the format expected by the router
    return {
      success: true,
      externalId: externalId,
      pixCode: pixCode,
      pixQrCode: qrCode,
      pixExpiresAt: null, // XDPAG doesn't provide expiration date
      metadata: {
        xdpag: {
          transactionId: externalId,
          status: status,
          provider: "XDPAG",
        },
      },
    };
  } catch (error) {
    logger.error("Error in createPixPayment", { error, params });
    throw error;
  }
}

// Process PIX withdrawal (PIX OUT)
export async function processPixWithdrawal(params: {
  amount: number;
  pixKey: string;
  pixKeyType: string;
  postbackUrl?: string;
  organizationId: string;
  transactionId?: string;
  description?: string;
  idempotencyKey?: string;
}): Promise<any> {
  try {
    logger.info("Processing XDPAG PIX withdrawal", {
      params: {
        ...params,
        amount: params.amount,
        pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined
      }
    });

    // Validate required parameters
    if (!params.pixKey || typeof params.pixKey !== 'string') {
      throw new Error('pixKey is required and must be a string');
    }
    if (!params.amount || params.amount <= 0) {
      throw new Error('amount is required and must be greater than 0');
    }
    if (!params.organizationId || typeof params.organizationId !== 'string') {
      throw new Error('organizationId is required and must be a string');
    }

    // Pre-validate amount
    const amountValidation = validateXdpagAmount(params.amount);
    if (!amountValidation.isValid) {
      throw amountValidation.error;
    }

    // Get JWT token
    const token = await loginXdpag(params.organizationId);
    const credentials = await getXdpagCredentials(params.organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    // Generate internal transaction ID if not provided
    const internalTransactionId = params.transactionId || `xdpag_transfer_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Map PIX key type to XDPAG format
    const pixKeyTypeMap: Record<string, string> = {
      'CPF': 'CPF',
      'CNPJ': 'CNPJ',
      'EMAIL': 'EMAIL',
      'PHONE': 'PHONE',
      'EVP': 'EVP'
    };

    const xdpagPixKeyType = pixKeyTypeMap[params.pixKeyType.toUpperCase()] || 'EVP';

    // Prepare the request payload according to XDPAG API documentation
    const webhookUrl = 'https://app.pluggou.io/api/webhooks/xdpag';

    const payload = {
      amount: params.amount,
      webhook: webhookUrl,
      document: params.pixKey, // For validation
      pixKey: params.pixKey,
      pixKeyType: xdpagPixKeyType,
      externalId: internalTransactionId,
      validate_document: false // Default false as per documentation
    };

    logger.info("Making payout request to XDPAG", {
      url: `${baseUrl}${XDPAG_API_PATHS.payout}`,
      webhookUrl: webhookUrl,
      payload: {
        ...payload,
        pixKey: `${params.pixKey.substring(0, 4)}***`
      },
      headers: {
        "Authorization": `Bearer ${token.substring(0, 20)}...`,
        "Content-Type": "application/json"
      }
    });

    const response = await makeHTTPRequest(`${baseUrl}${XDPAG_API_PATHS.payout}`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify(payload),
      timeout: 30000
    });

    logger.info("XDPAG payout response received", {
      status: response.status,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("XDPAG payout API error response", {
        status: response.status,
        body: errorText,
        url: `${baseUrl}${XDPAG_API_PATHS.payout}`,
        payload: {
          ...payload,
          pixKey: `${params.pixKey.substring(0, 4)}***`
        }
      });

      // Handle specific error codes
      let errorData;
      try {
        errorData = JSON.parse(errorText);
        if (errorData.message === "INVALID_PIX_KEY") {
          throw new Error("Chave PIX inválida");
        }
        if (errorData.message === "OUT_DISABLED_BY_SECURITY") {
          throw new Error("Transferência desabilitada por limite de segurança");
        }
      } catch (parseError) {
        // If parsing fails, throw generic error
      }

      throw new Error(`XDPAG payout API error: ${response.status} - ${errorText}`);
    }

    const responseData = await response.json();
    logger.info("XDPAG payout created successfully", {
      responseData
    });

    // Extract transfer data from XDPAG response
    const transferData = responseData.data;

    // Extract transfer information from the response structure
    const transferId = transferData?.id;
    const status = transferData?.status;
    const amount = transferData?.amount;
    const events = transferData?.events || [];

    logger.info("Payout created successfully with XDPAG", {
      transferId: internalTransactionId,
      externalId: transferId,
      status: status,
      amount: amount,
      pixKey: params.pixKey,
      responseStructure: {
        hasData: !!transferData,
        responseKeys: Object.keys(responseData),
        dataKeys: transferData ? Object.keys(transferData) : []
      }
    });

    // Return transfer data in the format expected by the router
    return {
      success: true,
      data: {
        jobId: transferId,
        externalReference: internalTransactionId,
        status: mapXdpagStatusToInternal(status),
        amount: amount,
        receiverPixKey: params.pixKey,
        description: params.description || "Transferência PIX",
        transferId: transferId,
        events: events
      },
      metadata: {
        xdpag: {
          transferId: transferId,
          provider: "XDPAG",
          originalStatus: status,
          createdAt: new Date().toISOString(),
          pixKeyType: xdpagPixKeyType
        },
      },
    };
  } catch (error) {
    logger.error("Error in processPixWithdrawal", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      params: {
        ...params,
        pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined
      }
    });

    if (error instanceof Error) {
      throw new Error(`XDPAG PIX withdrawal failed: ${error.message}`);
    }
    throw error;
  }
}

// Map XDPAG status to internal status
function mapXdpagStatusToInternal(xdpagStatus: string): string {
  const statusMap: Record<string, string> = {
    'CREATED': 'PENDING',
    'PROCESSING': 'PROCESSING',
    'FINISHED': 'APPROVED',
    'CANCELLED': 'CANCELLED',
    'REVERSED': 'REFUNDED',
    'PARTIALLY_REVERSED': 'PARTIALLY_REFUNDED',
    'TIMEOUT': 'EXPIRED'
  };

  return statusMap[xdpagStatus] || 'UNKNOWN';
}

// Get transaction status
export async function getTransactionStatus(params: {
  transactionId: string;
  organizationId: string;
  transactionType?: 'CHARGE' | 'SEND';
}): Promise<any> {
  try {
    logger.info("Getting XDPAG transaction status", {
      transactionId: params.transactionId,
      organizationId: params.organizationId,
      transactionType: params.transactionType
    });

    // Get JWT token
    const token = await loginXdpag(params.organizationId);
    const credentials = await getXdpagCredentials(params.organizationId);
    const baseUrl = XDPAG_API_BASE_URL[credentials.environment];

    // Try both payin and payout endpoints
    const possiblePaths = [
      XDPAG_API_PATHS.getPayin.replace(':id', params.transactionId),
      XDPAG_API_PATHS.getPayout.replace(':id', params.transactionId)
    ];

    for (const path of possiblePaths) {
      try {
        const response = await makeHTTPRequest(`${baseUrl}${path}`, {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${token}`,
            "Accept": "application/json"
          },
          timeout: 15000
        });

        if (response.ok) {
          const responseData = await response.json();
          logger.info("Transaction status retrieved successfully", {
            transactionId: params.transactionId,
            status: responseData.data?.status,
            path: path
          });

          return {
            id: responseData.data?.id || params.transactionId,
            status: mapXdpagStatusToInternal(responseData.data?.status),
            amount: responseData.data?.amount,
            externalId: responseData.data?.externalId,
            endToEndId: responseData.data?.endToEndId,
            fee: responseData.data?.fee,
            metadata: {
              xdpag: {
                originalStatus: responseData.data?.status,
                endpointUsed: path,
                provider: "XDPAG"
              }
            }
          };
        }
      } catch (pathError) {
        logger.debug(`Failed to get status from path ${path}`, { pathError });
        continue;
      }
    }

    throw new Error(`Transaction ${params.transactionId} not found in any endpoint`);
  } catch (error) {
    logger.error("Error getting transaction status", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId
    });
    throw error;
  }
}

// Process refund
export async function processRefund(params: {
  transactionId: string;
  amount: number;
  reason?: string;
  organizationId: string;
}): Promise<any> {
  try {
    logger.info("Processing XDPAG refund", {
      transactionId: params.transactionId,
      amount: params.amount,
      organizationId: params.organizationId,
      reason: params.reason
    });

    // XDPAG doesn't have a direct refund endpoint based on the documentation
    // This would need to be implemented as a reversal through their support
    throw new Error("XDPAG refund functionality not implemented - contact support for manual refunds");
  } catch (error) {
    logger.error("Error processing refund", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId
    });
    throw error;
  }
}

// Validate XDPAG webhook signature
export function validateXdpagWebhookSignature(
  body: string,
  signature: string,
  secretKey: string | undefined
): boolean {
  try {
    if (!secretKey) {
      logger.warn("XDPAG webhook secret not configured, skipping signature validation");
      return true;
    }

    // Compute expected signature (implementation depends on XDPAG's signature method)
    const computedSignature = crypto
      .createHmac('sha256', secretKey)
      .update(body)
      .digest('hex');

    // Compare signatures securely
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(computedSignature)
    );

    if (!isValid) {
      logger.warn("Invalid XDPAG webhook signature", {
        receivedSignature: signature,
        computedSignature
      });
    }

    return isValid;
  } catch (error) {
    logger.error("Error validating XDPAG webhook signature", { error });
    return false;
  }
}

// Webhook handler for XDPAG
export async function webhookHandler(params: {
  body: any;
  headers: Record<string, string>;
  organizationId?: string;
}): Promise<any> {
  try {
    logger.info("Processing XDPAG webhook", {
      body: params.body,
      headers: Object.keys(params.headers)
    });

    // Validate webhook signature (disabled by default for initial deployment)
    const signature = params.headers['x-xdpag-signature'] || params.headers['signature'];
    const webhookSecret = process.env.XDPAG_WEBHOOK_SECRET;
    const bypassValidation = process.env.XDPAG_BYPASS_SIGNATURE_VALIDATION !== 'false'; // Default to true (bypass)

    if (!bypassValidation && !validateXdpagWebhookSignature(
      JSON.stringify(params.body),
      signature || '',
      webhookSecret
    )) {
      throw new Error("Invalid webhook signature");
    }

    logger.info("XDPAG webhook signature validation", {
      bypassValidation,
      hasSignature: !!signature,
      hasSecret: !!webhookSecret
    });

    // Extract transaction information from XDPAG webhook payload
    const webhookData = params.body;
    const eventType = webhookData.type; // PAYIN or PAYOUT
    const transactionData = webhookData.data;
    const transactionId = transactionData?.id;
    const externalId = transactionData?.externalId;
    const status = transactionData?.status;
    const amount = transactionData?.amount;
    const endToEndId = transactionData?.endToEndId;

    logger.info("XDPAG webhook data extracted", {
      eventType,
      transactionId,
      externalId,
      status,
      amount,
      endToEndId
    });

    // Find the transaction in our database
    let transaction = null;

    if (externalId) {
      transaction = await db.transaction.findFirst({
        where: { externalId }
      });
    }

    if (!transaction && transactionId) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { externalId: transactionId }
          ]
        }
      });
    }

    if (!transaction) {
      logger.warn("Transaction not found for XDPAG webhook", {
        transactionId,
        externalId,
        eventType
      });
      return { success: false, message: "Transaction not found" };
    }

    // Update transaction status
    const internalStatus = mapXdpagStatusToInternal(status) as any;

    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: internalStatus,
        updatedAt: new Date(),
        metadata: {
          ...(transaction.metadata as Record<string, any>),
          lastWebhookEvent: eventType,
          lastWebhookReceived: new Date().toISOString(),
          endToEndId: endToEndId
        }
      }
    });

    logger.info("XDPAG webhook processed successfully", {
      transactionId: transaction.id,
      externalId,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    return {
      success: true,
      data: {
        transactionId: transaction.id,
        externalId,
        status: internalStatus,
        eventType
      }
    };
  } catch (error) {
    logger.error("Error processing XDPAG webhook", { error, params });
    throw error;
  }
}

// Export all functions for the payment provider interface
export default {
  type: 'XDPAG', // Add type property for proper identification
  testXdpagConnection,
  createPixPayment,
  processPixWithdrawal,
  getTransactionStatus,
  processRefund,
  webhookHandler,
  getXdpagBalance,
  loginXdpag
};
