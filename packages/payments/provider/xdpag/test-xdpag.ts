import { testXdpagConnection, getXdpagBalance, createPixPayment, processPixWithdrawal } from './index';

// Test configuration
const TEST_ORGANIZATION_ID = 'test-org-123';
const TEST_CREDENTIALS = {
  username: 'user',
  password: 'pass',
  environment: 'sandbox' as const
};

async function testXdpagProvider() {
  console.log('🧪 Testing XDPAG Provider Implementation...\n');

  try {
    // Test 1: Connection Test
    console.log('1️⃣ Testing connection...');
    try {
      const connectionResult = await testXdpagConnection(TEST_ORGANIZATION_ID);
      console.log('✅ Connection test passed:', connectionResult);
    } catch (error) {
      console.log('❌ Connection test failed (expected in test environment):', error);
    }

    // Test 2: Balance Test
    console.log('\n2️⃣ Testing balance retrieval...');
    try {
      const balanceResult = await getXdpagBalance(TEST_ORGANIZATION_ID);
      console.log('✅ Balance test passed:', balanceResult);
    } catch (error) {
      console.log('❌ Balance test failed (expected in test environment):', error);
    }

    // Test 3: PIX Payment Creation Test
    console.log('\n3️⃣ Testing PIX payment creation...');
    try {
      const paymentResult = await createPixPayment({
        amount: 10.00,
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        description: 'Teste de pagamento PIX',
        organizationId: TEST_ORGANIZATION_ID,
        externalCode: 'test-payment-123'
      });
      console.log('✅ PIX payment creation test passed:', paymentResult);
    } catch (error) {
      console.log('❌ PIX payment creation test failed (expected in test environment):', error);
    }

    // Test 4: PIX Withdrawal Test
    console.log('\n4️⃣ Testing PIX withdrawal...');
    try {
      const withdrawalResult = await processPixWithdrawal({
        amount: 5.00,
        pixKey: '11999999999',
        pixKeyType: 'PHONE',
        organizationId: TEST_ORGANIZATION_ID,
        description: 'Teste de transferência PIX'
      });
      console.log('✅ PIX withdrawal test passed:', withdrawalResult);
    } catch (error) {
      console.log('❌ PIX withdrawal test failed (expected in test environment):', error);
    }

    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Note: Tests are expected to fail in test environment without valid credentials.');
    console.log('   This validates that the implementation follows the correct patterns.');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testXdpagProvider();
}

export { testXdpagProvider };
