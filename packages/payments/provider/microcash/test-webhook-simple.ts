import microcash from "./index";

// Test data based on the actual webhook payload from logs
const TEST_WEBHOOK_PAYLOAD = {
  tipoIniciacao: 3,
  prioridade: 0,
  tipoPrioridade: 0,
  finalidade: 0,
  agente: 0,
  ispbPss: null,
  cnpjIniciadorPagamento: null,
  pagador: {
    tipoPessoa: 0,
    documento: 55850377000121,
    nome: 'ISMAEL MUNIZ DA COSTA TECNOLOGIA DA INFORMACAO LTDA',
    conta: {}
  },
  recebedor: { 
    tipoPessoa: 0, 
    documento: 27945891000105, 
    conta: {} 
  },
  creditadoEm: '2025-09-23T15:24:06.8197255+00:00',
  valor: '0.5',
  valoresDetalhados: [],
  informacaoEntreClientes: null,
  txId: '77a99d94234148cab1e51328e1',
  endToEndId: 'E9040088820250923182346714078626',
  chavePix: 'ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f',
  correlationId: '384799e6-f62d-4dd8-9747-3b0453fd249a',
  status: 2,
  tid: 'microcash_1758651812'
};

async function testWebhookHandler() {
  console.log("🎯 Testing webhook handler with real data...");
  console.log("📋 Webhook payload:", {
    txId: TEST_WEBHOOK_PAYLOAD.txId,
    tid: TEST_WEBHOOK_PAYLOAD.tid,
    endToEndId: TEST_WEBHOOK_PAYLOAD.endToEndId,
    status: TEST_WEBHOOK_PAYLOAD.status,
    amount: TEST_WEBHOOK_PAYLOAD.valor
  });
  
  try {
    const result = await microcash.webhookHandler({
      body: TEST_WEBHOOK_PAYLOAD,
      headers: {},
      organizationId: undefined
    });

    console.log("✅ Webhook handler result:", result);
    
    if (result.success && result.data?.transactionId) {
      console.log("🎉 SUCCESS: Transaction was found and processed!");
      console.log("📊 Transaction details:", {
        transactionId: result.data.transactionId,
        status: result.data.status,
        webhookProcessed: result.data.webhookProcessed
      });
    } else {
      console.log("❌ FAILED: Transaction was not found");
      console.log("📊 Result details:", result);
    }
    
    return result;
  } catch (error) {
    console.error("❌ Webhook handler failed:", error);
    return null;
  }
}

// Run the test
testWebhookHandler().catch(console.error);
