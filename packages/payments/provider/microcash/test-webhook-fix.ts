import { db } from "@repo/database";
import { logger } from "@repo/logs";
import microcash from "./index";

// Test data based on the actual webhook payload from logs
const TEST_WEBHOOK_PAYLOAD = {
  tipoIniciacao: 3,
  prioridade: 0,
  tipoPrioridade: 0,
  finalidade: 0,
  agente: 0,
  ispbPss: null,
  cnpjIniciadorPagamento: null,
  pagador: {
    tipoPessoa: 0,
    documento: 55850377000121,
    nome: 'ISMAEL MUNIZ DA COSTA TECNOLOGIA DA INFORMACAO LTDA',
    conta: {}
  },
  recebedor: { 
    tipoPessoa: 0, 
    documento: 27945891000105, 
    conta: {} 
  },
  creditadoEm: '2025-09-23T15:24:06.8197255+00:00',
  valor: '0.5',
  valoresDetalhados: [],
  informacaoEntreClientes: null,
  txId: '77a99d94234148cab1e51328e1',
  endToEndId: 'E9040088820250923182346714078626',
  chavePix: 'ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f',
  correlationId: '384799e6-f62d-4dd8-9747-3b0453fd249a',
  status: 2,
  tid: 'microcash_1758651812'
};

async function createTestTransaction() {
  console.log("🔧 Creating test transaction...");
  
  try {
    // Create a test transaction with the txId as externalId (as the system does)
    const transaction = await db.transaction.create({
      data: {
        externalId: TEST_WEBHOOK_PAYLOAD.txId, // This is what the system saves
        referenceCode: `test_${Date.now()}`,
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        amount: 0.5,
        status: "PENDING",
        type: "CHARGE",
        description: "Test transaction for webhook",
        organizationId: "cm1kcqhqj0000uxqhqhqhqhqh", // Replace with actual org ID
        metadata: {
          microcash: {
            txId: TEST_WEBHOOK_PAYLOAD.txId,
            pixKey: "ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f",
            provider: "MICROCASH",
          },
          allProviderIds: {
            transactionId: TEST_WEBHOOK_PAYLOAD.txId,
            txId: TEST_WEBHOOK_PAYLOAD.txId,
            tid: TEST_WEBHOOK_PAYLOAD.tid,
            endToEndId: TEST_WEBHOOK_PAYLOAD.endToEndId,
          },
        },
      },
    });

    console.log("✅ Test transaction created:", {
      id: transaction.id,
      externalId: transaction.externalId,
      metadata: transaction.metadata
    });

    return transaction;
  } catch (error) {
    console.error("❌ Failed to create test transaction:", error);
    return null;
  }
}

async function testWebhookHandler() {
  console.log("🎯 Testing webhook handler...");
  
  try {
    const result = await microcash.webhookHandler({
      body: TEST_WEBHOOK_PAYLOAD,
      headers: {},
      organizationId: undefined
    });

    console.log("✅ Webhook handler result:", result);
    return result;
  } catch (error) {
    console.error("❌ Webhook handler failed:", error);
    return null;
  }
}

async function cleanupTestTransaction(transactionId: string) {
  console.log("🧹 Cleaning up test transaction...");
  
  try {
    await db.transaction.delete({
      where: { id: transactionId }
    });
    console.log("✅ Test transaction cleaned up");
  } catch (error) {
    console.error("❌ Failed to cleanup test transaction:", error);
  }
}

async function runTest() {
  console.log("🚀 Starting Microcash webhook fix test...");
  console.log("📋 Test webhook payload:", {
    txId: TEST_WEBHOOK_PAYLOAD.txId,
    tid: TEST_WEBHOOK_PAYLOAD.tid,
    endToEndId: TEST_WEBHOOK_PAYLOAD.endToEndId,
    status: TEST_WEBHOOK_PAYLOAD.status,
    amount: TEST_WEBHOOK_PAYLOAD.valor
  });

  // Step 1: Create test transaction
  const transaction = await createTestTransaction();
  if (!transaction) {
    console.error("❌ Test failed: Could not create test transaction");
    return;
  }

  // Step 2: Test webhook handler
  const webhookResult = await testWebhookHandler();
  if (!webhookResult) {
    console.error("❌ Test failed: Webhook handler failed");
    await cleanupTestTransaction(transaction.id);
    return;
  }

  // Step 3: Check if transaction was found and updated
  if (webhookResult.success && webhookResult.data?.transactionId) {
    console.log("✅ Test PASSED: Transaction was found and processed!");
    console.log("📊 Results:", {
      transactionFound: true,
      transactionId: webhookResult.data.transactionId,
      status: webhookResult.data.status,
      webhookProcessed: webhookResult.data.webhookProcessed
    });
  } else {
    console.log("❌ Test FAILED: Transaction was not found");
    console.log("📊 Results:", webhookResult);
  }

  // Step 4: Cleanup
  await cleanupTestTransaction(transaction.id);
  
  console.log("🏁 Test completed!");
}

// Run the test if this file is executed directly
if (require.main === module) {
  runTest().catch(console.error);
}

export { runTest, createTestTransaction, testWebhookHandler };
