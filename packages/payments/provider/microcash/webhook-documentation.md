# Microcash Webhook Documentation

## Visão Geral

O Microcash envia webhooks para notificar sobre mudanças de status nas transações PIX. Este documento descreve como configurar e processar esses webhooks.

## Configuração

### 1. URL do Webhook

Configure a seguinte URL no painel do Microcash:
```
https://seu-dominio.com/api/webhooks/microcash
```

### 2. Variáveis de Ambiente

```bash
# Webhook Secret (opcional, para validação de assinatura)
MICROCASH_WEBHOOK_SECRET=your_webhook_secret_here

# Bypass de validação de assinatura (padrão: true)
MICROCASH_BYPASS_SIGNATURE_VALIDATION=true
```

## Estrutura do Webhook

### Headers

```
Content-Type: application/json; charset=utf-8
User-Agent: Microcash-Webhook/1.0
```

### Payload (PIX IN - Pagamento Recebido)

```json
{
  "tipoIniciacao": 3,
  "prioridade": 0,
  "tipoPrioridade": 0,
  "finalidade": 0,
  "agente": 0,
  "ispbPss": null,
  "cnpjIniciadorPagamento": null,
  "pagador": {
    "tipoPessoa": 0,
    "documento": 1239360514,
    "nome": "TESTE FREIRE DOS TESTES",
    "conta": {
      "agencia": "1",
      "numero": "143134752",
      "ispb": 416968,
      "tipo": 0
    }
  },
  "recebedor": {
    "tipoPessoa": 0,
    "documento": 12381237000107,
    "conta": {
      "agencia": "1",
      "numero": "12339",
      "ispb": 12312348,
      "tipo": 0
    }
  },
  "creditadoEm": "2024-06-17T12:24:13.6047759+00:00",
  "valor": 50.0,
  "valoresDetalhados": [],
  "informacaoEntreClientes": null,
  "txId": "f6fa3fb169eb46afb5ebbd9ae4",
  "endToEndId": "E00416968202406171524wl4l910LDDh",
  "chavePix": "dccb1680-a21d-46a1-84a6-f15bae6888b2",
  "correlationId": "c010a9d4-6a1a-4900-b7df-07b5090db1e2",
  "status": 2,
  "tid": "20240617122342139957XEtxWMn13"
}
```

### Payload (PIX OUT - Transferência Enviada)

```json
{
  "Status": 2,
  "EndToEndId": "E45756448202406110221LN7uKjaJfkS",
  "Tid": "A20240610232117822823mzx8VhaJs",
  "PagamentoIndiretoId": "0d7043e4-e19b-4b92-95cc-a2edf02df07c",
  "ChavePix": "11926408969",
  "QrCode": null,
  "TipoIniciacao": 1,
  "Finalidade": 0,
  "InfoEntreClientes": null,
  "Valor": 20.00,
  "Correntista": {
    "Nome": "TESTES EXPRESS LTDA",
    "Documento": "05318673500107",
    "Conta": {
      "Ispb": "45756448",
      "CodBanco": null,
      "BancoNome": null,
      "Agencia": "1",
      "Numero": "000060539",
      "Tipo": 0
    }
  },
  "Contraparte": {
    "Nome": "TESTE DA SILVA",
    "Documento": "11123408969",
    "Conta": {
      "Ispb": "3601235",
      "CodBanco": null,
      "BancoNome": null,
      "Agencia": "396",
      "Numero": "12880000008328040123",
      "Tipo": 2
    }
  },
  "QrCodeEstatico": null,
  "QrCodeDinamicoImediato": null,
  "QrCodeDinamicoVencimento": null,
  "VenceEmUtc": null,
  "ConfirmadoEmUtc": "2024-06-11T02:21:17.455202+00:00",
  "RealizarEmUtc": null,
  "EfetivacaoEnviadaEmUtc": "2024-06-11T02:21:18.68617+00:00",
  "EfetivacaoRealizadaEmUtc": "2024-06-11T02:21:26.1721931+00:00",
  "BloqueadoEmUtc": "2024-06-11T02:21:18.646127+00:00",
  "BloqueioProtocolo": "0",
  "DebitoEmUtc": "2024-06-11T02:21:26.1721925+00:00",
  "DebitoProtocolo": "E45756448202406110221LN7uKjaJfkS",
  "EstornoProtocolo": null,
  "EstornoValor": 0,
  "ErroEmUtc": null,
  "ErroCodigo": null,
  "ErroDescricao": null,
  "Devolvidos": [],
  "ProcessandoEmUtc": null,
  "ChaveIdempotencia": null,
  "Id": "637affdb-ef66-4908-bebc-ab77d007ea2c",
  "ChaveSequencial": 26875009,
  "CriadoEmUtc": "2024-06-11T02:21:17.402498+00:00"
}
```

## Campos Importantes

### Identificadores de Transação

- **txId**: ID da transação no Microcash (PIX IN)
- **Id**: ID da transação no Microcash (PIX OUT)
- **tid**: ID interno da transação
- **endToEndId**: ID end-to-end da transação PIX
- **correlationId**: ID de correlação

### Status da Transação

| Código | Descrição | Status Interno |
|--------|-----------|----------------|
| 0 | Pending | PENDING |
| 1 | Processing | PROCESSING |
| 2 | Success | APPROVED |
| 3 | Error | REJECTED |
| 4 | Failure | REJECTED |

### Informações do Pagador (PIX IN)

- **pagador.nome**: Nome do pagador
- **pagador.documento**: CPF/CNPJ do pagador
- **pagador.conta**: Dados da conta bancária

### Informações do Recebedor

- **recebedor.nome**: Nome do recebedor
- **recebedor.documento**: CPF/CNPJ do recebedor
- **recebedor.conta**: Dados da conta bancária

## Processamento do Webhook

### 1. Validação

O webhook handler valida:
- Estrutura do payload
- Presença de campos obrigatórios
- Assinatura (se configurada)

### 2. Busca da Transação

O sistema busca a transação no banco de dados usando:
1. `txId` (PIX IN) ou `Id` (PIX OUT)
2. `tid` como fallback

### 3. Atualização do Status

A transação é atualizada com:
- Novo status mapeado
- Dados do webhook no metadata
- Timestamp da atualização

### 4. Resposta

O webhook retorna:
```json
{
  "success": true,
  "data": {
    "transactionId": "internal_tx_id",
    "txId": "microcash_tx_id",
    "status": "APPROVED",
    "endToEndId": "E00416968202406171524wl4l910LDDh"
  }
}
```

## Tratamento de Erros

### Erro 400 - Bad Request
- Payload inválido ou vazio
- JSON malformado

### Erro 500 - Internal Server Error
- Erro no processamento
- Transação não encontrada
- Erro de banco de dados

## Logs

Todos os webhooks são logados com:
- Payload recebido
- Headers
- Resultado do processamento
- Erros (se houver)

## Testes

Use o arquivo `test-microcash.ts` para testar o webhook handler:

```typescript
import { testWebhookHandler } from './test-microcash';

// Testa o webhook handler com payload simulado
await testWebhookHandler();
```

## Monitoramento

Monitore os webhooks através de:
- Logs do sistema
- Métricas de sucesso/erro
- Alertas para falhas consecutivas

## Suporte

Para problemas com webhooks:
1. Verifique os logs do sistema
2. Confirme a configuração da URL
3. Teste com payload simulado
4. Entre em contato com o suporte técnico
