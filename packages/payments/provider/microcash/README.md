# Microcash Payment Provider

Gateway de pagamentos PIX do Banco Microcash com suporte completo a PIX IN e PIX OUT.

## Funcionalidades

- ✅ **PIX IN (Recebimento)**: Criação de QR codes dinâmicos para recebimento de pagamentos
- ✅ **PIX OUT (Envio)**: Transferências PIX para chaves PIX externas
- ✅ **Webhooks**: Processamento de notificações em tempo real
- ✅ **Consulta de Status**: Verificação do status das transações
- ✅ **Autenticação OAuth2**: Sistema de autenticação seguro
- ✅ **Cache de Tokens**: Otimização de performance com cache de tokens

## Configuração

### 1. Credenciais OAuth2

Obtenha suas credenciais OAuth2 do Banco Microcash:
- `clientId`: ID do cliente
- `clientSecret`: Chave secreta do cliente
- `pixKey`: Chave PIX da conta (EVP, CPF, CNPJ, email ou telefone)

### 2. Variáveis de Ambiente

Configure as seguintes variáveis no seu arquivo `.env`:

```bash
# Environment
MICROCASH_ENVIRONMENT=production

# OAuth2 Credentials (da conta ativada)
MICROCASH_CLIENT_ID=3e9oa57mqbqun0kllsi3tnd0fk
MICROCASH_CLIENT_SECRET=bc3s732tiirrmagbsn52kvoorftmd9lce5crr4g1u9s5som3jql
MICROCASH_PIX_KEY=ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f

# API Key (x-api-key)
MICROCASH_API_KEY=DECD359F-CE25-4697-968F-F26F97DF9B8E

# Webhook
MICROCASH_WEBHOOK_SECRET=DECD359F-CE25-4697-968F-F26F97DF9B8E
MICROCASH_BYPASS_SIGNATURE_VALIDATION=false
```

### 3. Informações da Conta

- **Nome**: VENDAS ONLINE STORE LTDA
- **CNPJ**: 27.945.891/0001-05
- **Banco**: Microcash
- **Agência**: 0001
- **Conta**: 000090988

### 4. URLs da API

- **Sandbox**: `https://apis-pix-hml.bancomicrocash.com`
- **Production**: `https://apis-pix.bancomicrocash.com`

## Uso

### PIX IN (Recebimento)

```typescript
import { microcash } from "@repo/payments/provider/microcash";

const payment = await microcash.createPixPayment({
  amount: 100.50,
  customerName: "João Silva",
  customerEmail: "<EMAIL>",
  customerDocument: "12345678901",
  description: "Pagamento de serviço",
  organizationId: "org_123"
});

// Retorna:
// {
//   success: true,
//   transactionId: "tx_123",
//   externalId: "microcash_tx_456",
//   pixCode: "00020126580014br.gov.bcb.pix...",
//   pixQrCode: "iVBORw0KGgoAAAANSUhEUgAA...",
//   pixExpiresAt: "2024-01-01T12:30:00.000Z"
// }
```

### PIX OUT (Envio)

```typescript
const transfer = await microcash.processPixWithdrawal({
  amount: 50.00,
  pixKey: "11999999999",
  pixKeyType: "PHONE",
  description: "Transferência para cliente",
  organizationId: "org_123"
});

// Retorna:
// {
//   success: true,
//   data: {
//     jobId: "payment_123",
//     status: "PROCESSING",
//     amount: 50.00,
//     receiverPixKey: "11999999999",
//     endToEndId: "E12345678901234567890"
//   }
// }
```

### Consulta de Status

```typescript
const status = await microcash.getTransactionStatus({
  transactionId: "tx_123",
  organizationId: "org_123"
});

// Retorna:
// {
//   id: "tx_123",
//   status: "APPROVED",
//   amount: 100.50,
//   pixKey: "chave_pix",
//   description: "Pagamento de serviço"
// }
```

## Webhooks

### Configuração

Configure o webhook no painel do Microcash para:
```
https://seu-dominio.com/api/webhooks/microcash
```

### Payload do Webhook

O Microcash envia webhooks com a seguinte estrutura:

```json
{
  "tipoIniciacao": 3,
  "prioridade": 0,
  "tipoPrioridade": 0,
  "finalidade": 0,
  "agente": 0,
  "ispbPss": null,
  "cnpjIniciadorPagamento": null,
  "pagador": {
    "tipoPessoa": 0,
    "documento": 1239360514,
    "nome": "TESTE FREIRE DOS TESTES",
    "conta": {
      "agencia": "1",
      "numero": "143134752",
      "ispb": 416968,
      "tipo": 0
    }
  },
  "recebedor": {
    "tipoPessoa": 0,
    "documento": 12381237000107,
    "conta": {
      "agencia": "1",
      "numero": "12339",
      "ispb": 12312348,
      "tipo": 0
    }
  },
  "creditadoEm": "2024-06-17T12:24:13.6047759+00:00",
  "valor": 50.0,
  "valoresDetalhados": [],
  "informacaoEntreClientes": null,
  "txId": "f6fa3fb169eb46afb5ebbd9ae4",
  "endToEndId": "E00416968202406171524wl4l910LDDh",
  "chavePix": "dccb1680-a21d-46a1-84a6-f15bae6888b2",
  "correlationId": "c010a9d4-6a1a-4900-b7df-07b5090db1e2",
  "status": 2,
  "tid": "20240617122342139957XEtxWMn13"
}
```

### Status Codes

- `0`: Pending
- `1`: Processing
- `2`: Success (Approved)
- `3`: Error (Rejected)
- `4`: Failure (Rejected)

## Limites

- **Valor Mínimo**: R$ 0,01
- **Valor Máximo**: R$ 50.000,00 (configurável)
- **Moeda**: BRL (Real Brasileiro)

## Tratamento de Erros

O provider inclui tratamento robusto de erros:

- Validação de credenciais
- Validação de valores
- Retry automático para falhas temporárias
- Logs detalhados para debugging
- Cache de tokens para otimização

## Logs

Todos os logs são enviados para o sistema de logging centralizado com níveis apropriados:

- `info`: Operações normais
- `warn`: Situações que requerem atenção
- `error`: Erros que impedem a operação

## Suporte

Para suporte técnico ou dúvidas sobre a integração, consulte:

- Documentação oficial do Microcash
- Logs do sistema para debugging
- Equipe de desenvolvimento da Pluggou
