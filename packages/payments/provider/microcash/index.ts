import * as crypto from "node:crypto";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import * as QRCode from "qrcode";
import { getGatewayCredentials } from "../factory";
import { updateTransactionStatus } from "../../src/transactions/service";

// Helper function to clean and validate proxy URL with comprehensive checks
function getProxyUrl(): string {
	const fallbackUrl = "https://pix-api-proxy.cloud.pluggou.io";
	
	// Try multiple environment variable names (case variations)
	const possibleEnvVars = [
		'PIX_API_PROXY_URL',
		'PIX_PROXY_URL', 
		'PIX_PROXY_BASE_URL',
		'PROXY_URL',
		'PIX_API_URL'
	];
	
	let envUrl: string | undefined;
	let usedEnvVar: string | undefined;
	
	// Find the first defined environment variable
	for (const envVar of possibleEnvVars) {
		const value = process.env[envVar];
		if (value && value.trim()) {
			envUrl = value;
			usedEnvVar = envVar;
			break;
		}
	}
	
	// Log all environment variables for debug
	logger.info("Microcash - Verificação de variáveis de ambiente PIX Proxy:", {
		checkedVariables: possibleEnvVars.map(envVar => ({
			name: envVar,
			defined: !!process.env[envVar],
			value: process.env[envVar] ? "***PRESENT***" : "undefined",
			length: process.env[envVar]?.length || 0
		})),
		selectedVariable: usedEnvVar,
		selectedValue: envUrl ? "***PRESENT***" : "undefined",
		selectedLength: envUrl?.length || 0
	});
	
	if (!envUrl) {
		logger.warn("Nenhuma variável de ambiente PIX Proxy encontrada, usando fallback", {
			fallbackUrl,
			checkedVariables: possibleEnvVars
		});
		return fallbackUrl;
	}
	
	// Clean the URL (remove leading @, trim whitespace, etc.)
	let cleanUrl = envUrl.trim();
	if (cleanUrl.startsWith('@')) {
		cleanUrl = cleanUrl.substring(1);
		logger.info("Removido @ do início da URL", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl
		});
	}
	
	// Validate URL
	try {
		new URL(cleanUrl);
		logger.info("URL do PIX Proxy validada com sucesso", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl,
			usedEnvVar,
			fallbackUrl
		});
		return cleanUrl;
	} catch (error) {
		logger.error("URL do PIX Proxy inválida, usando fallback", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl,
			usedEnvVar,
			error: error instanceof Error ? error.message : String(error),
			fallbackUrl
		});
		return fallbackUrl;
	}
}

// Base URL for Microcash Proxy API - configurável via variável de ambiente
const MICROCASH_PROXY_BASE_URL = getProxyUrl();

// Debug: Log das variáveis de ambiente para verificar se estão sendo lidas corretamente
logger.info("Microcash - Variáveis de ambiente PIX Proxy:", {
	PIX_API_PROXY_URL: process.env.PIX_API_PROXY_URL ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	PIX_API_PROXY_URL_LENGTH: process.env.PIX_API_PROXY_URL?.length || 0,
	PIX_API_PROXY_URL_RAW: process.env.PIX_API_PROXY_URL || "undefined",
	PIX_API_PROXY_KEY: process.env.PIX_API_PROXY_KEY ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	FINAL_PROXY_URL: MICROCASH_PROXY_BASE_URL,
	USING_FALLBACK: !process.env.PIX_API_PROXY_URL,
	ENVIRONMENT: process.env.NODE_ENV || "undefined",
	VERCEL_ENV: process.env.VERCEL_ENV || "undefined"
});

// API paths for Microcash Proxy
const MICROCASH_PROXY_PATHS = {
	create: "/api/v1/pix/create",
	status: (id: string) => `/api/v1/pix/status/${id}`,
	refund: "/api/v1/pix/refund",
	cancel: (id: string) => `/api/v1/pix/cancel/${id}`,
};

// PIX_API_PROXY_KEY: Chave secreta para autenticação com o pix-api-proxy - configurável via variável de ambiente
const proxyApiKey = process.env.PIX_API_PROXY_KEY || "pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a";

// Debug: Log da chave de API para verificar se está sendo lida corretamente
logger.info("Microcash - Chave de API PIX Proxy:", {
	PIX_API_PROXY_KEY_ENV: process.env.PIX_API_PROXY_KEY ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	PIX_API_PROXY_KEY_LENGTH: process.env.PIX_API_PROXY_KEY?.length || 0,
	PIX_API_PROXY_KEY_PREFIX: process.env.PIX_API_PROXY_KEY ? process.env.PIX_API_PROXY_KEY.substring(0, 10) + "..." : "undefined",
	FINAL_PROXY_KEY: proxyApiKey ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	FINAL_PROXY_KEY_LENGTH: proxyApiKey?.length || 0,
	USING_FALLBACK_KEY: !process.env.PIX_API_PROXY_KEY
});

// Microcash transaction limits configuration
const MICROCASH_LIMITS = {
	MIN_AMOUNT: 0.01, // R$ 0.01 - minimum PIX amount
	MAX_AMOUNT: 50000, // R$ 50,000 - conservative maximum limit
	CURRENCY: "BRL",
} as const;

// Helper function to validate transaction amounts
function validateMicrocashAmount(amount: number): {
	isValid: boolean;
	error?: Error;
} {
	if (amount < MICROCASH_LIMITS.MIN_AMOUNT) {
		const error = new Error(
			`Valor mínimo para transação PIX é R$ ${MICROCASH_LIMITS.MIN_AMOUNT.toFixed(2)}`,
		);
		(error as any).code = "AMOUNT_TOO_LOW";
		return { isValid: false, error };
	}

	if (amount > MICROCASH_LIMITS.MAX_AMOUNT) {
		const error = new Error(
			`Valor excede o limite máximo permitido por transação (R$ ${MICROCASH_LIMITS.MAX_AMOUNT.toLocaleString("pt-BR")}). Por favor, reduza o valor ou entre em contato com o suporte.`,
		);
		(error as any).code = "LIMIT_EXCEEDED_PER_TX";
		return { isValid: false, error };
	}

	return { isValid: true };
}

// Helper function to truncate description to Microcash limit
function truncateDescription(
	description: string | undefined,
	maxLength = 100,
): string {
	if (!description) {
		return "Pagamento PIX";
	}

	if (description.length <= maxLength) {
		return description;
	}

	const truncated = `${description.substring(0, maxLength - 3)}...`;
	logger.warn("Description truncated for Microcash API", {
		originalLength: description.length,
		truncatedLength: truncated.length,
		originalDescription: description,
		truncatedDescription: truncated,
	});

	return truncated;
}

// Função utilitária para gerar QR code a partir do código PIX
export async function generatePixQRCodeImage(pixCode: string): Promise<string> {
	try {
		logger.info("Iniciando geração de QR code", {
			pixCodeLength: pixCode?.length,
			pixCodePreview: pixCode?.substring(0, 50),
		});

		// Gerar QR code como base64
		const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
			type: "image/png",
			width: 256,
			margin: 2,
			color: {
				dark: "#000000",
				light: "#FFFFFF",
			},
		});

		// Retornar apenas a parte base64 (sem o prefixo data:image/png;base64,)
		const base64Image = qrCodeDataURL.split(",")[1];

		logger.info("QR code gerado com sucesso", {
			originalLength: qrCodeDataURL.length,
			base64Length: base64Image.length,
		});

		return base64Image;
	} catch (error) {
		logger.error("Erro ao gerar QR code para PIX", {
			error: error instanceof Error ? error.message : String(error),
			pixCode: pixCode?.substring(0, 50),
		});
		// Retornar string vazia em caso de erro
		return "";
	}
}

// Helper to get the Microcash environment for an organization (legacy)
export async function getMicrocashEnvironment(
	organizationId: string,
): Promise<"production" | "sandbox"> {
	try {
		const credentials = await getGatewayCredentials(
			organizationId,
			"MICROCASH",
		);
		return (
			(credentials.environment as "production" | "sandbox") || "production"
		);
	} catch (error) {
		logger.error("Error getting Microcash environment", {
			error,
			organizationId,
		});
		return "production"; // Default to production
	}
}

// Cache for webhook status - simple in-memory cache with TTL
const webhookStatusCache = new Map<string, { enabled: boolean; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const WEBHOOK_CHECK_TIMEOUT = 2000; // 2 seconds timeout
const MAX_CONSECUTIVE_ERRORS = 3; // Circuit breaker threshold
const webhookCheckErrors = new Map<string, number>(); // Track consecutive errors per org

// Check if webhooks are enabled for a specific gateway instance (optimized with cache)
export async function isWebhookEnabledForGateway(
	organizationId: string,
	gatewayType: string = "MICROCASH"
): Promise<boolean> {
	const cacheKey = `${organizationId}:${gatewayType}`;
	const now = Date.now();
	
	// Check cache first
	const cached = webhookStatusCache.get(cacheKey);
	if (cached && (now - cached.timestamp) < CACHE_TTL) {
		logger.debug("Webhook status from cache", {
			organizationId,
			gatewayType,
			webhookEnabled: cached.enabled,
			cacheAge: `${now - cached.timestamp}ms`,
			timestamp: new Date().toISOString()
		});
		return cached.enabled;
	}

	const startTime = Date.now();
	
	// Circuit breaker check
	const errorCount = webhookCheckErrors.get(cacheKey) || 0;
	if (errorCount >= MAX_CONSECUTIVE_ERRORS) {
		logger.warn("Circuit breaker open for webhook check, using cached value", {
			organizationId,
			gatewayType,
			errorCount,
			timestamp: new Date().toISOString()
		});
		// Return cached value if available, otherwise default to true
		const cached = webhookStatusCache.get(cacheKey);
		return cached ? cached.enabled : true;
	}
	
	try {
		logger.info("Checking webhook status for gateway (cache miss)", {
			organizationId,
			gatewayType,
			timestamp: new Date().toISOString()
		});

		// Create timeout promise
		const timeoutPromise = new Promise<never>((_, reject) => {
			setTimeout(() => reject(new Error('Webhook check timeout')), WEBHOOK_CHECK_TIMEOUT);
		});

		// Optimized query - only select necessary fields
		const queryPromise = db.payment_gateway.findFirst({
			where: {
				type: gatewayType,
				isActive: true,
				// Simplified query for better performance
				OR: [
					{ isGlobal: true },
					{
						organization_gateway: {
							some: {
								organizationId: organizationId,
								isActive: true
							}
						}
					}
				]
			},
			select: {
				id: true,
				name: true,
				webhooksEnabled: true,
				isGlobal: true
			}
		});

		// Race between query and timeout
		const gateway = await Promise.race([queryPromise, timeoutPromise]);

		// Check if webhooks are enabled for this gateway
		const webhookEnabled = (gateway as any)?.webhooksEnabled !== false; // Default to true if not set

		// Update cache
		webhookStatusCache.set(cacheKey, {
			enabled: webhookEnabled,
			timestamp: now
		});

		// Reset error count on success
		webhookCheckErrors.delete(cacheKey);

		const duration = Date.now() - startTime;

		logger.info("Webhook status check completed", {
			organizationId,
			gatewayType,
			webhookEnabled,
			gatewayId: gateway?.id,
			gatewayName: gateway?.name,
			duration: `${duration}ms`,
			cached: false,
			timestamp: new Date().toISOString()
		});

		return webhookEnabled;
	} catch (error) {
		const duration = Date.now() - startTime;
		
		// Increment error count
		const currentErrors = webhookCheckErrors.get(cacheKey) || 0;
		webhookCheckErrors.set(cacheKey, currentErrors + 1);
		
		logger.error("Error checking webhook status for gateway", {
			organizationId,
			gatewayType,
			error: error instanceof Error ? error.message : "Unknown error",
			stack: error instanceof Error ? error.stack : undefined,
			duration: `${duration}ms`,
			errorCount: currentErrors + 1,
			timestamp: new Date().toISOString()
		});

		// Fail-safe: assume webhooks are enabled in case of error (safer for business)
		// Don't cache errors to allow retry
		return true;
	}
}

// Function to invalidate cache when webhook settings change
export function invalidateWebhookCache(organizationId?: string, gatewayType?: string) {
	if (organizationId && gatewayType) {
		const cacheKey = `${organizationId}:${gatewayType}`;
		webhookStatusCache.delete(cacheKey);
		webhookCheckErrors.delete(cacheKey); // Also clear error count
		logger.info("Webhook cache invalidated", { organizationId, gatewayType, cacheKey });
	} else {
		// Clear all cache
		webhookStatusCache.clear();
		webhookCheckErrors.clear(); // Also clear all error counts
		logger.info("All webhook cache cleared");
	}
}

// Test connection to Microcash API via proxy
export async function testMicrocashConnection(
	organizationId: string,
): Promise<any> {
	try {
		logger.info("Testing connection to Microcash API via proxy");

		// Test with a simple status check
		const testResult = await getTransactionStatus({
			transactionId: "test_connection",
			organizationId: organizationId,
		});

		logger.info("Microcash connection test via proxy successful");
		return { success: true, message: "Connection test successful" };
	} catch (error) {
		logger.error("Error testing Microcash connection via proxy", { error });
		return {
			success: false,
			error: error instanceof Error ? error.message : String(error),
		};
	}
}

// Create a Pix payment (charge) - PIX IN via proxy
export async function createPixPayment(params: {
	amount: number;
	customerName: string;
	customerEmail?: string;
	customerPhone?: string;
	customerDocument?: string;
	customerDocumentType?: string;
	description?: string;
	postbackUrl?: string;
	organizationId: string;
	externalCode?: string;
	metadata?: Record<string, any>;
	idempotencyKey?: string;
}): Promise<any> {
	try {
		logger.info("Creating Microcash PIX payment via proxy", {
			params: { ...params, amount: params.amount },
		});

		// Pre-validate amount
		const amountValidation = validateMicrocashAmount(params.amount);
		if (!amountValidation.isValid) {
			throw amountValidation.error;
		}

		// Clean document (remove non-digits)
		const cleanDocument =
			params.customerDocument?.replace(/\D/g, "") || "12345678910";
		const documentType = cleanDocument.length === 14 ? "CNPJ" : "CPF";

		// Prepare request data for Microcash Proxy
		const pixRequestData = {
			calendario: {
				expiracao: 1800, // 30 minutes
			},
			devedor: {
				nome: params.customerName,
				tipo: documentType,
				documento: cleanDocument,
			},
			valor: {
				original: params.amount.toFixed(2), // Amount is already in reais
			},
			chave: "ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f", // Microcash PIX key
			solicitacaoPagador: truncateDescription(
				params.description || `Pagamento via ${params.customerEmail}`,
				140,
			),
			infoAdicionais: [
				{
					nome: "email",
					valor: params.customerEmail || "<EMAIL>",
				},
				{
					nome: "externalCode",
					valor: params.externalCode || `microcash_${Date.now()}`,
				},
			],
			provider: "microcash",
		};

		// Add phone if provided
		if (params.customerPhone) {
			pixRequestData.infoAdicionais.push({
				nome: "telefone",
				valor: params.customerPhone.replace(/\D/g, ""),
			});
		}

		logger.info("Microcash createPixPayment request data", {
			amount: pixRequestData.valor.original,
			customerName: pixRequestData.devedor.nome,
			customerDocument: `${cleanDocument.substring(0, 3)}...`,
			customerDocumentType: documentType,
			provider: "microcash",
		});

		// Verificar se a chave de API está configurada
		if (!proxyApiKey) {
			throw new Error("PIX_API_PROXY_KEY não está configurada");
		}

		// Log detalhado da requisição para debug
		logger.info("Microcash - Preparando requisição para PIX Proxy:", {
			url: `${MICROCASH_PROXY_BASE_URL}${MICROCASH_PROXY_PATHS.create}`,
			headers: {
				"Content-Type": "application/json",
				"User-Agent": "Pluggou-Microcash/1.0",
				"x-pluggou-key": proxyApiKey ? "***PRESENT***" : "MISSING",
				"x-pluggou-key-length": proxyApiKey?.length || 0,
			},
			proxyApiKeyPrefix: proxyApiKey ? proxyApiKey.substring(0, 10) + "..." : "undefined",
			requestDataKeys: Object.keys(pixRequestData)
		});

		// Make request to Microcash Proxy
		const response = await fetch(
			`${MICROCASH_PROXY_BASE_URL}${MICROCASH_PROXY_PATHS.create}`,
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "Pluggou-Microcash/1.0",
					"x-pluggou-key": proxyApiKey,
				},
				body: JSON.stringify(pixRequestData),
			},
		);

		if (!response.ok) {
			const errorText = await response.text();
			let errorDetails = {};

			// Tentar parsear detalhes do erro se for JSON
			try {
				errorDetails = JSON.parse(errorText);
			} catch {
				errorDetails = { rawError: errorText };
			}

			logger.error("Microcash createPixPayment error", {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
				errorDetails,
				requestUrl: `${MICROCASH_PROXY_BASE_URL}${MICROCASH_PROXY_PATHS.create}`,
				requestData: pixRequestData,
			});

			throw new Error(`Microcash API error: ${response.status} - ${errorText}`);
		}

		const result = await response.json();

		logger.info("Resposta completa do pix-api-proxy", {
			result,
			resultKeys: Object.keys(result),
			qrCodeText: result.qrCodeText ? "present" : "missing",
			qrCode: result.qrCode ? "present" : "missing",
			transactionId: result.transactionId,
		});

		// Gerar imagem QR code a partir do código PIX copia-e-cola
		let qrCodeImage = "";
		if (result.qrCodeText) {
			try {
				qrCodeImage = await generatePixQRCodeImage(result.qrCodeText);
				logger.info("QR code image generated successfully", {
					transactionId: result.transactionId,
					imageLength: qrCodeImage.length,
				});
			} catch (qrError) {
				logger.error("Failed to generate QR code image", {
					error: qrError,
					transactionId: result.transactionId,
				});
			}
		}

		logger.info("Microcash createPixPayment success", {
			transactionId: result.transactionId,
			status: result.status,
			qrCode: result.qrCode ? "present" : "missing",
			qrCodeImage: qrCodeImage ? "generated" : "missing",
		});

		// Decodificar o código PIX se estiver em base64
		let pixCode = result.qrCodeText;
		try {
			// Verificar se o código está em base64
			if (
				pixCode &&
				/^[A-Za-z0-9+/=]+$/.test(pixCode) &&
				pixCode.length > 100
			) {
				const decoded = Buffer.from(pixCode, "base64").toString("utf-8");
				// Verificar se o resultado decodificado parece um código PIX EMV
				if (
					decoded.startsWith("000201") &&
					decoded.includes("br.gov.bcb.pix")
				) {
					pixCode = decoded;
					logger.info("Código PIX decodificado de base64", {
						transactionId: result.transactionId,
						originalLength: result.qrCodeText.length,
						decodedLength: pixCode.length,
					});
				}
			}
		} catch (error) {
			logger.warn("Erro ao tentar decodificar código PIX", {
				error: error instanceof Error ? error.message : String(error),
				transactionId: result.transactionId,
			});
		}

		return {
			success: true,
			externalId: result.transactionId,
			pixCode: pixCode, // Código PIX EMV puro (decodificado se necessário)
			pixQrCode: qrCodeImage, // Imagem QR code gerada (base64)
			pixExpiresAt: result.expiresAt,
			pix: {
				payload: pixCode, // Código PIX EMV puro (decodificado se necessário)
				encodedImage: qrCodeImage, // Imagem QR code gerada
				expirationDate: result.expiresAt,
			},
			providerData: result.providerData,
			metadata: {
				...params.metadata,
				microcash: {
					txId: result.transactionId,
					pixKey: "ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f",
					provider: "MICROCASH",
				},
			},
		};
	} catch (error) {
		logger.error("Microcash createPixPayment failed", {
			error: error instanceof Error ? error.message : error,
			customerEmail: params.customerEmail,
			amount: params.amount,
		});
		throw error;
	}
}

// Process PIX withdrawal (PIX OUT) via PIX-API-Proxy
export async function processPixWithdrawal(params: {
	amount: number;
	pixKey: string;
	pixKeyType: string;
	postbackUrl?: string;
	organizationId: string;
	transactionId?: string;
	description?: string;
	idempotencyKey?: string;
}): Promise<any> {
	const startTime = Date.now();
	
	try {
		logger.info("processPixWithdrawal - Iniciando transferência PIX via proxy", {
			organizationId: params.organizationId,
			amount: params.amount,
			pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined,
			pixKeyType: params.pixKeyType,
			description: params.description,
			transactionId: params.transactionId,
			idempotencyKey: params.idempotencyKey,
			proxyBaseUrl: MICROCASH_PROXY_BASE_URL,
			timestamp: new Date().toISOString()
		});

		// Validar parâmetros obrigatórios
		if (!params.amount || params.amount <= 0) {
			throw new Error("Amount is required and must be greater than 0");
		}

		if (!params.pixKey) {
			throw new Error("PIX key is required");
		}

		// Validar limites de transação
		const amountValidation = validateMicrocashAmount(params.amount);
		if (!amountValidation.isValid) {
			throw amountValidation.error;
		}

		const requestUrl = `${MICROCASH_PROXY_BASE_URL}/api/v1/pix/transfer`;
		
		// Preparar payload para o PIX-API-Proxy
		const payload = {
			pixKey: params.pixKey,
			amount: params.amount,
			description: params.description || "Transferência PIX",
			provider: "microcash"
		};

		logger.info("processPixWithdrawal - Preparando requisição", {
			organizationId: params.organizationId,
			requestUrl,
			requestMethod: "POST",
			payload: {
				...payload,
				pixKey: payload.pixKey ? `${payload.pixKey.substring(0, 4)}***` : undefined
			},
			headers: {
				"Content-Type": "application/json",
				"x-pluggou-key": proxyApiKey ? "***present***" : "missing"
			},
			timestamp: new Date().toISOString()
		});

		const response = await fetch(requestUrl, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"x-pluggou-key": proxyApiKey,
			},
			body: JSON.stringify(payload)
		});

		logger.info("processPixWithdrawal - Resposta recebida", {
			organizationId: params.organizationId,
			status: response.status,
			statusText: response.statusText,
			headers: Object.fromEntries(response.headers.entries()),
			timestamp: new Date().toISOString()
		});

		if (!response.ok) {
			const errorText = await response.text();
			logger.error("processPixWithdrawal - Requisição falhou", {
				organizationId: params.organizationId,
				status: response.status,
				statusText: response.statusText,
				errorBody: errorText,
				requestUrl,
				payload: {
					...payload,
					pixKey: payload.pixKey ? `${payload.pixKey.substring(0, 4)}***` : undefined
				},
				timestamp: new Date().toISOString()
			});
			throw new Error(
				`Microcash transfer request failed: ${response.status} - ${errorText}`,
			);
		}

		const data = await response.json();
		const duration = Date.now() - startTime;
		
		logger.info("processPixWithdrawal - Dados de transferência recebidos", {
			organizationId: params.organizationId,
			transactionId: data.transactionId,
			endToEndId: data.endToEndId,
			status: data.status,
			amount: data.amount,
			provider: data.provider,
			dataKeys: Object.keys(data),
			duration: `${duration}ms`,
			timestamp: new Date().toISOString()
		});

		// Mapear status da Microcash para status interno
		const internalStatus = mapMicrocashStatusToInternal(data.status);

		const result = {
			success: true,
			transactionId: data.transactionId,
			endToEndId: data.endToEndId,
			status: internalStatus,
			amount: data.amount,
			createdAt: new Date(data.createdAt * 1000).toISOString(),
			provider: "microcash",
			providerData: data.providerData,
			originalStatus: data.status,
			// IDs específicos da Microcash para webhook matching
			tid: data.tid,
			pagamentoIndiretoId: data.pagamentoIndiretoId,
			// Mapear para campos esperados pelo sistema
			id: data.transactionId,
			txid: data.transactionId,
			id_envio: data.transactionId
		};

		logger.info("processPixWithdrawal - Retornando resultado", {
			organizationId: params.organizationId,
			resultSuccess: result.success,
			resultTransactionId: result.transactionId,
			resultStatus: result.status,
			resultAmount: result.amount,
			duration: `${duration}ms`,
			timestamp: new Date().toISOString()
		});

		return result;
	} catch (error) {
		const duration = Date.now() - startTime;
		
		logger.error("processPixWithdrawal - Erro na transferência", {
			organizationId: params.organizationId,
			amount: params.amount,
			pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined,
			error: error instanceof Error ? error.message : "Unknown error",
			stack: error instanceof Error ? error.stack : undefined,
			duration: `${duration}ms`,
			timestamp: new Date().toISOString()
		});
		throw error;
	}
}

// Map Microcash status to internal status
function mapMicrocashStatusToInternal(microcashStatus: number): string {
	const statusMap: Record<number, string> = {
		0: "PENDING",
		1: "PROCESSING", 
		2: "APPROVED",    // Success - Concluded
		3: "REJECTED",    // Canceled - Error
		4: "REJECTED",    // Canceled - Failure
	};

	return statusMap[microcashStatus] || "PENDING";
}

// Get transaction status via proxy
export async function getTransactionStatus(params: {
	transactionId: string;
	organizationId: string;
	transactionType?: "CHARGE" | "SEND";
}): Promise<any> {
	try {
		logger.info("Getting Microcash transaction status via proxy", {
			transactionId: params.transactionId,
			organizationId: params.organizationId,
			transactionType: params.transactionType,
		});

		// Verificar se a chave de API está configurada
		if (!proxyApiKey) {
			throw new Error("PIX_API_PROXY_KEY não está configurada");
		}

		// Make request to Microcash Proxy
		const response = await fetch(
			`${MICROCASH_PROXY_BASE_URL}${MICROCASH_PROXY_PATHS.status(params.transactionId)}?provider=microcash`,
			{
				method: "GET",
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "Pluggou-Microcash/1.0",
					"x-pluggou-key": proxyApiKey,
				},
			},
		);

		if (!response.ok) {
			const errorText = await response.text();
			logger.error("Microcash getTransactionStatus error", {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
				transactionId: params.transactionId,
			});
			throw new Error(`Microcash API error: ${response.status} - ${errorText}`);
		}

		const result = await response.json();

		logger.info("Microcash getTransactionStatus success", {
			transactionId: result.transactionId,
			status: result.status,
		});

		return {
			success: true,
			transactionId: result.transactionId,
			status: result.status, // Status já mapeado pelo proxy
			amount: result.amount,
			createdAt: result.createdAt,
			updatedAt: result.updatedAt,
			providerData: result.providerData,
		};
	} catch (error) {
		logger.error("Microcash getTransactionStatus failed", {
			error: error instanceof Error ? error.message : error,
			transactionId: params.transactionId,
		});
		throw error;
	}
}

// Process refund
export async function processRefund(params: {
	transactionId: string;
	amount: number;
	reason?: string;
	organizationId: string;
}): Promise<any> {
	try {
		logger.info("Processing Microcash refund", {
			transactionId: params.transactionId,
			amount: params.amount,
			organizationId: params.organizationId,
			reason: params.reason,
		});

		// Microcash doesn't have a direct refund endpoint based on the documentation
		// This would need to be implemented as a PIX OUT transaction to the original payer
		throw new Error(
			"Microcash refund functionality not implemented - contact support for manual refunds",
		);
	} catch (error) {
		logger.error("Error processing refund", {
			error: error instanceof Error ? error.message : String(error),
			transactionId: params.transactionId,
			organizationId: params.organizationId,
		});
		throw error;
	}
}

// Validate Microcash webhook signature
export function validateMicrocashWebhookSignature(
	body: string,
	signature: string,
	secretKey: string | undefined,
): boolean {
	try {
		if (!secretKey) {
			logger.warn(
				"Microcash webhook secret not configured, skipping signature validation",
			);
			return true;
		}

		// Compute expected signature (implementation depends on Microcash's signature method)
		const computedSignature = crypto
			.createHmac("sha256", secretKey)
			.update(body)
			.digest("hex");

		// Compare signatures securely
		const isValid = crypto.timingSafeEqual(
			Buffer.from(signature),
			Buffer.from(computedSignature),
		);

		if (!isValid) {
			logger.warn("Invalid Microcash webhook signature", {
				receivedSignature: signature,
				computedSignature,
			});
		}

		return isValid;
	} catch (error) {
		logger.error("Error validating Microcash webhook signature", { error });
		return false;
	}
}

// Webhook handler for Microcash
export async function webhookHandler(params: {
	body: any;
	headers: Record<string, string>;
	organizationId?: string;
}): Promise<any> {
	const startTime = Date.now();
	
	try {
		logger.info("Processing Microcash webhook", {
			body: params.body,
			headers: Object.keys(params.headers),
			payloadKeys: Object.keys(params.body || {}),
			payloadSize: JSON.stringify(params.body || {}).length,
			timestamp: new Date().toISOString()
		});

		// Validate webhook signature (disabled by default for initial deployment)
		const signature =
			params.headers["x-microcash-signature"] || params.headers.signature;
		const webhookSecret = process.env.MICROCASH_WEBHOOK_SECRET;
		const bypassValidation =
			process.env.MICROCASH_BYPASS_SIGNATURE_VALIDATION !== "false"; // Default to true (bypass)

		if (
			!bypassValidation &&
			!validateMicrocashWebhookSignature(
				JSON.stringify(params.body),
				signature || "",
				webhookSecret,
			)
		) {
			throw new Error("Invalid webhook signature");
		}

		logger.info("Microcash webhook signature validation", {
			bypassValidation,
			hasSignature: !!signature,
			hasSecret: !!webhookSecret,
			allHeaders: params.headers,
			signatureHeader: signature,
		});

		// Extract transaction information from Microcash webhook payload
		const webhookData = params.body;
		const txId = webhookData.txId || webhookData.TxId;
		const endToEndId = webhookData.endToEndId || webhookData.EndToEndId;
		const status = webhookData.status || webhookData.Status;
		const amount = webhookData.valor || webhookData.Valor;
		const tid = webhookData.tid || webhookData.Tid;
		const pagamentoIndiretoId = webhookData.pagamentoIndiretoId || webhookData.PagamentoIndiretoId;

		logger.info("Microcash webhook data extracted", {
			txId,
			endToEndId,
			status,
			amount,
			tid,
			pagamentoIndiretoId,
		});

		// Find the transaction in our database using hybrid search strategy
		let transaction = null;

		logger.info("Searching for transaction in database", {
			txId,
			tid,
			pagamentoIndiretoId,
			searchAttempts: []
		});

		// STRATEGY 1: Traditional search (compatibility with existing webhooks)
		// Search by externalId first (most common approach)
		if (tid) {
			logger.info("Searching by tid (externalId)", { tid });
			transaction = await db.transaction.findFirst({
				where: { externalId: tid },
			});
			logger.info("Search by tid result", { tid, found: !!transaction });
		}

		// Search by endToEndId if not found
		if (!transaction && endToEndId) {
			logger.info("Searching by endToEndId", { endToEndId });
			transaction = await db.transaction.findFirst({
				where: { endToEndId: endToEndId },
			});
			logger.info("Search by endToEndId result", { endToEndId, found: !!transaction });
		}

		// Search by referenceCode if not found
		if (!transaction && pagamentoIndiretoId) {
			logger.info("Searching by pagamentoIndiretoId (referenceCode)", { pagamentoIndiretoId });
			transaction = await db.transaction.findFirst({
				where: { referenceCode: pagamentoIndiretoId },
			});
			logger.info("Search by pagamentoIndiretoId result", { pagamentoIndiretoId, found: !!transaction });
		}

		// STRATEGY 2: Generic search in metadata (new approach)
		if (!transaction) {
			logger.info("Using generic metadata search strategy");
			const searchIds = [txId, tid, pagamentoIndiretoId].filter(Boolean);
			
			for (const searchId of searchIds) {
				if (!searchId) continue;
				
				logger.info("Generic search by ID", { searchId });
				
				// Search in metadata using JSON path queries
				transaction = await db.transaction.findFirst({
					where: {
						OR: [
							// Search in allProviderIds (generic approach)
							{
								metadata: {
									path: ["allProviderIds", "tid"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["allProviderIds", "pagamentoIndiretoId"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["allProviderIds", "transactionId"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["allProviderIds", "txid"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["allProviderIds", "id"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["allProviderIds", "id_envio"],
									equals: searchId
								}
							},
							// Search in provider-specific metadata
							{
								metadata: {
									path: ["microcash", "allIds", "tid"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["microcash", "allIds", "pagamentoIndiretoId"],
									equals: searchId
								}
							},
							{
								metadata: {
									path: ["microcash", "allIds", "transactionId"],
									equals: searchId
								}
							}
						],
					},
				});
				
				logger.info("Generic search by ID result", { searchId, found: !!transaction });
				
				if (transaction) {
					logger.info("Transaction found via generic search!", {
						transactionId: transaction.id,
						externalId: transaction.externalId,
						searchId,
						metadataKeys: Object.keys(transaction.metadata as any || {})
					});
					break;
				}
			}
		}

		if (!transaction) {
			logger.warn("Transaction not found for Microcash webhook", {
				txId,
				tid,
				pagamentoIndiretoId,
				status,
			});
			return { success: false, message: "Transaction not found" };
		}

		// Check if webhooks are enabled for this organization's gateway
		let webhookEnabled = true; // Default to true for safety
		try {
			webhookEnabled = await isWebhookEnabledForGateway(
				transaction.organizationId,
				"MICROCASH"
			);
		} catch (webhookCheckError) {
			logger.error("Error checking webhook status, defaulting to enabled", {
				transactionId: transaction.id,
				organizationId: transaction.organizationId,
				error: webhookCheckError instanceof Error ? webhookCheckError.message : String(webhookCheckError),
				timestamp: new Date().toISOString()
			});
			// Continue processing - fail-safe approach
		}

		const duration = Date.now() - startTime;

		if (!webhookEnabled) {
			logger.info("Webhook ignored - webhooks disabled for gateway", {
				transactionId: transaction.id,
				organizationId: transaction.organizationId,
				txId,
				tid,
				pagamentoIndiretoId,
				status,
				duration: `${duration}ms`,
				action: 'ignored',
				reason: 'webhooks_disabled',
				timestamp: new Date().toISOString()
			});

			// Return success but don't process the transaction
			return {
				success: true,
				data: {
					transactionId: transaction.id,
					txId,
					status: "ignored",
					reason: "webhooks_disabled",
					webhookProcessed: false,
				},
			};
		}

		logger.info("Webhook processing enabled - proceeding with transaction update", {
			transactionId: transaction.id,
			organizationId: transaction.organizationId,
			txId,
			tid,
			pagamentoIndiretoId,
			status,
			duration: `${duration}ms`,
			action: 'processing',
			timestamp: new Date().toISOString()
		});

		// Update transaction status using the proper service that handles balance updates
		const internalStatus = mapMicrocashStatusToInternal(status) as any;

		// Use updateTransactionStatus to ensure balance is updated when transaction is approved
		await updateTransactionStatus(
			transaction.id,
			internalStatus,
			internalStatus === "APPROVED" ? new Date() : undefined
		);

		// Update metadata separately to preserve webhook information
		await db.transaction.update({
			where: { id: transaction.id },
			data: {
				metadata: {
					...(transaction.metadata as Record<string, any>),
					lastWebhookEvent: "microcash_webhook",
					lastWebhookReceived: new Date().toISOString(),
					endToEndId: endToEndId,
					webhookData: webhookData,
					webhookProcessed: true,
				},
			},
		});

		const finalDuration = Date.now() - startTime;

		logger.info("Microcash webhook processed successfully", {
			transactionId: transaction.id,
			organizationId: transaction.organizationId,
			txId,
			tid,
			pagamentoIndiretoId,
			oldStatus: transaction.status,
			newStatus: internalStatus,
			duration: `${finalDuration}ms`,
			action: 'processed',
			webhookProcessed: true,
			timestamp: new Date().toISOString()
		});

		return {
			success: true,
			data: {
				transactionId: transaction.id,
				txId,
				status: internalStatus,
				endToEndId,
				webhookProcessed: true,
			},
		};
	} catch (error) {
		logger.error("Error processing Microcash webhook", { error, params });
		throw error;
	}
}

// Get account balance from Microcash via PIX-API-Proxy
export async function getMicrocashBalance(
	organizationId: string,
): Promise<any> {
	const startTime = Date.now();
	
	try {
		logger.info("getMicrocashBalance - Iniciando busca de saldo via proxy", {
			organizationId,
			proxyBaseUrl: MICROCASH_PROXY_BASE_URL,
			proxyApiKeyPresent: !!proxyApiKey,
			timestamp: new Date().toISOString()
		});

		const requestUrl = `${MICROCASH_PROXY_BASE_URL}/api/v1/pix/balance?provider=microcash`;
		
		logger.info("getMicrocashBalance - Preparando requisição", {
			organizationId,
			requestUrl,
			requestMethod: "GET",
			headers: {
				"Content-Type": "application/json",
				"x-pluggou-key": proxyApiKey ? "***present***" : "missing"
			},
			timestamp: new Date().toISOString()
		});

		const response = await fetch(requestUrl, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				"x-pluggou-key": proxyApiKey,
			},
		});

		logger.info("getMicrocashBalance - Resposta recebida", {
			organizationId,
			status: response.status,
			statusText: response.statusText,
			headers: Object.fromEntries(response.headers.entries()),
			timestamp: new Date().toISOString()
		});

		if (!response.ok) {
			const errorText = await response.text();
			logger.error("getMicrocashBalance - Requisição falhou", {
				organizationId,
				status: response.status,
				statusText: response.statusText,
				errorBody: errorText,
				requestUrl,
				timestamp: new Date().toISOString()
			});
			throw new Error(
				`Microcash balance request failed: ${response.status} - ${errorText}`,
			);
		}

		const data = await response.json();
		const duration = Date.now() - startTime;
		
		logger.info("getMicrocashBalance - Dados de saldo recebidos", {
			organizationId,
			balance: data.balance,
			accountId: data.accountId,
			timestamp: data.timestamp,
			provider: data.provider,
			dataKeys: Object.keys(data),
			duration: `${duration}ms`,
			logTimestamp: new Date().toISOString()
		});

		const result = {
			success: true,
			data: {
				accountId: data.accountId,
				balance: data.balance,
				timestamp: data.timestamp,
				provider: data.provider,
			},
		};

		logger.info("getMicrocashBalance - Retornando resultado", {
			organizationId,
			resultSuccess: result.success,
			resultDataKeys: Object.keys(result.data),
			duration: `${duration}ms`,
			timestamp: new Date().toISOString()
		});

		return result;
	} catch (error) {
		const duration = Date.now() - startTime;
		
		logger.error("getMicrocashBalance - Erro na busca de saldo", {
			organizationId,
			error: error instanceof Error ? error.message : "Unknown error",
			stack: error instanceof Error ? error.stack : undefined,
			duration: `${duration}ms`,
			timestamp: new Date().toISOString()
		});
		throw error;
	}
}

// Export all functions for the payment provider interface
export default {
	type: "MICROCASH",
	testMicrocashConnection,
	createPixPayment,
	processPixWithdrawal,
	getTransactionStatus,
	processRefund,
	webhookHandler,
	getMicrocashBalance,
};
