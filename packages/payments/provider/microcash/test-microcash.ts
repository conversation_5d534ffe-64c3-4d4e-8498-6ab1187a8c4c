import { microcash } from "./index";

// Test configuration
const TEST_CONFIG = {
  organizationId: "test-org-123",
  amount: 10.50,
  customerName: "VENDAS ONLINE STORE LTDA",
  customerEmail: "<EMAIL>",
  customerDocument: "27945891000105", // CNPJ da conta
  description: "Teste de pagamento PIX",
  pixKey: "11999999999",
  pixKeyType: "PHONE"
};

async function testMicrocashConnection() {
  console.log("🔍 Testing Microcash connection...");

  try {
    const result = await microcash.testMicrocashConnection(TEST_CONFIG.organizationId);
    console.log("✅ Connection test successful:", result);
    return true;
  } catch (error) {
    console.error("❌ Connection test failed:", error);
    return false;
  }
}

async function testMicrocashBalance() {
  console.log("💰 Testing Microcash balance...");

  try {
    const result = await microcash.getMicrocashBalance(TEST_CONFIG.organizationId);
    console.log("✅ Balance retrieved successfully:", result);
    return true;
  } catch (error) {
    console.error("❌ Balance test failed:", error);
    return false;
  }
}

async function testCreatePixPayment() {
  console.log("💳 Testing PIX payment creation...");

  try {
    const result = await microcash.createPixPayment({
      amount: TEST_CONFIG.amount,
      customerName: TEST_CONFIG.customerName,
      customerEmail: TEST_CONFIG.customerEmail,
      customerDocument: TEST_CONFIG.customerDocument,
      description: TEST_CONFIG.description,
      organizationId: TEST_CONFIG.organizationId,
      externalCode: `test_${Date.now()}`
    });

    console.log("✅ PIX payment created successfully:", {
      success: result.success,
      transactionId: result.transactionId,
      externalId: result.externalId,
      hasPixCode: !!result.pixCode,
      hasPixQrCode: !!result.pixQrCode
    });

    return result;
  } catch (error) {
    console.error("❌ PIX payment creation failed:", error);
    return null;
  }
}

async function testPixWithdrawal() {
  console.log("💸 Testing PIX withdrawal...");

  try {
    const result = await microcash.processPixWithdrawal({
      amount: 5.00,
      pixKey: TEST_CONFIG.pixKey,
      pixKeyType: TEST_CONFIG.pixKeyType,
      description: "Teste de transferência PIX",
      organizationId: TEST_CONFIG.organizationId,
      transactionId: `test_transfer_${Date.now()}`
    });

    console.log("✅ PIX withdrawal created successfully:", {
      success: result.success,
      status: result.data?.status,
      jobId: result.data?.jobId,
      amount: result.data?.amount
    });

    return result;
  } catch (error) {
    console.error("❌ PIX withdrawal failed:", error);
    return null;
  }
}

async function testWebhookHandler() {
  console.log("🔔 Testing webhook handler...");

  try {
    // Simulate a webhook payload based on Microcash documentation
    const webhookPayload = {
      tipoIniciacao: 3,
      prioridade: 0,
      tipoPrioridade: 0,
      finalidade: 0,
      agente: 0,
      ispbPss: null,
      cnpjIniciadorPagamento: null,
      pagador: {
        tipoPessoa: 0,
        documento: 1239360514,
        nome: "TESTE FREIRE DOS TESTES",
        conta: {
          agencia: "1",
          numero: "143134752",
          ispb: 416968,
          tipo: 0
        }
      },
      recebedor: {
        tipoPessoa: 0,
        documento: 12381237000107,
        conta: {
          agencia: "1",
          numero: "12339",
          ispb: 12312348,
          tipo: 0
        }
      },
      creditadoEm: "2024-06-17T12:24:13.6047759+00:00",
      valor: 50.0,
      valoresDetalhados: [],
      informacaoEntreClientes: null,
      txId: "test_tx_123456",
      endToEndId: "E00416968202406171524wl4l910LDDh",
      chavePix: "dccb1680-a21d-46a1-84a6-f15bae6888b2",
      correlationId: "c010a9d4-6a1a-4900-b7df-07b5090db1e2",
      status: 2,
      tid: "test_tid_123456"
    };

    const result = await microcash.webhookHandler({
      body: webhookPayload,
      headers: {
        'content-type': 'application/json',
        'user-agent': 'Microcash-Webhook/1.0'
      },
      organizationId: TEST_CONFIG.organizationId
    });

    console.log("✅ Webhook handler processed successfully:", result);
    return true;
  } catch (error) {
    console.error("❌ Webhook handler test failed:", error);
    return false;
  }
}

async function runAllTests() {
  console.log("🚀 Starting Microcash provider tests...\n");

  const results = {
    connection: false,
    balance: false,
    pixPayment: false,
    pixWithdrawal: false,
    webhook: false
  };

  // Test connection
  results.connection = await testMicrocashConnection();
  console.log("");

  // Test balance
  results.balance = await testMicrocashBalance();
  console.log("");

  // Test PIX payment creation
  const paymentResult = await testCreatePixPayment();
  results.pixPayment = !!paymentResult;
  console.log("");

  // Test PIX withdrawal
  const withdrawalResult = await testPixWithdrawal();
  results.pixWithdrawal = !!withdrawalResult;
  console.log("");

  // Test webhook handler
  results.webhook = await testWebhookHandler();
  console.log("");

  // Summary
  console.log("📊 Test Results Summary:");
  console.log("========================");
  console.log(`Connection: ${results.connection ? '✅' : '❌'}`);
  console.log(`Balance: ${results.balance ? '✅' : '❌'}`);
  console.log(`PIX Payment: ${results.pixPayment ? '✅' : '❌'}`);
  console.log(`PIX Withdrawal: ${results.pixWithdrawal ? '✅' : '❌'}`);
  console.log(`Webhook: ${results.webhook ? '✅' : '❌'}`);

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! Microcash provider is working correctly.");
  } else {
    console.log("⚠️  Some tests failed. Check the configuration and try again.");
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  testMicrocashConnection,
  testMicrocashBalance,
  testCreatePixPayment,
  testPixWithdrawal,
  testWebhookHandler,
  runAllTests
};
