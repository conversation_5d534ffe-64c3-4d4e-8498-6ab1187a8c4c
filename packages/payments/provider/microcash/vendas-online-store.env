# Configuração específica para VENDAS ONLINE STORE LTDA
# Copie estas variáveis para seu arquivo .env principal

# Environment
MICROCASH_ENVIRONMENT=production

# OAuth2 Credentials (da conta ativada)
MICROCASH_CLIENT_ID=3e9oa57mqbqun0kllsi3tnd0fk
MICROCASH_CLIENT_SECRET=bc3s732tiirrmagbsn52kvoorftmd9lce5crr4g1u9s5som3jql

# PIX Key (chave PIX da conta)
MICROCASH_PIX_KEY=ba44d5f5-e52f-4cb2-9588-dbe1c7c45b2f

# API Key (x-api-key for additional authentication)
MICROCASH_API_KEY=DECD359F-CE25-4697-968F-F26F97DF9B8E

# Webhook Configuration
MICROCASH_WEBHOOK_SECRET=DECD359F-CE25-4697-968F-F26F97DF9B8E
MICROCASH_BYPASS_SIGNATURE_VALIDATION=false

# Account Information
# Nome: VENDAS ONLINE STORE LTDA
# CNPJ: 27.945.891/0001-05
# Banco: Microcash
# Agência: 0001
# Conta: *********

# API URLs
# Production: https://apis-pix.bancomicrocash.com
# Sandbox: https://apis-pix-hml.bancomicrocash.com

# Webhook Endpoints (configure no painel do Microcash)
# Pix In Endpoint: https://seu-dominio.com/api/webhooks/microcash
# Pix Out Endpoint: https://seu-dominio.com/api/webhooks/microcash
# Pix Refund Endpoint: https://seu-dominio.com/api/webhooks/microcash
