#!/usr/bin/env node

/**
 * Teste de integração com o pix-api-proxy funcionando
 */

import { createPixPayment } from './index';
import { logger } from '@repo/logs';

async function testProxyIntegration() {
  console.log('🚀 Testando integração com pix-api-proxy...');
  console.log('URL do proxy:', process.env.PIX_API_PROXY_URL || 'https://pix-api-proxy-iuu5qv6jja-uc.a.run.app');
  console.log('');

  try {
    // Teste com os mesmos dados que funcionaram no curl
    const pixParams = {
      amount: 1000, // R$ 10,00 em centavos
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerDocument: '12345678901',
      customerDocumentType: 'CPF',
      description: 'Teste de PIX via API Proxy',
      organizationId: 'test-org-123',
      externalCode: `test_${Date.now()}`
    };

    console.log('📋 Dados da requisição:');
    console.log('- Valor: R$ 10,00');
    console.log('- Cliente:', pixParams.customerName);
    console.log('- CPF:', pixParams.customerDocument);
    console.log('- Descrição:', pixParams.description);
    console.log('');

    console.log('⏳ Criando PIX payment...');
    const result = await createPixPayment(pixParams);

    console.log('✅ PIX criado com sucesso!');
    console.log('📊 Resultado:');
    console.log('- Transaction ID:', result.transactionId);
    console.log('- Status:', result.status || 'N/A');
    console.log('- QR Code URL:', result.pixQrCode ? 'Presente' : 'Ausente');
    console.log('- QR Code Text:', result.pixCode ? `${result.pixCode.substring(0, 50)}...` : 'Ausente');
    console.log('- Expira em:', result.pixExpiresAt ? new Date(result.pixExpiresAt * 1000).toLocaleString() : 'N/A');
    console.log('');

    if (result.success) {
      console.log('🎉 Teste concluído com sucesso!');
      console.log('✅ O provider Ecomovi está funcionando corretamente com o pix-api-proxy');
    } else {
      console.log('❌ Teste falhou - success: false');
    }

    return result;

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
    console.error('');
    
    if (error instanceof Error) {
      console.error('📝 Detalhes do erro:');
      console.error('- Mensagem:', error.message);
      console.error('- Stack:', error.stack?.split('\n').slice(0, 5).join('\n'));
    }
    
    throw error;
  }
}

// Executar o teste se chamado diretamente
if (require.main === module) {
  testProxyIntegration()
    .then(() => {
      console.log('\n🏁 Teste finalizado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Teste falhou:', error);
      process.exit(1);
    });
}

export { testProxyIntegration };