// =========================
// VERSÃO HARDCODED PARA DEBUG LOCAL E2E
// NÃO USAR EM PRODUÇÃO!
// =========================

import { db } from '@repo/database';
import { logger } from '@repo/logs';
import { getGatewayCredentials } from '../factory';
import { TransactionStatus } from '@prisma/client';
import * as QRCode from 'qrcode';
import { updateTransactionStatus } from '../../src/transactions';
import { processMedInfraction } from '../../src/med/med-service';


// Helper function to clean and validate proxy URL with comprehensive checks
function getProxyUrl(): string {
	const fallbackUrl = 'https://pix-api-proxy.cloud.pluggou.io';
	
	// Try multiple environment variable names (case variations)
	const possibleEnvVars = [
		'PIX_API_PROXY_URL',
		'PIX_PROXY_URL', 
		'PIX_PROXY_BASE_URL',
		'PROXY_URL',
		'PIX_API_URL'
	];
	
	let envUrl: string | undefined;
	let usedEnvVar: string | undefined;
	
	// Find the first defined environment variable
	for (const envVar of possibleEnvVars) {
		const value = process.env[envVar];
		if (value && value.trim()) {
			envUrl = value;
			usedEnvVar = envVar;
			break;
		}
	}
	
	// Log all environment variables for debug
	logger.info("Ecomovi - Verificação de variáveis de ambiente PIX Proxy:", {
		checkedVariables: possibleEnvVars.map(envVar => ({
			name: envVar,
			defined: !!process.env[envVar],
			value: process.env[envVar] ? "***PRESENT***" : "undefined",
			length: process.env[envVar]?.length || 0
		})),
		selectedVariable: usedEnvVar,
		selectedValue: envUrl ? "***PRESENT***" : "undefined",
		selectedLength: envUrl?.length || 0
	});
	
	if (!envUrl) {
		logger.warn("Nenhuma variável de ambiente PIX Proxy encontrada, usando fallback", {
			fallbackUrl,
			checkedVariables: possibleEnvVars
		});
		return fallbackUrl;
	}
	
	// Clean the URL (remove leading @, trim whitespace, etc.)
	let cleanUrl = envUrl.trim();
	if (cleanUrl.startsWith('@')) {
		cleanUrl = cleanUrl.substring(1);
		logger.info("Removido @ do início da URL", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl
		});
	}
	
	// Validate URL
	try {
		new URL(cleanUrl);
		logger.info("URL do PIX Proxy validada com sucesso", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl,
			usedEnvVar,
			fallbackUrl
		});
		return cleanUrl;
	} catch (error) {
		logger.error("URL do PIX Proxy inválida, usando fallback", {
			originalUrl: envUrl,
			cleanedUrl: cleanUrl,
			usedEnvVar,
			error: error instanceof Error ? error.message : String(error),
			fallbackUrl
		});
		return fallbackUrl;
	}
}

// Base URL for Ecomovi Proxy API - configurável via variável de ambiente
const ECOMOVI_PROXY_BASE_URL = getProxyUrl();

// Debug: Log das variáveis de ambiente para verificar se estão sendo lidas corretamente
logger.info("Ecomovi - Variáveis de ambiente PIX Proxy:", {
	PIX_API_PROXY_URL: process.env.PIX_API_PROXY_URL ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	PIX_API_PROXY_URL_LENGTH: process.env.PIX_API_PROXY_URL?.length || 0,
	PIX_API_PROXY_URL_RAW: process.env.PIX_API_PROXY_URL || "undefined",
	PIX_API_PROXY_KEY: process.env.PIX_API_PROXY_KEY ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	FINAL_PROXY_URL: ECOMOVI_PROXY_BASE_URL,
	USING_FALLBACK: !process.env.PIX_API_PROXY_URL,
	ENVIRONMENT: process.env.NODE_ENV || "undefined",
	VERCEL_ENV: process.env.VERCEL_ENV || "undefined"
});

// API paths for Ecomovi Proxy
const ECOMOVI_PROXY_PATHS = {
	create: '/api/v1/pix/create',
	createCashout: '/api/v1/pix/create-cashout',
	status: (id: string) => `/api/v1/pix/status/${id}`,
	statusCashout: (id: string) => `/api/v1/pix/status-cashout/${id}`,
	refund: '/api/v1/pix/refund',
	cancel: (id: string) => `/api/v1/pix/cancel/${id}`,
};

// Chave PIX fixa da Ecomovi
const ECOMOVI_PIX_KEY = '36ae341f-e369-4e46-ac4b-efd126a5b56e';

// PIX_API_PROXY_KEY: Chave secreta para autenticação com o pix-api-proxy - configurável via variável de ambiente
const proxyApiKey = process.env.PIX_API_PROXY_KEY || 'pluggou_proxy_2e7c1b7e-4a2b-4c8e-9f1e-8d2e7c1b7e4a';

// Debug: Log da chave de API para verificar se está sendo lida corretamente
logger.info("Ecomovi - Chave de API PIX Proxy:", {
	PIX_API_PROXY_KEY_ENV: process.env.PIX_API_PROXY_KEY ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	PIX_API_PROXY_KEY_LENGTH: process.env.PIX_API_PROXY_KEY?.length || 0,
	PIX_API_PROXY_KEY_PREFIX: process.env.PIX_API_PROXY_KEY ? process.env.PIX_API_PROXY_KEY.substring(0, 10) + "..." : "undefined",
	FINAL_PROXY_KEY: proxyApiKey ? "***CONFIGURADA***" : "NÃO CONFIGURADA",
	FINAL_PROXY_KEY_LENGTH: proxyApiKey?.length || 0,
	USING_FALLBACK_KEY: !process.env.PIX_API_PROXY_KEY
});

// Função utilitária para gerar QR code a partir do código PIX
export async function generatePixQRCodeImage(pixCode: string): Promise<string> {
	try {
		// Gerar QR code como base64
		const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
			type: 'image/png',
			width: 256,
			margin: 2,
			color: {
				dark: '#000000',
				light: '#FFFFFF'
			}
		});

		// Retornar apenas a parte base64 (sem o prefixo data:image/png;base64,)
		return qrCodeDataURL.split(',')[1];
	} catch (error) {
		logger.error('Erro ao gerar QR code para PIX', { error, pixCode: pixCode?.substring(0, 50) });
		// Retornar string vazia em caso de erro
		return '';
	}
}

// Helper to get the Ecomovi environment for an organization
export async function getEcomoviEnvironment(
	organizationId: string
): Promise<'production' | 'sandbox'> {
	try {
		const credentials = await getGatewayCredentials(organizationId, 'ECOMOVI');
		return (
			(credentials.environment as 'production' | 'sandbox') || 'production'
		);
	} catch (error) {
		logger.error('Error getting Ecomovi environment', {
			error,
			organizationId,
		});
		return 'production'; // Default to production
	}
}

// Importar o módulo de idempotência
let generateIdempotencyKey: (
	operation: string,
	params: Record<string, any>
) => string;

try {
	const idempotencyModule = require('../../src/idempotency');
	generateIdempotencyKey = idempotencyModule.generateIdempotencyKey;
} catch (importError) {
	logger.error('Erro ao importar módulo de idempotência', { importError });
	// Criar função mock para evitar erros
	generateIdempotencyKey = (_operation: string, _params: Record<string, any>) =>
		`mock_${Date.now()}`;
}

// Create a Pix payment (charge)
export async function createPixPayment(params: {
	amount: number;
	customerName: string;
	customerEmail: string;
	customerPhone?: string;
	customerDocument?: string;
	customerDocumentType?: string;
	description?: string;
	postbackUrl?: string;
	organizationId: string;
	externalCode?: string;
	metadata?: Record<string, any>;
	idempotencyKey?: string;
}): Promise<any> {
	// Implementar idempotência simples usando transações existentes como chave
	const simpleIdempotencyKey =
		params.idempotencyKey ||
		generateIdempotencyKey('ecomovi.createPixPayment', {
			customerEmail: params.customerEmail,
			amount: params.amount,
			organizationId: params.organizationId,
			timestamp: Math.floor(Date.now() / 60000), // Agrupar por minuto
		});

	// Verificar se já existe uma transação com esta chave de idempotência
	try {
		const existingTransaction = await db.transaction.findFirst({
			where: {
				organizationId: params.organizationId,
				customerEmail: params.customerEmail,
				amount: params.amount,
				status: { in: ['PENDING', 'APPROVED'] },
				createdAt: {
					gte: new Date(Date.now() - 5 * 60 * 1000), // Últimos 5 minutos
				},
			},
			orderBy: { createdAt: 'desc' },
		});

		if (existingTransaction) {
			logger.info('Retornando transação existente para evitar duplicação', {
				transactionId: existingTransaction.id,
				idempotencyKey: simpleIdempotencyKey,
			});

			// Verificar se precisa gerar QR code para transação existente
			let existingQrCode = (existingTransaction.metadata as any)?.pixQrCode;
			const existingPixCode = (existingTransaction.metadata as any)?.pixCode;

			// Se não tem QR code mas tem o código PIX, gerar a imagem
			if (!existingQrCode && existingPixCode) {
				try {
					existingQrCode = await generatePixQRCodeImage(existingPixCode);
					logger.info('QR code gerado para transação existente', {
						transactionId: existingTransaction.id,
						imageLength: existingQrCode.length
					});
				} catch (qrError) {
					logger.error('Erro ao gerar QR code para transação existente', {
						error: qrError,
						transactionId: existingTransaction.id
					});
				}
			}

			return {
				success: true,
				transactionId: existingTransaction.id,
				externalId: existingTransaction.externalId,
				pixCode: existingPixCode,
				pixQrCode: existingQrCode,
				pixExpiresAt: (existingTransaction.metadata as any)?.pixExpiresAt,
				pix: {
					payload: existingPixCode,
					encodedImage: existingQrCode,
					expirationDate: (existingTransaction.metadata as any)?.pixExpiresAt,
				},
			};
		}
	} catch (checkError) {
		logger.warn(
			'Erro ao verificar transação existente, prosseguindo com criação',
			{ checkError }
		);
	}

	try {
		const {
			amount,
			customerName,
			customerEmail,
			customerPhone,
			customerDocument,
			customerDocumentType = 'CPF',
			description,
			organizationId,
			externalCode,
			metadata,
		} = params;

		// Log customer document information for debugging
		logger.info('Ecomovi createPixPayment customer document info', {
			customerDocument: customerDocument
				? `${customerDocument.substring(0, 3)}...`
				: 'not provided',
			customerDocumentType,
		});

		// If customerDocument is not provided, try to get it from organization legal info
		let finalCustomerDocument = customerDocument;
		let finalCustomerDocumentType = customerDocumentType;

		if (!finalCustomerDocument) {
			logger.info(
				'Customer document not provided, attempting to fetch from organization legal info',
				{ organizationId }
			);

			try {
				const organizationLegalInfo = await db.organization_legal_info.findUnique({
					where: { organizationId },
					select: { document: true, documentType: true },
				});

				if (organizationLegalInfo?.document) {
					finalCustomerDocument = organizationLegalInfo.document;
					finalCustomerDocumentType = organizationLegalInfo.documentType || 'CPF';

					logger.info('Using organization legal info for customer document', {
						hasLegalInfo: !!organizationLegalInfo,
						documentType: finalCustomerDocumentType,
					});
				}
			} catch (orgError) {
				logger.warn('Error fetching organization legal info', {
					orgError,
					organizationId,
				});
			}
		}

		// Validate customer document
		if (!finalCustomerDocument) {
			throw new Error('Customer document is required for Ecomovi PIX payments');
		}

		// Detect document type automatically based on length if not provided
		if (!finalCustomerDocumentType) {
			const cleanDocument = finalCustomerDocument.replace(/\D/g, '');
			if (cleanDocument.length === 11) {
				finalCustomerDocumentType = 'CPF';
			} else if (cleanDocument.length === 14) {
				finalCustomerDocumentType = 'CNPJ';
			} else {
				logger.warn('Invalid document length, defaulting to CPF', {
					documentLength: cleanDocument.length,
					document: `${cleanDocument.substring(0, 3)}...`
				});
				finalCustomerDocumentType = 'CPF';
			}
		}

		// Clean document (remove non-digits)
		const cleanDocument = finalCustomerDocument.replace(/\D/g, '');
		const documentType = cleanDocument.length === 14 ? 'CNPJ' : 'CPF';

		// Log document type for debugging
		logger.info('Document type detected', {
			originalDocument: finalCustomerDocument,
			cleanDocument: `${cleanDocument.substring(0, 3)}...`,
			documentType: finalCustomerDocumentType,
			documentLength: cleanDocument.length
		});

		// Prepare request data for Ecomovi Proxy
		// Truncar descrição para máximo de 140 caracteres (limite da Ecomovi)
		const rawDescription = description || `Pagamento via ${customerEmail}`;
		const truncatedDescription = rawDescription.length > 140
			? rawDescription.substring(0, 137) + '...'
			: rawDescription;

		const pixRequestData: {
			calendario: { expiracao: number };
			devedor: { nome: string; tipo: string; documento: string };
			valor: { original: string };
			chave: string;
			solicitacaoPagador: string;
			infoAdicionais: Array<{ nome: string; valor: string }>;
			provider: string;
		} = {
			calendario: {
				expiracao: 86400, // 1 dia de expiração (24 horas)
			},
			devedor: {
				nome: customerName,
				tipo: documentType,
				documento: cleanDocument,
			},
			valor: {
				original: amount.toFixed(2), // Amount is already in reais, not centavos
			},
			chave: ECOMOVI_PIX_KEY,
			solicitacaoPagador: truncatedDescription,
			infoAdicionais: [
				{
					nome: 'email',
					valor: customerEmail,
				},
				{
					nome: 'externalCode',
					valor: externalCode || `pix_${Date.now()}`,
				},
			],
			provider: 'ecomovi',
		};

		// Add phone if provided
		if (customerPhone) {
			pixRequestData.infoAdicionais.push({
				nome: 'telefone',
				valor: customerPhone.replace(/\D/g, ''),
			});
		}

		logger.info('Ecomovi createPixPayment request data', {
			amount: pixRequestData.valor.original,
			customerName: pixRequestData.devedor.nome,
			customerDocument: `${cleanDocument.substring(0, 3)}...`,
			customerDocumentType: finalCustomerDocumentType,
			pixKey: ECOMOVI_PIX_KEY,
		});

		// Verificar se a chave de API está configurada
		if (!proxyApiKey) {
			throw new Error('PIX_API_PROXY_KEY não está configurada');
		}

		// Make request to Ecomovi Proxy
		const response = await fetch(
			`${ECOMOVI_PROXY_BASE_URL}${ECOMOVI_PROXY_PATHS.create}`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'User-Agent': 'Pluggou-Ecomovi/1.0',
					'x-pluggou-key': proxyApiKey || '',
				},
				body: JSON.stringify(pixRequestData),
			}
		);

		if (!response.ok) {
			const errorText = await response.text();
			logger.error('Ecomovi createPixPayment error', {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
			});
			throw new Error(`Ecomovi API error: ${response.status} - ${errorText}`);
		}

		const result = await response.json();

		// Gerar imagem QR code a partir do código PIX copia-e-cola
		let qrCodeImage = '';
		if (result.qrCodeText) {
			try {
				qrCodeImage = await generatePixQRCodeImage(result.qrCodeText);
				logger.info('QR code image generated successfully', {
					transactionId: result.transactionId,
					imageLength: qrCodeImage.length
				});
			} catch (qrError) {
				logger.error('Failed to generate QR code image', {
					error: qrError,
					transactionId: result.transactionId
				});
			}
		}

		logger.info('Ecomovi createPixPayment success', {
			transactionId: result.transactionId,
			status: result.status,
			qrCode: result.qrCode ? 'present' : 'missing',
			qrCodeImage: qrCodeImage ? 'generated' : 'missing',
		});

		return {
			success: true,
			// Não retornar transactionId para que o sistema crie a transação no banco
			externalId: result.transactionId,
			pixCode: result.qrCodeText,
			pixQrCode: qrCodeImage || result.qrCode, // Usar a imagem gerada ou fallback para o original
			pixExpiresAt: result.expiresAt,
			pix: {
				payload: result.qrCodeText,
				encodedImage: qrCodeImage || result.qrCode, // Usar a imagem gerada ou fallback
				expirationDate: result.expiresAt,
			},
			providerData: result.providerData,
			metadata: {
				...metadata,
				ecomovi: {
					txId: result.transactionId,
					pixKey: ECOMOVI_PIX_KEY,
					idempotencyKey: simpleIdempotencyKey,
				},
			},
		};
	} catch (error) {
		logger.error('Ecomovi createPixPayment failed', {
			error: error instanceof Error ? error.message : error,
			customerEmail: params.customerEmail,
			amount: params.amount,
		});
		throw error;
	}
}

// Process PIX withdrawal
export async function processPixWithdrawal(params: {
	amount: number;
	pixKey: string;
	pixKeyType: string;
	postbackUrl?: string;
	organizationId: string;
	transactionId?: string;
	customerName?: string;
	customerDocument?: string;
	description?: string;
	metadata?: Record<string, any>;
	idempotencyKey?: string;
}): Promise<any> {
	// Usar a mesma forma de gerar idempotency key do test-transfer-cnpj.sh
	const timestamp = Math.floor(Date.now() / 1000); // Timestamp em segundos
	const simpleIdempotencyKey = params.idempotencyKey || `ecomovi-cashout-${timestamp}`;

	logger.info('Ecomovi processPixWithdrawal - usando idempotency key simples', {
		idempotencyKey: simpleIdempotencyKey,
		timestamp,
		amount: params.amount,
		pixKey: params.pixKey,
		organizationId: params.organizationId,
	});

	// Verificar se já existe uma transação de withdrawal com esta chave de idempotência específica
	try {
		const existingTransaction = await db.transaction.findFirst({
			where: {
				organizationId: params.organizationId,
				amount: params.amount,
				type: 'SEND',
				status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] },
				createdAt: {
					gte: new Date(Date.now() - 2 * 60 * 1000), // Últimos 2 minutos apenas
				},
			},
			orderBy: { createdAt: 'desc' },
		});

		// Verificar se a transação existente tem a mesma chave de idempotência
		if (existingTransaction) {
			const existingMetadata = existingTransaction.metadata as any;
			const existingIdempotencyKey = existingMetadata?.ecomovi?.idempotencyKey;

			if (existingIdempotencyKey === simpleIdempotencyKey) {
				logger.info(
					'Retornando transação de withdrawal existente para evitar duplicação',
					{
						transactionId: existingTransaction.id,
						idempotencyKey: simpleIdempotencyKey,
					}
				);

				return {
					success: true,
					transactionId: existingTransaction.id,
					externalId: existingTransaction.externalId,
					status: existingTransaction.status,
					amount: existingTransaction.amount,
					metadata: {
						...params.metadata,
						ecomovi: {
							idempotencyKey: simpleIdempotencyKey,
							pixKey: params.pixKey,
						},
					},
				};
			}
		}
	} catch (checkError) {
		logger.warn(
			'Erro ao verificar transação de withdrawal existente, prosseguindo com criação',
			{ checkError }
		);
	}

	const maxRetries = 3;
	const maxTokenRetries = 2;
	let tokenRetryCount = 0;
	let lastError: Error | null = null;

	for (let attempt = 0; attempt < maxRetries; attempt++) {
		try {
			logger.info('Ecomovi processPixWithdrawal started', {
				amount: params.amount,
				pixKey: params.pixKey,
				pixKeyType: params.pixKeyType,
				organizationId: params.organizationId,
				attempt: attempt + 1,
				maxRetries,
				tokenRetryCount,
				maxTokenRetries,
			});

			const {
				amount,
				pixKey,
				pixKeyType,
				organizationId,
				transactionId,
				customerName,
				customerDocument,
				description,
				metadata,
			} = params;

			if (!pixKey) {
				throw new Error('PIX key is required for withdrawal');
			}
			if (!pixKeyType) {
				throw new Error('PIX key type is required for withdrawal');
			}

			// Para transferências PIX (cash-out), não precisamos do creditorDocument
			// pois a API da Ecomovi/ONZ Finance não requer esse campo para transferências
			logger.info('Cash-out transfer - creditorDocument not required for PIX transfers', {
				organizationId,
				pixKey: pixKey,
				pixKeyType: pixKeyType,
			});

			// Truncar descrição para máximo de 140 caracteres (limite da Ecomovi)
			const rawDescription = description || `Saque PIX via ${pixKey}`;
			const truncatedDescription = rawDescription.length > 140
				? rawDescription.substring(0, 137) + '...'
				: rawDescription;

			// Montar payload no formato ONZ Finance conforme documentação
			const withdrawalRequestData: any = {
				pixKey: pixKey,
				pixKeyType: pixKeyType, // ADICIONAR pixKeyType que estava faltando!
				priority: 'NORM',
				description: truncatedDescription,
				paymentFlow: 'INSTANT',
				expiration: 600,
				payment: {
					currency: 'BRL',
					amount: amount
				},
				idempotencyKey: simpleIdempotencyKey,
				provider: 'ecomovi-cashout',
				// Adicionar referência única para vínculo seguro
				reference: simpleIdempotencyKey, // Usar idempotencyKey como referência
				metadata: {
					internalTransactionId: transactionId, // Incluir nosso ID interno
					idempotencyKey: simpleIdempotencyKey,
					createdAt: new Date().toISOString(),
				}
			};

			logger.info('Ecomovi processPixWithdrawal request data', {
				amount: withdrawalRequestData.payment.amount,
				pixKey: withdrawalRequestData.pixKey,
				pixKeyType: withdrawalRequestData.pixKeyType, // Adicionar pixKeyType ao log
				description: withdrawalRequestData.description,
				idempotencyKey: simpleIdempotencyKey,
				hasCreditorDocument: false, // Não enviamos creditorDocument para transferências PIX
			});

			// Verificar se a chave de API está configurada
			if (!proxyApiKey) {
				throw new Error('PIX_API_PROXY_KEY não está configurada');
			}

			const response = await fetch(
				`${ECOMOVI_PROXY_BASE_URL}${ECOMOVI_PROXY_PATHS.createCashout}`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'User-Agent': 'Pluggou-Ecomovi/1.0',
						'x-pluggou-key': proxyApiKey, // Header de autenticação obrigatório
					},
					body: JSON.stringify(withdrawalRequestData),
				}
			);

			if (response.ok) {
				const result = await response.json();

				logger.info('Ecomovi processPixWithdrawal success', {
					transactionId: result.transactionId,
					status: result.status,
					endToEndId: result.endToEndId,
					totalAttempts: attempt + 1,
					tokenRetries: tokenRetryCount,
				});

				// Log detalhado do resultado para debug de webhooks
				logger.info('Ecomovi processPixWithdrawal - resultado detalhado para debug de webhook', {
					resultTransactionId: result.transactionId,
					resultStatus: result.status,
					resultEndToEndId: result.endToEndId,
					idempotencyKey: simpleIdempotencyKey,
					pixKey: pixKey,
					amount: params.amount,
					webhookExpectations: {
						expectedExternalId: result.transactionId,
						expectedIdempotencyKey: simpleIdempotencyKey,
						expectedEndToEndId: result.endToEndId,
						expectedPixKey: pixKey,
						expectedAmount: params.amount,
					},
				});

				return {
					success: result.success,
					transactionId: result.transactionId, // ID principal que será usado como externalId
					externalId: result.transactionId,
					// Adicionar campos que o Pluggou espera
					id: result.transactionId,
					id_envio: result.transactionId,
					txid: result.transactionId,
					status: result.status,
					amount: result.amount,
					endToEndId: result.endToEndId,
					providerData: result.providerData,
					metadata: {
						...metadata,
						ecomovi: {
							txId: result.transactionId,
							pixKey: pixKey,
							pixKeyType: pixKeyType,
							endToEndId: result.endToEndId,
							idempotencyKey: simpleIdempotencyKey,
						},
						idempotencyKey: simpleIdempotencyKey, // Também no nível raiz
					},
				};
			}

			const errorText = await response.text();

			// Tratamento específico para erro 401 (Unauthorized)
			if (response.status === 401) {
				tokenRetryCount++;

				logger.warn('Erro de autenticação detectado no provedor Ecomovi', {
					transactionId: params.transactionId,
					attempt: attempt + 1,
					tokenRetryCount,
					maxTokenRetries,
					status: response.status,
					statusText: response.statusText,
					error: errorText,
				});

				if (tokenRetryCount <= maxTokenRetries) {
					// Aguardar antes de tentar novamente (backoff exponencial)
					const backoffTime = Math.min(2 ** tokenRetryCount * 1000, 8000); // Max 8 segundos

					logger.info('Aguardando antes de retry por erro de token', {
						transactionId: params.transactionId,
						tokenRetryCount,
						backoffTime,
						nextAttempt: attempt + 1,
					});

					await new Promise(resolve => setTimeout(resolve, backoffTime));
					continue; // Tentar novamente
				} else {
					// Excedeu tentativas de token - erro crítico
					const tokenError = new Error(
						`Falha de autenticação após ${maxTokenRetries} tentativas de renovação de token: ${response.status} - ${errorText}`
					);

					logger.error('Excedeu tentativas de renovação de token no provedor Ecomovi', {
						transactionId: params.transactionId,
						tokenRetryCount,
						maxTokenRetries,
						status: response.status,
						error: errorText,
						errorMessage: tokenError.message,
					});

					throw tokenError;
				}
			}

			// Outros erros HTTP
			// Create more specific error messages based on status code
			let errorMessage = `Ecomovi withdrawal API error: ${response.status} - ${errorText}`;
			let isPixKeyError = false;

			// Check for PIX key related errors
			if (response.status === 400 || response.status === 404) {
				const errorLower = errorText.toLowerCase();
				if (errorLower.includes('chave') || errorLower.includes('key') ||
				    errorLower.includes('invalid') || errorLower.includes('not found') ||
				    errorLower.includes('inexistente') || errorLower.includes('inválida')) {
					isPixKeyError = true;
					errorMessage = `Chave PIX inválida ou inexistente: ${errorText}`;
				}
			}

			const httpError = new Error(errorMessage);
			// Add custom properties to help with error handling
			(httpError as any).statusCode = response.status;
			(httpError as any).isPixKeyError = isPixKeyError;
			(httpError as any).originalError = errorText;

			logger.error('Erro HTTP no processPixWithdrawal', {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
				transactionId: params.transactionId,
				attempt: attempt + 1,
				errorMessage: httpError.message,
				isPixKeyError: isPixKeyError,
				pixKey: params.pixKey
			});

			// Para erros HTTP diferentes de 401, não fazer retry
			if (response.status >= 400 && response.status < 500 && response.status !== 401) {
				throw httpError;
			}

			// Para erros 5xx, tentar novamente
			if (response.status >= 500) {
				lastError = httpError;
				if (attempt < maxRetries - 1) {
					const backoffTime = Math.min(2 ** attempt * 1000, 16000); // Max 16 segundos
					logger.info('Aguardando antes de retry por erro 5xx', {
						transactionId: params.transactionId,
						attempt: attempt + 1,
						backoffTime,
						status: response.status,
					});
					await new Promise(resolve => setTimeout(resolve, backoffTime));
					continue;
				}
			}

			throw httpError;

		} catch (error) {
			lastError = error as Error;

			// Se é um erro de rede/timeout, tentar novamente
			if (error instanceof TypeError || (error as Error).message.includes('fetch')) {
				logger.warn('Erro de rede no processPixWithdrawal', {
					error: (error as Error).message,
					transactionId: params.transactionId,
					attempt: attempt + 1,
					maxRetries,
				});

				if (attempt < maxRetries - 1) {
					const backoffTime = Math.min(2 ** attempt * 1000, 16000);
					await new Promise(resolve => setTimeout(resolve, backoffTime));
					continue;
				}
			}

			// Para outros erros, não fazer retry
			logger.error('Ecomovi processPixWithdrawal failed', {
				error: error instanceof Error ? error.message : error,
				errorType: (error as Error)?.constructor?.name || 'Unknown',
				transactionId: params.transactionId,
				pixKey: params.pixKey,
				amount: params.amount,
				attempt: attempt + 1,
			});

			throw error;
		}
	}

	// Se chegou aqui, todas as tentativas falharam
	logger.error('Todas as tentativas de processPixWithdrawal falharam', {
		transactionId: params.transactionId,
		totalAttempts: maxRetries,
		tokenRetries: tokenRetryCount,
		lastError: lastError?.message,
	});

	throw lastError || new Error(`Falha após ${maxRetries} tentativas`);
}

// Get transaction status
export async function getTransactionStatus(params: {
	transactionId: string;
	organizationId: string;
}): Promise<any> {
	try {
		logger.info('Ecomovi getTransactionStatus', {
			transactionId: params.transactionId,
			organizationId: params.organizationId,
		});

		// Verificar se a chave de API está configurada
		if (!proxyApiKey) {
			throw new Error('PIX_API_PROXY_KEY não está configurada');
		}

		// Buscar a transação no banco de dados para obter o endToEndId
		let endToEndId: string | null = null;
		try {
			const transaction = await db.transaction.findFirst({
				where: {
					OR: [
						{ id: params.transactionId },
						{ externalId: params.transactionId }
					],
					organizationId: params.organizationId,
					type: 'SEND' // Cash-out transactions
				}
			});

			if (transaction) {
				// Tentar obter endToEndId do campo direto ou dos metadados
				endToEndId = transaction.endToEndId;
				if (!endToEndId && transaction.metadata) {
					const metadata = transaction.metadata as any;
					endToEndId = metadata?.ecomovi?.endToEndId || metadata?.endToEndId;
				}

				logger.info('Transação encontrada no banco de dados', {
					transactionId: params.transactionId,
					databaseId: transaction.id,
					externalId: transaction.externalId,
					endToEndId: endToEndId,
					hasEndToEndId: !!endToEndId
				});
			} else {
				logger.warn('Transação não encontrada no banco de dados', {
					transactionId: params.transactionId,
					organizationId: params.organizationId
				});
			}
		} catch (dbError) {
			logger.error('Erro ao buscar transação no banco de dados', {
				transactionId: params.transactionId,
				error: dbError instanceof Error ? dbError.message : dbError
			});
		}

		// Construir URL com endToEndId se disponível
		let statusUrl = `${ECOMOVI_PROXY_BASE_URL}${ECOMOVI_PROXY_PATHS.statusCashout(
			params.transactionId
		)}?provider=ecomovi-cashout`;

		if (endToEndId) {
			statusUrl += `&end_to_end_id=${encodeURIComponent(endToEndId)}`;
			logger.info('Consultando status com endToEndId', {
				transactionId: params.transactionId,
				endToEndId: endToEndId,
				statusUrl: statusUrl
			});
		} else {
			logger.warn('Consultando status sem endToEndId - pode falhar', {
				transactionId: params.transactionId,
				statusUrl: statusUrl
			});
		}

		// Make request to Ecomovi Proxy
		const response = await fetch(statusUrl, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'User-Agent': 'Pluggou-Ecomovi/1.0',
				'x-pluggou-key': proxyApiKey, // Header de autenticação obrigatório
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			logger.error('Ecomovi getTransactionStatus error', {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
				transactionId: params.transactionId,
			});
			throw new Error(`Ecomovi API error: ${response.status} - ${errorText}`);
		}

		const result = await response.json();

		logger.info('Ecomovi getTransactionStatus success', {
			transactionId: result.transactionId,
			status: result.status,
		});

		return {
			success: true,
			transactionId: result.transactionId,
			status: mapEcomoviStatusToInternal(result.status),
			amount: result.amount,
			createdAt: result.createdAt,
			updatedAt: result.updatedAt,
			providerData: result.providerData,
		};
	} catch (error) {
		logger.error('Ecomovi getTransactionStatus failed', {
			error: error instanceof Error ? error.message : error,
			transactionId: params.transactionId,
		});
		throw error;
	}
}

// Get PIX QR Code (not needed for Ecomovi as it's returned in createPixPayment)
export async function getPixQRCode(params: {
	paymentId: string;
	organizationId: string;
}): Promise<any> {
	try {
		logger.info('Ecomovi getPixQRCode', {
			paymentId: params.paymentId,
			organizationId: params.organizationId,
		});

		// For Ecomovi, QR code is returned in createPixPayment
		// This function is kept for compatibility but returns an error
		throw new Error(
			'QR code is already returned in createPixPayment for Ecomovi'
		);
	} catch (error) {
		logger.error('Ecomovi getPixQRCode failed', {
			error: error instanceof Error ? error.message : error,
			paymentId: params.paymentId,
		});
		throw error;
	}
}

// Process refund
export async function processRefund(params: {
	transactionId: string;
	amount: number;
	reason?: string;
	organizationId: string;
}): Promise<any> {
	try {
		logger.info('Ecomovi processRefund', {
			transactionId: params.transactionId,
			amount: params.amount,
			reason: params.reason,
			organizationId: params.organizationId,
		});

		// Prepare refund request data
		const refundRequestData = {
			transactionId: params.transactionId,
			amount: params.amount,
			reason: params.reason || 'Devolução solicitada pelo cliente',
			provider: 'ecomovi',
		};

		// Make request to Ecomovi Proxy
		const response = await fetch(
			`${ECOMOVI_PROXY_BASE_URL}${ECOMOVI_PROXY_PATHS.refund}`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'User-Agent': 'Pluggou-Ecomovi/1.0',
				},
				body: JSON.stringify(refundRequestData),
			}
		);

		if (!response.ok) {
			const errorText = await response.text();
			logger.error('Ecomovi processRefund error', {
				status: response.status,
				statusText: response.statusText,
				error: errorText,
				transactionId: params.transactionId,
			});
			throw new Error(`Ecomovi API error: ${response.status} - ${errorText}`);
		}

		const result = await response.json();

		logger.info('Ecomovi processRefund success', {
			transactionId: result.transactionId,
			refundId: result.refundId,
			status: result.status,
		});

		return {
			success: true,
			refundId: result.refundId,
			transactionId: result.transactionId,
			amount: result.amount,
			status: mapEcomoviStatusToInternal(result.status),
			createdAt: result.createdAt,
			providerData: result.providerData,
		};
	} catch (error) {
		logger.error('Ecomovi processRefund failed', {
			error: error instanceof Error ? error.message : error,
			transactionId: params.transactionId,
		});
		throw error;
	}
}

// Map Ecomovi status to internal status
function mapEcomoviStatusToInternal(ecomoviStatus: string): string {
	const statusMap: Record<string, string> = {
		ATIVA: 'PENDING',
		CONCLUIDA: 'APPROVED',
		REMOVIDA_PELO_USUARIO_RECEBEDOR: 'CANCELED',
		REMOVIDA_PELO_PSP: 'CANCELED',
		CANCELADA: 'CANCELED',
		REJEITADA: 'CANCELED',
		EXPIRADA: 'CANCELED',
		EM_PROCESSAMENTO: 'PROCESSING',
		DEVOLVIDO: 'REFUNDED',
		NAO_REALIZADO: 'REFUNDED',
		LIQUIDATED: 'APPROVED',
		SETTLED: 'APPROVED',
		COMPLETED: 'APPROVED',
		PENDING: 'PENDING', // PENDING deve permanecer PENDING
		QUEUED: 'PENDING', // QUEUED é PENDING
		PROCESSED: 'PROCESSING', // PROCESSED é PROCESSING, não APPROVED
	};

	return statusMap[ecomoviStatus] || 'PENDING';
}

// Webhook handler for Ecomovi
export async function webhookHandler(req: Request): Promise<Response> {
	try {
		const rawBody = await req.text();
		logger.info('[ECOMOVI][WEBHOOK] Payload recebido:', { rawBody });

		// Verificar se o corpo está vazio
		if (!rawBody || rawBody.trim() === '') {
			logger.error('[ECOMOVI][WEBHOOK] Payload vazio recebido');
			return new Response(JSON.stringify({ error: 'Empty payload' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		// Parse do JSON
		let payload;
		try {
			payload = JSON.parse(rawBody);
			logger.info('[ECOMOVI][WEBHOOK] Payload JSON:', payload);
		} catch (parseError) {
			logger.error('[ECOMOVI][WEBHOOK] Erro ao fazer parse do JSON:', {
				parseError,
				rawBody,
			});
			return new Response(
				JSON.stringify({ error: 'Invalid JSON payload' }),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' },
				}
			);
		}

		// Extrair dados do payload
		const { data, type: webhookType } = payload;
		const {
			id: transactionId,
			txId,
			id_envio,
			status,
			payment,
			endToEndId,
			withdrawal,
			amount,
			pixKey,
			pixKeyType,
			transactionId: providerTransactionId,
		} = data || {};

		// Determinar se é uma transação de cobrança ou saque
		const isWithdrawal = !!withdrawal ||
			webhookType === 'WITHDRAWAL' ||
			webhookType === 'CASH_OUT' ||
			webhookType === 'TRANSFER' ||
			(data && data.creditDebitType === 'DEBIT') ||
			(data && data.pixKey && !data.payment) ||
			(data && data.provider === 'ecomovi-cashout') ||
			(data && data.localInstrument === 'DICT');
		const transactionType = isWithdrawal ? 'SEND' : 'CHARGE';
		const transactionAmount = amount || payment?.amount;
		const numericAmount = transactionAmount ? parseFloat(transactionAmount) : undefined;
		const transactionIdString = transactionId ? String(transactionId) : null;

		// Log detalhado da detecção de tipo de transação
		const withdrawalReasons = [];
		if (withdrawal) withdrawalReasons.push('withdrawal flag');
		if (webhookType === 'WITHDRAWAL') withdrawalReasons.push('webhookType WITHDRAWAL');
		if (webhookType === 'CASH_OUT') withdrawalReasons.push('webhookType CASH_OUT');
		if (webhookType === 'TRANSFER') withdrawalReasons.push('webhookType TRANSFER');
		if (data && data.creditDebitType === 'DEBIT') withdrawalReasons.push('creditDebitType DEBIT');
		if (data && data.pixKey && !data.payment) withdrawalReasons.push('pixKey without payment');
		if (data && data.provider === 'ecomovi-cashout') withdrawalReasons.push('provider ecomovi-cashout');
		if (data && data.localInstrument === 'DICT') withdrawalReasons.push('localInstrument DICT');

		logger.info('[ECOMOVI][WEBHOOK] Processando webhook com deduplicação', {
			webhookType,
			transactionType,
			isWithdrawal,
			withdrawalReasons,
			transactionId,
			txId,
			id_envio,
			providerTransactionId,
			status,
			amount: transactionAmount,
			endToEndId,
			creditDebitType: data?.creditDebitType,
			localInstrument: data?.localInstrument,
			deduplicationKey: `ecomovi-${transactionId}-${txId}-${status}-${transactionType}`,
		});

		// Verificar se é uma infração MED - DEVE SER FEITO ANTES da verificação de status relevante
		if (webhookType === 'INFRACTION') {
			logger.info('[ECOMOVI][WEBHOOK] Processando infração MED', { data });

			try {
				// Buscar a transação relacionada pelo endToEndId ou transactionId
				let relatedTransaction = null;
				if (data.endToEndId) {
					relatedTransaction = await db.transaction.findFirst({
						where: {
							endToEndId: data.endToEndId,
						},
						include: {
							organization: true,
						},
					});
				}

				// Se não encontrou pela endToEndId, tentar pelo transactionId
				if (!relatedTransaction && data.transactionId) {
					relatedTransaction = await db.transaction.findFirst({
						where: {
							OR: [
								{ externalId: data.transactionId },
								{ id: data.transactionId },
							],
						},
						include: {
							organization: true,
						},
					});
				}

				if (!relatedTransaction) {
					logger.warn('[ECOMOVI][MED] Transação relacionada não encontrada para infração MED', {
						endToEndId: data.endToEndId,
						transactionId: data.transactionId,
						externalId: data.id,
					});
					// Mesmo sem transação relacionada, vamos usar uma organização padrão
					const defaultOrg = await db.organization.findFirst();
					if (!defaultOrg) {
						logger.error('[ECOMOVI][MED] Nenhuma organização encontrada para criar infração MED');
						return new Response(JSON.stringify({ success: false, error: 'No organization found' }), {
							status: 500,
							headers: { 'Content-Type': 'application/json' },
						});
					}
					// Criar uma transação fictícia para o processamento
					relatedTransaction = {
						id: data.transactionId || `ecomovi_med_${data.id}`,
						organizationId: defaultOrg.id,
						organization: defaultOrg
					};
				}

				// Usar o novo serviço MED centralizado
				const medResult = await processMedInfraction({
					transactionId: relatedTransaction.id,
					externalId: data.id,
					reportDetails: data.reportDetails || `Infração MED via ECOMOVI - Tipo: ${data.type}`,
					organizationId: relatedTransaction.organizationId,
					gatewayName: 'ECOMOVI',
					autoApprove: false, // Ecomovi não faz aprovação automática por padrão
					reportedBy: data.reportedBy || 'DEBITED_PARTICIPANT',
					type: data.type || 'REFUND_REQUEST'
				});

				if (medResult.success) {
					logger.info('[ECOMOVI][MED] Processamento de MED concluído com sucesso', {
						infractionId: medResult.infractionId,
						transactionId: relatedTransaction.id,
						organizationId: relatedTransaction.organizationId
					});
					return new Response(JSON.stringify({ success: true, infractionId: medResult.infractionId }), {
						status: 200,
						headers: { 'Content-Type': 'application/json' },
					});
				} else {
					logger.error('[ECOMOVI][MED] Erro ao processar MED', {
						error: medResult.error,
						message: medResult.message,
						transactionId: relatedTransaction.id,
					});
					return new Response(JSON.stringify({ success: false, error: medResult.error }), {
						status: 500,
						headers: { 'Content-Type': 'application/json' },
					});
				}

			} catch (error) {
				logger.error('[ECOMOVI][MED] Erro ao processar infração MED', {
					error: error instanceof Error ? error.message : String(error),
					data,
				});
				return new Response(JSON.stringify({ success: false, error: 'Internal error processing MED infraction' }), {
					status: 500,
					headers: { 'Content-Type': 'application/json' },
				});
			}
		}

		// Verificar se é um status relevante para transações normais (apenas para webhooks que não são infrações)
		const relevantStatuses = isWithdrawal
			? ['COMPLETED', 'SETTLED', 'SUCCESS', 'APPROVED', 'CONFIRMED', 'LIQUIDATED'] // Status para cash-out (incluindo LIQUIDATED)
			: ['LIQUIDATED', 'SETTLED', 'COMPLETED']; // Status para cobrança

		if (!relevantStatuses.includes(status)) {
			logger.info(
				'[ECOMOVI][WEBHOOK] Status não relevante, ignorando webhook',
				{ status, transactionType, relevantStatuses }
			);
			return new Response(JSON.stringify({ success: true }), {
				status: 200,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		// 1. Buscar por externalId (todos os possíveis IDs do provider)
		let transaction = null;
		const externalIdsToTry = [
			txId,
			id_envio,
			providerTransactionId,
			transactionIdString,
		].filter(Boolean);
		for (const extId of externalIdsToTry) {
			if (!extId) continue;
			logger.info('[ECOMOVI][WEBHOOK] Tentativa: buscar por externalId', { extId, transactionType });
			transaction = await db.transaction.findFirst({
				where: {
					externalId: extId,
					type: transactionType,
				},
			});
			if (transaction) {
				logger.info('[ECOMOVI][WEBHOOK] Transação encontrada por externalId', {
					transactionId: transaction.id,
					externalId: transaction.externalId,
					searchExternalId: extId,
					searchStrategy: 'externalId',
				});
				break;
			}
		}

		// 2. Buscar por endToEndId
		if (!transaction && endToEndId) {
			logger.info('[ECOMOVI][WEBHOOK] Tentativa: buscar por endToEndId', { endToEndId, transactionType });
			transaction = await db.transaction.findFirst({
				where: {
					endToEndId: endToEndId,
					type: transactionType,
				},
			});
			if (transaction) {
				logger.info('[ECOMOVI][WEBHOOK] Transação encontrada por endToEndId', {
					transactionId: transaction.id,
					externalId: transaction.externalId,
					endToEndId,
					searchStrategy: 'endToEndId',
				});
			}
		}

		// 3. Buscar por pixKey + amount + janela de tempo
		if (!transaction && isWithdrawal && numericAmount && pixKey) {
			logger.info('[ECOMOVI][WEBHOOK] Tentativa: buscar por pixKey + amount + janela de tempo', {
				amount: numericAmount,
				pixKey,
				transactionType,
				searchStrategy: 'pixKey + amount + time',
			});
			const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
			transaction = await db.transaction.findFirst({
				where: {
					type: transactionType,
					pixKey: pixKey,
					amount: {
						gte: numericAmount - 0.01,
						lte: numericAmount + 0.01,
					},
					createdAt: {
						gte: thirtyMinutesAgo,
					},
					status: { in: ['PENDING', 'PROCESSING'] },
				},
				orderBy: { createdAt: 'desc' },
			});
			if (transaction) {
				logger.info('[ECOMOVI][WEBHOOK] Transação encontrada por pixKey + amount + time', {
					transactionId: transaction.id,
					externalId: transaction.externalId,
					amount: transaction.amount,
					searchAmount: numericAmount,
					difference: Math.abs(transaction.amount - numericAmount),
					searchStrategy: 'pixKey + amount + time',
				});
			}
		}

		// 4. Buscar por idempotencyKey (estratégia específica para transferências)
		if (!transaction && data?.idempotencyKey && isWithdrawal) {
			logger.info('[ECOMOVI][WEBHOOK] Tentativa: buscar por idempotencyKey', {
				idempotencyKey: data.idempotencyKey,
				transactionType,
				searchStrategy: 'idempotencyKey',
			});
			const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
			const candidates = await db.transaction.findMany({
				where: {
					type: transactionType,
					createdAt: { gte: oneHourAgo },
					status: { in: ['PENDING', 'PROCESSING'] },
				},
				select: {
					id: true,
					externalId: true,
					status: true,
					endToEndId: true,
					metadata: true,
				},
				orderBy: { createdAt: 'desc' },
			});
			transaction = candidates.find(t => {
				const meta = t.metadata as any;
				return (
					meta?.ecomovi?.idempotencyKey === data.idempotencyKey ||
					meta?.idempotencyKey === data.idempotencyKey
				);
			}) || null;
			if (transaction) {
				logger.info('[ECOMOVI][WEBHOOK] Transação encontrada por idempotencyKey', {
					transactionId: transaction.id,
					externalId: transaction.externalId,
					idempotencyKey: data.idempotencyKey,
					searchStrategy: 'idempotencyKey',
				});
			}
		}

		// 5. Buscar candidatas recentes e filtrar em memória por metadata (último recurso)
		if (!transaction && (txId || id_envio || providerTransactionId || transactionIdString)) {
			logger.info('[ECOMOVI][WEBHOOK] Tentativa: buscar candidatas recentes e filtrar por metadata', {
				txId,
				id_envio,
				providerTransactionId,
				transactionIdString,
				transactionType,
				searchStrategy: 'metadata fallback',
			});
			const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
			const candidates = await db.transaction.findMany({
				where: {
					type: transactionType,
					createdAt: { gte: oneHourAgo },
					status: { in: ['PENDING', 'PROCESSING'] },
				},
				select: {
					id: true,
					externalId: true,
					status: true,
					endToEndId: true,
					metadata: true,
				},
				orderBy: { createdAt: 'desc' },
			});
			transaction = candidates.find(t => {
				const meta = t.metadata as any;
				return (
					meta?.ecomovi?.txId === txId ||
					meta?.ecomovi?.id_envio === id_envio ||
					meta?.ecomovi?.transactionId === providerTransactionId ||
					meta?.ecomovi?.transactionId === transactionIdString ||
					meta?.ecomovi?.allIds?.transactionId === transactionIdString ||
					meta?.ecomovi?.allIds?.txId === txId ||
					meta?.ecomovi?.allIds?.id === transactionIdString ||
					meta?.ecomovi?.allIds?.id_envio === id_envio ||
					meta?.allProviderIds?.transactionId === transactionIdString ||
					meta?.allProviderIds?.txId === txId ||
					meta?.allProviderIds?.id === transactionIdString ||
					meta?.allProviderIds?.id_envio === id_envio ||
					meta?.transactionId === transactionIdString ||
					meta?.txid === txId ||
					meta?.id === transactionIdString ||
					meta?.id_envio === id_envio
				);
			}) || null;
			if (transaction) {
				logger.info('[ECOMOVI][WEBHOOK] Transação encontrada por metadata fallback', {
					transactionId: transaction.id,
					externalId: transaction.externalId,
					txId,
					id_envio,
					providerTransactionId,
					transactionIdString,
					searchStrategy: 'metadata fallback',
				});
			}
		}

		if (!transaction) {
			// Log detalhado de todas as tentativas de busca
			const searchAttempts = [];
			if (transactionIdString) searchAttempts.push('transactionId (externalId)');
			if (txId) searchAttempts.push('txId (externalId)');
			if (id_envio) searchAttempts.push('id_envio (externalId)');
			if (providerTransactionId) searchAttempts.push('providerTransactionId (externalId)');
			if (endToEndId) searchAttempts.push('endToEndId');
			if (numericAmount) searchAttempts.push('amount + time (30min)');
			if (isWithdrawal && pixKey) searchAttempts.push('pixKey (direct field)');
			if (isWithdrawal && pixKey) searchAttempts.push('pixKey (metadata.ecomovi.pixKey)');
			if (isWithdrawal && pixKey) searchAttempts.push('pixKey (metadata.pixKey)');
			if (isWithdrawal && numericAmount) searchAttempts.push('amount exact (no time limit)');
			if (transactionIdString) searchAttempts.push('transactionId (metadata.transactionId)');
			if (txId) searchAttempts.push('txId (metadata.txid)');
			if (transactionIdString || txId) searchAttempts.push('txId (metadata.ecomovi.txId)');

			logger.warn(
				'[ECOMOVI][WEBHOOK] Transação não encontrada para o webhook após todas as tentativas',
				{
					transactionId,
					transactionIdString,
					txId,
					id_envio,
					providerTransactionId,
					endToEndId,
					amount: numericAmount,
					status,
					transactionType,
					isWithdrawal,
					pixKey,
					webhookType,
					searchAttempts,
					// Informações adicionais para debug
					webhookReceivedAt: new Date().toISOString(),
					payloadKeys: Object.keys(data || {}),
					// Verificar se há transações similares no banco
					similarTransactionsQuery: {
						type: transactionType,
						amount: numericAmount,
						status: { in: ['PENDING', 'PROCESSING'] },
						createdAt: { gte: new Date(Date.now() - 60 * 60 * 1000) } // Última hora
					}
				}
			);

			// Log adicional: verificar se existem transações similares no banco
			try {
				const similarTransactions = await db.transaction.findMany({
					where: {
						type: transactionType,
						status: { in: ['PENDING', 'PROCESSING'] },
						createdAt: {
							gte: new Date(Date.now() - 60 * 60 * 1000), // Última hora
						},
					},
					select: {
						id: true,
						externalId: true,
						amount: true,
						pixKey: true,
						status: true,
						createdAt: true,
						metadata: true,
					},
					orderBy: { createdAt: 'desc' },
					take: 5, // Limitar a 5 resultados
				});

				if (similarTransactions.length > 0) {
					logger.info('[ECOMOVI][WEBHOOK] Transações similares encontradas no banco (última hora)', {
						count: similarTransactions.length,
						transactions: similarTransactions.map(t => ({
							id: t.id,
							externalId: t.externalId,
							amount: t.amount,
							pixKey: t.pixKey,
							status: t.status,
							createdAt: t.createdAt,
							metadataKeys: Object.keys(t.metadata as any || {}),
						})),
					});
				}
			} catch (debugError) {
				logger.warn('[ECOMOVI][WEBHOOK] Erro ao buscar transações similares para debug', {
					error: debugError instanceof Error ? debugError.message : String(debugError),
				});
			}

			// Retornar 200 para evitar reenvios
			return new Response(JSON.stringify({ success: true }), {
				status: 200,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		logger.info('[ECOMOVI][WEBHOOK] Transação encontrada para processamento', {
			transactionId: transaction.id,
			externalId: transaction.externalId,
			currentStatus: transaction.status,
			newStatus: 'APPROVED',
			transactionType,
			isWithdrawal,
		});

		// Atualizar metadata da transação com informações do webhook
		const currentMetadata = (transaction.metadata as Record<string, any>) || {};
		const updatedMetadata = {
			...currentMetadata,
			lastWebhook: new Date().toISOString(),
			webhookUpdate: {
				webhookSource: 'ecomovi',
				receivedAt: new Date().toISOString(),
				payload: {
					transactionId,
					txId,
					status,
					amount: transactionAmount,
					endToEndId,
					webhookType,
					transactionType,
					isWithdrawal,
				},
			},
			updatedByWebhook: true,
			deduplicationKey: `ecomovi-${transactionId}-${txId}-${status}-${transactionType}`,
			processedAt: new Date().toISOString(),
			// Informações específicas da Ecomovi
			ecomovi: {
				transactionId,
				txId,
				status,
				amount: transactionAmount,
				endToEndId,
				webhookType,
				transactionType,
				isWithdrawal,
				processedAt: new Date().toISOString(),
			},
		};

		// Atualizar externalId se necessário
		if (txId && transaction.externalId !== txId) {
			await db.transaction.update({
				where: { id: transaction.id },
				data: {
					externalId: txId,
				},
			});
		}

		// Atualizar a transação no banco de dados
		await db.transaction.update({
			where: { id: transaction.id },
			data: {
				metadata: updatedMetadata,
				endToEndId: endToEndId || transaction.endToEndId,
				updatedAt: new Date(),
			},
		});

		// Atualizar status da transação usando updateTransactionStatus
		// Isso automaticamente cuidará do saldo da organização e taxas
		try {
			logger.info('[ECOMOVI][WEBHOOK] Iniciando atualização do status da transação', {
				transactionId: transaction.id,
				currentStatus: transaction.status,
				newStatus: 'APPROVED',
				transactionType,
			});

			const previousStatus = transaction.status;

			// Use updateTransactionStatus que cuida do saldo da organização e taxas
			const updatedTransaction = await updateTransactionStatus(
				transaction.id,
				TransactionStatus.APPROVED,
				new Date() // paymentAt
			);

			logger.info('[ECOMOVI][WEBHOOK] Status da transação atualizado com sucesso', {
				transactionId: transaction.id,
				oldStatus: previousStatus,
				newStatus: updatedTransaction.status,
				updatedAt: updatedTransaction.updatedAt,
				transactionType,
			});
		} catch (updateError) {
			const updateErrorMessage = updateError instanceof Error ? updateError.message : String(updateError);
			const updateErrorStack = updateError instanceof Error ? updateError.stack : undefined;

			logger.error('[ECOMOVI][WEBHOOK] Erro ao atualizar status da transação:', {
				updateErrorMessage,
				updateErrorStack,
				transactionId: transaction.id,
				errorType: updateError?.constructor?.name || 'Unknown',
				transactionType,
			});

			// Ainda assim retornar sucesso para evitar reenvios
			return new Response(JSON.stringify({ success: true, warning: 'Transaction update error' }), {
				status: 200,
				headers: { 'Content-Type': 'application/json' },
			});
		}

		// Log de sucesso do processamento
		logger.info('[ECOMOVI][WEBHOOK] Transação atualizada com sucesso', {
			transactionId: transaction.id,
			oldStatus: transaction.status,
			newStatus: 'APPROVED',
			txId,
			amount: numericAmount,
			endToEndId,
			transactionType,
			isWithdrawal,
		});

		logger.info('[ECOMOVI][WEBHOOK] Webhook processado com sucesso', {
			transactionId: transaction.id,
			status: 'APPROVED',
			txId,
			amount: transactionAmount,
			transactionType,
		});

		return new Response(JSON.stringify({ success: true }), {
			status: 200,
			headers: { 'Content-Type': 'application/json' },
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const errorStack = error instanceof Error ? error.stack : undefined;

		logger.error('[ECOMOVI][WEBHOOK] Erro ao processar webhook:', {
			errorMessage,
			errorStack,
			errorType: error?.constructor?.name || 'Unknown'
		});

		return new Response(
			JSON.stringify({ error: 'Internal server error' }),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' },
			}
		);
	}
}

// Test function to verify if the provider is working
export async function testEcomoviProvider() {
	try {
		logger.info('[ECOMOVI] 🧪 Iniciando teste do provider Ecomovi...');

		// Test PIX creation
		const pixResult = await createPixPayment({
			amount: 50, // R$ 0,50
			customerName: 'Teste Pluggou',
			customerEmail: '<EMAIL>',
			customerDocument: '12345678909',
			description: 'Teste de integração Pluggou',
			organizationId: 'test-organization',
		});

		logger.info('[ECOMOVI] ✅ PIX criado com sucesso:', pixResult);

		return {
			success: true,
			message: 'Provider Ecomovi funcionando corretamente',
			pixResult,
		};
	} catch (error) {
		logger.error('[ECOMOVI] ❌ Erro no teste do provider:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : error,
		};
	}
}

export default {
	createPixPayment,
	getTransactionStatus,
	processRefund,
	processPixWithdrawal,
	webhookHandler,
	testEcomoviProvider,
};

