#!/usr/bin/env npx tsx

/**
 * Test script to verify Owempay V2 improvements
 * Run with: npx tsx packages/payments/provider/owempayv2/test-improvements.ts
 */

import { logger } from "@repo/logs";
import { 
  testOwempayV2Connection,
  getOwempayV2Credentials,
  createExternalTransfer,
  getOwempayV2Balance
} from "./index";

// Test configuration
const TEST_ORG_ID = process.env.OWEMPAY_V2_ORG_ID || "your_organization_id_here";
const TEST_PIX_KEY = "<EMAIL>"; // Use a test PIX key
const TEST_AMOUNT = 1.00; // R$ 1.00 for testing

async function runTests() {
  console.log("🧪 Testing Owempay V2 Improvements...\n");

  try {
    // Test 1: Credential Loading
    console.log("1️⃣ Testing credential loading...");
    try {
      const credentials = await getOwempayV2Credentials(TEST_ORG_ID);
      console.log("✅ Credentials loaded successfully");
      console.log(`   Environment: ${credentials.environment}`);
      console.log(`   API Key: ${credentials.apiKey.substring(0, 8)}...`);
    } catch (error) {
      console.log("❌ Credential loading failed:", error instanceof Error ? error.message : String(error));
    }

    // Test 2: Connection Test
    console.log("\n2️⃣ Testing API connection...");
    try {
      const connectionResult = await testOwempayV2Connection(TEST_ORG_ID);
      console.log("✅ Connection test successful");
      console.log("   Response:", connectionResult);
    } catch (error) {
      console.log("❌ Connection test failed:", error instanceof Error ? error.message : String(error));
    }

    // Test 3: Balance Check
    console.log("\n3️⃣ Testing balance retrieval...");
    try {
      const balance = await getOwempayV2Balance(TEST_ORG_ID);
      console.log("✅ Balance retrieved successfully");
      console.log("   Balance:", balance);
    } catch (error) {
      console.log("❌ Balance retrieval failed:", error instanceof Error ? error.message : String(error));
    }

    // Test 4: Input Validation
    console.log("\n4️⃣ Testing input validation...");
    
    // Test invalid receiverPixKey
    try {
      await createExternalTransfer({
        receiverPixKey: "", // Invalid: empty string
        amount: TEST_AMOUNT,
        organizationId: TEST_ORG_ID,
        description: "Test validation"
      });
      console.log("❌ Should have failed with empty receiverPixKey");
    } catch (error) {
      console.log("✅ Correctly rejected empty receiverPixKey");
    }

    // Test invalid amount
    try {
      await createExternalTransfer({
        receiverPixKey: TEST_PIX_KEY,
        amount: 0, // Invalid: zero amount
        organizationId: TEST_ORG_ID,
        description: "Test validation"
      });
      console.log("❌ Should have failed with zero amount");
    } catch (error) {
      console.log("✅ Correctly rejected zero amount");
    }

    // Test invalid organizationId
    try {
      await createExternalTransfer({
        receiverPixKey: TEST_PIX_KEY,
        amount: TEST_AMOUNT,
        organizationId: "", // Invalid: empty string
        description: "Test validation"
      });
      console.log("❌ Should have failed with empty organizationId");
    } catch (error) {
      console.log("✅ Correctly rejected empty organizationId");
    }

    // Test 5: External Transfer (with test mode)
    console.log("\n5️⃣ Testing external transfer creation...");
    try {
      const transferResult = await createExternalTransfer({
        receiverPixKey: TEST_PIX_KEY,
        amount: TEST_AMOUNT,
        organizationId: TEST_ORG_ID,
        description: "Test transfer - improvements verification",
        test: true, // Use test mode
        testStatus: 'succeeded' // Force success in test mode
      });
      
      console.log("✅ External transfer created successfully");
      console.log("   Transfer ID:", transferResult.data?.jobId);
      console.log("   Status:", transferResult.data?.status);
      console.log("   Amount:", transferResult.data?.amount);
      console.log("   Receiver PIX Key:", transferResult.data?.receiverPixKey);
    } catch (error) {
      console.log("❌ External transfer failed:", error instanceof Error ? error.message : String(error));
      console.log("   This might be expected if the API endpoint is not available yet");
    }

    console.log("\n🎉 Test suite completed!");
    console.log("\n📋 Summary:");
    console.log("- Credential loading: Improved to handle both apiKey/apiSecret and clientId/clientSecret");
    console.log("- Input validation: Added proper validation for required parameters");
    console.log("- Function signature: Updated to use receiverPixKey instead of pixKey");
    console.log("- Error handling: Enhanced with better logging and fallback endpoints");
    console.log("- Return format: Standardized to match other providers");

  } catch (error) {
    console.error("💥 Test suite failed:", error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests };
