import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getGatewayCredentials } from "../factory";
import { processMedInfraction } from "../../src/med/med-service";
import * as crypto from "crypto";

// Base URL for Owempay v2 API
// ✅ CONFIRMADO: https://api.owem.com.br está funcionando (conecta e autentica)
// ❌ TESTADO: https://safe.owempay.com.br tem problemas de SSL/conectividade
const OWEMPAY_V2_API_BASE_URL = {
  production: "https://api.owem.com.br",
  sandbox: "https://api.owem.com.br", // Same URL for both environments
};

// API paths for Owempay v2
const OWEMPAY_V2_API_PATHS = {
  ping: "/v4/i/ping",
  webhooks: "/v4/i/webhooks/config",
  pixDynamicQrCode: "/v4/i/pix/in/dynamic-qrcode",
  balance: "/v4/i/bank-accounts/************/balance",
  // PIX OUT transfer endpoints - CORRECTED ENDPOINT
  pixExternalTransfer: "/v4/i/bank-accounts/:accountId/transfer/external", // ✅ CORRECT ENDPOINT
  pixExternalTransferWithAccount: "/v4/i/bank-accounts/:accountId/transfer/external", // Same as above
  pixExternalTransferLegacy: "/v4/i/bank-accounts/:accountId/transfer/external", // Same as above
  // Add more endpoints as they become available in the documentation
};

// Owempay v2 transaction limits configuration
const OWEMPAY_V2_LIMITS = {
  MIN_AMOUNT: 0.01, // R$ 0.01 - minimum PIX amount
  MAX_AMOUNT: 50000, // R$ 50,000 - conservative maximum limit
  MAX_AMOUNT_PRODUCTION: 100000, // R$ 100,000 - production limit (if different)
  CURRENCY: 'BRL',
} as const;

// Helper function to validate transaction amounts
function validateOwempayV2Amount(amount: number): { isValid: boolean; error?: Error } {
  if (amount < OWEMPAY_V2_LIMITS.MIN_AMOUNT) {
    const error = new Error(`Valor mínimo para transação PIX é R$ ${OWEMPAY_V2_LIMITS.MIN_AMOUNT.toFixed(2)}`);
    (error as any).code = "AMOUNT_TOO_LOW";
    return { isValid: false, error };
  }

  if (amount > OWEMPAY_V2_LIMITS.MAX_AMOUNT) {
    const error = new Error(`Valor excede o limite máximo permitido por transação (R$ ${OWEMPAY_V2_LIMITS.MAX_AMOUNT.toLocaleString('pt-BR')}). Por favor, reduza o valor ou entre em contato com o suporte.`);
    (error as any).code = "LIMIT_EXCEEDED_PER_TX";
    return { isValid: false, error };
  }

  return { isValid: true };
}

// Helper function to truncate description to Owempay v2 limit (100 characters)
function truncateDescription(description: string | undefined, maxLength: number = 100): string {
  if (!description) {
    return "Pagamento PIX";
  }

  if (description.length <= maxLength) {
    return description;
  }

  // Truncate and add ellipsis to indicate truncation
  const truncated = description.substring(0, maxLength - 3) + "...";
  logger.warn("Description truncated for Owempay v2 API", {
    originalLength: description.length,
    truncatedLength: truncated.length,
    originalDescription: description,
    truncatedDescription: truncated
  });

  return truncated;
}

// Helper to get the Owempay v2 credentials for an organization
export async function getOwempayV2Credentials(organizationId: string): Promise<{
  apiKey: string;
  apiSecret: string;
  environment: "production" | "sandbox";
}> {
  try {
    logger.info(`Getting Owempay v2 credentials for organization ${organizationId}`);

    const credentials = await getGatewayCredentials(organizationId, "OWEMPAY_V2");

    if (!credentials) {
      throw new Error("Owempay v2 credentials not found");
    }

    // Log available credential fields for debugging
    logger.info("Available credential fields", {
      fields: Object.keys(credentials),
      hasApiKey: !!credentials.apiKey,
      hasApiSecret: !!credentials.apiSecret,
      hasClientId: !!credentials.clientId,
      hasClientSecret: !!credentials.clientSecret
    });

    // Handle both field naming conventions (apiKey/apiSecret and clientId/clientSecret)
    const apiKey = credentials.apiKey || credentials.clientId;
    const apiSecret = credentials.apiSecret || credentials.clientSecret;

    if (!apiKey || !apiSecret) {
      throw new Error("Owempay v2 credentials incomplete - missing apiKey/clientId or apiSecret/clientSecret");
    }

    return {
      apiKey: apiKey as string,
      apiSecret: apiSecret as string,
      environment: (credentials.environment as "production" | "sandbox") || "sandbox"
    };
  } catch (error) {
    logger.error("Error getting Owempay v2 credentials", { error, organizationId });
    throw error;
  }
}

// Helper function to create Basic Auth header
export function createBasicAuthHeader(apiKey: string, apiSecret: string): string {
  const credentials = `${apiKey}:${apiSecret}`;
  const encodedCredentials = Buffer.from(credentials).toString('base64');
  return `Basic ${encodedCredentials}`;
}

// Helper function to make HTTP requests
export async function makeHTTPRequest(url: string, options: {
  method: string;
  headers: Record<string, string>;
  body?: string;
  timeout?: number;
}): Promise<{ ok: boolean; status: number; json: () => Promise<any>; text: () => Promise<string> }> {
  const controller = new AbortController();
  const timeoutId = options.timeout ? setTimeout(() => controller.abort(), options.timeout) : null;

  try {
    const response = await fetch(url, {
      method: options.method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: options.body,
      signal: controller.signal,
    });

    if (timeoutId) clearTimeout(timeoutId);

    return {
      ok: response.ok,
      status: response.status,
      json: () => response.json(),
      text: () => response.text(),
    };
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    throw error;
  }
}

// Test connection to Owempay v2 API
export async function testOwempayV2Connection(organizationId: string): Promise<any> {
  try {
    logger.info("Testing connection to Owempay v2 API");

    const credentials = await getOwempayV2Credentials(organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    const response = await makeHTTPRequest(`${baseUrl}${OWEMPAY_V2_API_PATHS.ping}`, {
      method: "GET",
      headers: {
        "Authorization": authHeader,
        "Accept": "application/json"
      },
      timeout: 10000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Owempay v2 connection test failed", {
        status: response.status,
        body: errorText
      });
      throw new Error(`Owempay v2 connection test failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    logger.info("Owempay v2 connection test successful", { data });
    return data;
  } catch (error) {
    logger.error("Error testing Owempay v2 connection", { error });
    throw error;
  }
}

// Get account balance from Owempay v2 API
export async function getOwempayV2Balance(organizationId: string): Promise<any> {
  try {
    logger.info("Getting Owempay v2 account balance", { organizationId });

    const credentials = await getOwempayV2Credentials(organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    const response = await makeHTTPRequest(`${baseUrl}${OWEMPAY_V2_API_PATHS.balance}`, {
      method: "GET",
      headers: {
        "Authorization": authHeader,
        "Accept": "application/json"
      },
      timeout: 10000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Owempay v2 balance request failed", {
        status: response.status,
        body: errorText
      });
      throw new Error(`Owempay v2 balance request failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    logger.info("Owempay v2 balance retrieved successfully", {
      accountId: data.data?.accountId,
      balance: data.data?.balance
    });

    return {
      success: true,
      data: {
        accountId: data.data?.accountId,
        balance: data.data?.balance,
        requestId: data.requestId
      }
    };
  } catch (error) {
    logger.error("Error getting Owempay v2 balance", { error, organizationId });
    throw error;
  }
}

// Função simples para gerar chave de idempotência
function generateIdempotencyKey(operation: string, params: Record<string, any>): string {
  const idempotencyFields = {
    operation,
    ...(params.amount && { amount: params.amount }),
    ...(params.organizationId && { organizationId: params.organizationId }),
    ...(params.idempotencyKey && { idempotencyKey: params.idempotencyKey }),
    timestamp: Math.floor(Date.now() / (5 * 60 * 1000))
  };

  const hash = crypto
    .createHash("sha256")
    .update(JSON.stringify(idempotencyFields))
    .digest("hex");

  return `${operation}_${hash}`;
}

// Create a Pix payment (charge)
export async function createPixPayment(params: {
  amount: number;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  postbackUrl?: string;
  organizationId: string;
  externalCode?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
}): Promise<any> {
  try {
    logger.info("Creating Owempay v2 PIX payment", { params: { ...params, amount: params.amount } });

    // Pre-validate amount to prevent LIMIT_EXCEEDED_PER_TX errors
    const amountValidation = validateOwempayV2Amount(params.amount);
    if (!amountValidation.isValid) {
      throw amountValidation.error;
    }

    const credentials = await getOwempayV2Credentials(params.organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    // Generate internal transaction ID if not provided
    const internalTransactionId = params.externalCode || `owempay_v2_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Generate idempotency key if not provided
    const idempotencyKey = params.idempotencyKey || generateIdempotencyKey("create_pix_payment", {
      amount: params.amount,
      customerEmail: params.customerEmail,
      externalCode: params.externalCode,
      organizationId: params.organizationId
    });

    // Prepare the request payload according to Owempay v2 API documentation
    // Based on successful Postman test: https://api.owem.com.br/v4/i/pix/in/dynamic-qrcode
    const payload = {
      accountId: "************", // Fixed account ID as per documentation
      amount: params.amount, // Amount in decimal format (not cents)
      description: truncateDescription(params.description) // Truncate description to 100 chars max
    };

    logger.info("Making request to Owempay v2", {
      url: `${baseUrl}${OWEMPAY_V2_API_PATHS.pixDynamicQrCode}`,
      payload,
      headers: {
        "Authorization": `Basic ${authHeader.substring(0, 20)}...`,
        "Content-Type": "application/json",
        "X-Idempotency-Key": idempotencyKey
      }
    });

    const response = await makeHTTPRequest(`${baseUrl}${OWEMPAY_V2_API_PATHS.pixDynamicQrCode}`, {
      method: "POST",
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
        "X-Idempotency-Key": idempotencyKey,
        "User-Agent": "Pluggou/1.0",
        "Accept": "application/json"
      },
      body: JSON.stringify(payload),
      timeout: 30000
    });

    logger.info("Owempay v2 response received", {
      status: response.status
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Owempay v2 API error response", {
        status: response.status,
        body: errorText
      });

      // Parse error response to handle specific error codes
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (parseError) {
        // If parsing fails, throw generic error
        throw new Error(`Owempay v2 API error: ${response.status} - ${errorText}`);
      }

      // Handle specific error codes with user-friendly messages
      if (errorData.code === "LIMIT_EXCEEDED_PER_TX") {
        const userFriendlyError = new Error(`Valor excede o limite máximo permitido por transação. Por favor, reduza o valor ou entre em contato com o suporte.`);
        (userFriendlyError as any).code = "LIMIT_EXCEEDED_PER_TX";
        (userFriendlyError as any).originalError = errorData;
        throw userFriendlyError;
      }

      // For other errors, throw with original message
      throw new Error(`Owempay v2 API error: ${response.status} - ${errorText}`);
    }

    const responseData = await response.json();
    logger.info("Owempay v2 payment created successfully", {
      responseData
    });

    // Extract transaction data from Owempay v2 response
    // Based on actual API response: {"success":true,"data":{"txId":"...","dueDate":"...","emv":"..."}}
    const transactionData = responseData.data;

    // Extract PIX data from response
    const pixPayload = transactionData?.emv; // PIX EMV code
    const pixExpirationDate = transactionData?.dueDate; // Expiration date
    const txId = transactionData?.txId; // Transaction ID

    logger.info("PIX payment created successfully with Owempay v2", {
      transactionId: internalTransactionId,
      externalId: txId,
      pixPayload: pixPayload ? "present" : "missing",
      pixExpirationDate: pixExpirationDate ? "present" : "missing",
      responseStructure: {
        hasData: !!transactionData,
        responseKeys: Object.keys(responseData),
        dataKeys: transactionData ? Object.keys(transactionData) : []
      }
    });

    // Validate PIX data is present
    if (!pixPayload) {
      logger.error("Missing PIX data in Owempay v2 response", {
        hasPayload: !!pixPayload,
        responseData,
        transactionData
      });
      throw new Error("PIX data missing in Owempay v2 response");
    }

    // NOTE: Transaction is already created by the router via ChargeTransactionService
    // We only need to return the PIX data for the existing transaction
    logger.info("Skipping transaction creation - transaction already created by router", {
      externalId: txId,
      organizationId: params.organizationId,
      amount: params.amount,
      reason: "Router creates transaction via ChargeTransactionService"
    });

    // Return payment data in the format expected by the router
    return {
      success: true,
      externalId: txId,
      pixCode: pixPayload,
      pixQrCode: null, // API v2 doesn't return QR code image, only EMV
      pixExpiresAt: pixExpirationDate,
      metadata: {
        owempay_v2: {
          transactionId: txId,
          accountId: "************",
          provider: "OWEMPAY_V2",
        },
      },
    };
  } catch (error) {
    logger.error("Error in createPixPayment", { error, params });
    throw error;
  }
}

// Create webhook configuration
export async function createWebhookConfig(params: {
  targetUrl: string;
  events: string[];
  authHeader?: string;
  organizationId: string;
}): Promise<any> {
  try {
    logger.info("Creating Owempay v2 webhook configuration", { params });

    const credentials = await getOwempayV2Credentials(params.organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    const payload = {
      event: params.events,
      targetUrl: params.targetUrl,
      authHeader: params.authHeader
    };

    const response = await makeHTTPRequest(`${baseUrl}${OWEMPAY_V2_API_PATHS.webhooks}`, {
      method: "POST",
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload),
      timeout: 30000
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Error creating Owempay v2 webhook config", {
        status: response.status,
        body: errorText
      });
      throw new Error(`Failed to create Owempay v2 webhook config: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    logger.info("Owempay v2 webhook config created successfully", { data });

    return {
      success: true,
      data: {
        configId: data.data?.configId,
        targetUrl: data.data?.targetUrl,
        events: data.data?.event,
        isEnabled: data.data?.isEnabled,
        createdAt: data.data?.createdAt
      }
    };
  } catch (error) {
    logger.error("Error in createWebhookConfig", { error, params });
    throw error;
  }
}

// Validate Owempay v2 webhook signature
export function validateOwempayV2WebhookSignature(
  body: string,
  signature: string,
  secretKey: string | undefined
): boolean {
  try {
    if (!secretKey) {
      logger.warn("Owempay v2 webhook secret not configured, skipping signature validation");
      return true;
    }

    // Compute expected signature (implementation depends on Owempay v2's signature method)
    const computedSignature = crypto
      .createHmac('sha256', secretKey)
      .update(body)
      .digest('hex');

    // Compare signatures securely
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(computedSignature)
    );

    if (!isValid) {
      logger.warn("Invalid Owempay v2 webhook signature", {
        receivedSignature: signature,
        computedSignature
      });
    }

    return isValid;
  } catch (error) {
    logger.error("Error validating Owempay v2 webhook signature", { error });
    return false;
  }
}

// Map Owempay v2 status to internal status
function mapOwempayV2StatusToInternal(owempayStatus: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'PENDING',
    'processing': 'PROCESSING',
    'succeeded': 'APPROVED', // ✅ API returns 'succeeded' for successful transfers
    'failed': 'REJECTED',
    'cancelled': 'CANCELED',
    'rejected': 'REJECTED',
    'approved': 'APPROVED',
    'completed': 'APPROVED',
    'paid': 'COMPLETED',
    'expired': 'EXPIRED',
    'refunded': 'REFUNDED',
    'refunded_med': 'REFUNDED_MED',
    'refunding': 'REFUNDING'
  };

  return statusMap[owempayStatus] || 'UNKNOWN';
}

// Create external transfer (PIX OUT)
export async function createExternalTransfer(params: {
  receiverPixKey: string; // Changed from pixKey to match other providers
  amount: number;
  description?: string;
  organizationId: string;
  externalReference?: string; // Changed from externalCode to match other providers
  postbackUrl?: string; // Added for consistency
  idempotencyKey?: string;
  test?: boolean;
  testStatus?: 'processing' | 'succeeded' | 'failed';
}): Promise<any> {
  try {
    logger.info("Creating Owempay v2 external transfer", { params: { ...params, amount: params.amount } });

    // Input validation
    if (!params.receiverPixKey || typeof params.receiverPixKey !== 'string') {
      throw new Error('receiverPixKey is required and must be a string');
    }
    if (!params.amount || params.amount <= 0) {
      throw new Error('amount is required and must be greater than 0');
    }
    if (!params.organizationId || typeof params.organizationId !== 'string') {
      throw new Error('organizationId is required and must be a string');
    }

    // Pre-validate amount to prevent LIMIT_EXCEEDED_PER_TX errors
    const amountValidation = validateOwempayV2Amount(params.amount);
    if (!amountValidation.isValid) {
      throw amountValidation.error;
    }

    const credentials = await getOwempayV2Credentials(params.organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    // Generate internal transaction ID if not provided
    const internalTransactionId = params.externalReference || `owempay_v2_transfer_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Generate idempotency key if not provided
    const idempotencyKey = params.idempotencyKey || generateIdempotencyKey("create_external_transfer", {
      receiverPixKey: params.receiverPixKey,
      amount: params.amount,
      organizationId: params.organizationId
    });

    // Prepare the request payload according to Owempay v2 API documentation
    const payload = {
      pixKey: params.receiverPixKey,
      amount: params.amount, // Amount in decimal format
      description: truncateDescription(params.description || "Transferência PIX"), // Truncate description to 100 chars max
      ...(params.test && { test: params.test }),
      ...(params.testStatus && { testStatus: params.testStatus })
    };

    // Use the correct PIX OUT transfer path with account ID
    const transferPath = OWEMPAY_V2_API_PATHS.pixExternalTransfer.replace(':accountId', '************');

    logger.info("Making external transfer request to Owempay v2", {
      url: `${baseUrl}${transferPath}`,
      payload,
      headers: {
        "Authorization": `Basic ${authHeader.substring(0, 20)}...`,
        "Content-Type": "application/json",
        "X-Idempotency-Key": idempotencyKey
      }
    });

    const response = await makeHTTPRequest(`${baseUrl}${transferPath}`, {
      method: "POST",
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
        "X-Idempotency-Key": idempotencyKey,
        "User-Agent": "Pluggou/1.0",
        "Accept": "application/json"
      },
      body: JSON.stringify(payload),
      timeout: 30000
    });

    logger.info("Owempay v2 external transfer response received", {
      status: response.status,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Owempay v2 external transfer API error response", {
        status: response.status,
        body: errorText,
        url: `${baseUrl}${transferPath}`,
        payload: payload
      });

      // No need for alternative endpoints since we're using the correct one

      throw new Error(`Owempay v2 external transfer API error: ${response.status} - ${errorText}`);
    }

    const responseData = await response.json();
    logger.info("Owempay v2 external transfer created successfully", {
      responseData
    });

    // Extract transfer data from Owempay v2 response
    const transferData = responseData.data || responseData;

    // Extract transfer information from the correct response structure
    const transferId = transferData.id || transferData.endToEndId;
    const status = transferData.status;
    const amount = transferData.grossAmount || transferData.amount;
    const pixKey = params.receiverPixKey; // Use the original PIX key from request
    const description = transferData.reason || transferData.description;

    logger.info("External transfer created successfully with Owempay v2", {
      transferId: internalTransactionId,
      externalId: transferId,
      endToEndId: transferData.endToEndId,
      status: status,
      amount: amount,
      pixKey: pixKey,
      responseStructure: {
        hasData: !!transferData,
        responseKeys: Object.keys(responseData),
        dataKeys: transferData ? Object.keys(transferData) : []
      }
    });

    // Return transfer data in the format expected by the router (matching Flow2Pay pattern)
    return {
      success: true,
      data: {
        jobId: transferId, // Match Flow2Pay pattern
        externalReference: internalTransactionId,
        status: mapOwempayV2StatusToInternal(status),
        amount: amount,
        receiverPixKey: params.receiverPixKey,
        description: description || "Transferência PIX",
        transferId: transferId,
        endToEndId: transferData.endToEndId, // Add endToEndId from response
        feeAmount: transferData.feeAmount, // Add fee information
        netAmount: transferData.netAmount // Add net amount
      },
      metadata: {
        owempay_v2: {
          transferId: transferId,
          accountId: "************",
          provider: "OWEMPAY_V2",
          originalStatus: status,
          createdAt: new Date().toISOString(),
          endToEndId: transferData.endToEndId,
          requestId: transferData.requestId,
          payer: transferData.payer,
          receiver: transferData.receiver
        },
      },
    };
  } catch (error) {
    logger.error("Error in createExternalTransfer", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      params: {
        ...params,
        // Don't log sensitive data
        receiverPixKey: params.receiverPixKey ? `${params.receiverPixKey.substring(0, 4)}***` : undefined
      }
    });

    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`Owempay V2 external transfer failed: ${error.message}`);
    }
    throw error;
  }
}

// Process PIX withdrawal (required by PaymentGatewayProvider interface)
// This is a wrapper around createExternalTransfer to match the interface
export async function processPixWithdrawal(params: {
  amount: number;
  pixKey: string;
  pixKeyType: string;
  postbackUrl?: string;
  organizationId: string;
  transactionId?: string;
  description?: string;
  idempotencyKey?: string;
}): Promise<any> {
  try {
    logger.info("Processing Owempay v2 PIX withdrawal", {
      params: {
        ...params,
        amount: params.amount,
        pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined
      }
    });

    // Validate required parameters (same as working providers)
    if (!params.pixKey || typeof params.pixKey !== 'string') {
      throw new Error('pixKey is required and must be a string');
    }
    if (!params.amount || params.amount <= 0) {
      throw new Error('amount is required and must be greater than 0');
    }
    if (!params.organizationId || typeof params.organizationId !== 'string') {
      throw new Error('organizationId is required and must be a string');
    }

    // Pre-validate amount to prevent LIMIT_EXCEEDED_PER_TX errors
    const amountValidation = validateOwempayV2Amount(params.amount);
    if (!amountValidation.isValid) {
      throw amountValidation.error;
    }

    // Map the processPixWithdrawal parameters to createExternalTransfer parameters
    logger.info("Calling createExternalTransfer from processPixWithdrawal", {
      receiverPixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined,
      amount: params.amount,
      organizationId: params.organizationId,
      hasTransactionId: !!params.transactionId,
      hasPostbackUrl: !!params.postbackUrl
    });

    try {
      const transferResult = await createExternalTransfer({
        receiverPixKey: params.pixKey,
        amount: params.amount,
        description: truncateDescription(params.description || `Transferência PIX para ${params.pixKeyType} ${params.pixKey}`),
        organizationId: params.organizationId,
        externalReference: params.transactionId,
        postbackUrl: params.postbackUrl,
        idempotencyKey: params.idempotencyKey
      });

      logger.info("createExternalTransfer completed successfully", {
        success: transferResult.success,
        hasData: !!transferResult.data,
        dataKeys: transferResult.data ? Object.keys(transferResult.data) : [],
        status: transferResult.data?.status
      });

      // Map the response to match what PixTransferService expects (based on working providers)
      // PixTransferService looks for: transactionId, id, txid, id_envio, status, endToEndId at root level
      const externalId = transferResult.data?.jobId || transferResult.data?.transferId;

    const response = {
      // Primary identifiers that PixTransferService looks for
      transactionId: externalId,
      id: externalId,
      txid: externalId,
      id_envio: externalId,

      // Status and other required fields
      status: transferResult.data?.status || "PROCESSING",
      amount: transferResult.data?.amount || params.amount,
      pixKey: transferResult.data?.receiverPixKey || params.pixKey,
      pixKeyType: params.pixKeyType,
      description: transferResult.data?.description || params.description,
      endToEndId: externalId, // Required by PixTransferService

      // Additional fields for compatibility
      externalReference: transferResult.data?.externalReference,
      metadata: transferResult.metadata,

      // Store raw response for debugging
      raw: transferResult
    };

      logger.info("processPixWithdrawal response prepared", {
        externalId,
        status: response.status,
        hasAllRequiredIds: !!(response.transactionId && response.id && response.txid && response.id_envio),
        responseKeys: Object.keys(response)
      });

      return response;
    } catch (createExternalTransferError) {
      logger.error("Error in createExternalTransfer call", {
        error: createExternalTransferError instanceof Error ? createExternalTransferError.message : String(createExternalTransferError),
        stack: createExternalTransferError instanceof Error ? createExternalTransferError.stack : undefined,
        params: {
          ...params,
          pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined
        }
      });
      throw createExternalTransferError;
    }
  } catch (error) {
    logger.error("Error in processPixWithdrawal", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      params: {
        ...params,
        // Don't log sensitive data
        pixKey: params.pixKey ? `${params.pixKey.substring(0, 4)}***` : undefined
      }
    });

    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`Owempay V2 PIX withdrawal failed: ${error.message}`);
    }
    throw error;
  }
}

// Get transaction status (required by PaymentGatewayProvider interface)
export async function getTransactionStatus(params: {
  transactionId: string;
  organizationId: string;
  transactionType?: 'CHARGE' | 'SEND';
}): Promise<any> {
  try {
    logger.info("Getting Owempay v2 transaction status", {
      transactionId: params.transactionId,
      organizationId: params.organizationId,
      transactionType: params.transactionType
    });

    const credentials = await getOwempayV2Credentials(params.organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    // Try different endpoint patterns for transaction status
    const possiblePaths = [
      `/v4/i/transactions/${params.transactionId}`,
      `/v4/i/pix/transactions/${params.transactionId}`,
      `/v4/i/bank-accounts/************/transactions/${params.transactionId}`
    ];

    for (const path of possiblePaths) {
      try {
        const response = await makeHTTPRequest(`${baseUrl}${path}`, {
          method: "GET",
          headers: {
            "Authorization": authHeader,
            "Accept": "application/json"
          },
          timeout: 15000
        });

        if (response.ok) {
          const responseData = await response.json();
          logger.info("Transaction status retrieved successfully", {
            transactionId: params.transactionId,
            status: responseData.status,
            path: path
          });

          return {
            id: responseData.id || params.transactionId,
            status: mapOwempayV2StatusToInternal(responseData.status),
            amount: responseData.amount,
            pixKey: responseData.pixKey,
            description: responseData.description,
            createdAt: responseData.createdAt,
            updatedAt: responseData.updatedAt,
            metadata: {
              owempay_v2: {
                originalStatus: responseData.status,
                endpointUsed: path,
                provider: "OWEMPAY_V2"
              }
            }
          };
        }
      } catch (pathError) {
        logger.debug(`Failed to get status from path ${path}`, { pathError });
        continue;
      }
    }

    throw new Error(`Transaction ${params.transactionId} not found in any endpoint`);
  } catch (error) {
    logger.error("Error getting transaction status", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId
    });
    throw error;
  }
}

// Process refund (required by PaymentGatewayProvider interface)
export async function processRefund(params: {
  transactionId: string;
  amount: number;
  reason?: string;
  organizationId: string;
}): Promise<any> {
  try {
    logger.info("Processing Owempay v2 refund", {
      transactionId: params.transactionId,
      amount: params.amount,
      organizationId: params.organizationId,
      reason: params.reason
    });

    const credentials = await getOwempayV2Credentials(params.organizationId);
    const baseUrl = OWEMPAY_V2_API_BASE_URL[credentials.environment];
    const authHeader = createBasicAuthHeader(credentials.apiKey, credentials.apiSecret);

    const payload = {
      transactionId: params.transactionId,
      amount: params.amount,
      reason: params.reason || "Refund requested"
    };

    // Try different refund endpoint patterns
    const possiblePaths = [
      `/v4/i/transactions/${params.transactionId}/refund`,
      `/v4/i/pix/transactions/${params.transactionId}/refund`,
      `/v4/i/refunds`
    ];

    for (const path of possiblePaths) {
      try {
        const response = await makeHTTPRequest(`${baseUrl}${path}`, {
          method: "POST",
          headers: {
            "Authorization": authHeader,
            "Content-Type": "application/json",
            "Accept": "application/json"
          },
          body: JSON.stringify(payload),
          timeout: 30000
        });

        if (response.ok) {
          const responseData = await response.json();
          logger.info("Refund processed successfully", {
            transactionId: params.transactionId,
            refundId: responseData.id,
            status: responseData.status,
            path: path
          });

          return {
            id: responseData.id,
            transactionId: params.transactionId,
            status: mapOwempayV2StatusToInternal(responseData.status),
            amount: responseData.amount || params.amount,
            reason: responseData.reason || params.reason,
            createdAt: responseData.createdAt,
            metadata: {
              owempay_v2: {
                originalStatus: responseData.status,
                endpointUsed: path,
                provider: "OWEMPAY_V2"
              }
            }
          };
        }
      } catch (pathError) {
        logger.debug(`Failed to process refund from path ${path}`, { pathError });
        continue;
      }
    }

    throw new Error(`Failed to process refund for transaction ${params.transactionId}`);
  } catch (error) {
    logger.error("Error processing refund", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId
    });
    throw error;
  }
}

// Webhook handler for Owempay v2
export async function webhookHandler(params: {
  body: any;
  headers: Record<string, string>;
  organizationId?: string;
}): Promise<any> {
  try {
    logger.info("Processing Owempay v2 webhook", {
      body: params.body,
      headers: Object.keys(params.headers)
    });

    // Validate webhook signature
    const signature = params.headers['x-owempay-signature'] || params.headers['signature'];
    const webhookSecret = process.env.OWEMPAY_V2_WEBHOOK_SECRET;
    const bypassValidation = process.env.OWEMPAY_V2_BYPASS_SIGNATURE_VALIDATION === 'true';

    if (!bypassValidation && !validateOwempayV2WebhookSignature(
      JSON.stringify(params.body),
      signature || '',
      webhookSecret
    )) {
      throw new Error("Invalid webhook signature");
    }

    // Extract transaction information from Owempay v2 webhook payload
    const webhookData = params.body.object || params.body;
    const transactionId = webhookData.id;
    const externalId = webhookData.externalReference;
    const status = webhookData.status;
    const amount = webhookData.amount;
    const timeline = webhookData.timeline;
    const eventType = params.body.event;

    // Log the extracted data for debugging
    logger.info("Webhook data extracted", {
      transactionId,
      externalId,
      status,
      amount,
      eventType,
      hasTimeline: !!timeline
    });

    // Find the transaction in our database
    let transaction = null;

    if (externalId) {
      transaction = await db.transaction.findFirst({
        where: { externalId }
      });
    }

    if (!transaction && transactionId) {
      transaction = await db.transaction.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { externalId: transactionId }
          ]
        }
      });
    }

    if (!transaction) {
      logger.warn("Transaction not found for Owempay v2 webhook", {
        transactionId,
        externalId,
        eventType
      });
      return { success: false, message: "Transaction not found" };
    }

    // Check if this is a MED refund (refunded_med status)
    if (status === 'refunded_med') {
      logger.info('[OWEMPAY_V2][MED] Detectado status refunded_med, criando infração MED', {
        transactionId: transaction.id,
        externalId,
        status,
        amount: transaction.amount
      });

      try {
        // Usar o novo serviço MED centralizado
        const medResult = await processMedInfraction({
          transactionId: transaction.id,
          externalId: externalId || `owempay_v2_${transaction.id}_${Date.now()}`,
          reportDetails: `Devolução MED via OWEMPAY V2 - Status: ${status}`,
          organizationId: transaction.organizationId,
          gatewayName: 'OWEMPAY_V2',
          autoApprove: true, // Aprovação automática para Owempay v2
          reportedBy: 'DEBITED_PARTICIPANT',
          type: 'REFUND_REQUEST'
        });

        if (medResult.success) {
          logger.info('[OWEMPAY_V2][MED] Processamento de MED concluído com sucesso', {
            infractionId: medResult.infractionId,
            transactionId: transaction.id,
            autoApproved: true
          });
        } else {
          logger.error('[OWEMPAY_V2][MED] Erro ao processar MED', {
            error: medResult.error,
            message: medResult.message,
            transactionId: transaction.id,
          });
          // Não falhar o webhook se o processamento do MED falhar
        }

      } catch (medError) {
        logger.error('[OWEMPAY_V2][MED] Erro ao processar MED', {
          error: medError,
          transactionId: transaction.id,
          externalId,
          status
        });
        // Não falhar o webhook se a criação do MED falhar
      }
    }

    // Update transaction status
    const internalStatus = mapOwempayV2StatusToInternal(status) as any;

    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: internalStatus,
        updatedAt: new Date(),
        metadata: {
          ...(transaction.metadata as Record<string, any>),
          lastWebhookEvent: eventType,
          lastWebhookReceived: new Date().toISOString(),
          timeline: timeline
        }
      }
    });

    logger.info("Owempay v2 webhook processed successfully", {
      transactionId: transaction.id,
      externalId,
      oldStatus: transaction.status,
      newStatus: internalStatus,
      eventType
    });

    return {
      success: true,
      data: {
        transactionId: transaction.id,
        externalId,
        status: internalStatus,
        eventType
      }
    };
  } catch (error) {
    logger.error("Error processing Owempay v2 webhook", { error, params });
    throw error;
  }
}

// Export all functions for the payment provider interface
export default {
  type: 'OWEMPAY_V2', // Add type property for proper identification
  testOwempayV2Connection,
  createPixPayment,
  createExternalTransfer,
  processPixWithdrawal,
  getTransactionStatus,
  processRefund,
  createWebhookConfig,
  webhookHandler,
  getOwempayV2Balance
};
