-- Rollback script for transaction constraints migration
-- Use this script to safely remove the constraints if issues occur

-- Log the rollback operation
DO $$
BEGIN
    RAISE NOTICE 'Starting rollback of transaction constraints at %', NOW();
END $$;

-- 1. Drop the unique constraint for SEND transactions
DROP INDEX CONCURRENTLY IF EXISTS "unique_send_transaction";
RAISE NOTICE 'Dropped unique_send_transaction index';

-- 2. Drop the unique constraint for reference codes
DROP INDEX CONCURRENTLY IF EXISTS "unique_reference_code";
RAISE NOTICE 'Dropped unique_reference_code index';

-- 3. Drop the unique constraint for external IDs
DROP INDEX CONCURRENTLY IF EXISTS "unique_external_id";
RAISE NOTICE 'Dropped unique_external_id index';

-- 4. Drop the idempotency key index
DROP INDEX CONCURRENTLY IF EXISTS "idx_transaction_idempotency";
RAISE NOTICE 'Dropped idx_transaction_idempotency index';

-- 5. Drop the recent transaction lookup index
DROP INDEX CONCURRENTLY IF EXISTS "idx_transaction_recent_lookup";
RAISE NOTICE 'Dropped idx_transaction_recent_lookup index';

-- 6. Drop the active transactions index
DROP INDEX CONCURRENTLY IF EXISTS "idx_transaction_active";
RAISE NOTICE 'Dropped idx_transaction_active index';

-- Verify rollback completion
DO $$
DECLARE
    index_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes
    WHERE tablename = 'transaction'
    AND indexname IN (
        'unique_send_transaction',
        'unique_reference_code',
        'unique_external_id',
        'idx_transaction_idempotency',
        'idx_transaction_recent_lookup',
        'idx_transaction_active'
    );
    
    IF index_count = 0 THEN
        RAISE NOTICE 'Rollback completed successfully. All constraint indexes removed.';
    ELSE
        RAISE WARNING 'Rollback incomplete. % indexes still exist.', index_count;
    END IF;
END $$;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Rollback of transaction constraints completed at %', NOW();
END $$;
