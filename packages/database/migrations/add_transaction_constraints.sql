-- Migration: Add transaction constraints to prevent duplicates
-- This migration adds unique constraints to prevent duplicate transactions
-- ⚠️ CRITICAL: Test this migration in staging first to ensure no existing duplicates

-- Pre-migration check: Verify no existing duplicates that would cause constraint violation
DO $$
DECLARE
    duplicate_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO duplicate_count
    FROM (
        SELECT "organizationId", "customerEmail", amount, "pixKey", "pixKeyType", status, COUNT(*)
        FROM "transaction"
        WHERE "type" = 'SEND' AND status IN ('PENDING', 'PROCESSING', 'APPROVED')
        GROUP BY "organizationId", "customerEmail", amount, "pixKey", "pixKeyType", status
        HAVING COUNT(*) > 1
    ) duplicates;

    IF duplicate_count > 0 THEN
        RAISE EXCEPTION 'Found % duplicate SEND transactions. Clean up duplicates before applying constraint.', duplicate_count;
    END IF;

    RAISE NOTICE 'Pre-migration check passed: No duplicate SEND transactions found.';
END $$;

-- 1. Add unique constraint for SEND transactions (transfers) to prevent duplicates
-- This ensures no duplicate transfers can be created
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS "unique_send_transaction"
ON "transaction" ("organizationId", "customerEmail", "amount", "pixKey", "pixKeyType", "status")
WHERE "type" = 'SEND' AND "status" IN ('PENDING', 'PROCESSING', 'APPROVED');

-- Pre-check for referenceCode duplicates
DO $$
DECLARE
    ref_duplicate_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO ref_duplicate_count
    FROM (
        SELECT "referenceCode", COUNT(*)
        FROM "transaction"
        WHERE "referenceCode" IS NOT NULL
        GROUP BY "referenceCode"
        HAVING COUNT(*) > 1
    ) duplicates;

    IF ref_duplicate_count > 0 THEN
        RAISE WARNING 'Found % duplicate reference codes. These will need to be resolved manually.', ref_duplicate_count;
        -- Don't fail, just warn - reference codes can be regenerated
    END IF;
END $$;

-- Pre-check for externalId duplicates
DO $$
DECLARE
    ext_duplicate_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO ext_duplicate_count
    FROM (
        SELECT "externalId", COUNT(*)
        FROM "transaction"
        WHERE "externalId" IS NOT NULL
        GROUP BY "externalId"
        HAVING COUNT(*) > 1
    ) duplicates;

    IF ext_duplicate_count > 0 THEN
        RAISE EXCEPTION 'Found % duplicate external IDs. Clean up duplicates before applying constraint.', ext_duplicate_count;
    END IF;
END $$;

-- 2. Add unique constraint for reference codes (with warning for duplicates)
-- Note: This may fail if duplicates exist, but that's acceptable for reference codes
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS "unique_reference_code"
ON "transaction" ("referenceCode")
WHERE "referenceCode" IS NOT NULL;

-- 3. Add unique constraint for external IDs to prevent duplicate external IDs
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS "unique_external_id"
ON "transaction" ("externalId")
WHERE "externalId" IS NOT NULL;

-- 4. Add composite index for idempotency key lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transaction_idempotency"
ON "transaction" USING GIN (("metadata"->>'idempotencyKey'))
WHERE "metadata"->>'idempotencyKey' IS NOT NULL;

-- 5. Add index for recent transaction lookups (last 10 minutes)
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transaction_recent_lookup"
ON "transaction" ("organizationId", "customerEmail", "amount", "type", "status", "createdAt")
WHERE "createdAt" > NOW() - INTERVAL '10 minutes';

-- 6. Add partial index for active transactions only
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_transaction_active"
ON "transaction" ("organizationId", "customerEmail", "amount", "type")
WHERE "status" IN ('PENDING', 'PROCESSING', 'APPROVED');
