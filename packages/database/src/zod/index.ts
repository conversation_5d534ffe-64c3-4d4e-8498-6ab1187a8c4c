import { z } from 'zod';
import { Prisma } from '@prisma/client';
import Decimal from 'decimal.js';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;

// DECIMAL
//------------------------------------------------------

export const DecimalJsLikeSchema: z.ZodType<Prisma.DecimalJsLike> = z.object({
  d: z.array(z.number()),
  e: z.number(),
  s: z.number(),
  toFixed: z.function(z.tuple([]), z.string()),
})

export const DECIMAL_STRING_REGEX = /^(?:-?Infinity|NaN|-?(?:0[bB][01]+(?:\.[01]+)?(?:[pP][-+]?\d+)?|0[oO][0-7]+(?:\.[0-7]+)?(?:[pP][-+]?\d+)?|0[xX][\da-fA-F]+(?:\.[\da-fA-F]+)?(?:[pP][-+]?\d+)?|(?:\d+|\d*\.\d+)(?:[eE][-+]?\d+)?))$/;

export const isValidDecimalInput =
  (v?: null | string | number | Prisma.DecimalJsLike): v is string | number | Prisma.DecimalJsLike => {
    if (v === undefined || v === null) return false;
    return (
      (typeof v === 'object' && 'd' in v && 'e' in v && 's' in v && 'toFixed' in v) ||
      (typeof v === 'string' && DECIMAL_STRING_REGEX.test(v)) ||
      typeof v === 'number'
    )
  };

/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const AccountScalarFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','expiresAt','password','accessTokenExpiresAt','refreshTokenExpiresAt','scope','createdAt','updatedAt']);

export const Api_keyScalarFieldEnumSchema = z.enum(['id','name','type','prefix','hash','permissions','organizationId','createdById','createdAt','updatedAt','lastUsedAt']);

export const Balance_historyScalarFieldEnumSchema = z.enum(['id','organizationId','balanceId','transactionId','operation','amount','description','balanceAfterOperation','createdAt','currency']);

export const Cautionary_blockScalarFieldEnumSchema = z.enum(['id','reason','status','transactionId','createdAt','releasedAt','updatedAt']);

export const Idempotency_keyScalarFieldEnumSchema = z.enum(['key','operation','status','result','createdAt','completedAt','expiresAt']);

export const InvitationScalarFieldEnumSchema = z.enum(['id','organizationId','email','role','status','expiresAt','inviterId']);

export const MemberScalarFieldEnumSchema = z.enum(['id','organizationId','userId','role','createdAt']);

export const OrganizationScalarFieldEnumSchema = z.enum(['id','name','slug','logo','createdAt','metadata','status','withdrawalBlocked','withdrawalBlockedReason','withdrawalBlockedAt','withdrawalBlockedBy']);

export const Organization_balanceScalarFieldEnumSchema = z.enum(['id','organizationId','availableBalance','pendingBalance','reservedBalance','updatedAt','currency']);

export const Organization_documentsScalarFieldEnumSchema = z.enum(['id','organizationId','cnpjDocument','businessLicense','bankStatement','representativeIdDocument','proofOfAddress','additionalDocument','status','reviewedAt','reviewedById','reviewNotes','submittedById','createdAt','updatedAt']);

export const Organization_gatewayScalarFieldEnumSchema = z.enum(['id','organizationId','gatewayId','isDefault','isActive','priority','createdAt','updatedAt']);

export const Organization_legal_infoScalarFieldEnumSchema = z.enum(['id','organizationId','companyName','tradingName','document','documentType','legalRepresentative','legalRepDocumentNumber','address','city','state','postalCode','contactEmail','contactPhone','documentsUploaded','reviewNotes','reviewedBy','reviewedAt','createdAt','updatedAt']);

export const Organization_taxesScalarFieldEnumSchema = z.enum(['id','organizationId','pixChargePercentFee','pixTransferPercentFee','pixChargeFixedFee','pixTransferFixedFee','gatewaySpecificTaxes','createdAt','updatedAt']);

export const PasskeyScalarFieldEnumSchema = z.enum(['id','name','publicKey','userId','webauthnUserID','counter','deviceType','backedUp','transports','createdAt']);

export const Payment_gatewayScalarFieldEnumSchema = z.enum(['id','name','displayName','type','credentials','isActive','isDefault','createdAt','updatedAt','priority','canReceive','canSend','configuredById','isGlobal','pixChargeFixedFee','pixChargePercentFee','pixTransferFixedFee','pixTransferPercentFee','instanceNumber','description','webhooksEnabled']);

export const Pix_keyScalarFieldEnumSchema = z.enum(['id','key','type','description','isDefault','organizationId','createdAt','updatedAt']);

export const Pix_webhook_deliveriesScalarFieldEnumSchema = z.enum(['id','error','createdAt','updatedAt','eventId','success','response']);

export const Pix_webhook_eventsScalarFieldEnumSchema = z.enum(['id','type','status','payload','processedAt','createdAt','updatedAt','transactionId','organizationId']);

export const PurchaseScalarFieldEnumSchema = z.enum(['id','organizationId','userId','type','customerId','subscriptionId','productId','status','createdAt','updatedAt']);

export const SessionScalarFieldEnumSchema = z.enum(['id','expiresAt','ipAddress','userAgent','userId','impersonatedBy','activeOrganizationId','token','createdAt','updatedAt']);

export const Svix_app_channelScalarFieldEnumSchema = z.enum(['id','organizationId','svixAppId','createdAt','updatedAt']);

export const Svix_configScalarFieldEnumSchema = z.enum(['id','appId','appName','enabled','createdAt','updatedAt']);

export const TransactionScalarFieldEnumSchema = z.enum(['id','externalId','referenceCode','customerName','customerEmail','customerPhone','customerDocument','customerDocumentType','createdAt','paymentAt','amount','status','pixKey','pixKeyType','type','description','metadata','organizationId','gatewayId','updatedAt','gatewayName','originalTransactionId','processedAt','reason','endToEndId','fixedFee','netAmount','percentFee','totalFee']);

export const TwoFactorScalarFieldEnumSchema = z.enum(['id','userId','secret','backupCodes','createdAt','updatedAt']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','createdAt','updatedAt','username','role','banned','banReason','banExpires','onboardingComplete','locale','twoFactorEnabled']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','createdAt','updatedAt']);

export const WebhookScalarFieldEnumSchema = z.enum(['id','url','secret','events','isActive','organizationId','createdAt','updatedAt','svixEndpointId','useSvix']);

export const Webhook_deliveryScalarFieldEnumSchema = z.enum(['id','webhookId','eventId','status','attempts','maxAttempts','nextAttemptAt','lastAttemptAt','response','error','createdAt','updatedAt','svixAttemptId']);

export const Webhook_eventScalarFieldEnumSchema = z.enum(['id','type','payload','transactionId','organizationId','createdAt','svixEventType','svixMessageId']);

export const System_settingsScalarFieldEnumSchema = z.enum(['id','globalWithdrawalBlocked','globalWithdrawalMessage','globalWithdrawalBlockedAt','globalWithdrawalBlockedBy','medAutoApprovalEnabled','medAutoApprovalMessage','medAutoApprovalEnabledAt','medAutoApprovalEnabledBy','createdAt','updatedAt']);

export const Med_infractionScalarFieldEnumSchema = z.enum(['id','externalId','transactionId','organizationId','status','type','reportedBy','creationDate','reportDetails','analysisResult','analysisDetails','transactionAmount','lastModificationDate','processedAt','createdAt','updatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const JsonNullValueInputSchema = z.enum(['JsonNull',]).transform((value) => (value === 'JsonNull' ? Prisma.JsonNull : value));

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const NullsOrderSchema = z.enum(['first','last']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const DocumentReviewStatusSchema = z.enum(['PENDING_REVIEW','APPROVED','REJECTED','REQUIRES_ADDITIONAL_INFO']);

export type DocumentReviewStatusType = `${z.infer<typeof DocumentReviewStatusSchema>}`

export const OrganizationStatusSchema = z.enum(['PENDING_REVIEW','APPROVED','REJECTED','BLOCKED']);

export type OrganizationStatusType = `${z.infer<typeof OrganizationStatusSchema>}`

export const PixKeyTypeSchema = z.enum(['CPF','CNPJ','EMAIL','PHONE','RANDOM']);

export type PixKeyTypeType = `${z.infer<typeof PixKeyTypeSchema>}`

export const PurchaseTypeSchema = z.enum(['SUBSCRIPTION','ONE_TIME']);

export type PurchaseTypeType = `${z.infer<typeof PurchaseTypeSchema>}`

export const TransactionStatusSchema = z.enum(['PENDING','APPROVED','REJECTED','CANCELED','PROCESSING','REFUNDED','BLOCKED']);

export type TransactionStatusType = `${z.infer<typeof TransactionStatusSchema>}`

export const TransactionTypeSchema = z.enum(['CHARGE','SEND','RECEIVE','REFUND']);

export type TransactionTypeType = `${z.infer<typeof TransactionTypeSchema>}`

export const WebhookDeliveryStatusSchema = z.enum(['PENDING','SUCCESS','FAILED','RETRYING']);

export type WebhookDeliveryStatusType = `${z.infer<typeof WebhookDeliveryStatusSchema>}`

export const MedInfractionStatusSchema = z.enum(['WAITING_PSP','BLOCKED','ANALYZED','CLOSED','REFUNDED']);

export type MedInfractionStatusType = `${z.infer<typeof MedInfractionStatusSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const accountSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  userId: z.string(),
  accessToken: z.string().nullable(),
  refreshToken: z.string().nullable(),
  idToken: z.string().nullable(),
  expiresAt: z.coerce.date().nullable(),
  password: z.string().nullable(),
  accessTokenExpiresAt: z.coerce.date().nullable(),
  refreshTokenExpiresAt: z.coerce.date().nullable(),
  scope: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type account = z.infer<typeof accountSchema>

/////////////////////////////////////////
// API KEY SCHEMA
/////////////////////////////////////////

export const api_keySchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  prefix: z.string(),
  hash: z.string(),
  permissions: JsonValueSchema,
  organizationId: z.string(),
  createdById: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  lastUsedAt: z.coerce.date().nullable(),
})

export type api_key = z.infer<typeof api_keySchema>

/////////////////////////////////////////
// BALANCE HISTORY SCHEMA
/////////////////////////////////////////

export const balance_historySchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  balanceId: z.string().nullable(),
  transactionId: z.string().nullable(),
  operation: z.string(),
  amount: z.instanceof(Prisma.Decimal, { message: "Field 'amount' must be a Decimal. Location: ['Models', 'balance_history']"}),
  description: z.string().nullable(),
  balanceAfterOperation: JsonValueSchema,
  createdAt: z.coerce.date(),
  currency: z.string(),
})

export type balance_history = z.infer<typeof balance_historySchema>

/////////////////////////////////////////
// CAUTIONARY BLOCK SCHEMA
/////////////////////////////////////////

export const cautionary_blockSchema = z.object({
  id: z.string(),
  reason: z.string(),
  status: z.string(),
  transactionId: z.string(),
  createdAt: z.coerce.date(),
  releasedAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date(),
})

export type cautionary_block = z.infer<typeof cautionary_blockSchema>

/////////////////////////////////////////
// IDEMPOTENCY KEY SCHEMA
/////////////////////////////////////////

export const idempotency_keySchema = z.object({
  key: z.string(),
  operation: z.string(),
  status: z.string(),
  result: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  completedAt: z.coerce.date().nullable(),
  expiresAt: z.coerce.date(),
})

export type idempotency_key = z.infer<typeof idempotency_keySchema>

/////////////////////////////////////////
// INVITATION SCHEMA
/////////////////////////////////////////

export const invitationSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  email: z.string(),
  role: z.string().nullable(),
  status: z.string(),
  expiresAt: z.coerce.date(),
  inviterId: z.string(),
})

export type invitation = z.infer<typeof invitationSchema>

/////////////////////////////////////////
// MEMBER SCHEMA
/////////////////////////////////////////

export const memberSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  role: z.string(),
  createdAt: z.coerce.date(),
})

export type member = z.infer<typeof memberSchema>

/////////////////////////////////////////
// ORGANIZATION SCHEMA
/////////////////////////////////////////

export const organizationSchema = z.object({
  status: OrganizationStatusSchema,
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  createdAt: z.coerce.date(),
  metadata: z.string().nullable(),
  withdrawalBlocked: z.boolean(),
  withdrawalBlockedReason: z.string().nullable(),
  withdrawalBlockedAt: z.coerce.date().nullable(),
  withdrawalBlockedBy: z.string().nullable(),
})

export type organization = z.infer<typeof organizationSchema>

/////////////////////////////////////////
// ORGANIZATION BALANCE SCHEMA
/////////////////////////////////////////

export const organization_balanceSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  availableBalance: z.instanceof(Prisma.Decimal, { message: "Field 'availableBalance' must be a Decimal. Location: ['Models', 'organization_balance']"}),
  pendingBalance: z.instanceof(Prisma.Decimal, { message: "Field 'pendingBalance' must be a Decimal. Location: ['Models', 'organization_balance']"}),
  reservedBalance: z.instanceof(Prisma.Decimal, { message: "Field 'reservedBalance' must be a Decimal. Location: ['Models', 'organization_balance']"}),
  updatedAt: z.coerce.date(),
  currency: z.string(),
})

export type organization_balance = z.infer<typeof organization_balanceSchema>

/////////////////////////////////////////
// ORGANIZATION DOCUMENTS SCHEMA
/////////////////////////////////////////

export const organization_documentsSchema = z.object({
  status: DocumentReviewStatusSchema,
  id: z.string(),
  organizationId: z.string(),
  cnpjDocument: z.string().nullable(),
  businessLicense: z.string().nullable(),
  bankStatement: z.string().nullable(),
  representativeIdDocument: z.string().nullable(),
  proofOfAddress: z.string().nullable(),
  additionalDocument: z.string().nullable(),
  reviewedAt: z.coerce.date().nullable(),
  reviewedById: z.string().nullable(),
  reviewNotes: z.string().nullable(),
  submittedById: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type organization_documents = z.infer<typeof organization_documentsSchema>

/////////////////////////////////////////
// ORGANIZATION GATEWAY SCHEMA
/////////////////////////////////////////

export const organization_gatewaySchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  gatewayId: z.string(),
  isDefault: z.boolean(),
  isActive: z.boolean(),
  priority: z.number().int(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type organization_gateway = z.infer<typeof organization_gatewaySchema>

/////////////////////////////////////////
// ORGANIZATION LEGAL INFO SCHEMA
/////////////////////////////////////////

export const organization_legal_infoSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  companyName: z.string(),
  tradingName: z.string().nullable(),
  document: z.string(),
  documentType: z.string(),
  legalRepresentative: z.string(),
  legalRepDocumentNumber: z.string(),
  address: z.string(),
  city: z.string(),
  state: z.string(),
  postalCode: z.string(),
  contactEmail: z.string(),
  contactPhone: z.string(),
  documentsUploaded: JsonValueSchema.nullable(),
  reviewNotes: z.string().nullable(),
  reviewedBy: z.string().nullable(),
  reviewedAt: z.coerce.date().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type organization_legal_info = z.infer<typeof organization_legal_infoSchema>

/////////////////////////////////////////
// ORGANIZATION TAXES SCHEMA
/////////////////////////////////////////

export const organization_taxesSchema = z.object({
  id: z.string().cuid(),
  organizationId: z.string(),
  pixChargePercentFee: z.number(),
  pixTransferPercentFee: z.number(),
  pixChargeFixedFee: z.number(),
  pixTransferFixedFee: z.number(),
  gatewaySpecificTaxes: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type organization_taxes = z.infer<typeof organization_taxesSchema>

/////////////////////////////////////////
// PASSKEY SCHEMA
/////////////////////////////////////////

export const passkeySchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  publicKey: z.string(),
  userId: z.string(),
  webauthnUserID: z.string(),
  counter: z.number().int(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string().nullable(),
  createdAt: z.coerce.date().nullable(),
})

export type passkey = z.infer<typeof passkeySchema>

/////////////////////////////////////////
// PAYMENT GATEWAY SCHEMA
/////////////////////////////////////////

export const payment_gatewaySchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  displayName: z.string().nullable(),
  type: z.string(),
  credentials: JsonValueSchema,
  isActive: z.boolean(),
  isDefault: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  priority: z.number().int(),
  canReceive: z.boolean(),
  canSend: z.boolean(),
  configuredById: z.string().nullable(),
  isGlobal: z.boolean(),
  pixChargeFixedFee: z.instanceof(Prisma.Decimal, { message: "Field 'pixChargeFixedFee' must be a Decimal. Location: ['Models', 'payment_gateway']"}),
  pixChargePercentFee: z.instanceof(Prisma.Decimal, { message: "Field 'pixChargePercentFee' must be a Decimal. Location: ['Models', 'payment_gateway']"}),
  pixTransferFixedFee: z.instanceof(Prisma.Decimal, { message: "Field 'pixTransferFixedFee' must be a Decimal. Location: ['Models', 'payment_gateway']"}),
  pixTransferPercentFee: z.instanceof(Prisma.Decimal, { message: "Field 'pixTransferPercentFee' must be a Decimal. Location: ['Models', 'payment_gateway']"}),
  instanceNumber: z.number().int(),
  description: z.string().nullable(),
  webhooksEnabled: z.boolean(),
})

export type payment_gateway = z.infer<typeof payment_gatewaySchema>

/////////////////////////////////////////
// PIX KEY SCHEMA
/////////////////////////////////////////

export const pix_keySchema = z.object({
  type: PixKeyTypeSchema,
  id: z.string(),
  key: z.string(),
  description: z.string().nullable(),
  isDefault: z.boolean(),
  organizationId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type pix_key = z.infer<typeof pix_keySchema>

/////////////////////////////////////////
// PIX WEBHOOK DELIVERIES SCHEMA
/////////////////////////////////////////

export const pix_webhook_deliveriesSchema = z.object({
  id: z.string(),
  error: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  eventId: z.string(),
  success: z.boolean(),
  response: JsonValueSchema.nullable(),
})

export type pix_webhook_deliveries = z.infer<typeof pix_webhook_deliveriesSchema>

/////////////////////////////////////////
// PIX WEBHOOK EVENTS SCHEMA
/////////////////////////////////////////

export const pix_webhook_eventsSchema = z.object({
  id: z.string(),
  type: z.string(),
  status: z.string(),
  payload: JsonValueSchema,
  processedAt: z.coerce.date().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  transactionId: z.string().nullable(),
  organizationId: z.string(),
})

export type pix_webhook_events = z.infer<typeof pix_webhook_eventsSchema>

/////////////////////////////////////////
// PURCHASE SCHEMA
/////////////////////////////////////////

export const purchaseSchema = z.object({
  type: PurchaseTypeSchema,
  id: z.string(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  customerId: z.string(),
  subscriptionId: z.string().nullable(),
  productId: z.string().nullable(),
  status: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type purchase = z.infer<typeof purchaseSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const sessionSchema = z.object({
  id: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  userId: z.string(),
  impersonatedBy: z.string().nullable(),
  activeOrganizationId: z.string().nullable(),
  token: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type session = z.infer<typeof sessionSchema>

/////////////////////////////////////////
// SVIX APP CHANNEL SCHEMA
/////////////////////////////////////////

export const svix_app_channelSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  svixAppId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type svix_app_channel = z.infer<typeof svix_app_channelSchema>

/////////////////////////////////////////
// SVIX CONFIG SCHEMA
/////////////////////////////////////////

export const svix_configSchema = z.object({
  id: z.string(),
  appId: z.string(),
  appName: z.string(),
  enabled: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type svix_config = z.infer<typeof svix_configSchema>

/////////////////////////////////////////
// TRANSACTION SCHEMA
/////////////////////////////////////////

export const transactionSchema = z.object({
  status: TransactionStatusSchema,
  pixKeyType: PixKeyTypeSchema.nullable(),
  type: TransactionTypeSchema,
  id: z.string().cuid(),
  externalId: z.string().nullable(),
  referenceCode: z.string().nullable(),
  customerName: z.string(),
  customerEmail: z.string(),
  customerPhone: z.string().nullable(),
  customerDocument: z.string().nullable(),
  customerDocumentType: z.string().nullable(),
  createdAt: z.coerce.date(),
  paymentAt: z.coerce.date().nullable(),
  amount: z.number(),
  pixKey: z.string().nullable(),
  description: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
  organizationId: z.string(),
  gatewayId: z.string().nullable(),
  updatedAt: z.coerce.date(),
  gatewayName: z.string().nullable(),
  originalTransactionId: z.string().nullable(),
  processedAt: z.coerce.date().nullable(),
  reason: z.string().nullable(),
  endToEndId: z.string().nullable(),
  fixedFee: z.number(),
  netAmount: z.number().nullable(),
  percentFee: z.number(),
  totalFee: z.number(),
})

export type transaction = z.infer<typeof transactionSchema>

/////////////////////////////////////////
// TWO FACTOR SCHEMA
/////////////////////////////////////////

export const twoFactorSchema = z.object({
  id: z.string(),
  userId: z.string(),
  secret: z.string(),
  backupCodes: z.string().nullable(),
  createdAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date().nullable(),
})

export type twoFactor = z.infer<typeof twoFactorSchema>

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const userSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  username: z.string().nullable(),
  role: z.string().nullable(),
  banned: z.boolean().nullable(),
  banReason: z.string().nullable(),
  banExpires: z.coerce.date().nullable(),
  onboardingComplete: z.boolean(),
  locale: z.string().nullable(),
  twoFactorEnabled: z.boolean(),
})

export type user = z.infer<typeof userSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const verificationSchema = z.object({
  id: z.string(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date().nullable(),
})

export type verification = z.infer<typeof verificationSchema>

/////////////////////////////////////////
// WEBHOOK SCHEMA
/////////////////////////////////////////

export const webhookSchema = z.object({
  id: z.string(),
  url: z.string(),
  secret: z.string().nullable(),
  events: z.string().array(),
  isActive: z.boolean(),
  organizationId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  svixEndpointId: z.string().nullable(),
  useSvix: z.boolean(),
})

export type webhook = z.infer<typeof webhookSchema>

/////////////////////////////////////////
// WEBHOOK DELIVERY SCHEMA
/////////////////////////////////////////

export const webhook_deliverySchema = z.object({
  status: WebhookDeliveryStatusSchema,
  id: z.string(),
  webhookId: z.string(),
  eventId: z.string(),
  attempts: z.number().int(),
  maxAttempts: z.number().int(),
  nextAttemptAt: z.coerce.date().nullable(),
  lastAttemptAt: z.coerce.date().nullable(),
  response: JsonValueSchema.nullable(),
  error: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  svixAttemptId: z.string().nullable(),
})

export type webhook_delivery = z.infer<typeof webhook_deliverySchema>

/////////////////////////////////////////
// WEBHOOK EVENT SCHEMA
/////////////////////////////////////////

export const webhook_eventSchema = z.object({
  id: z.string().cuid(),
  type: z.string(),
  payload: JsonValueSchema,
  transactionId: z.string().nullable(),
  organizationId: z.string(),
  createdAt: z.coerce.date(),
  svixEventType: z.string().nullable(),
  svixMessageId: z.string().nullable(),
})

export type webhook_event = z.infer<typeof webhook_eventSchema>

/////////////////////////////////////////
// SYSTEM SETTINGS SCHEMA
/////////////////////////////////////////

export const system_settingsSchema = z.object({
  id: z.string().cuid(),
  globalWithdrawalBlocked: z.boolean(),
  globalWithdrawalMessage: z.string().nullable(),
  globalWithdrawalBlockedAt: z.coerce.date().nullable(),
  globalWithdrawalBlockedBy: z.string().nullable(),
  medAutoApprovalEnabled: z.boolean(),
  medAutoApprovalMessage: z.string().nullable(),
  medAutoApprovalEnabledAt: z.coerce.date().nullable(),
  medAutoApprovalEnabledBy: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type system_settings = z.infer<typeof system_settingsSchema>

/////////////////////////////////////////
// MED INFRACTION SCHEMA
/////////////////////////////////////////

export const med_infractionSchema = z.object({
  status: MedInfractionStatusSchema,
  id: z.string().cuid(),
  externalId: z.string().nullable(),
  transactionId: z.string().nullable(),
  organizationId: z.string(),
  type: z.string(),
  reportedBy: z.string(),
  creationDate: z.coerce.date(),
  reportDetails: z.string().nullable(),
  analysisResult: z.string().nullable(),
  analysisDetails: z.string().nullable(),
  transactionAmount: JsonValueSchema.nullable(),
  lastModificationDate: z.coerce.date(),
  processedAt: z.coerce.date().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type med_infraction = z.infer<typeof med_infractionSchema>
