generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  addIncludeType   = "false"
  addSelectType    = "false"
  createInputTypes = "false"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  expiresAt             DateTime?
  password              String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  user      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model api_key {
  id             String       @id
  name           String
  type           String
  prefix         String       @unique
  hash           String
  permissions    Json
  organizationId String
  createdById    String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  lastUsedAt     DateTime?
  user           user         @relation(fields: [createdById], references: [id], onDelete: Cascade)
  organization   organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([createdById])
  @@index([organizationId])
}

model balance_history {
  id                    String                @id
  organizationId        String
  balanceId             String?
  transactionId         String?
  operation             String
  amount                Decimal               @db.Decimal(15, 2)
  description           String?
  balanceAfterOperation Json
  createdAt             DateTime              @default(now())
  currency              String                @default("BRL")
  organization_balance  organization_balance? @relation(fields: [balanceId], references: [id])
  organization          organization          @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  transaction           transaction?          @relation(fields: [transactionId], references: [id])

  @@index([operation])
  @@index([organizationId])
  @@index([transactionId])
}

model cautionary_block {
  id            String      @id
  reason        String
  status        String
  transactionId String
  createdAt     DateTime    @default(now())
  releasedAt    DateTime?
  updatedAt     DateTime
  transaction   transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
}

model idempotency_key {
  key         String    @id
  operation   String
  status      String
  result      Json?
  createdAt   DateTime  @default(now())
  completedAt DateTime?
  expiresAt   DateTime

  @@index([expiresAt])
  @@index([operation])
  @@index([status])
}

model invitation {
  id             String       @id
  organizationId String
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           user         @relation(fields: [inviterId], references: [id], onDelete: Cascade)
  organization   organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model member {
  id             String       @id
  organizationId String
  userId         String
  role           String
  createdAt      DateTime
  organization   organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           user         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
}

model organization {
  id                      String                   @id
  name                    String
  slug                    String?                  @unique
  logo                    String?
  createdAt               DateTime
  metadata                String?
  status                  OrganizationStatus       @default(PENDING_REVIEW)
  withdrawalBlocked       Boolean                  @default(false)
  withdrawalBlockedReason String?
  withdrawalBlockedAt     DateTime?
  withdrawalBlockedBy     String?
  api_key                 api_key[]
  balance_history         balance_history[]
  invitation              invitation[]
  member                  member[]
  organization_balance    organization_balance?
  organization_documents  organization_documents?
  organization_gateway    organization_gateway[]
  organization_legal_info organization_legal_info?
  organization_taxes      organization_taxes?
  pix_key                 pix_key[]
  pix_webhook_events      pix_webhook_events[]
  purchase                purchase[]
  svix_app_channel        svix_app_channel?
  transaction             transaction[]
  webhook                 webhook[]
  webhook_event           webhook_event[]
  med_infraction          med_infraction[]
}

model organization_balance {
  id               String            @id
  organizationId   String            @unique
  availableBalance Decimal           @default(0) @db.Decimal(15, 2)
  pendingBalance   Decimal           @default(0) @db.Decimal(15, 2)
  reservedBalance  Decimal           @default(0) @db.Decimal(15, 2)
  updatedAt        DateTime
  currency         String            @default("BRL")
  balance_history  balance_history[]
  organization     organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model organization_documents {
  id                       String               @id
  organizationId           String               @unique
  cnpjDocument             String?
  businessLicense          String?
  bankStatement            String?
  representativeIdDocument String?
  proofOfAddress           String?
  additionalDocument       String?
  status                   DocumentReviewStatus @default(PENDING_REVIEW)
  reviewedAt               DateTime?
  reviewedById             String?
  reviewNotes              String?
  submittedById            String
  createdAt                DateTime             @default(now())
  updatedAt                DateTime
  organization             organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model organization_gateway {
  id              String          @id
  organizationId  String
  gatewayId       String
  isDefault       Boolean         @default(false)
  isActive        Boolean         @default(true)
  priority        Int             @default(999)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime
  payment_gateway payment_gateway @relation(fields: [gatewayId], references: [id], onDelete: Cascade)
  organization    organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, gatewayId])
  @@index([gatewayId])
  @@index([organizationId])
  @@index([organizationId, isActive, isDefault])
  @@index([organizationId, isActive, priority])
}

model organization_legal_info {
  id                     String       @id
  organizationId         String       @unique
  companyName            String
  tradingName            String?
  document               String
  documentType           String       @default("CNPJ")
  legalRepresentative    String
  legalRepDocumentNumber String
  address                String
  city                   String
  state                  String
  postalCode             String
  contactEmail           String
  contactPhone           String
  documentsUploaded      Json?
  reviewNotes            String?
  reviewedBy             String?
  reviewedAt             DateTime?
  createdAt              DateTime     @default(now())
  updatedAt              DateTime
  organization           organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model organization_taxes {
  id                    String       @id @default(cuid())
  organizationId        String       @unique
  pixChargePercentFee   Float        @default(0)
  pixTransferPercentFee Float        @default(0)
  pixChargeFixedFee     Float        @default(0)
  pixTransferFixedFee   Float        @default(0)
  gatewaySpecificTaxes  Json?
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt
  organization          organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model passkey {
  id             String    @id
  name           String?
  publicKey      String
  userId         String
  webauthnUserID String
  counter        Int
  deviceType     String
  backedUp       Boolean
  transports     String?
  createdAt      DateTime?
  user           user      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model payment_gateway {
  id                    String                 @id @default(cuid())
  name                  String
  displayName           String? // Nome personalizado para múltiplas instâncias
  type                  String
  credentials           Json
  isActive              Boolean                @default(false)
  isDefault             Boolean                @default(false)
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  priority              Int                    @default(999)
  canReceive            Boolean                @default(true)
  canSend               Boolean                @default(false)
  configuredById        String?
  isGlobal              Boolean                @default(false)
  pixChargeFixedFee     Decimal                @default(0) @db.Decimal(10, 2)
  pixChargePercentFee   Decimal                @default(0) @db.Decimal(5, 4)
  pixTransferFixedFee   Decimal                @default(0) @db.Decimal(10, 2)
  pixTransferPercentFee Decimal                @default(0) @db.Decimal(5, 4)
  instanceNumber        Int                    @default(1) // Para identificar múltiplas instâncias
  description           String? // Descrição personalizada da instância
  webhooksEnabled       Boolean                @default(true) // Controle de processamento de webhooks
  organization_gateway  organization_gateway[]
  transaction           transaction[]

  @@index([isActive, canReceive, priority])
  @@index([isGlobal, isDefault, isActive, canReceive, priority])
  @@index([type, isActive, canReceive])
  @@index([type, instanceNumber])
}

model pix_key {
  id             String       @id
  key            String
  type           PixKeyType
  description    String?
  isDefault      Boolean      @default(false)
  organizationId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime
  organization   organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model pix_webhook_deliveries {
  id                 String             @id
  error              String?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime
  eventId            String
  success            Boolean
  response           Json?
  pix_webhook_events pix_webhook_events @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
}

model pix_webhook_events {
  id                     String                   @id
  type                   String
  status                 String
  payload                Json
  processedAt            DateTime?
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime
  transactionId          String?
  organizationId         String
  pix_webhook_deliveries pix_webhook_deliveries[]
  organization           organization             @relation(fields: [organizationId], references: [id])
  transaction            transaction?             @relation(fields: [transactionId], references: [id])

  @@index([organizationId])
  @@index([status])
  @@index([transactionId])
  @@index([type])
}

model purchase {
  id             String        @id
  organizationId String?
  userId         String?
  type           PurchaseType
  customerId     String
  subscriptionId String?       @unique
  productId      String?
  status         String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime
  organization   organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           user?         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([subscriptionId])
}

model session {
  id                   String   @id
  expiresAt            DateTime
  ipAddress            String?
  userAgent            String?
  userId               String
  impersonatedBy       String?
  activeOrganizationId String?
  token                String   @unique
  createdAt            DateTime
  updatedAt            DateTime
  user                 user     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model svix_app_channel {
  id             String       @id
  organizationId String       @unique
  svixAppId      String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime
  organization   organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
}

model svix_config {
  id        String   @id
  appId     String   @unique
  appName   String
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime
}

model transaction {
  id                    String               @id @default(cuid())
  externalId            String?
  referenceCode         String?
  customerName          String
  customerEmail         String
  customerPhone         String?
  customerDocument      String?
  customerDocumentType  String?
  createdAt             DateTime             @default(now())
  paymentAt             DateTime?
  amount                Float
  status                TransactionStatus
  pixKey                String?
  pixKeyType            PixKeyType?
  type                  TransactionType
  description           String?
  metadata              Json?
  organizationId        String
  gatewayId             String?
  updatedAt             DateTime             @updatedAt
  gatewayName           String?
  originalTransactionId String?
  processedAt           DateTime?
  reason                String?
  endToEndId            String?
  fixedFee              Float                @default(0)
  netAmount             Float?
  percentFee            Float                @default(0)
  totalFee              Float                @default(0)
  balance_history       balance_history[]
  cautionary_block      cautionary_block[]
  pix_webhook_events    pix_webhook_events[]
  med_infraction        med_infraction[]
  payment_gateway       payment_gateway?     @relation(fields: [gatewayId], references: [id])
  organization          organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  transaction           transaction?         @relation("transactionTotransaction", fields: [originalTransactionId], references: [id])
  other_transaction     transaction[]        @relation("transactionTotransaction")
  webhook_event         webhook_event[]

  @@index([customerEmail, amount, organizationId, type, createdAt])
  @@index([customerEmail])
  @@index([endToEndId])
  @@index([externalId])
  @@index([organizationId, createdAt])
  @@index([organizationId])
  @@index([organizationId, status, type])
  @@index([originalTransactionId])
  @@index([referenceCode])
  @@index([status])
  @@index([type])
}

model twoFactor {
  id          String    @id
  userId      String    @unique
  secret      String
  backupCodes String?
  createdAt   DateTime? @default(now())
  updatedAt   DateTime?
  user        user      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model user {
  id                 String       @id
  name               String
  email              String       @unique
  emailVerified      Boolean
  image              String?
  createdAt          DateTime
  updatedAt          DateTime
  username           String?      @unique
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean      @default(false)
  locale             String?
  twoFactorEnabled   Boolean      @default(false)
  account            account[]
  api_key            api_key[]
  invitation         invitation[]
  member             member[]
  passkey            passkey[]
  purchase           purchase[]
  session            session[]
  twoFactor          twoFactor?
}

model verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?
}

model webhook {
  id               String             @id
  url              String
  secret           String?
  events           String[]
  isActive         Boolean            @default(true)
  organizationId   String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime
  svixEndpointId   String?            @unique
  useSvix          Boolean            @default(false)
  organization     organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  webhook_delivery webhook_delivery[]

  @@index([organizationId])
}

model webhook_delivery {
  id            String                @id
  webhookId     String
  eventId       String
  status        WebhookDeliveryStatus
  attempts      Int                   @default(0)
  maxAttempts   Int                   @default(5)
  nextAttemptAt DateTime?
  lastAttemptAt DateTime?
  response      Json?
  error         String?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime
  svixAttemptId String?               @unique
  webhook_event webhook_event         @relation(fields: [eventId], references: [id], onDelete: Cascade)
  webhook       webhook               @relation(fields: [webhookId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([status])
  @@index([webhookId])
}

model webhook_event {
  id               String             @id @default(cuid())
  type             String
  payload          Json
  transactionId    String?
  organizationId   String
  createdAt        DateTime           @default(now())
  svixEventType    String?
  svixMessageId    String?            @unique
  webhook_delivery webhook_delivery[]
  organization     organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  transaction      transaction?       @relation(fields: [transactionId], references: [id])

  @@index([organizationId])
  @@index([transactionId])
  @@index([type])
}

model system_settings {
  id                        String    @id @default(cuid())
  globalWithdrawalBlocked   Boolean   @default(false)
  globalWithdrawalMessage   String?
  globalWithdrawalBlockedAt DateTime?
  globalWithdrawalBlockedBy String?
  medAutoApprovalEnabled    Boolean   @default(false)
  medAutoApprovalMessage    String?   @default("Aprovação automática de MED ativada")
  medAutoApprovalEnabledAt  DateTime?
  medAutoApprovalEnabledBy  String?
  createdAt                 DateTime  @default(now())
  updatedAt                 DateTime  @updatedAt

  @@index([globalWithdrawalBlocked])
  @@index([medAutoApprovalEnabled])
}

model med_infraction {
  id                   String              @id @default(cuid())
  externalId           String?             @unique
  transactionId        String?
  organizationId       String
  status               MedInfractionStatus @default(WAITING_PSP)
  type                 String              @default("REFUND_REQUEST")
  reportedBy           String              @default("DEBITED_PARTICIPANT")
  creationDate         DateTime
  reportDetails        String?
  analysisResult       String?
  analysisDetails      String?
  transactionAmount    Json?
  lastModificationDate DateTime
  processedAt          DateTime?
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt

  // Relations
  organization organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  transaction  transaction? @relation(fields: [transactionId], references: [id])

  @@index([organizationId])
  @@index([transactionId])
  @@index([status])
  @@index([externalId])
  @@index([creationDate])
}

enum DocumentReviewStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
  REQUIRES_ADDITIONAL_INFO
}

enum OrganizationStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
  BLOCKED
}

enum PixKeyType {
  CPF
  CNPJ
  EMAIL
  PHONE
  RANDOM
}

enum PurchaseType {
  SUBSCRIPTION
  ONE_TIME
}

enum TransactionStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELED
  PROCESSING
  REFUNDED
  BLOCKED
}

enum TransactionType {
  CHARGE
  SEND
  RECEIVE
  REFUND
}

enum WebhookDeliveryStatus {
  PENDING
  SUCCESS
  FAILED
  RETRYING
}

enum MedInfractionStatus {
  WAITING_PSP
  BLOCKED
  ANALYZED
  CLOSED
  REFUNDED
}
