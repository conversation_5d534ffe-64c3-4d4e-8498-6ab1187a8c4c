# Transfeera SVIX Integration Implementation Template

## Overview

This template provides the exact code needed to add SVIX integration to the Transfeera webhook handler (`apps/web/app/api/webhooks/transfeera/route.ts`) following the same pattern as Pluggou PIX and MEDIUSPAG.

## Required Function: sendTransfeeraWebhookEvent

Add this function at the end of the Transfeera webhook handler file:

```typescript
/**
 * Send SVIX webhook event for Transfeera transaction status changes
 * Follows the same pattern as Pluggou PIX and MEDIUSPAG handlers
 */
async function sendTransfeeraWebhookEvent(
  transaction: any, 
  previousStatus: string, 
  newStatus: string
) {
  try {
    // Validate organization exists
    if (!transaction.organizationId) {
      logger.error("Transaction has no organizationId for SVIX webhook", {
        transactionId: transaction.id
      });
      return;
    }

    const organization = await db.organization.findUnique({
      where: { id: transaction.organizationId }
    });

    if (!organization) {
      logger.error("Organization not found for SVIX webhook", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId
      });
      return;
    }

    // Determine event type based on transaction type and status
    let eventType: string;
    const isConfirmation = newStatus === "APPROVED";
    const isRefund = newStatus === "REFUNDED";

    logger.info("Determining SVIX event type for TRANSFEERA", {
      transactionId: transaction.id,
      transactionType: transaction.type,
      newStatus,
      previousStatus,
      isConfirmation,
      isRefund
    });

    if (isRefund && transaction.type === "CHARGE") {
      // PIX IN Reversal
      eventType = "pix.in.reversal.confirmation";
    } else if (transaction.type === "SEND" && isConfirmation) {
      eventType = "pix.out.confirmation";
    } else if (transaction.type === "CHARGE" && isConfirmation) {
      eventType = "pix.in.confirmation";
    } else if (transaction.type === "SEND" && newStatus === "PROCESSING") {
      eventType = "pix.out.processing";
    } else if (transaction.type === "CHARGE" && newStatus === "PROCESSING") {
      eventType = "pix.in.processing";
    } else if (newStatus === "REJECTED") {
      eventType = transaction.type === "SEND" ? "pix.out.failure" : "transaction.failed";
    } else if (newStatus === "CANCELED") {
      eventType = "transaction.canceled";
    } else {
      eventType = "transaction.updated";
    }

    logger.info("Selected SVIX event type for TRANSFEERA", {
      transactionId: transaction.id,
      eventType,
      transactionType: transaction.type,
      newStatus,
      isConfirmation,
      isRefund
    });

    // Create standardized transaction data payload (same format as other providers)
    const transactionData = {
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      pixKey: transaction.pixKey,
      status: transaction.status,
      fixedFee: transaction.fixedFee || 0,
      totalFee: transaction.totalFee || 0,
      createdAt: transaction.createdAt,
      netAmount: transaction.netAmount,
      paymentAt: transaction.paymentAt,
      endToEndId: transaction.endToEndId,
      externalId: transaction.externalId,
      percentFee: transaction.percentFee || 0,
      pixKeyType: transaction.pixKeyType,
      description: transaction.description,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      referenceCode: transaction.referenceCode,
      organizationId: transaction.organizationId,
      customerDocument: transaction.customerDocument,
      // Include previous status only if different
      ...(previousStatus !== newStatus ? { previousStatus: previousStatus } : {}),
      updatedAt: transaction.updatedAt,
      processedAt: transaction.processedAt,
      // TRANSFEERA-specific fields
      provider: "TRANSFEERA",
      webhookSource: "transfeera"
    };

    // Send SVIX event using the same service as other providers
    const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");

    await createWebhookEvent({
      type: eventType,
      payload: transactionData,
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
    });

    logger.info("TRANSFEERA webhook sent successfully via SVIX", {
      transactionId: transaction.id,
      eventType,
      organizationId: transaction.organizationId,
      provider: "TRANSFEERA"
    });

  } catch (error) {
    logger.error("Error sending TRANSFEERA webhook via SVIX", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: transaction?.id || 'unknown',
      organizationId: transaction?.organizationId || 'unknown',
      errorType: error?.constructor?.name,
      provider: "TRANSFEERA"
    });
  }
}
```

## Integration Points

### 1. Add SVIX Event Call After Transaction Updates

Find the location where transactions are updated and add the SVIX event call:

```typescript
// After successful transaction update
if (previousStatus !== internalStatus) {
  try {
    logger.info(`Sending SVIX webhook for TRANSFEERA transaction status change`, {
      transactionId: transaction.id,
      previousStatus,
      newStatus: internalStatus,
      provider: "TRANSFEERA"
    });

    await sendTransfeeraWebhookEvent(updatedTransaction, previousStatus, internalStatus);
  } catch (svixError) {
    logger.error(`Error sending SVIX webhook for TRANSFEERA transaction`, {
      error: svixError instanceof Error ? svixError.message : String(svixError),
      transactionId: transaction.id,
      provider: "TRANSFEERA"
    });
  }
}
```

### 2. Add Deduplication Mechanism

Add deduplication similar to other providers:

```typescript
// Create deduplication key for TRANSFEERA webhooks
const deduplicationKey = `transfeera-${webhookId || 'unknown'}-${objectId || 'unknown'}-${status}`;

// Check for recent processing (last 5 minutes)
const recentTransaction = await db.transaction.findFirst({
  where: {
    AND: [
      {
        OR: [
          { metadata: { path: ['lastWebhookId'], equals: webhookId } },
          { metadata: { path: ['deduplicationKey'], equals: deduplicationKey } }
        ]
      },
      { updatedAt: { gte: new Date(Date.now() - 5 * 60 * 1000) } }
    ]
  }
});

if (recentTransaction) {
  logger.info("TRANSFEERA webhook already processed recently, skipping", {
    webhookId,
    deduplicationKey,
    transactionId: recentTransaction.id
  });
  return NextResponse.json({ success: true, message: "Already processed" });
}
```

### 3. Update Transaction Metadata

Ensure transaction metadata includes webhook tracking:

```typescript
// Update transaction metadata to include webhook tracking
const metadata = {
  ...(transaction.metadata as any || {}),
  lastWebhookId: webhookId,
  lastWebhookAt: new Date().toISOString(),
  deduplicationKey: deduplicationKey,
  updatedByWebhook: true,
  webhookSource: 'transfeera',
  provider: 'TRANSFEERA'
};

await db.transaction.update({
  where: { id: transaction.id },
  data: {
    status: internalStatus,
    metadata: metadata,
    ...(internalStatus === "APPROVED" ? { paymentAt: new Date() } : {}),
    ...(internalStatus === "REJECTED" ? { processedAt: new Date() } : {})
  }
});
```

## Event Type Mapping for TRANSFEERA

| Transaction Type | Status | Event Type |
|-----------------|--------|------------|
| CHARGE | APPROVED | `pix.in.confirmation` |
| CHARGE | PROCESSING | `pix.in.processing` |
| CHARGE | REJECTED | `transaction.failed` |
| CHARGE | CANCELED | `transaction.canceled` |
| CHARGE | REFUNDED | `pix.in.reversal.confirmation` |
| SEND | APPROVED | `pix.out.confirmation` |
| SEND | PROCESSING | `pix.out.processing` |
| SEND | REJECTED | `pix.out.failure` |
| SEND | CANCELED | `transaction.canceled` |
| Any | Other | `transaction.updated` |

## Implementation Steps

1. **Add the `sendTransfeeraWebhookEvent` function** to the end of the route file
2. **Identify transaction update locations** in the existing handler
3. **Add SVIX event calls** after successful transaction updates
4. **Implement deduplication** using the same pattern as other providers
5. **Update metadata** to include webhook tracking information
6. **Test with sample webhooks** to verify event delivery

## Expected Outcome

After implementation, the Transfeera webhook handler will:

- Send identical SVIX events as Pluggou PIX and MEDIUSPAG
- Use the same event types for equivalent transaction states
- Provide consistent payload structure to clients
- Include proper deduplication to prevent duplicate events
- Maintain the same error handling patterns

This ensures clients receive uniform webhook events regardless of which payment provider (MEDIUSPAG for PIX IN, Transfeera for PIX OUT) processes their transactions.

## Testing

To verify the implementation:

1. **Create a PIX OUT transaction** that will use Transfeera
2. **Trigger a webhook** from Transfeera (or simulate one)
3. **Check SVIX dashboard** for event delivery
4. **Verify client webhook** receives the standardized payload
5. **Compare with MEDIUSPAG events** to ensure consistency

The payload structure should be identical between providers, with only the `provider` and `webhookSource` fields indicating the source.
