# Webhook Handler Comparison: Pluggou PIX vs MEDIUSPAG

## Overview

This document compares the webhook handlers for Pluggou PIX and MEDIUSPAG to demonstrate the consistency achieved in SVIX event posting across both payment providers.

## Key Similarities

### 1. **SVIX Event Creation**

**Pluggou PIX:**
```typescript
const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");

await createWebhookEvent({
  type: eventType,
  payload: transactionData,
  transactionId: transaction.id,
  organizationId: transaction.organizationId,
});
```

**MEDIUSPAG:**
```typescript
const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");

await createWebhookEvent({
  type: eventType,
  payload: transactionData,
  transactionId: transaction.id,
  organizationId: transaction.organizationId,
});
```

### 2. **Event Type Mapping**

Both handlers use identical event type mapping logic:

**PIX IN (CHARGE) Events:**
- `APPROVED` → `pix.in.confirmation`
- `PROCESSING` → `pix.in.processing`
- `REJECTED` → `transaction.failed`
- `CANCELED` → `transaction.canceled`
- `REFUNDED` → `pix.in.reversal.confirmation`

**PIX OUT (SEND) Events:**
- `APPROVED` → `pix.out.confirmation`
- `PROCESSING` → `pix.out.processing`
- `REJECTED` → `pix.out.failure`
- `CANCELED` → `transaction.canceled`

### 3. **Transaction Data Payload**

Both handlers create identical transaction data structures:

```typescript
const transactionData = {
  id: transaction.id,
  type: transaction.type,
  amount: transaction.amount,
  pixKey: transaction.pixKey,
  status: transaction.status,
  fixedFee: transaction.fixedFee || 0,
  totalFee: transaction.totalFee || 0,
  createdAt: transaction.createdAt,
  netAmount: transaction.netAmount,
  paymentAt: transaction.paymentAt,
  endToEndId: transaction.endToEndId,
  externalId: transaction.externalId,
  percentFee: transaction.percentFee || 0,
  pixKeyType: transaction.pixKeyType,
  description: transaction.description,
  customerName: transaction.customerName,
  customerEmail: transaction.customerEmail,
  referenceCode: transaction.referenceCode,
  organizationId: transaction.organizationId,
  customerDocument: transaction.customerDocument,
  previousStatus: previousStatus, // Only if different
  updatedAt: transaction.updatedAt,
  processedAt: transaction.processedAt
};
```

### 4. **Deduplication Logic**

Both handlers implement similar deduplication mechanisms:

**Pluggou PIX:**
```typescript
const deduplicationKey = `${idEnvio || 'unknown'}-${endToEndId || 'unknown'}-${status}-${webhookId}`;
```

**MEDIUSPAG:**
```typescript
const deduplicationKey = `mediuspag-${webhookId || 'unknown'}-${objectId || 'unknown'}-${status}`;
```

### 5. **Error Handling**

Both handlers use identical error handling patterns:

```typescript
try {
  await sendWebhookEvent(transaction, previousStatus, newStatus);
} catch (error) {
  logger.error("Error sending webhook via SVIX", {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    transactionId: transaction?.id || 'unknown',
    organizationId: transaction?.organizationId || 'unknown',
    errorType: error?.constructor?.name,
    provider: "PROVIDER_NAME"
  });
}
```

## Key Differences

### 1. **Provider-Specific Fields**

**Pluggou PIX:**
```typescript
webhookSource: 'pluggou-pix'
```

**MEDIUSPAG:**
```typescript
provider: "MEDIUSPAG",
webhookSource: "mediuspag"
```

### 2. **Webhook Payload Structure**

**Pluggou PIX:** Handles nested payload structures and multiple identifier formats
**MEDIUSPAG:** Handles MediusPag-specific webhook format with `objectId` and `data` structure

### 3. **Deduplication Key Format**

**Pluggou PIX:** Uses `idEnvio-endToEndId-status-webhookId`
**MEDIUSPAG:** Uses `mediuspag-webhookId-objectId-status`

## Client Experience

From the client's perspective, both webhook handlers provide:

1. **Identical Event Types**: Same event names for the same transaction states
2. **Consistent Payload**: Same transaction data structure
3. **Reliable Delivery**: SVIX ensures delivery with retries
4. **Standardized Format**: Same webhook payload wrapper

## Example Client Webhook Payload

Regardless of whether the transaction was processed by Pluggou PIX or MEDIUSPAG, clients receive:

```json
{
  "id": "evt_123456789",
  "type": "pix.in.confirmation",
  "created_at": "2024-01-15T10:30:00Z",
  "data": {
    "id": "tx_abc123",
    "type": "CHARGE",
    "amount": 100.50,
    "status": "APPROVED",
    "organizationId": "org_xyz789",
    "customerEmail": "<EMAIL>",
    "pixKey": "<EMAIL>",
    "endToEndId": "E12345678202401151030123456789",
    "createdAt": "2024-01-15T10:25:00Z",
    "paymentAt": "2024-01-15T10:30:00Z",
    // ... other transaction fields
  }
}
```

## Benefits of Consistency

1. **Unified Integration**: Clients only need one webhook integration for both providers
2. **Simplified Logic**: Same event handling code works for both providers
3. **Consistent Behavior**: Predictable webhook behavior regardless of payment provider
4. **Easy Migration**: Switching between providers requires no client code changes
5. **Reduced Complexity**: Single webhook event format to maintain

## Testing Both Handlers

To verify consistency:

1. **Create PIX IN with MEDIUSPAG**: Verify `pix.in.confirmation` event
2. **Create PIX IN with Pluggou PIX**: Verify same `pix.in.confirmation` event
3. **Compare Payloads**: Ensure identical structure (except provider-specific fields)
4. **Test All Status Changes**: Verify same event types for same status transitions

## Monitoring

Both handlers log similar messages for monitoring:

**Pluggou PIX:**
```
Webhook sent successfully in standardized format
```

**MEDIUSPAG:**
```
MEDIUSPAG webhook sent successfully via SVIX
```

This consistency ensures that the dual gateway setup provides a seamless webhook experience for clients, regardless of which payment provider processes their transactions.
