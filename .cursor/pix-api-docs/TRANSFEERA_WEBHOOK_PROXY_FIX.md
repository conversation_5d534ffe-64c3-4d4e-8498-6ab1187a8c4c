# Transfeera Webhook Proxy Fix

## Issue Resolved

### **Webhook Only Sending to One URL**
**Problem**: The Transfeera webhook proxy was only sending webhooks to `app.pluggou.io` instead of forwarding to both the new system and the legacy system.

**Root Cause**: The configuration logic was requiring an explicit environment variable `ENABLE_LEGACY_WEBHOOK=true` to include the legacy URL, but this variable wasn't being set, so only the primary URL was being used.

## Code Changes Made

### Modified Files
- `supabase/functions/transfeera-webhook-proxy/index.ts`

### Key Changes

#### 1. **Fixed URL Configuration Logic**
**Before** (Lines 14-20):
```typescript
const FORWARD_URLS = [
  // Sistema legado (compatibilidade) - pode ser desabilitado em desenvolvimento
  ...(Deno.env.get("ENABLE_LEGACY_WEBHOOK") === "true" ? ["https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas"] : []),
  // Novo sistema dual gateway - produção
  Deno.env.get("DUAL_GATEWAY_WEBHOOK_URL") || "https://app.pluggou.io/api/webhooks/transfeera"
].filter(Boolean);
```

**After** (Lines 14-20):
```typescript
const FORWARD_URLS = [
  // Novo sistema dual gateway - produção (sempre incluído)
  Deno.env.get("DUAL_GATEWAY_WEBHOOK_URL") || "https://app.pluggou.io/api/webhooks/transfeera",
  // Sistema legado (compatibilidade) - incluído por padrão, pode ser desabilitado
  ...(Deno.env.get("DISABLE_LEGACY_WEBHOOK") !== "true" ? ["https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas"] : [])
].filter(Boolean);
```

#### 2. **Added Detailed Logging**
Added comprehensive logging to show which URLs are configured and being used:

```typescript
// Log das URLs configuradas para encaminhamento
console.log(`[${requestId}] URLs configuradas para encaminhamento:`, {
  totalUrls: FORWARD_URLS.length,
  urls: FORWARD_URLS,
  enableLegacyWebhook: Deno.env.get("ENABLE_LEGACY_WEBHOOK"),
  disableLegacyWebhook: Deno.env.get("DISABLE_LEGACY_WEBHOOK"),
  dualGatewayWebhookUrl: Deno.env.get("DUAL_GATEWAY_WEBHOOK_URL")
});
```

#### 3. **Added Safety Check**
Added validation to ensure at least one URL is configured:

```typescript
// Verificar se temos pelo menos uma URL configurada
if (FORWARD_URLS.length === 0) {
  console.error(`[${requestId}] Nenhuma URL configurada para encaminhamento!`);
  return new Response(
    JSON.stringify({ error: "Nenhuma URL configurada para encaminhamento" }),
    { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}
```

## Logic Changes

### Before Fix
- **Opt-in for Legacy**: Required `ENABLE_LEGACY_WEBHOOK=true` to include legacy URL
- **Default Behavior**: Only sent to primary URL (`app.pluggou.io`)
- **Result**: Single URL forwarding

### After Fix
- **Opt-out for Legacy**: Includes legacy URL by default, requires `DISABLE_LEGACY_WEBHOOK=true` to exclude
- **Default Behavior**: Sends to both URLs
- **Result**: Dual URL forwarding

## Environment Variables

### New Behavior
- **`DUAL_GATEWAY_WEBHOOK_URL`**: Override for primary URL (optional)
- **`DISABLE_LEGACY_WEBHOOK=true`**: Disable legacy URL forwarding (optional)
- **Default**: Both URLs are included

### URLs Used by Default
1. **Primary**: `https://app.pluggou.io/api/webhooks/transfeera`
2. **Legacy**: `https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas`

## Testing

### How to Test
1. **Check Logs**: Look for the new logging output showing configured URLs
2. **Send Test Webhook**: Use Transfeera's webhook testing feature
3. **Monitor Both Endpoints**: Verify both URLs receive the webhook

### Expected Log Output
```
[uuid] URLs configuradas para encaminhamento: {
  totalUrls: 2,
  urls: [
    "https://app.pluggou.io/api/webhooks/transfeera",
    "https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas"
  ],
  enableLegacyWebhook: null,
  disableLegacyWebhook: null,
  dualGatewayWebhookUrl: null
}
[uuid] Encaminhando para https://app.pluggou.io/api/webhooks/transfeera
[uuid] Encaminhando para https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas
```

## Deployment

### Supabase Function Deployment
```bash
# Deploy the updated function
supabase functions deploy transfeera-webhook-proxy

# Check function logs
supabase functions logs transfeera-webhook-proxy
```

### Environment Variables (if needed)
```bash
# To disable legacy webhook (optional)
supabase secrets set DISABLE_LEGACY_WEBHOOK=true

# To override primary URL (optional)
supabase secrets set DUAL_GATEWAY_WEBHOOK_URL=https://custom.domain.com/webhook
```

## Impact

### Before Fix
- Webhooks only sent to `app.pluggou.io`
- Legacy system not receiving updates
- Potential data inconsistency

### After Fix
- Webhooks sent to both systems
- Dual gateway setup working correctly
- Consistent data across both systems
- Better logging for debugging

## Backward Compatibility

- No breaking changes
- Default behavior now includes both URLs
- Can still be customized with environment variables
- Maintains all existing functionality
