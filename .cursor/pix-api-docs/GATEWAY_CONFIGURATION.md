# Dual Gateway Configuration Guide

## Dual Payment Gateway Setup

O sistema agora utiliza uma configuração de gateway duplo otimizada para diferentes tipos de transação, proporcionando máxima performance e especialização.

### Configuração Dual Gateway

O sistema automaticamente seleciona o gateway apropriado baseado no tipo de transação:

- **MEDIUSPAG**: Para transações PIX IN (charge/recebimento de dinheiro)
- **TRANSFEERA**: Para transações PIX OUT (withdrawal/envio de dinheiro)

```bash
# Credenciais MEDIUSPAG (PIX IN)
MEDIUSPAG_API_KEY=your_mediuspag_api_key
MEDIUSPAG_API_SECRET=your_mediuspag_api_secret
MEDIUSPAG_ENVIRONMENT=sandbox|production
MEDIUSPAG_WEBHOOK_SECRET=your_mediuspag_webhook_secret

# Credenciais TRANSFEERA (PIX OUT)
TRANSFEERA_CLIENT_ID=your_transfeera_client_id
TRANSFEERA_CLIENT_SECRET=your_transfeera_client_secret
TRANSFEERA_ENVIRONMENT=sandbox|production
TRANSFEERA_WEBHOOK_SECRET=your_transfeera_webhook_secret
```

### Gateways Suportados

- `ZENDRY` - Gateway para PIX IN (recebimentos) - **PADRÃO PARA CHARGE**
- `TRANSFEERA` - Gateway para PIX OUT (transferências) - **PADRÃO PARA WITHDRAWAL**
- `MEDIUSPAG` - Gateway para PIX IN (recebimentos)
- `PLUGGOU_PIX` - Gateway legado (ainda suportado)
- `FLOW2PAY` - Integração direta Flow2Pay
- `PIXIUM` - Gateway Pixium
- `REFLOWPAY` - Gateway ReflowPay
- `PRIMEPAG` - Gateway PrimePag
- `MOCKSIM` - Gateway de simulação para testes

### Seleção Automática de Gateway

1. **PIX IN (Charge)**: Sistema automaticamente usa ZENDRY
2. **PIX OUT (Withdrawal)**: Sistema automaticamente usa TRANSFEERA
3. **Forçar Gateway**: Pode ser especificado via parâmetro `forceType`
4. **Fallback**: Em caso de erro, usa ZENDRY como padrão

### Benefícios da Configuração Dual Gateway

- ⚡ **Velocidade**: Seleção automática de gateway elimina lógica de decisão complexa
- 🚀 **Performance**: Cada gateway é otimizado para seu tipo de operação específica
- 🎯 **Especialização**: ZENDRY otimizado para recebimentos, TRANSFEERA para envios
- 🔧 **Flexibilidade**: Suporte a múltiplos provedores de webhook simultaneamente
- 🔒 **Segurança**: Credenciais isoladas por tipo de operação
- 📈 **Escalabilidade**: Distribuição de carga entre diferentes provedores
- 🛡️ **Confiabilidade**: Redundância através de múltiplos provedores especializados

### Exemplo de Configuração Dual Gateway

```bash
# .env.local

# =============================================================================
# DUAL GATEWAY CONFIGURATION
# =============================================================================

# ZENDRY - PIX IN (Recebimentos)
ZENDRY_CLIENT_ID=your_zendry_client_id
ZENDRY_CLIENT_SECRET=your_zendry_client_secret
ZENDRY_ENVIRONMENT=production
ZENDRY_WEBHOOK_SECRET=your_zendry_webhook_secret

# TRANSFEERA - PIX OUT (Transferências)
TRANSFEERA_CLIENT_ID=your_transfeera_client_id
TRANSFEERA_CLIENT_SECRET=your_transfeera_client_secret
TRANSFEERA_ENVIRONMENT=sandbox
TRANSFEERA_WEBHOOK_SECRET=your_transfeera_webhook_secret

# =============================================================================
# WEBHOOK ENDPOINTS
# =============================================================================
# ZENDRY webhooks: /api/webhooks/zendry
# TRANSFEERA webhooks: /api/webhooks/transfeera
```

### Variáveis de Ambiente por Gateway

#### PLUGGOU_PIX
```bash
PLUGGOU_PIX_API_KEY=your_api_key
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
PLUGGOU_PIX_ENVIRONMENT=sandbox|production
PLUGGOU_PIX_WEBHOOK_SECRET=your_webhook_secret
```

#### FLOW2PAY
```bash
FLOW2PAY_CLIENT_ID=your_client_id
FLOW2PAY_CLIENT_SECRET=your_client_secret
FLOW2PAY_EVENT_TOKEN=your_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
FLOW2PAY_ENVIRONMENT=sandbox|production
FLOW2PAY_TIMEOUT=15000
FLOW2PAY_RETRIES=2
```

#### TRANSFEERA
```bash
TRANSFEERA_CLIENT_ID=your_client_id
TRANSFEERA_CLIENT_SECRET=your_client_secret
TRANSFEERA_ENVIRONMENT=sandbox|production
TRANSFEERA_WEBHOOK_SECRET=your_webhook_secret
```

#### PIXIUM
```bash
PIXIUM_API_KEY=your_api_key
PIXIUM_API_SECRET=your_api_secret
PIXIUM_ENVIRONMENT=sandbox|production
PIXIUM_WEBHOOK_SECRET=your_webhook_secret
```

#### REFLOWPAY
```bash
REFLOWPAY_API_KEY=your_api_key
REFLOWPAY_API_SECRET=your_api_secret
REFLOWPAY_ENVIRONMENT=sandbox|production
REFLOWPAY_WEBHOOK_SECRET=your_webhook_secret
```

#### PRIMEPAG
```bash
PRIMEPAG_API_KEY=your_api_key
PRIMEPAG_API_SECRET=your_api_secret
PRIMEPAG_ENVIRONMENT=sandbox|production
PRIMEPAG_WEBHOOK_SECRET=your_webhook_secret
```

#### MEDIUSPAG
```bash
MEDIUSPAG_API_KEY=your_api_key
MEDIUSPAG_API_SECRET=your_api_secret
MEDIUSPAG_ENVIRONMENT=sandbox|production
MEDIUSPAG_WEBHOOK_SECRET=your_webhook_secret
```

### Logs de Monitoramento

O sistema registra logs detalhados para monitoramento do dual gateway:

```
🔵 Using MEDIUSPAG for PIX IN (charge) transaction
🟢 Using TRANSFEERA for PIX OUT (withdrawal) transaction
🔵 Using MEDIUSPAG as default gateway
✅ Dual gateway setup - MEDIUSPAG for receiving
✅ Dual gateway setup - Transfeera for sending
```

### Configuração Avançada via Painel

Para configurações mais complexas, use o painel administrativo em `/admin/gateways` onde você pode:

- Configurar múltiplos gateways
- Definir prioridades
- Configurar credenciais específicas por organização
- Ativar/desativar gateways

### Migração para Dual Gateway

A migração para o sistema dual gateway é automática. O sistema agora:

1. **Automaticamente** usa ZENDRY para transações PIX IN (charge)
2. **Automaticamente** usa TRANSFEERA para transações PIX OUT (withdrawal)
3. **Mantém compatibilidade** com configurações existentes via `forceType`

```bash
# Configuração necessária para dual gateway
ZENDRY_CLIENT_ID=your_zendry_client_id
ZENDRY_CLIENT_SECRET=your_zendry_client_secret
TRANSFEERA_CLIENT_ID=your_transfeera_client_id
TRANSFEERA_CLIENT_SECRET=your_transfeera_secret

# DEFAULT_PAYMENT_GATEWAY não é mais necessário
# O sistema seleciona automaticamente baseado no tipo de transação
```

### Troubleshooting

1. **Gateway não encontrado**: Verifique se o nome está correto e em maiúsculas
2. **Credenciais por ENV**:
   - Verifique se as variáveis de ambiente estão definidas corretamente
   - Use o padrão `GATEWAY_TYPE_CREDENTIAL_NAME` (ex: `PLUGGOU_PIX_API_KEY`)
   - Reinicie a aplicação após alterar variáveis de ambiente
3. **Fallback para banco**: Se as credenciais ENV não estiverem disponíveis, o sistema usa o banco
4. **Logs**: Monitore os logs para verificar qual gateway e fonte de credenciais está sendo usado
5. **Cache**: Credenciais são cacheadas por 5 minutos para performance

### Verificação de Configuração Dual Gateway

Para verificar se o dual gateway está funcionando corretamente, observe os logs:

```bash
# PIX IN (Charge) - usando ZENDRY
🔵 Using ZENDRY for PIX IN (charge) transaction
✅ Dual gateway setup - ZENDRY for receiving

# PIX OUT (Withdrawal) - usando TRANSFEERA
🟢 Using TRANSFEERA for PIX OUT (withdrawal) transaction
✅ Dual gateway setup - Transfeera for sending

# Fallback para ZENDRY
🔵 Using ZENDRY as default gateway
```

### Suporte

Para dúvidas ou problemas, consulte os logs do sistema ou entre em contato com o suporte técnico.
