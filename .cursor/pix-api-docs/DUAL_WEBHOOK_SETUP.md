# Dual Webhook Setup Guide

## Overview

O sistema utiliza uma configuração de webhook duplo para processar eventos de diferentes provedores de pagamento simultaneamente:

- **MEDIUSPAG**: Processa webhooks para transações PIX IN (recebimentos)
- **TRANSFEERA**: Processa webhooks para transações PIX OUT (transferências)

## Webhook Endpoints

### MEDIUSPAG Webhooks
- **Endpoint**: `/api/webhooks/mediuspag`
- **Método**: POST
- **Propósito**: Processar eventos de transações PIX IN (charge)
- **Eventos suportados**:
  - `transaction` - Atualização de status de transação
  - `waiting_payment` - Transação criada, aguardando pagamento
  - `approved` - Pagamento aprovado
  - `rejected` - Pagamento rejeitado

### TRANSFEERA Webhooks
- **Endpoint**: `/api/webhooks/transfeera`
- **Método**: POST
- **Propósito**: Processar eventos de transações PIX OUT (withdrawal)
- **Eventos suportados**:
  - `Transfer` - Atualização de status de transferência
  - `CashIn` - Recebimento PIX (se configurado)
  - `CashInRefund` - Devolução de recebimento

## Configuração de Webhooks

### MEDIUSPAG
Configure o webhook na MediusPag apontando para:
```
https://seu-dominio.com/api/webhooks/mediuspag
```

### TRANSFEERA
Configure o webhook na Transfeera apontando para:
```
https://seu-dominio.com/api/webhooks/transfeera
```

## Processamento de Eventos

### Fluxo PIX IN (MEDIUSPAG)
1. Cliente faz pagamento PIX
2. MEDIUSPAG envia webhook para `/api/webhooks/mediuspag`
3. Sistema processa evento e atualiza status da transação
4. Sistema dispara eventos SVIX para webhooks configurados
5. Saldo da organização é atualizado (se aprovado)

### Fluxo PIX OUT (TRANSFEERA)
1. Sistema inicia transferência PIX
2. TRANSFEERA processa transferência
3. TRANSFEERA envia webhook para `/api/webhooks/transfeera`
4. Sistema processa evento e atualiza status da transação
5. Sistema dispara eventos SVIX para webhooks configurados
6. Saldo reservado é convertido para enviado

## Segurança

### Validação de Assinatura
Ambos os endpoints validam assinaturas de webhook:

- **MEDIUSPAG**: Usa `MEDIUSPAG_WEBHOOK_SECRET`
- **TRANSFEERA**: Usa `TRANSFEERA_WEBHOOK_SECRET`

### Variáveis de Ambiente
```bash
# Secrets para validação de webhook
MEDIUSPAG_WEBHOOK_SECRET=your_mediuspag_webhook_secret
TRANSFEERA_WEBHOOK_SECRET=your_transfeera_webhook_secret
```

## Monitoramento

### Logs de Webhook
O sistema registra logs detalhados para cada webhook recebido:

```bash
# MEDIUSPAG webhook
[INFO] Received MediusPag webhook: transaction approved
[INFO] Updated transaction status: PENDING -> APPROVED

# TRANSFEERA webhook  
[INFO] Received Transfeera webhook: Transfer completed
[INFO] Updated transaction status: PROCESSING -> APPROVED
```

### Métricas
- Taxa de sucesso de processamento de webhooks
- Tempo de resposta para cada provedor
- Eventos processados por tipo

## Troubleshooting

### Webhook não recebido
1. Verifique se a URL está correta no provedor
2. Confirme que o endpoint está acessível externamente
3. Verifique logs de erro no sistema

### Assinatura inválida
1. Confirme que `WEBHOOK_SECRET` está correto
2. Verifique se o provedor está usando a assinatura correta
3. Teste com webhook de exemplo

### Transação não atualizada
1. Verifique se o `externalId` está correto
2. Confirme que a transação existe no banco
3. Verifique logs de processamento

## Integração SVIX

Ambos os webhooks utilizam SVIX para entrega confiável:

- **Retry automático**: Em caso de falha na entrega
- **Assinatura segura**: Todos os eventos são assinados
- **Dashboard**: Monitoramento via painel SVIX

### Eventos SVIX Disparados

#### PIX IN (MEDIUSPAG)
- `transaction.created` - Nova transação criada
- `transaction.approved` - Pagamento aprovado
- `transaction.rejected` - Pagamento rejeitado

#### PIX OUT (TRANSFEERA)
- `transfer.created` - Nova transferência criada
- `transfer.processing` - Transferência em processamento
- `transfer.completed` - Transferência concluída
- `transfer.failed` - Transferência falhou

## Configuração Avançada

### Rate Limiting
Cada endpoint tem rate limiting configurado:
- **MEDIUSPAG**: 100 requests/minuto
- **TRANSFEERA**: 100 requests/minuto

### Timeout
- **Processamento**: 30 segundos máximo
- **Resposta**: 5 segundos máximo

### Retry Policy
- **Tentativas**: 3 tentativas automáticas
- **Backoff**: Exponencial (1s, 2s, 4s)
- **Timeout total**: 60 segundos
