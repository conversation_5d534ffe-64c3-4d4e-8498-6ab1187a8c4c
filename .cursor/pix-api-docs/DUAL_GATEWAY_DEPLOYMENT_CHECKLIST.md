# Dual Gateway Deployment Checklist

## Overview

This checklist ensures safe deployment of the dual gateway implementation (MEDIUSPAG for PIX IN, Transfeera for PIX OUT) with proper webhook proxy configuration and SVIX integration.

## Phase 1: ✅ COMPLETED - Dual Gateway Code Deployment

### ✅ Code Changes Committed
- [x] Payment provider factory updated for automatic gateway selection
- [x] Transaction router updated for dual gateway usage  
- [x] MEDIUSPAG webhook handler with SVIX integration
- [x] Webhook routing for dual gateway setup
- [x] Environment configuration updates
- [x] Comprehensive documentation created

### ✅ Feature Branch Created
- [x] Branch: `feature/dual-gateway-mediuspag-transfeera`
- [x] All changes committed with detailed commit message
- [x] Branch pushed to origin
- [x] Ready for staging deployment

## Phase 2: 🔄 IN PROGRESS - Transfeera Webhook Proxy Configuration

### Supabase Proxy Function Updates

#### ✅ Proxy Function Updated
- [x] Updated forward URLs in `supabase/functions/transfeera-webhook-proxy/index.ts`
- [x] Changed environment variable from `PLUGGOU_WEBHOOK_URL` to `DUAL_GATEWAY_WEBHOOK_URL`
- [x] Updated deployment script `supabase/deploy-transfeera-proxy.sh`

#### 🔄 Deployment Tasks
- [ ] **Deploy updated Supabase proxy function**
  ```bash
  cd supabase
  ./deploy-transfeera-proxy.sh
  ```
- [ ] **Configure environment variables:**
  - `DUAL_GATEWAY_WEBHOOK_URL`: Your production domain webhook URL
  - `TRANSFEERA_SIGNATURE_SECRET`: Transfeera webhook signature secret
  - `ENVIRONMENT`: production
  - `IGNORE_SIGNATURE_VALIDATION`: false (for production)

#### 🔄 Webhook URL Migration
- [ ] **Test proxy function** with sample payload
- [ ] **Update Transfeera webhook configuration** to point to Supabase proxy URL
- [ ] **Verify dual forwarding** (legacy + new system)
- [ ] **Monitor webhook delivery** for both endpoints

### Current Proxy Configuration
```
Legacy API: https://api.safepagoficial.com/functions/v1/handleTransfeeraWebhookBaas
New System: https://[your-domain]/api/webhooks/transfeera
```

## Phase 3: 🔄 IN PROGRESS - Transfeera SVIX Integration

### Required Implementation

#### 🔄 Add SVIX Event Function
- [ ] **Add `sendTransfeeraWebhookEvent` function** to `apps/web/app/api/webhooks/transfeera/route.ts`
- [ ] **Follow template** in `TRANSFEERA_SVIX_INTEGRATION_TEMPLATE.md`

#### 🔄 Integration Points
- [ ] **Add SVIX event calls** after transaction status updates
- [ ] **Implement deduplication** mechanism
- [ ] **Update transaction metadata** with webhook tracking
- [ ] **Add error handling** for SVIX failures

#### 🔄 Event Type Verification
- [ ] **PIX OUT APPROVED** → `pix.out.confirmation`
- [ ] **PIX OUT PROCESSING** → `pix.out.processing`  
- [ ] **PIX OUT REJECTED** → `pix.out.failure`
- [ ] **PIX IN APPROVED** → `pix.in.confirmation` (for CashIn events)
- [ ] **PIX IN PROCESSING** → `pix.in.processing`

## Phase 4: 🔄 PENDING - Testing and Verification

### Staging Environment Testing
- [ ] **Deploy feature branch** to staging environment
- [ ] **Test MEDIUSPAG PIX IN flow:**
  - [ ] Create charge transaction
  - [ ] Verify automatic MEDIUSPAG selection
  - [ ] Test webhook reception and SVIX event delivery
  - [ ] Verify balance updates and fee calculations
- [ ] **Test Transfeera PIX OUT flow:**
  - [ ] Create transfer transaction  
  - [ ] Verify automatic Transfeera selection
  - [ ] Test webhook proxy forwarding
  - [ ] Verify SVIX event delivery
  - [ ] Verify balance operations (DEBIT_RESERVED/UNRESERVE)

### SVIX Event Consistency Testing
- [ ] **Compare event payloads** between providers
- [ ] **Verify identical event types** for same transaction states
- [ ] **Test client webhook delivery** for both providers
- [ ] **Validate payload structure** consistency

### Integration Testing
- [ ] **End-to-end PIX IN flow** (MEDIUSPAG)
- [ ] **End-to-end PIX OUT flow** (Transfeera)
- [ ] **Webhook proxy dual forwarding** verification
- [ ] **Legacy system compatibility** testing

## Phase 5: 🔄 PENDING - Production Deployment

### Pre-Deployment Checklist
- [ ] **All staging tests passed**
- [ ] **Webhook proxy deployed and tested**
- [ ] **SVIX integration verified**
- [ ] **Legacy system compatibility confirmed**
- [ ] **Rollback plan prepared**

### Environment Configuration
- [ ] **Update production environment variables:**
  ```bash
  # Remove old configuration
  # DEFAULT_PAYMENT_GATEWAY=PLUGGOU_PIX
  
  # Dual gateway is now automatic based on transaction type
  # MEDIUSPAG for PIX IN, TRANSFEERA for PIX OUT
  ```

### Deployment Steps
- [ ] **Create pull request** from feature branch to main
- [ ] **Code review** and approval
- [ ] **Merge to main branch**
- [ ] **Deploy to production**
- [ ] **Monitor webhook processing** for both providers
- [ ] **Verify SVIX event delivery** to clients

### Post-Deployment Monitoring
- [ ] **Monitor transaction processing** for both providers
- [ ] **Check webhook delivery rates** and error rates
- [ ] **Verify balance operations** are working correctly
- [ ] **Monitor SVIX event delivery** success rates
- [ ] **Check client webhook reception** for consistency

## Phase 6: 🔄 PENDING - Transfeera Webhook URL Migration

### Migration Strategy
- [ ] **Coordinate with Transfeera** for webhook URL change
- [ ] **Schedule maintenance window** if needed
- [ ] **Update webhook URL** in Transfeera dashboard
- [ ] **Monitor webhook delivery** during transition
- [ ] **Verify no webhook loss** during migration

### Webhook URL Configuration
```
Current: Direct to legacy system
Target: Supabase proxy function URL
Proxy forwards to: Legacy system + New dual gateway system
```

## Rollback Plan

### If Issues Occur
1. **Immediate rollback** to main branch (previous version)
2. **Revert Transfeera webhook URL** to direct legacy system
3. **Disable Supabase proxy** if causing issues
4. **Monitor system recovery**
5. **Investigate and fix issues** in feature branch

### Rollback Triggers
- **High error rates** in webhook processing
- **Balance calculation errors**
- **SVIX event delivery failures**
- **Client webhook delivery issues**
- **Transaction processing failures**

## Success Criteria

### Deployment Successful When:
- [x] **Dual gateway selection** works automatically
- [ ] **MEDIUSPAG handles all PIX IN** transactions
- [ ] **Transfeera handles all PIX OUT** transactions  
- [ ] **Webhook proxy forwards** to both systems
- [ ] **SVIX events delivered** consistently from both providers
- [ ] **Client webhooks receive** identical payloads regardless of provider
- [ ] **Balance operations** work correctly for both transaction types
- [ ] **Legacy system continues** to receive webhooks during transition
- [ ] **Zero downtime** during deployment
- [ ] **No transaction processing errors**

## Current Status

### ✅ Completed
- Dual gateway code implementation
- MEDIUSPAG SVIX integration
- Feature branch creation and commit
- Documentation and implementation guides

### 🔄 In Progress  
- Supabase proxy function deployment
- Transfeera SVIX integration implementation
- Testing preparation

### 🔄 Pending
- Staging environment testing
- Production deployment
- Webhook URL migration
- Post-deployment monitoring

## Next Steps

1. **Deploy Supabase proxy function** with updated configuration
2. **Implement Transfeera SVIX integration** using the provided template
3. **Test complete dual gateway flow** in staging environment
4. **Deploy to production** after successful testing
5. **Migrate Transfeera webhook URL** to proxy function
6. **Monitor and verify** all systems working correctly

This checklist ensures a safe, systematic deployment of the dual gateway implementation with proper fallback mechanisms and comprehensive testing.
