# Transaction Duplication Fix

## Issues Resolved

### **Duplicate Transaction Creation**
**Problem**: The same transaction was being created twice with identical reference codes (e.g., `tx_1749800748523_713`), resulting in duplicate entries in the database.

**Root Causes**:
1. **Double Transaction Creation**: Both the MEDIUSPAG provider and the API router were creating transactions in the database
2. **Weak Deduplication Logic**: The existing transaction check was not robust enough to prevent duplicates
3. **Race Conditions**: Multiple requests with similar parameters could create duplicates

### **Duplicate Webhook Events**
**Problem**: `pix.in.confirmation` events were being sent twice, with one containing a `provider` field exposing internal implementation details.

**Root Causes**:
1. **Double Event Creation**: Both `updateTransactionStatus` and manual `sendMediusPagWebhookEvent` calls were creating events
2. **Provider Field Exposure**: Internal provider information was being included in webhook payloads

## Code Changes Made

### Modified Files
- `packages/api/src/routes/payments/transactions/router.ts`
- `apps/web/app/api/webhooks/mediuspag/route.ts`
- `apps/web/app/api/webhooks/transfeera/route.ts`
- `packages/payments/src/webhooks/events.ts`

### Key Changes

#### 1. **Fixed Double Transaction Creation**
**Before** (Lines 414-445):
```typescript
// Create or update transaction in database (fast operation)
let dbTransaction;
if (existingTransaction) {
  // Update existing transaction
  dbTransaction = await db.transaction.update({...});
} else {
  // Create new transaction (let Prisma handle id and updatedAt automatically)
  dbTransaction = await db.transaction.create({...});
}
```

**After** (Lines 414-469):
```typescript
// The MEDIUSPAG provider already created the transaction in the database
// We just need to get the transaction that was created
let dbTransaction;
if (paymentResult.transactionId) {
  // Get the transaction that was created by the provider
  dbTransaction = await db.transaction.findUnique({
    where: { id: paymentResult.transactionId }
  });
} else if (existingTransaction) {
  // Update existing transaction if provider didn't create a new one
  dbTransaction = await db.transaction.update({...});
} else {
  // Fallback: create transaction if provider didn't create one
  dbTransaction = await db.transaction.create({...});
}
```

#### 2. **Enhanced Duplicate Detection**
**Before** (Lines 138-170):
```typescript
const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
const existingTransaction = await db.transaction.findFirst({
  where: {
    OR: [
      { referenceCode },
      {
        customerEmail,
        amount,
        organizationId,
        type: "CHARGE",
        createdAt: { gte: twoMinutesAgo }
      }
    ]
  }
});
```

**After** (Lines 138-172):
```typescript
const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000); // Extended to 5 minutes
const existingTransaction = await db.transaction.findFirst({
  where: {
    OR: [
      { referenceCode },
      {
        customerEmail,
        amount,
        organizationId,
        type: "CHARGE",
        status: { in: ["PENDING", "PROCESSING", "APPROVED"] }, // Only active transactions
        createdAt: { gte: fiveMinutesAgo }
      }
    ]
  }
});
```

#### 3. **Removed Duplicate Webhook Events**
**MEDIUSPAG Webhook Handler** (Lines 633-650):
```typescript
// Before: Manual SVIX event creation
await sendMediusPagWebhookEvent(updatedTransaction, previousStatus, internalStatus);

// After: Rely on updateTransactionStatus events
logger.info("SVIX webhook events already triggered by updateTransactionStatus", {
  note: "Avoiding duplicate webhook events"
});
```

**Transfeera Webhook Handler** (Lines 1409-1427):
```typescript
// Before: Manual SVIX event creation
await sendTransfeeraWebhookEvent(updatedTransaction, previousStatus, internalStatus);

// After: Rely on updateTransactionStatus events
logger.info("SVIX webhook events already triggered by updateTransactionStatus", {
  note: "Avoiding duplicate webhook events"
});
```

#### 4. **Removed Provider Field Exposure**
**MEDIUSPAG Events** (Lines 818-824):
```typescript
// Before: Exposed provider information
provider: "MEDIUSPAG",
webhookSource: "mediuspag"

// After: Clean payload without internal details
// Note: Removed provider and webhookSource fields to avoid exposing internal provider details
```

**Transfeera Events** (Lines 2042-2048):
```typescript
// Before: Exposed provider information
provider: "TRANSFEERA",
webhookSource: "transfeera"

// After: Clean payload without internal details
// Note: Removed provider and webhookSource fields to avoid exposing internal provider details
```

## Logic Changes

### Transaction Creation Flow
1. **Before**: API creates transaction → Provider creates another transaction → Duplicates
2. **After**: Provider creates transaction → API uses existing transaction → No duplicates

### Webhook Event Flow
1. **Before**: `updateTransactionStatus` creates event → Manual webhook call creates another event → Duplicates
2. **After**: `updateTransactionStatus` creates event → Manual webhook calls removed → Single event

### Deduplication Strategy
1. **Before**: 2-minute window, any status
2. **After**: 5-minute window, only active statuses (PENDING, PROCESSING, APPROVED)

## Expected Behavior

### Transaction Creation
- **Before**: Two transactions with same reference code
- **After**: Single transaction per unique request

### Webhook Events
- **Before**: Two `pix.in.confirmation` events (one with `provider` field)
- **After**: Single `pix.in.confirmation` event without internal fields

## Testing

### Manual Testing
1. **Create PIX Payment**: Make multiple requests with same parameters
2. **Verify Single Transaction**: Check that only one transaction is created
3. **Verify Single Event**: Check that only one webhook event is sent
4. **Verify Clean Payload**: Check that webhook events don't contain `provider` or `webhookSource` fields

### Expected Results
- No duplicate transactions in database
- No duplicate webhook events
- Clean webhook payloads without internal provider information
- Improved system reliability and consistency

## Impact

### Before Fix
- Duplicate transactions causing confusion
- Duplicate webhook events causing client-side issues
- Exposed internal provider information
- Inconsistent data across systems

### After Fix
- Single transaction per unique request
- Single webhook event per status change
- Clean webhook payloads
- Consistent and reliable transaction processing

## Backward Compatibility

- No breaking changes to API responses
- Webhook event structure remains the same (just removes internal fields)
- All existing functionality preserved
- Improved reliability without changing interfaces
