# MEDIUSPAG Integration Fixes

## Issues Resolved

### 1. **IdempotencyKey Model Missing**
**Problem**: The system was showing warnings that the IdempotencyKey model was not found in the Prisma client, causing operations to run without idempotency protection.

**Solution**: 
- Replaced the complex Prisma-based idempotency system with a simpler transaction-based approach
- Now checks for existing transactions with the same parameters within the last 5 minutes
- Uses customer email, amount, and organization ID as natural idempotency keys
- Removes dependency on the `idempotency_key` Prisma model

### 2. **Missing Customer Document Error**
**Problem**: MEDIUSPAG requires a customer document (CPF/CNPJ) for all transactions, but the `customerDocument` parameter was arriving as `undefined`.

**Solution**:
- Added automatic document retrieval from organization legal info when `customerDocument` is not provided
- Tries to use organization's main document (usually CNPJ) first
- Falls back to legal representative document (usually CPF) if main document is not available
- Provides clear error messages indicating the source of the problem
- Logs document source in transaction metadata for debugging

## Code Changes Made

### Modified Files
- `packages/payments/provider/mediuspag/index.ts`

### Key Changes

1. **Auto-Document Population**:
```typescript
// If customerDocument is not provided, try to get it from organization legal info
let finalCustomerDocument = customerDocument;
let finalCustomerDocumentType = customerDocumentType;

if (!finalCustomerDocument) {
  const orgLegalInfo = await db.organization_legal_info.findUnique({
    where: { organizationId },
    select: {
      document: true,
      documentType: true,
      legalRepDocumentNumber: true
    }
  });

  if (orgLegalInfo?.document) {
    finalCustomerDocument = orgLegalInfo.document;
    finalCustomerDocumentType = orgLegalInfo.documentType || "CNPJ";
  } else if (orgLegalInfo?.legalRepDocumentNumber) {
    finalCustomerDocument = orgLegalInfo.legalRepDocumentNumber;
    finalCustomerDocumentType = "CPF";
  }
}
```

2. **Simplified Idempotency**:
```typescript
// Check for existing transactions within the last 5 minutes
const existingTransaction = await db.transaction.findFirst({
  where: {
    organizationId: params.organizationId,
    customerEmail: params.customerEmail,
    amount: params.amount,
    status: { in: ["PENDING", "APPROVED"] },
    createdAt: {
      gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
    }
  },
  orderBy: { createdAt: 'desc' }
});
```

## Testing

### Test Script
A test script has been created: `scripts/test-mediuspag-fixes.ts`

### Manual Testing
1. **Test Auto-Document Population**:
   - Create a PIX payment without providing `customerDocument`
   - Verify that the system automatically uses the organization's legal document
   - Check logs for document source information

2. **Test Idempotency**:
   - Create the same PIX payment twice within 5 minutes
   - Verify that the second call returns the existing transaction
   - Check that no duplicate transactions are created

### Expected Behavior

#### Before Fixes
```
WARN  Modelo IdempotencyKey não encontrado no cliente Prisma
ERROR Missing customer document for MediusPag transaction
ERROR Customer document (CPF/CNPJ) is required for MediusPag transactions
```

#### After Fixes
```
INFO  Customer document not provided, attempting to fetch from organization legal info
INFO  Using organization document for MediusPag transaction
INFO  MediusPag API request with document type: CNPJ
```

## Organization Requirements

For the auto-document feature to work, organizations must have:
1. Complete `organization_legal_info` record with either:
   - `document` field (company CNPJ)
   - `legalRepDocumentNumber` field (representative CPF)

## Backward Compatibility

- All existing functionality remains unchanged
- When `customerDocument` is provided, it takes precedence
- Only when `customerDocument` is missing does the system attempt auto-population
- Clear error messages guide users when neither source has a document

## Error Handling

- Graceful fallback when organization legal info is not available
- Detailed logging for debugging document source issues
- Clear error messages indicating what's missing and how to fix it
