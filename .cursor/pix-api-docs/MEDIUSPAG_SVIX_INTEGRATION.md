# MEDIUSPAG SVIX Integration Implementation

## Overview

The MEDIUSPAG webhook handler has been updated to implement the same SVIX event posting flow as the Pluggou PIX webhook handler, ensuring consistent webhook event delivery to clients across both payment providers in the dual gateway setup.

## Key Changes Made

### 1. **SVIX Event Creation Integration**

Added the same `createWebhookEvent` function import and usage pattern:

```typescript
const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");

await createWebhookEvent({
  type: eventType,
  payload: transactionData,
  transactionId: transaction.id,
  organizationId: transaction.organizationId,
});
```

### 2. **Event Type Determination**

Implemented the same event type mapping logic as Pluggou PIX:

**PIX IN (CHARGE) Events:**
- `APPROVED` → `pix.in.confirmation`
- `PROCESSING` → `pix.in.processing`
- `REJECTED` → `transaction.failed`
- `CANCELED` → `transaction.canceled`
- `REFUNDED` → `pix.in.reversal.confirmation`

**PIX OUT (SEND) Events:**
- `APPROVED` → `pix.out.confirmation`
- `PROCESSING` → `pix.out.processing`
- `REJECTED` → `pix.out.failure`
- `CANCELED` → `transaction.canceled`

**Generic Transaction Events:**
- `APPROVED` → `transaction.paid`
- `REJECTED` → `transaction.failed`
- `CANCELED` → `transaction.canceled`
- `REFUNDED` → `transaction.refunded`
- `BLOCKED` → `transaction.failed`
- Default → `transaction.updated`

### 3. **Standardized Payload Structure**

Created the same transaction data payload format as Pluggou PIX:

```typescript
const transactionData = {
  id: transaction.id,
  type: transaction.type,
  amount: transaction.amount,
  pixKey: transaction.pixKey,
  status: transaction.status,
  fixedFee: transaction.fixedFee || 0,
  totalFee: transaction.totalFee || 0,
  createdAt: transaction.createdAt,
  netAmount: transaction.netAmount,
  paymentAt: transaction.paymentAt,
  endToEndId: transaction.endToEndId,
  externalId: transaction.externalId,
  percentFee: transaction.percentFee || 0,
  pixKeyType: transaction.pixKeyType,
  description: transaction.description,
  customerName: transaction.customerName,
  customerEmail: transaction.customerEmail,
  referenceCode: transaction.referenceCode,
  organizationId: transaction.organizationId,
  customerDocument: transaction.customerDocument,
  previousStatus: previousStatus, // Only if different
  updatedAt: transaction.updatedAt,
  processedAt: transaction.processedAt,
  // MEDIUSPAG-specific fields
  provider: "MEDIUSPAG",
  webhookSource: "mediuspag"
};
```

### 4. **Deduplication Mechanism**

Added the same deduplication logic as Pluggou PIX:

- Creates deduplication key: `mediuspag-{webhookId}-{objectId}-{status}`
- Checks for recent processing (last 5 minutes)
- Stores deduplication key in transaction metadata
- Prevents duplicate webhook processing

### 5. **Error Handling**

Implemented the same error handling patterns:

```typescript
try {
  await sendMediusPagWebhookEvent(updatedTransaction, previousStatus, internalStatus);
} catch (error) {
  logger.error("Error sending MEDIUSPAG webhook via SVIX", {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    transactionId: transaction?.id || 'unknown',
    organizationId: transaction?.organizationId || 'unknown',
    errorType: error?.constructor?.name,
    provider: "MEDIUSPAG"
  });
}
```

## Webhook Event Flow

### PIX IN (Charge) Flow with MEDIUSPAG
1. **Transaction Creation**: Client creates charge via API → MEDIUSPAG selected
2. **Payment Processing**: MEDIUSPAG processes PIX charge
3. **Webhook Reception**: MEDIUSPAG sends webhook to `/api/webhooks/mediuspag`
4. **Status Update**: System updates transaction status
5. **SVIX Event**: System creates SVIX event (e.g., `pix.in.confirmation`)
6. **Client Delivery**: SVIX delivers event to client webhooks

### Event Types Sent

**For PIX IN transactions:**
- `pix.in.processing` - When payment is being processed
- `pix.in.confirmation` - When payment is approved
- `pix.in.reversal.confirmation` - When payment is refunded
- `transaction.failed` - When payment is rejected
- `transaction.canceled` - When payment is canceled

**For PIX OUT transactions:**
- `pix.out.processing` - When transfer is being processed
- `pix.out.confirmation` - When transfer is approved
- `pix.out.failure` - When transfer is rejected
- `transaction.canceled` - When transfer is canceled

## Consistency with Pluggou PIX

The MEDIUSPAG webhook handler now maintains complete consistency with the Pluggou PIX handler:

1. **Same Event Types**: Uses identical event type mapping
2. **Same Payload Format**: Sends identical transaction data structure
3. **Same SVIX Integration**: Uses the same `createWebhookEvent` service
4. **Same Error Handling**: Follows identical error logging patterns
5. **Same Deduplication**: Uses the same deduplication mechanism

## Benefits

1. **Unified Webhook Experience**: Clients receive identical webhook events regardless of payment provider
2. **Consistent Event Types**: Same event types for PIX IN/OUT across both providers
3. **Standardized Payload**: Same transaction data structure for all events
4. **Reliable Delivery**: SVIX ensures reliable webhook delivery with retries
5. **Deduplication**: Prevents duplicate event processing
6. **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Testing

To test the MEDIUSPAG SVIX integration:

1. **Create PIX IN Transaction**: Use MEDIUSPAG for charge transaction
2. **Simulate Webhook**: Send test webhook from MEDIUSPAG
3. **Verify SVIX Event**: Check SVIX dashboard for event delivery
4. **Check Client Webhook**: Verify client receives standardized payload

## Monitoring

Monitor these logs to verify correct SVIX integration:

```
MEDIUSPAG webhook sent successfully via SVIX
Selected PIX event type for MEDIUSPAG
Processing MEDIUSPAG webhook with deduplication
Sending SVIX webhook for MEDIUSPAG transaction status change
```

## Configuration

Ensure these environment variables are set:

```bash
# MEDIUSPAG Configuration
MEDIUSPAG_API_KEY=your_api_key
MEDIUSPAG_API_SECRET=your_api_secret
MEDIUSPAG_WEBHOOK_SECRET=your_webhook_secret
MEDIUSPAG_ENVIRONMENT=sandbox

# SVIX Configuration (shared)
SVIX_AUTH_TOKEN=your_svix_token
```

The MEDIUSPAG webhook handler now provides the same level of SVIX integration as the Pluggou PIX handler, ensuring consistent webhook event delivery across the dual gateway setup.
