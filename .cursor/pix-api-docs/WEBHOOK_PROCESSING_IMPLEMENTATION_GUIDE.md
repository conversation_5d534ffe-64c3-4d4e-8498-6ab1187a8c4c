# Webhook Processing Implementation Guide

## Overview

This guide documents the complete webhook processing flow used in the Pluggou PIX webhook handler (`apps/web/app/api/webhooks/pluggou-pix/route.ts`) to serve as a standardization template for all payment provider webhook handlers (MEDIUSPAG, Transfeera, etc.).

## Complete Webhook Processing Flow

### 1. **Webhook Reception and Payload Processing**

```typescript
export async function POST(request: Request) {
  try {
    const rawPayload = await request.json();
    
    // Handle nested payload structure
    let payload = rawPayload.payload || rawPayload;
    
    // Parse string payloads
    if (typeof payload === 'string') {
      payload = JSON.parse(payload);
    }
    
    logger.info("Webhook received", {
      timestamp: new Date().toISOString(),
      payload: payload
    });
```

### 2. **Deduplication Mechanism**

```typescript
    // Create webhook ID for deduplication
    const webhookId = createWebhookId(payload);
    
    // Extract identifiers
    const idEnvio = payload.idEnvio || payload.id_envio;
    const endToEndId = payload.endToEndId || payload.end_to_end_id;
    const status = payload.status || "";
    
    // Create deduplication key including status
    const deduplicationKey = `${idEnvio || 'unknown'}-${endToEndId || 'unknown'}-${status}-${webhookId}`;
    
    // Check for recent processing (last 5 minutes)
    const recentTransaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              { metadata: { path: ['lastWebhookId'], equals: webhookId } },
              { metadata: { path: ['deduplicationKey'], equals: deduplicationKey } }
            ]
          },
          { updatedAt: { gte: new Date(Date.now() - 5 * 60 * 1000) } }
        ]
      }
    });
    
    if (recentTransaction) {
      return NextResponse.json({ success: true, message: "Already processed" });
    }
```

### 3. **Event Type Determination**

```typescript
function determineEventType(payload: any): 'pixin' | 'pixout' | 'pixinreversal' | 'unknown' {
  const eventFields = [
    payload.evento,
    payload.event,
    payload.event_type,
    payload.flow2pay_event_type
  ].filter(Boolean);
  
  for (const event of eventFields) {
    const eventLower = String(event).toLowerCase();
    
    // PIX OUT detection
    if (eventLower.includes("pixout") || eventLower.includes("pix_out") || 
        eventLower.includes("pix.out")) {
      return 'pixout';
    }
    
    // PIX IN Reversal detection (check BEFORE PIX IN)
    if (eventLower.includes("pixinreversal")) {
      return 'pixinreversal';
    }
    
    // PIX IN detection
    if (eventLower.includes("pixin") || eventLower.includes("pix_in") || 
        eventLower.includes("pix.in")) {
      return 'pixin';
    }
  }
  
  return 'unknown';
}
```

### 4. **Transaction Identification Strategy**

```typescript
async function findTransactionByIdentifiers(identifiers: string[]) {
  // Priority 1: Search by externalId
  let transaction = await db.transaction.findFirst({
    where: { externalId: { in: identifiers } }
  });
  
  if (transaction) return transaction;
  
  // Priority 2: Search by txid in metadata
  transaction = await db.transaction.findFirst({
    where: {
      OR: identifiers.map(id => ({
        metadata: { path: ['txid'], equals: id }
      }))
    }
  });
  
  if (transaction) return transaction;
  
  // Priority 3: Search by endToEndId
  transaction = await db.transaction.findFirst({
    where: { endToEndId: { in: identifiers } }
  });
  
  if (transaction) return transaction;
  
  // Priority 4: Search by referenceCode
  transaction = await db.transaction.findFirst({
    where: { referenceCode: { in: identifiers } }
  });
  
  return transaction;
}
```

### 5. **Status Mapping and Validation**

```typescript
// Enhanced status mapping
let transactionStatus: TransactionStatus = "PENDING";
const statusLower = status.toLowerCase();

if (statusLower.includes("sucesso") || statusLower.includes("success") || 
    statusLower.includes("completed") || statusLower.includes("aprovado")) {
  transactionStatus = "APPROVED";
} else if (statusLower.includes("processamento") || statusLower.includes("processing") || 
           statusLower.includes("pendente")) {
  transactionStatus = "PROCESSING";
} else if (statusLower.includes("falha") || statusLower.includes("failed") || 
           statusLower.includes("rejeitado")) {
  transactionStatus = "REJECTED";
}

// CRITICAL: Validate status transitions
const invalidTransitions = [
  ['REFUNDED', 'APPROVED'],
  ['REFUNDED', 'PROCESSING'],
  ['CANCELED', 'APPROVED'],
  ['CANCELED', 'PROCESSING'],
  ['REJECTED', 'APPROVED']
];

const currentTransition = [transaction.status, transactionStatus];
const isInvalidTransition = invalidTransitions.some(([from, to]) =>
  from === currentTransition[0] && to === currentTransition[1]
);

if (isInvalidTransition) {
  logger.error("BLOCKED: Invalid status transition", {
    transactionId: transaction.id,
    from: transaction.status,
    to: transactionStatus,
    blocked: true
  });
  return; // Block invalid transition
}
```

## Transaction Processing by Type

### PIX IN (CHARGE) Transaction Processing

#### 1. **Fee Calculation and Transaction Update**

```typescript
if (transactionStatus === "APPROVED") {
  // Calculate organization taxes for PIX IN
  const fees = await calculateTransactionFees(
    transaction.organizationId,
    transaction.amount,
    'CHARGE'
  );
  
  // Calculate net amount (gross amount minus fees)
  const netAmount = transaction.amount - fees.totalFee;
  
  // Update transaction with fees
  const updatedTransactionData = {
    status: transactionStatus,
    percentFee: fees.percentFee,
    fixedFee: fees.fixedFee,
    totalFee: fees.totalFee,
    netAmount: netAmount,
    paymentAt: new Date(),
    metadata: {
      ...(transaction.metadata as any || {}),
      lastWebhookId: webhookId,
      deduplicationKey: deduplicationKey,
      updatedByWebhook: true,
      webhookSource: 'provider-name',
      fees: {
        percentFee: fees.percentFee,
        fixedFee: fees.fixedFee,
        totalFee: fees.totalFee,
        source: fees.source || 'organization',
        calculatedAt: new Date().toISOString()
      }
    }
  };
  
  const updatedTransaction = await db.transaction.update({
    where: { id: transaction.id },
    data: updatedTransactionData
  });
}
```

#### 2. **Balance Operations for PIX IN**

```typescript
if (transactionStatus === "APPROVED") {
  // Credit net amount (after fees) to organization balance
  const netAmount = updatedTransaction.netAmount || 0;
  
  if (netAmount > 0) {
    await updateOrganizationBalance(
      transaction.organizationId,
      netAmount,
      BalanceOperationType.CREDIT,
      transaction.id,
      `PIX IN approved: ${transaction.id} (gross: ${transaction.amount}, fees: ${totalFee}, net: ${netAmount})`
    );
  }
}
```

### PIX OUT (SEND) Transaction Processing

#### 1. **Fee Calculation and Transaction Update**

```typescript
if (transactionStatus === "APPROVED") {
  // Calculate organization taxes for PIX OUT
  const fees = await calculateTransactionFees(
    transaction.organizationId,
    transaction.amount,
    'TRANSFER'
  );
  
  // Calculate total amount (transfer amount + fees)
  const totalAmount = transaction.amount + fees.totalFee;
  
  // Update transaction with fees
  const updatedTransactionData = {
    status: transactionStatus,
    percentFee: fees.percentFee,
    fixedFee: fees.fixedFee,
    totalFee: fees.totalFee,
    netAmount: transaction.amount, // For transfers, netAmount is the transfer amount
    paymentAt: new Date(),
    metadata: {
      ...(transaction.metadata as any || {}),
      lastWebhookId: webhookId,
      deduplicationKey: deduplicationKey,
      updatedByWebhook: true,
      webhookSource: 'provider-name',
      totalAmount: totalAmount
    }
  };
}
```

#### 2. **Balance Operations for PIX OUT**

```typescript
if (transactionStatus === "APPROVED") {
  // Debit total amount (transfer + fees) from reserved balance
  await updateOrganizationBalance(
    transaction.organizationId,
    totalAmount,
    BalanceOperationType.DEBIT_RESERVED,
    transaction.id,
    `PIX OUT approved: ${transaction.id} (amount: ${transaction.amount}, fees: ${fees.totalFee}, total: ${totalAmount})`
  );
} else if (transactionStatus === "REJECTED") {
  // Return reserved amount to available balance
  await updateOrganizationBalance(
    transaction.organizationId,
    totalAmount,
    BalanceOperationType.UNRESERVE,
    transaction.id,
    `PIX OUT rejected: ${transaction.id} (amount: ${transaction.amount}, fees: ${fees.totalFee}, total: ${totalAmount})`
  );
}
```

## SVIX Event Integration

### 1. **Event Type Determination**

```typescript
function determineEventType(transaction: any, newStatus: string, previousStatus: string): string {
  const isConfirmation = newStatus === "APPROVED";
  const isRefund = newStatus === "REFUNDED";
  
  if (isRefund && transaction.type === "CHARGE") {
    return "pix.in.reversal.confirmation";
  } else if (transaction.type === "SEND" && isConfirmation) {
    return "pix.out.confirmation";
  } else if (transaction.type === "CHARGE" && isConfirmation) {
    return "pix.in.confirmation";
  } else if (transaction.type === "SEND" && newStatus === "PROCESSING") {
    return "pix.out.processing";
  } else if (transaction.type === "CHARGE" && newStatus === "PROCESSING") {
    return "pix.in.processing";
  } else if (newStatus === "REJECTED") {
    return transaction.type === "SEND" ? "pix.out.failure" : "transaction.failed";
  } else {
    return "transaction.updated";
  }
}
```

### 2. **SVIX Event Creation**

```typescript
async function sendWebhookEvent(transaction: any, previousStatus: string, newStatus: string) {
  try {
    // Determine event type
    const eventType = determineEventType(transaction, newStatus, previousStatus);
    
    // Create standardized transaction data payload
    const transactionData = {
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      pixKey: transaction.pixKey,
      status: transaction.status,
      fixedFee: transaction.fixedFee || 0,
      totalFee: transaction.totalFee || 0,
      createdAt: transaction.createdAt,
      netAmount: transaction.netAmount,
      paymentAt: transaction.paymentAt,
      endToEndId: transaction.endToEndId,
      externalId: transaction.externalId,
      percentFee: transaction.percentFee || 0,
      pixKeyType: transaction.pixKeyType,
      description: transaction.description,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      referenceCode: transaction.referenceCode,
      organizationId: transaction.organizationId,
      customerDocument: transaction.customerDocument,
      ...(previousStatus !== newStatus ? { previousStatus: previousStatus } : {}),
      updatedAt: transaction.updatedAt,
      processedAt: transaction.processedAt
    };
    
    // Send SVIX event
    const { createWebhookEvent } = await import("@repo/payments/src/webhooks/service");
    
    await createWebhookEvent({
      type: eventType,
      payload: transactionData,
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
    });
    
    logger.info("SVIX webhook sent successfully", {
      transactionId: transaction.id,
      eventType,
      organizationId: transaction.organizationId
    });
    
  } catch (error) {
    logger.error("Error sending SVIX webhook", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: transaction?.id || 'unknown',
      organizationId: transaction?.organizationId || 'unknown'
    });
  }
}
```

## Error Handling Patterns

### 1. **Balance Operation Error Handling**

```typescript
try {
  await updateOrganizationBalance(/* parameters */);
} catch (balanceError) {
  logger.error("Error updating organization balance", {
    error: balanceError instanceof Error ? balanceError.message : String(balanceError),
    transactionId: transaction.id,
    organizationId: transaction.organizationId
  });
  // Continue processing - don't fail webhook for balance errors
}
```

### 2. **Fee Calculation Error Handling**

```typescript
try {
  const fees = await calculateTransactionFees(/* parameters */);
  // Use calculated fees
} catch (feeError) {
  logger.error("Error calculating fees", {
    error: feeError instanceof Error ? feeError.message : String(feeError),
    transactionId: transaction.id,
    organizationId: transaction.organizationId
  });
  // Continue with zero fees or default values
}
```

### 3. **SVIX Event Error Handling**

```typescript
try {
  await sendWebhookEvent(transaction, previousStatus, newStatus);
} catch (svixError) {
  logger.error("Error sending SVIX webhook", {
    error: svixError instanceof Error ? svixError.message : String(svixError),
    transactionId: transaction.id
  });
  // Don't fail the webhook processing for SVIX errors
}
```

## Implementation Checklist

### For Each Webhook Handler:

1. **✅ Payload Processing**
   - Handle nested payloads
   - Parse string payloads
   - Extract provider-specific identifiers

2. **✅ Deduplication**
   - Create unique webhook ID
   - Build deduplication key with status
   - Check recent processing (5-minute window)

3. **✅ Transaction Identification**
   - Multi-level search strategy
   - Priority: externalId → metadata.txid → endToEndId → referenceCode

4. **✅ Status Validation**
   - Map provider status to internal status
   - Validate status transitions
   - Block invalid transitions

5. **✅ Fee Processing**
   - Calculate fees for approved transactions
   - Different logic for CHARGE vs TRANSFER
   - Store fee details in metadata

6. **✅ Balance Operations**
   - PIX IN: CREDIT net amount (after fees)
   - PIX OUT APPROVED: DEBIT_RESERVED total amount (amount + fees)
   - PIX OUT REJECTED: UNRESERVE total amount

7. **✅ SVIX Integration**
   - Determine correct event type
   - Create standardized payload
   - Send via createWebhookEvent service

8. **✅ Error Handling**
   - Continue processing on non-critical errors
   - Comprehensive error logging
   - Don't fail webhook for balance/SVIX errors

9. **✅ Metadata Updates**
   - Store webhook ID and deduplication key
   - Mark as updated by webhook
   - Store provider-specific data

This guide ensures consistent webhook processing across all payment providers in the dual gateway setup.
