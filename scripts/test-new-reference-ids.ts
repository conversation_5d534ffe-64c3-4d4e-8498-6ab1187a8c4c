#!/usr/bin/env tsx

/**
 * Teste do novo sistema de Reference IDs usando CUID
 * Demonstra a melhoria de segurança e unicidade
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import {
  generateUniqueTransactionId,
  generateSecureTransactionId,
  generateIdempotencyKey,
  isValidTransactionId,
  isValidEndToEndId
} from '../packages/utils/src/transaction-id';

async function testNewReferenceIds() {
  console.log('🔐 TESTE DO NOVO SISTEMA DE REFERENCE IDs');
  console.log('='.repeat(60));
  console.log('Comparando formato antigo vs novo formato com CUID\n');

  // Teste 1: Comparação de formatos
  console.log('📊 COMPARAÇÃO DE FORMATOS:');
  console.log('-'.repeat(40));

  console.log('🔴 FORMATO ANTIGO (timestamp + random):');
  for (let i = 0; i < 5; i++) {
    const oldId = generateUniqueTransactionId();
    console.log(`   ${i + 1}. ${oldId}`);
  }

  console.log('\n🟢 FORMATO NOVO (CUID):');
  for (let i = 0; i < 5; i++) {
    const newId = generateSecureTransactionId();
    console.log(`   ${i + 1}. ${newId}`);
  }

  // Teste 2: Validação de IDs
  console.log('\n✅ VALIDAÇÃO DE IDs:');
  console.log('-'.repeat(40));

  const testIds = [
    'tx_1758343717259_612', // Formato antigo
    'tx_1758343717259_999', // Formato antigo
    generateSecureTransactionId(), // Formato novo
    generateSecureTransactionId(), // Formato novo
    'tx_invalid_format', // Inválido
    'tx_123', // Inválido
  ];

  testIds.forEach((id, index) => {
    const isValid = isValidTransactionId(id);
    const status = isValid ? '✅' : '❌';
    console.log(`   ${status} ${id}`);
  });

  // Teste 3: Geração de chaves de idempotência
  console.log('\n🔑 CHAVES DE IDEMPOTÊNCIA:');
  console.log('-'.repeat(40));

  const testParams = {
    customerEmail: '<EMAIL>',
    amount: 1000, // R$ 10,00
    organizationId: 'fC99w8SdDGbNJM_q0b2s5',
    customerDocument: '12345678901',
    description: 'Teste de idempotência'
  };

  for (let i = 0; i < 3; i++) {
    const idempotencyKey = generateIdempotencyKey(testParams);
    console.log(`   ${i + 1}. ${idempotencyKey}`);
  }

  // Teste 4: Teste de unicidade (simulação de alta concorrência)
  console.log('\n🚀 TESTE DE UNICIDADE (1000 IDs):');
  console.log('-'.repeat(40));

  const ids = new Set();
  const startTime = Date.now();

  for (let i = 0; i < 1000; i++) {
    const id = generateSecureTransactionId();
    ids.add(id);
  }

  const duration = Date.now() - startTime;
  const uniqueCount = ids.size;
  const duplicates = 1000 - uniqueCount;

  console.log(`   Total gerados: 1000`);
  console.log(`   Únicos: ${uniqueCount}`);
  console.log(`   Duplicados: ${duplicates}`);
  console.log(`   Tempo: ${duration}ms`);
  console.log(`   Taxa de unicidade: ${(uniqueCount / 1000 * 100).toFixed(2)}%`);

  if (duplicates === 0) {
    console.log('   ✅ PERFEITO: Zero duplicações!');
  } else {
    console.log('   ❌ PROBLEMA: Duplicações detectadas!');
  }

  // Teste 5: Comparação de performance
  console.log('\n⚡ COMPARAÇÃO DE PERFORMANCE:');
  console.log('-'.repeat(40));

  // Teste formato antigo
  const oldStartTime = Date.now();
  for (let i = 0; i < 1000; i++) {
    generateUniqueTransactionId();
  }
  const oldDuration = Date.now() - oldStartTime;

  // Teste formato novo
  const newStartTime = Date.now();
  for (let i = 0; i < 1000; i++) {
    generateSecureTransactionId();
  }
  const newDuration = Date.now() - newStartTime;

  console.log(`   Formato antigo: ${oldDuration}ms (1000 IDs)`);
  console.log(`   Formato novo: ${newDuration}ms (1000 IDs)`);
  console.log(`   Diferença: ${newDuration - oldDuration}ms`);

  if (newDuration < oldDuration) {
    console.log('   ✅ Formato novo é mais rápido!');
  } else {
    console.log('   ⚠️ Formato antigo é mais rápido (mas menos seguro)');
  }

  // Teste 6: Análise de segurança
  console.log('\n🛡️ ANÁLISE DE SEGURANÇA:');
  console.log('-'.repeat(40));

  console.log('   Formato antigo:');
  console.log('     - Baseado em timestamp (previsível)');
  console.log('     - Random de 0-999 (baixa entropia)');
  console.log('     - Possível colisão em alta concorrência');
  console.log('     - Vulnerável a ataques de força bruta');

  console.log('\n   Formato novo (CUID):');
  console.log('     - Criptograficamente seguro');
  console.log('     - Alta entropia (25 caracteres)');
  console.log('     - Unicidade global garantida');
  console.log('     - Resistente a ataques de força bruta');
  console.log('     - Otimizado para performance');

  console.log('\n🎯 RECOMENDAÇÃO:');
  console.log('-'.repeat(40));
  console.log('✅ USAR FORMATO NOVO (CUID) para todas as novas transações');
  console.log('✅ Manter compatibilidade com formato antigo');
  console.log('✅ CUID oferece melhor segurança e unicidade');
  console.log('✅ Performance similar ou melhor que formato antigo');
}

if (require.main === module) {
  testNewReferenceIds()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { testNewReferenceIds };
