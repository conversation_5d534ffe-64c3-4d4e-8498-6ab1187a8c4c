#!/usr/bin/env tsx

/**
 * Debug do problema do webhook não encontrar transação
 * O externalId que chega no webhook é nosso ID interno, mas o webhook procura por externalId
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class WebhookExternalIdDebugger {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async debugWebhookIssue(): Promise<void> {
    console.log('🔍 DEBUG - PROBLEMA DO WEBHOOK EXTERNAL ID');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar a transação específica mencionada no log
    const transactionId = 'cmfrun7dg0001jo04qlfw5kmd';

    console.log(`🔍 Buscando transação: ${transactionId}`);
    console.log('');

    // 1. Buscar por ID (nosso ID interno)
    const byId = await db.transaction.findUnique({
      where: { id: transactionId }
    });

    console.log('1️⃣ BUSCA POR ID (nosso ID interno):');
    console.log(`   Critério: id = "${transactionId}"`);
    console.log(`   Resultado: ${byId ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

    if (byId) {
      console.log(`   ✅ ID: ${byId.id}`);
      console.log(`   ✅ External ID: ${byId.externalId || 'N/A'}`);
      console.log(`   ✅ Status: ${byId.status}`);
      console.log(`   ✅ Amount: R$ ${byId.amount.toFixed(2)}`);
      console.log(`   ✅ Type: ${byId.type}`);
      console.log(`   ✅ Criado em: ${byId.createdAt.toISOString()}`);
    }
    console.log('');

    // 2. Buscar por externalId (como o webhook está fazendo)
    const byExternalId = await db.transaction.findFirst({
      where: { externalId: transactionId }
    });

    console.log('2️⃣ BUSCA POR EXTERNAL ID (como webhook faz):');
    console.log(`   Critério: externalId = "${transactionId}"`);
    console.log(`   Resultado: ${byExternalId ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

    if (byExternalId) {
      console.log(`   ✅ ID: ${byExternalId.id}`);
      console.log(`   ✅ External ID: ${byExternalId.externalId || 'N/A'}`);
      console.log(`   ✅ Status: ${byExternalId.status}`);
    }
    console.log('');

    // 3. Verificar se a transação tem externalId salvo
    if (byId) {
      console.log('3️⃣ VERIFICAÇÃO DO EXTERNAL ID SALVO:');
      console.log(`   External ID no banco: ${byId.externalId || 'N/A'}`);

      if (byId.externalId) {
        console.log(`   ✅ Transação TEM externalId salvo`);
        console.log(`   ✅ External ID: ${byId.externalId}`);
      } else {
        console.log(`   ❌ Transação NÃO TEM externalId salvo`);
        console.log(`   ❌ Isso explica por que o webhook não encontra`);
      }
      console.log('');
    }

    // 4. Buscar todas as transferências recentes para comparar
    console.log('4️⃣ TODAS AS TRANSFERÊNCIAS RECENTES:');
    const recentTransfers = await db.transaction.findMany({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        createdAt: {
          gte: new Date(Date.now() - 2 * 60 * 60 * 1000) // Últimas 2 horas
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    console.log(`   Total encontradas: ${recentTransfers.length}`);
    recentTransfers.forEach((tx, index) => {
      console.log(`   ${index + 1}. ID: ${tx.id}`);
      console.log(`      External ID: ${tx.externalId || 'N/A'}`);
      console.log(`      Status: ${tx.status}`);
      console.log(`      Amount: R$ ${tx.amount.toFixed(2)}`);
      console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
      console.log('');
    });

    // 5. Análise do problema
    console.log('5️⃣ ANÁLISE DO PROBLEMA:');
    console.log('-'.repeat(40));

    if (byId && !byId.externalId) {
      console.log('❌ PROBLEMA IDENTIFICADO:');
      console.log('   - A transação existe no banco');
      console.log('   - Mas NÃO tem externalId salvo');
      console.log('   - O webhook recebe o ID interno como "externalId"');
      console.log('   - Mas procura por externalId no banco (que é null)');
      console.log('');
      console.log('🔧 SOLUÇÃO:');
      console.log('   - O webhook deve procurar por ID quando externalId for nosso ID interno');
      console.log('   - Ou garantir que o externalId seja salvo corretamente');
    } else if (byId && byId.externalId) {
      console.log('✅ TRANSAÇÃO TEM EXTERNAL ID:');
      console.log(`   - ID: ${byId.id}`);
      console.log(`   - External ID: ${byId.externalId}`);
      console.log('   - O webhook deveria encontrar por externalId');
    } else {
      console.log('❌ TRANSAÇÃO NÃO ENCONTRADA:');
      console.log('   - A transação não existe no banco');
      console.log('   - Pode ter sido deletada ou não criada');
    }
  }
}

// Executar debug se chamado diretamente
if (require.main === module) {
  const webhookDebugger = new WebhookExternalIdDebugger();
  webhookDebugger.debugWebhookIssue()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { WebhookExternalIdDebugger };
