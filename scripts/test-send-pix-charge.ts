#!/usr/bin/env tsx

/**
 * Teste específico para transações SEND (criação de cobrança PIX)
 * Usa o .env da raiz do projeto
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { SendTransactionService } from '@repo/utils/src/send-transaction-service';
import { db } from '@repo/database';
import { logger } from '@repo/logs';

interface SendTestResult {
  testName: string;
  successful: boolean;
  transactionId?: string;
  referenceCode?: string;
  amount: number;
  pixKey: string;
  duration: number;
  error?: string;
}

class SendPixChargeTest {
  private testOrganizationId = 'fC99w8SdDGbNJM_q0b2s5'; // Sua organização de homologação
  private results: SendTestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🎯 TESTE ESPECÍFICO - TRANSAÇÕES SEND (CRIAÇÃO DE COBRANÇA PIX)');
    console.log('='.repeat(80));
    console.log(`Organização: ${this.testOrganizationId}`);
    console.log(`Banco: ${process.env.DATABASE_URL ? '✅ Configurado' : '❌ Não configurado'}`);
    console.log('');

    // Teste 1: Criação de cobrança PIX simples
    await this.testSimplePixCharge();

    // Teste 2: Teste de duplicação (deve prevenir)
    await this.testDuplicatePrevention();

    // Teste 3: Teste com diferentes valores
    await this.testDifferentAmounts();

    // Teste 4: Teste de concorrência
    await this.testConcurrency();

    this.printResults();
    await this.cleanupTestData();
  }

  /**
   * Teste 1: Criação de cobrança PIX simples
   */
  private async testSimplePixCharge(): Promise<void> {
    const testName = 'Criação de Cobrança PIX Simples';
    const amount = 50.00;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      const result = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Send PIX',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste de criação de cobrança PIX',
        pixKey,
        pixKeyType: 'PHONE'
      });

      const duration = Date.now() - startTime;

      this.results.push({
        testName,
        successful: true,
        transactionId: result.id,
        referenceCode: result.referenceCode,
        amount,
        pixKey,
        duration
      });

      console.log(`✅ ${testName}: SUCESSO`);
      console.log(`   ID: ${result.id}`);
      console.log(`   Reference Code: ${result.referenceCode}`);
      console.log(`   Amount: R$ ${amount}`);
      console.log(`   PIX Key: ${pixKey}`);
      console.log(`   Duration: ${duration}ms`);

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 2: Teste de duplicação (deve prevenir)
   */
  private async testDuplicatePrevention(): Promise<void> {
    const testName = 'Prevenção de Duplicação';
    const amount = 75.50;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      // Primeira tentativa
      const result1 = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Duplicação 1',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste de duplicação - primeira tentativa',
        pixKey,
        pixKeyType: 'PHONE'
      });

      // Segunda tentativa (deve retornar a mesma transação)
      const result2 = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Duplicação 2',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste de duplicação - segunda tentativa',
        pixKey,
        pixKeyType: 'PHONE'
      });

      const duration = Date.now() - startTime;

      // Verificar se são a mesma transação (prevenção de duplicação)
      const isDuplicate = result1.id === result2.id;

      this.results.push({
        testName,
        successful: isDuplicate,
        transactionId: result1.id,
        referenceCode: result1.referenceCode,
        amount,
        pixKey,
        duration,
        error: isDuplicate ? undefined : 'Duplicação não foi prevenida'
      });

      if (isDuplicate) {
        console.log(`✅ ${testName}: SUCESSO - Duplicação prevenida`);
        console.log(`   ID: ${result1.id}`);
        console.log(`   Reference Code: ${result1.referenceCode}`);
        console.log(`   Duration: ${duration}ms`);
      } else {
        console.log(`❌ ${testName}: FALHOU - Duplicação não foi prevenida`);
        console.log(`   ID 1: ${result1.id}`);
        console.log(`   ID 2: ${result2.id}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 3: Teste com diferentes valores
   */
  private async testDifferentAmounts(): Promise<void> {
    const testName = 'Diferentes Valores';
    const amounts = [25.00, 100.50, 250.75];
    const pixKey = '11999887766';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();
    let successCount = 0;

    try {
      for (let i = 0; i < amounts.length; i++) {
        const amount = amounts[i];
        const customerEmail = `test-amount-${i}@pluggou.com`;

        try {
          const result = await SendTransactionService.createSendTransaction({
            customerEmail,
            customerName: `Teste Valor ${i + 1}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `Teste com valor R$ ${amount}`,
            pixKey,
            pixKeyType: 'PHONE'
          });

          successCount++;
          console.log(`   ✅ Valor R$ ${amount}: ID ${result.id}`);

        } catch (error) {
          console.log(`   ❌ Valor R$ ${amount}: ${error instanceof Error ? error.message : error}`);
        }
      }

      const duration = Date.now() - startTime;
      const successful = successCount === amounts.length;

      this.results.push({
        testName,
        successful,
        amount: amounts.reduce((sum, amount) => sum + amount, 0),
        pixKey,
        duration,
        error: successful ? undefined : `${successCount}/${amounts.length} sucessos`
      });

      if (successful) {
        console.log(`✅ ${testName}: SUCESSO - Todos os valores processados`);
      } else {
        console.log(`⚠️ ${testName}: PARCIAL - ${successCount}/${amounts.length} valores processados`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 4: Teste de concorrência
   */
  private async testConcurrency(): Promise<void> {
    const testName = 'Teste de Concorrência';
    const concurrentRequests = 10;
    const amount = 30.00;
    const pixKey = '11999887766';

    console.log(`🔄 ${testName} - ${concurrentRequests} requisições simultâneas...`);

    const startTime = Date.now();

    try {
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const customerEmail = `concurrent-${index}@pluggou.com`;

        try {
          const result = await SendTransactionService.createSendTransaction({
            customerEmail,
            customerName: `Teste Concorrência ${index}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `Teste de concorrência ${index}`,
            pixKey,
            pixKeyType: 'PHONE'
          });

          return { success: true, id: result.id, referenceCode: result.referenceCode };
        } catch (error) {
          return { success: false, error: error instanceof Error ? error.message : String(error) };
        }
      });

      const results = await Promise.all(promises);
      const successful = results.filter(r => r.success).length;
      const duration = Date.now() - startTime;

      this.results.push({
        testName,
        successful: successful > 0,
        amount: amount * concurrentRequests,
        pixKey,
        duration,
        error: successful === concurrentRequests ? undefined : `${successful}/${concurrentRequests} sucessos`
      });

      console.log(`✅ ${testName}: ${successful}/${concurrentRequests} sucessos`);
      console.log(`   Duration: ${duration}ms`);
      console.log(`   Avg per request: ${(duration / concurrentRequests).toFixed(0)}ms`);

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Imprimir resultados
   */
  private printResults(): void {
    console.log('\n📊 RESULTADOS DOS TESTES SEND PIX');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.successful).length;
    const totalAmount = this.results.reduce((sum, r) => sum + r.amount, 0);
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Passou: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Valor Total Testado: R$ ${totalAmount.toFixed(2)}`);
    console.log(`Tempo Total: ${totalDuration}ms`);

    console.log('\n📋 DETALHES DOS TESTES');
    console.log('-'.repeat(80));

    this.results.forEach(result => {
      const status = result.successful ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      console.log(`   Valor: R$ ${result.amount.toFixed(2)}`);
      console.log(`   PIX Key: ${result.pixKey}`);
      console.log(`   Tempo: ${result.duration}ms`);

      if (result.transactionId) {
        console.log(`   ID: ${result.transactionId}`);
        console.log(`   Reference Code: ${result.referenceCode}`);
      }

      if (result.error) {
        console.log(`   Erro: ${result.error}`);
      }
      console.log('');
    });

    if (passedTests === totalTests) {
      console.log('🎉 TODOS OS TESTES PASSARAM!');
      console.log('✅ Sistema de criação de cobrança PIX funcionando perfeitamente');
      console.log('✅ Prevenção de duplicação funcionando');
      console.log('✅ Sistema suporta concorrência');
    } else {
      console.log('⚠️ ALGUNS TESTES FALHARAM');
      console.log('🔍 Revise os erros antes de prosseguir');
    }
  }

  /**
   * Limpar dados de teste
   */
  private async cleanupTestData(): Promise<void> {
    try {
      console.log('\n🧹 Limpando dados de teste...');

      // Deletar transações de teste
      const deleted = await db.transaction.deleteMany({
        where: {
          organizationId: this.testOrganizationId,
          description: {
            contains: 'Teste'
          }
        }
      });

      console.log(`✅ ${deleted.count} transações de teste removidas`);

    } catch (error) {
      console.log(`⚠️ Erro na limpeza: ${error instanceof Error ? error.message : error}`);
    }
  }
}

// Executar testes se script for chamado diretamente
if (require.main === module) {
  const tester = new SendPixChargeTest();
  tester.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { SendPixChargeTest };
