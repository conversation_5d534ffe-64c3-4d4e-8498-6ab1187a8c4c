#!/usr/bin/env tsx

/**
 * Script para corrigir o problema do organizationId no processamento de taxas
 * Verifica e corrige o problema onde organizationId está chegando como undefined
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '../packages/database';

async function fixOrganizationIdIssue() {
  console.log('🔧 CORRIGINDO PROBLEMA DO ORGANIZATIONID');
  console.log('='.repeat(60));

  const organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  try {
    // 1. Verificar se a organização existe
    console.log('1. Verificando organização...');
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        status: true
      }
    });

    if (!organization) {
      console.log('❌ Organização não encontrada!');
      return;
    }

    console.log(`✅ Organização encontrada: ${organization.name} (${organization.status})`);

    // 2. Verificar taxas da organização
    console.log('\n2. Verificando taxas da organização...');
    const taxes = await db.organization_taxes.findUnique({
      where: { organizationId }
    });

    if (taxes) {
      console.log(`✅ Taxas encontradas:`);
      console.log(`   PIX Charge: ${taxes.pixChargePercentFee}% + R$ ${taxes.pixChargeFixedFee}`);
      console.log(`   PIX Transfer: ${taxes.pixTransferPercentFee}% + R$ ${taxes.pixTransferFixedFee}`);
    } else {
      console.log('❌ Nenhuma taxa configurada para a organização!');
      console.log('   Criando taxas padrão...');

      await db.organization_taxes.create({
        data: {
          organizationId,
          pixChargePercentFee: 1.0,
          pixChargeFixedFee: 2.0,
          pixTransferPercentFee: 0.5,
          pixTransferFixedFee: 1.0
        }
      });

      console.log('✅ Taxas padrão criadas!');
    }

    // 3. Verificar transações recentes sem taxa
    console.log('\n3. Verificando transações sem taxa...');
    const transactionsWithoutFees = await db.transaction.findMany({
      where: {
        organizationId,
        totalFee: 0
      },
      select: {
        id: true,
        amount: true,
        totalFee: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`📊 Transações sem taxa encontradas: ${transactionsWithoutFees.length}`);

    if (transactionsWithoutFees.length > 0) {
      console.log('\n4. Aplicando taxas retroativamente...');

      let processedCount = 0;
      let successCount = 0;

      for (const transaction of transactionsWithoutFees) {
        try {
          // Calcular taxas manualmente
          const percentFee = (transaction.amount * 1.0) / 100; // 1%
          const fixedFee = 2.0; // R$ 2,00
          const totalFee = percentFee + fixedFee;
          const netAmount = transaction.amount - totalFee;

          await db.transaction.update({
            where: { id: transaction.id },
            data: {
              percentFee,
              fixedFee,
              totalFee,
              netAmount,
              metadata: {
                fees: {
                  percentFee,
                  fixedFee,
                  totalFee,
                  source: 'organization',
                  calculatedAt: new Date().toISOString()
                },
                netAmount,
                feeProcessed: true,
                feeProcessedAt: new Date().toISOString()
              }
            }
          });

          console.log(`✅ Transação ${transaction.id}: R$ ${totalFee.toFixed(2)} de taxa aplicada`);
          successCount++;
        } catch (error) {
          console.log(`❌ Erro ao processar transação ${transaction.id}:`, error);
        }

        processedCount++;
      }

      console.log(`\n📊 RESUMO DA CORREÇÃO:`);
      console.log(`   Total processadas: ${processedCount}`);
      console.log(`   Sucessos: ${successCount}`);
      console.log(`   Taxa de sucesso: ${((successCount / processedCount) * 100).toFixed(1)}%`);
    }

    // 5. Verificar resultado final
    console.log('\n5. Verificação final...');
    const finalCheck = await db.transaction.count({
      where: {
        organizationId,
        totalFee: 0
      }
    });

    console.log(`✅ Transações sem taxa restantes: ${finalCheck}`);

    if (finalCheck === 0) {
      console.log('🎉 TODAS AS TAXAS FORAM APLICADAS COM SUCESSO!');
    } else {
      console.log('⚠️ Ainda há transações sem taxa. Verificar manualmente.');
    }

    // 6. Teste de criação de nova transação
    console.log('\n6. Testando criação de nova transação...');

    const testResponse = await fetch('http://localhost:3000/api/payments/transactions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9'
      },
      body: JSON.stringify({
        amount: 300, // R$ 3,00
        customerName: 'Teste Taxa Corrigida',
        customerEmail: '<EMAIL>',
        customerPhone: '11999887766',
        customerDocument: '12345678901',
        customerDocumentType: 'cpf',
        description: 'Teste de correção de taxas',
        organizationId: organizationId
      })
    });

    if (testResponse.ok) {
      const testData = await testResponse.json();
      console.log('✅ Nova transação criada com sucesso!');
      console.log(`   ID: ${testData.id}`);
      console.log(`   Valor: R$ ${(testData.paymentInfo?.amount || 0) / 100}`);

      // Verificar se as taxas foram aplicadas
      const createdTransaction = await db.transaction.findUnique({
        where: { id: testData.id },
        select: {
          totalFee: true,
          percentFee: true,
          fixedFee: true,
          netAmount: true
        }
      });

      if (createdTransaction && createdTransaction.totalFee > 0) {
        console.log('🎉 TAXAS APLICADAS AUTOMATICAMENTE!');
        console.log(`   Taxa Total: R$ ${createdTransaction.totalFee}`);
        console.log(`   Taxa %: R$ ${createdTransaction.percentFee}`);
        console.log(`   Taxa Fixa: R$ ${createdTransaction.fixedFee}`);
        console.log(`   Valor Líquido: R$ ${createdTransaction.netAmount}`);
      } else {
        console.log('❌ Taxas não foram aplicadas automaticamente');
        console.log('   Verificar logs do servidor para debug');
      }
    } else {
      console.log('❌ Erro ao criar nova transação:', await testResponse.text());
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

if (require.main === module) {
  fixOrganizationIdIssue()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { fixOrganizationIdIssue };
