#!/usr/bin/env tsx

/**
 * Script de auditoria para verificar isolamento de webhooks por organização
 *
 * Este script:
 * 1. Verifica se todos os endpoints têm canais de organização
 * 2. Testa se eventos são enviados apenas para a organização correta
 * 3. Identifica possíveis vazamentos de dados
 */

import { svixService } from "../packages/utils/src/svix/index.js";
// @ts-ignore
import { db } from "@repo/database";
// @ts-ignore
import { logger } from "@repo/logs";

const SVIX_APP_ID = process.env.SVIX_APP_ID || "app_2xJGtEh9B3sOptpW4thRObvtlh9";

interface AuditResult {
  totalEndpoints: number;
  safeEndpoints: number;
  problematicEndpoints: number;
  endpointsWithoutChannels: number;
  endpointsWithWrongChannels: number;
  crossOrganizationLeaks: number;
  recommendations: string[];
}

async function auditWebhookIsolation(): Promise<AuditResult> {
  const result: AuditResult = {
    totalEndpoints: 0,
    safeEndpoints: 0,
    problematicEndpoints: 0,
    endpointsWithoutChannels: 0,
    endpointsWithWrongChannels: 0,
    crossOrganizationLeaks: 0,
    recommendations: []
  };

  try {
    // Get all endpoints
    const svix = svixService.getClient();
    const endpointsResponse = await svix.endpoint.list(SVIX_APP_ID);
    const endpoints = endpointsResponse.data;
    result.totalEndpoints = endpoints.length;

    // Get all webhooks from database
    const webhooks = await db.webhook.findMany({
      select: {
        id: true,
        url: true,
        organizationId: true,
        svixEndpointId: true,
        isActive: true,
        events: true,
      },
    });

    // Create a map of endpoint ID to webhook info
    const endpointToWebhook = new Map<string, any>();
    webhooks.forEach((webhook: any) => {
      if (webhook.svixEndpointId) {
        endpointToWebhook.set(webhook.svixEndpointId, webhook);
      }
    });

    // Analyze each endpoint
    for (const endpoint of endpoints) {
      const webhook = endpointToWebhook.get(endpoint.id);

      if (!webhook) {
        result.problematicEndpoints++;
        result.recommendations.push(`Endpoint ${endpoint.id} has no corresponding webhook in database`);
        continue;
      }

      const expectedChannel = `org-${webhook.organizationId}`;

      if (!endpoint.channels || endpoint.channels.length === 0) {
        result.endpointsWithoutChannels++;
        result.problematicEndpoints++;
        result.recommendations.push(`Endpoint ${endpoint.id} (${endpoint.url}) has no channels - potential security risk`);
      } else if (!endpoint.channels.includes(expectedChannel)) {
        result.endpointsWithWrongChannels++;
        result.problematicEndpoints++;
        result.recommendations.push(`Endpoint ${endpoint.id} (${endpoint.url}) has wrong channels: ${endpoint.channels.join(', ')} (expected: ${expectedChannel})`);
      } else {
        result.safeEndpoints++;
      }
    }

    // Check for potential cross-organization leaks
    const organizationChannels = new Set();
    endpoints.forEach(endpoint => {
      if (endpoint.channels) {
        endpoint.channels.forEach(channel => {
          if (channel.startsWith('org-')) {
            organizationChannels.add(channel);
          }
        });
      }
    });

    // Check if any webhook URLs are shared across organizations
    const urlToOrganizations = new Map<string, string[]>();
    webhooks.forEach((webhook: any) => {
      if (!urlToOrganizations.has(webhook.url)) {
        urlToOrganizations.set(webhook.url, []);
      }
      urlToOrganizations.get(webhook.url)!.push(webhook.organizationId);
    });

    urlToOrganizations.forEach((orgIds: string[], url: string) => {
      if (orgIds.length > 1) {
        result.crossOrganizationLeaks++;
        result.recommendations.push(`URL ${url} is shared across organizations: ${orgIds.join(', ')}`);
      }
    });

    // Generate recommendations
    if (result.endpointsWithoutChannels > 0) {
      result.recommendations.push("Run fix-webhook-organization-isolation.ts to fix endpoints without channels");
    }

    if (result.endpointsWithWrongChannels > 0) {
      result.recommendations.push("Review and fix endpoints with incorrect organization channels");
    }

    if (result.crossOrganizationLeaks > 0) {
      result.recommendations.push("Investigate shared webhook URLs across organizations");
    }

    if (result.problematicEndpoints === 0) {
      result.recommendations.push("✅ All webhooks are properly isolated by organization");
    }

  } catch (error) {
    logger.error("Audit failed", { error });
    result.recommendations.push(`Audit failed: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

async function main() {
  try {
    console.log("🔍 Webhook Organization Isolation Audit\n");

    const result = await auditWebhookIsolation();

    console.log("📊 Audit Results:");
    console.log(`   Total Endpoints: ${result.totalEndpoints}`);
    console.log(`   Safe Endpoints: ${result.safeEndpoints}`);
    console.log(`   Problematic Endpoints: ${result.problematicEndpoints}`);
    console.log(`   - Without Channels: ${result.endpointsWithoutChannels}`);
    console.log(`   - With Wrong Channels: ${result.endpointsWithWrongChannels}`);
    console.log(`   Cross-Organization Leaks: ${result.crossOrganizationLeaks}\n`);

    if (result.recommendations.length > 0) {
      console.log("💡 Recommendations:");
      result.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
      console.log();
    }

    // Security assessment
    if (result.problematicEndpoints === 0 && result.crossOrganizationLeaks === 0) {
      console.log("🛡️  Security Status: ✅ SECURE - All webhooks are properly isolated");
    } else if (result.problematicEndpoints > 0) {
      console.log("🛡️  Security Status: ⚠️  AT RISK - Some webhooks may leak data between organizations");
    } else {
      console.log("🛡️  Security Status: ⚠️  REVIEW NEEDED - Potential issues detected");
    }

  } catch (error) {
    logger.error("Audit script failed", { error });
    console.error("❌ Audit failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { auditWebhookIsolation };
