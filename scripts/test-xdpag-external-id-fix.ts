#!/usr/bin/env tsx

/**
 * Teste para verificar se a correção do externalId do XDPAG está funcionando
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class XdpagExternalIdFixTester {
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private pixKey = 'bfd515ce-16fb-48ac-bd37-9dc57333c7e9';
  private baseUrl = 'http://localhost:3000';

  async testExternalIdFix(): Promise<void> {
    console.log('🔧 TESTE - CORREÇÃO DO EXTERNAL ID XDPAG');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log(`🔑 Chave PIX: ${this.pixKey}`);
    console.log(`💰 Valor: R$ 0,01 (1 centavo)`);
    console.log('');

    try {
      console.log('🔄 Criando transferência de teste...');

      const response = await fetch(`${this.baseUrl}/api/payments/transfers/pix`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify({
          amount: 0.01,
          organizationId: this.organizationId,
          pixKey: this.pixKey,
          pixKeyType: 'RANDOM',
          description: 'Teste correção externalId XDPAG'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      
      console.log('✅ Transferência criada com sucesso');
      console.log(`   ID: ${data.id}`);
      console.log(`   External ID: ${data.externalId || 'N/A'}`);
      console.log(`   Status: ${data.status}`);
      console.log('');

      // Aguardar um pouco para o processamento
      console.log('⏳ Aguardando processamento...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verificar no banco de dados
      const transaction = await db.transaction.findUnique({
        where: { id: data.id }
      });

      if (transaction) {
        console.log('📊 DADOS NO BANCO DE DADOS:');
        console.log(`   ID: ${transaction.id}`);
        console.log(`   External ID: ${transaction.externalId || 'N/A'}`);
        console.log(`   Status: ${transaction.status}`);
        console.log(`   End-to-End ID: ${transaction.endToEndId || 'N/A'}`);
        console.log(`   Criado em: ${transaction.createdAt.toISOString()}`);
        console.log(`   Atualizado em: ${transaction.updatedAt.toISOString()}`);
        
        if (transaction.metadata) {
          const metadata = transaction.metadata as any;
          console.log(`   Metadata:`);
          console.log(`     - Provider: ${metadata.provider || 'N/A'}`);
          console.log(`     - Provider Type: ${metadata.providerType || 'N/A'}`);
          if (metadata.xdpag) {
            console.log(`     - XDPAG Data: ${JSON.stringify(metadata.xdpag)}`);
          }
        }

        if (transaction.externalId) {
          console.log('');
          console.log('✅ SUCESSO: External ID foi salvo corretamente!');
          console.log('✅ A correção está funcionando!');
        } else {
          console.log('');
          console.log('❌ PROBLEMA: External ID não foi salvo');
          console.log('❌ A correção não funcionou');
        }
      } else {
        console.log('❌ Transação não encontrada no banco de dados');
      }

    } catch (error) {
      console.log(`❌ Erro no teste: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new XdpagExternalIdFixTester();
  tester.testExternalIdFix()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { XdpagExternalIdFixTester };
