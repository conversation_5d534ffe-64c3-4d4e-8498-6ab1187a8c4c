#!/usr/bin/env tsx

/**
 * Análise de Risco para Volume de Produção
 * Simula 2 milhões de reais por dia com ticket médio de R$ 99
 * Testa riscos de concorrência e duplicidade de transações/QR codes
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

interface ProductionVolumeAnalysis {
  dailyVolume: number; // R$ 2.000.000
  averageTicket: number; // R$ 99
  dailyTransactions: number; // ~20.202 transações
  peakHourMultiplier: number; // 3x (horário de pico)
  peakHourTransactions: number; // ~2.525 transações/hora
  peakMinuteTransactions: number; // ~42 transações/minuto
  concurrentRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  duplicateRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  recommendations: string[];
}

interface ConcurrencyTestResult {
  testName: string;
  transactionsPerMinute: number;
  concurrentRequests: number;
  successfulRequests: number;
  failedRequests: number;
  duplicateIds: number;
  duplicateQRCodes: number;
  duplicateReferenceCodes: number;
  uniqueIds: Set<string>;
  uniqueQRCodes: Set<string>;
  uniqueReferenceCodes: Set<string>;
  duration: number;
  averageResponseTime: number;
  errors: string[];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

class ProductionVolumeAnalyzer {
  private baseUrl = 'http://localhost:3000';
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private results: ConcurrencyTestResult[] = [];

  async runProductionAnalysis(): Promise<void> {
    console.log('🏭 ANÁLISE DE RISCO PARA VOLUME DE PRODUÇÃO');
    console.log('='.repeat(80));
    console.log('Simulando: R$ 2.000.000/dia com ticket médio de R$ 99');
    console.log('');

    // Calcular métricas de produção
    const analysis = this.calculateProductionMetrics();
    this.printProductionMetrics(analysis);

    // Testes de concorrência baseados no volume real
    await this.runConcurrencyTests(analysis);

    // Análise de risco final
    this.performRiskAnalysis(analysis);
  }

  /**
   * Calcular métricas de produção
   */
  private calculateProductionMetrics(): ProductionVolumeAnalysis {
    const dailyVolume = 2000000; // R$ 2.000.000
    const averageTicket = 99; // R$ 99
    const dailyTransactions = Math.ceil(dailyVolume / averageTicket); // ~20.202
    const peakHourMultiplier = 3; // Horário de pico é 3x a média
    const peakHourTransactions = Math.ceil(dailyTransactions / 24 * peakHourMultiplier); // ~2.525/hora
    const peakMinuteTransactions = Math.ceil(peakHourTransactions / 60); // ~42/minuto

    return {
      dailyVolume,
      averageTicket,
      dailyTransactions,
      peakHourMultiplier,
      peakHourTransactions,
      peakMinuteTransactions,
      concurrentRisk: 'LOW',
      duplicateRisk: 'LOW',
      recommendations: []
    };
  }

  /**
   * Imprimir métricas de produção
   */
  private printProductionMetrics(analysis: ProductionVolumeAnalysis): void {
    console.log('📊 MÉTRICAS DE PRODUÇÃO CALCULADAS');
    console.log('-'.repeat(60));
    console.log(`Volume Diário: R$ ${analysis.dailyVolume.toLocaleString('pt-BR')}`);
    console.log(`Ticket Médio: R$ ${analysis.averageTicket}`);
    console.log(`Transações Diárias: ${analysis.dailyTransactions.toLocaleString('pt-BR')}`);
    console.log(`Transações por Hora (Média): ${Math.ceil(analysis.dailyTransactions / 24).toLocaleString('pt-BR')}`);
    console.log(`Transações por Hora (Pico): ${analysis.peakHourTransactions.toLocaleString('pt-BR')}`);
    console.log(`Transações por Minuto (Pico): ${analysis.peakMinuteTransactions}`);
    console.log(`Transações por Segundo (Pico): ${Math.ceil(analysis.peakMinuteTransactions / 60)}`);
    console.log('');
  }

  /**
   * Executar testes de concorrência baseados no volume real
   */
  private async runConcurrencyTests(analysis: ProductionVolumeAnalysis): Promise<void> {
    console.log('🧪 TESTES DE CONCORRÊNCIA BASEADOS NO VOLUME REAL');
    console.log('-'.repeat(60));

    // Teste 1: Volume normal (1x)
    await this.testConcurrencyLevel(
      Math.ceil(analysis.peakMinuteTransactions / 2), // ~21 transações
      'Volume Normal (1x)',
      analysis
    );

    // Teste 2: Volume de pico (2x)
    await this.testConcurrencyLevel(
      analysis.peakMinuteTransactions, // ~42 transações
      'Volume de Pico (2x)',
      analysis
    );

    // Teste 3: Volume crítico (3x)
    await this.testConcurrencyLevel(
      Math.ceil(analysis.peakMinuteTransactions * 1.5), // ~63 transações
      'Volume Crítico (3x)',
      analysis
    );

    // Teste 4: Volume extremo (5x)
    await this.testConcurrencyLevel(
      Math.ceil(analysis.peakMinuteTransactions * 2.5), // ~105 transações
      'Volume Extremo (5x)',
      analysis
    );

    // Teste 5: Volume de stress (10x)
    await this.testConcurrencyLevel(
      Math.ceil(analysis.peakMinuteTransactions * 5), // ~210 transações
      'Volume de Stress (10x)',
      analysis
    );
  }

  /**
   * Teste de nível de concorrência específico
   */
  private async testConcurrencyLevel(
    concurrentRequests: number,
    testName: string,
    analysis: ProductionVolumeAnalysis
  ): Promise<void> {
    console.log(`🔄 ${testName} (${concurrentRequests} transações simultâneas)...`);

    const startTime = Date.now();
    const uniqueIds = new Set<string>();
    const uniqueQRCodes = new Set<string>();
    const uniqueReferenceCodes = new Set<string>();
    const errors: string[] = [];
    let successfulRequests = 0;
    let failedRequests = 0;

    // Criar array de promessas para execução simultânea
    const promises = Array.from({ length: concurrentRequests }, (_, index) => 
      this.createPixChargeWithAnalysis(index, concurrentRequests)
    );

    try {
      // Executar todas as requisições simultaneamente
      const results = await Promise.allSettled(promises);

      // Processar resultados
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulRequests++;
          const data = result.value.data;
          
          if (data?.id) {
            if (uniqueIds.has(data.id)) {
              console.log(`⚠️ ID DUPLICADO: ${data.id} (índice ${index})`);
            } else {
              uniqueIds.add(data.id);
            }
          }
          
          if (data?.referenceCode) {
            if (uniqueReferenceCodes.has(data.referenceCode)) {
              console.log(`⚠️ REFERENCE CODE DUPLICADO: ${data.referenceCode} (índice ${index})`);
            } else {
              uniqueReferenceCodes.add(data.referenceCode);
            }
          }

          if (data?.pix?.qrCode?.emv) {
            if (uniqueQRCodes.has(data.pix.qrCode.emv)) {
              console.log(`⚠️ QR CODE DUPLICADO: ${data.pix.qrCode.emv.substring(0, 50)}... (índice ${index})`);
            } else {
              uniqueQRCodes.add(data.pix.qrCode.emv);
            }
          }
        } else {
          failedRequests++;
          const error = result.status === 'rejected' 
            ? result.reason 
            : result.value.error || 'Unknown error';
          errors.push(`Request ${index}: ${error}`);
        }
      });

      const duration = Date.now() - startTime;
      const averageResponseTime = duration / concurrentRequests;
      const duplicateIds = concurrentRequests - uniqueIds.size;
      const duplicateQRCodes = concurrentRequests - uniqueQRCodes.size;
      const duplicateReferenceCodes = concurrentRequests - uniqueReferenceCodes.size;

      // Determinar nível de risco
      let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
      if (duplicateIds > 0 || duplicateQRCodes > 0 || duplicateReferenceCodes > 0) {
        riskLevel = 'CRITICAL';
      } else if (failedRequests > concurrentRequests * 0.1) {
        riskLevel = 'HIGH';
      } else if (failedRequests > concurrentRequests * 0.05) {
        riskLevel = 'MEDIUM';
      }

      this.results.push({
        testName,
        transactionsPerMinute: concurrentRequests,
        concurrentRequests,
        successfulRequests,
        failedRequests,
        duplicateIds,
        duplicateQRCodes,
        duplicateReferenceCodes,
        uniqueIds,
        uniqueQRCodes,
        uniqueReferenceCodes,
        duration,
        averageResponseTime,
        errors,
        riskLevel
      });

      // Log do resultado
      const successRate = (successfulRequests / concurrentRequests) * 100;
      const duplicateRate = (duplicateIds / concurrentRequests) * 100;

      console.log(`✅ ${testName}: CONCLUÍDO`);
      console.log(`   Total: ${concurrentRequests} | Sucesso: ${successfulRequests} | Falhas: ${failedRequests}`);
      console.log(`   Taxa de Sucesso: ${successRate.toFixed(1)}%`);
      console.log(`   IDs Únicos: ${uniqueIds.size} | Duplicados: ${duplicateIds}`);
      console.log(`   QR Codes Únicos: ${uniqueQRCodes.size} | Duplicados: ${duplicateQRCodes}`);
      console.log(`   Reference Codes Únicos: ${uniqueReferenceCodes.size} | Duplicados: ${duplicateReferenceCodes}`);
      console.log(`   Nível de Risco: ${riskLevel}`);
      console.log(`   Duração: ${duration}ms | Tempo Médio: ${averageResponseTime.toFixed(0)}ms`);

      if (duplicateIds > 0 || duplicateQRCodes > 0 || duplicateReferenceCodes > 0) {
        console.log(`   🚨 CRÍTICO: Duplicações detectadas!`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        transactionsPerMinute: concurrentRequests,
        concurrentRequests,
        successfulRequests: 0,
        failedRequests: concurrentRequests,
        duplicateIds: 0,
        duplicateQRCodes: 0,
        duplicateReferenceCodes: 0,
        uniqueIds: new Set(),
        uniqueQRCodes: new Set(),
        uniqueReferenceCodes: new Set(),
        duration,
        averageResponseTime: 0,
        errors: [errorMessage],
        riskLevel: 'CRITICAL'
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }

    console.log('');
  }

  /**
   * Criar uma cobrança PIX com análise detalhada
   */
  private async createPixChargeWithAnalysis(index: number, total: number): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    duration?: number;
  }> {
    const startTime = Date.now();
    const amount = 99; // Ticket médio de R$ 99
    const customerEmail = `prod-test-${index}-${Date.now()}@pluggou.com`;

    try {
      const requestBody = {
        amount,
        customerName: `Cliente Produção ${index + 1}/${total}`,
        customerEmail,
        customerPhone: `1199988${String(index).padStart(4, '0')}`,
        customerDocument: `1234567890${String(index).padStart(2, '0')}`,
        customerDocumentType: 'cpf' as const,
        description: `Transação de produção ${index + 1}/${total} - R$ ${amount}`,
        organizationId: this.organizationId,
        metadata: {
          testType: 'production_volume_test',
          testIndex: index,
          totalTests: total,
          timestamp: Date.now(),
          productionSimulation: true
        }
      };

      const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (response.ok) {
        return {
          success: true,
          data: responseData,
          duration
        };
      } else {
        return {
          success: false,
          error: `API Error ${response.status}: ${responseData.message || 'Unknown error'}`,
          duration
        };
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration
      };
    }
  }

  /**
   * Realizar análise de risco final
   */
  private performRiskAnalysis(analysis: ProductionVolumeAnalysis): void {
    console.log('\n🔒 ANÁLISE DE RISCO FINAL');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const totalRequests = this.results.reduce((sum, r) => sum + r.concurrentRequests, 0);
    const totalSuccessful = this.results.reduce((sum, r) => sum + r.successfulRequests, 0);
    const totalFailed = this.results.reduce((sum, r) => sum + r.failedRequests, 0);
    const totalDuplicateIds = this.results.reduce((sum, r) => sum + r.duplicateIds, 0);
    const totalDuplicateQRCodes = this.results.reduce((sum, r) => sum + r.duplicateQRCodes, 0);
    const totalDuplicateReferenceCodes = this.results.reduce((sum, r) => sum + r.duplicateReferenceCodes, 0);

    console.log(`📊 RESUMO GERAL DOS TESTES`);
    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Total de Requisições: ${totalRequests}`);
    console.log(`Sucessos: ${totalSuccessful} (${Math.round(totalSuccessful / totalRequests * 100)}%)`);
    console.log(`Falhas: ${totalFailed} (${Math.round(totalFailed / totalRequests * 100)}%)`);
    console.log(`IDs Duplicados: ${totalDuplicateIds}`);
    console.log(`QR Codes Duplicados: ${totalDuplicateQRCodes}`);
    console.log(`Reference Codes Duplicados: ${totalDuplicateReferenceCodes}`);

    // Análise de risco por tipo
    console.log('\n🎯 ANÁLISE DE RISCO POR TIPO');
    console.log('-'.repeat(60));

    // Risco de IDs duplicados
    if (totalDuplicateIds === 0) {
      console.log('✅ RISCO DE IDs DUPLICADOS: BAIXO');
      console.log('   Sistema garante unicidade de IDs');
    } else {
      console.log('❌ RISCO DE IDs DUPLICADOS: CRÍTICO');
      console.log(`   ${totalDuplicateIds} IDs duplicados detectados`);
    }

    // Risco de QR Codes duplicados
    if (totalDuplicateQRCodes === 0) {
      console.log('✅ RISCO DE QR CODES DUPLICADOS: BAIXO');
      console.log('   Sistema garante unicidade de QR Codes');
    } else {
      console.log('❌ RISCO DE QR CODES DUPLICADOS: CRÍTICO');
      console.log(`   ${totalDuplicateQRCodes} QR Codes duplicados detectados`);
    }

    // Risco de Reference Codes duplicados
    if (totalDuplicateReferenceCodes === 0) {
      console.log('✅ RISCO DE REFERENCE CODES DUPLICADOS: BAIXO');
      console.log('   Sistema garante unicidade de Reference Codes');
    } else {
      console.log('❌ RISCO DE REFERENCE CODES DUPLICADOS: CRÍTICO');
      console.log(`   ${totalDuplicateReferenceCodes} Reference Codes duplicados detectados`);
    }

    // Análise de capacidade
    console.log('\n📈 ANÁLISE DE CAPACIDADE');
    console.log('-'.repeat(60));

    const maxSuccessfulTest = this.results.reduce((max, r) => 
      r.successfulRequests > max.successfulRequests ? r : max
    );

    console.log(`Maior Volume Testado: ${maxSuccessfulTest.concurrentRequests} transações simultâneas`);
    console.log(`Taxa de Sucesso no Maior Volume: ${Math.round(maxSuccessfulTest.successfulRequests / maxSuccessfulTest.concurrentRequests * 100)}%`);

    if (maxSuccessfulTest.concurrentRequests >= analysis.peakMinuteTransactions) {
      console.log('✅ CAPACIDADE: Suficiente para volume de pico');
    } else {
      console.log('⚠️ CAPACIDADE: Pode ser insuficiente para volume de pico');
    }

    // Recomendações finais
    console.log('\n🎯 RECOMENDAÇÕES FINAIS');
    console.log('-'.repeat(60));

    if (totalDuplicateIds === 0 && totalDuplicateQRCodes === 0 && totalDuplicateReferenceCodes === 0) {
      console.log('🎉 SISTEMA APROVADO PARA PRODUÇÃO!');
      console.log('✅ Nenhuma duplicação detectada');
      console.log('✅ Sistema robusto para volume de 2 milhões/dia');
      console.log('✅ Unicidade garantida para todos os identificadores');
    } else {
      console.log('❌ SISTEMA NÃO APROVADO PARA PRODUÇÃO');
      console.log('❌ Duplicações detectadas - risco financeiro alto');
      console.log('❌ Necessário corrigir antes de ir para produção');
    }

    // Recomendações específicas
    console.log('\n📋 RECOMENDAÇÕES ESPECÍFICAS');
    console.log('-'.repeat(60));

    if (totalDuplicateIds > 0) {
      console.log('🔧 Implementar verificação adicional de unicidade de IDs');
    }

    if (totalDuplicateQRCodes > 0) {
      console.log('🔧 Implementar verificação adicional de unicidade de QR Codes');
    }

    if (totalDuplicateReferenceCodes > 0) {
      console.log('🔧 Implementar verificação adicional de unicidade de Reference Codes');
    }

    if (totalFailed > totalRequests * 0.05) {
      console.log('🔧 Melhorar robustez do sistema para reduzir falhas');
    }

    console.log('🔧 Implementar monitoramento em tempo real de duplicações');
    console.log('🔧 Configurar alertas para detecção de duplicações');
    console.log('🔧 Implementar rollback automático em caso de duplicação');
  }
}

// Executar análise se script for chamado diretamente
if (require.main === module) {
  const analyzer = new ProductionVolumeAnalyzer();
  analyzer.runProductionAnalysis()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ProductionVolumeAnalyzer };
