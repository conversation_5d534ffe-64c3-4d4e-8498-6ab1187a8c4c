#!/usr/bin/env tsx

/**
 * Script para corrigir taxas em transações que não tiveram taxas aplicadas
 * Aplica taxas retroativamente para todas as transações sem taxa
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '../packages/database';
import { processTransactionFees } from '../packages/payments/src/taxes/fee-service';

async function fixMissingFees() {
  console.log('🔧 CORRIGINDO TAXAS EM TRANSAÇÕES SEM TAXA');
  console.log('='.repeat(60));

  const organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  try {
    // Buscar todas as transações sem taxa
    const transactionsWithoutFees = await db.transaction.findMany({
      where: {
        organizationId,
        totalFee: 0
      },
      select: {
        id: true,
        amount: true,
        percentFee: true,
        fixedFee: true,
        totalFee: true,
        netAmount: true,
        status: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Transações sem taxa encontradas: ${transactionsWithoutFees.length}`);

    if (transactionsWithoutFees.length === 0) {
      console.log('✅ Todas as transações já têm taxas aplicadas!');
      return;
    }

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    for (const transaction of transactionsWithoutFees) {
      try {
        console.log(`\n🔄 Processando transação ${transaction.id} (R$ ${transaction.amount})...`);

        const feeResult = await processTransactionFees(transaction, 'CHARGE');

        if (feeResult.success) {
          await db.transaction.update({
            where: { id: transaction.id },
            data: {
              percentFee: feeResult.fees.percentFee,
              fixedFee: feeResult.fees.fixedFee,
              totalFee: feeResult.fees.totalFee,
              netAmount: feeResult.netAmount,
              metadata: {
                ...(transaction.metadata as Record<string, any> || {}),
                fees: {
                  percentFee: feeResult.fees.percentFee,
                  fixedFee: feeResult.fees.fixedFee,
                  totalFee: feeResult.fees.totalFee,
                  source: feeResult.fees.source || 'organization',
                  calculatedAt: new Date().toISOString()
                },
                netAmount: feeResult.netAmount,
                feeProcessed: true,
                feeProcessedAt: new Date().toISOString()
              }
            }
          });

          console.log(`✅ Taxas aplicadas: R$ ${feeResult.fees.totalFee} (${feeResult.fees.percentFee}% + R$ ${feeResult.fees.fixedFee})`);
          successCount++;
        } else {
          console.log(`❌ Falha no processamento de taxas`);
          errorCount++;
        }

        processedCount++;

        // Pausa pequena para não sobrecarregar o banco
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`❌ Erro ao processar transação ${transaction.id}:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 RESUMO DA CORREÇÃO:');
    console.log(`   Total processadas: ${processedCount}`);
    console.log(`   Sucessos: ${successCount}`);
    console.log(`   Erros: ${errorCount}`);
    console.log(`   Taxa de sucesso: ${((successCount / processedCount) * 100).toFixed(1)}%`);

    // Verificar resultado final
    const finalCheck = await db.transaction.count({
      where: {
        organizationId,
        OR: [
          { totalFee: 0 },
          { totalFee: null },
          { percentFee: 0 },
          { percentFee: null }
        ]
      }
    });

    console.log(`\n✅ Transações sem taxa restantes: ${finalCheck}`);

    if (finalCheck === 0) {
      console.log('🎉 TODAS AS TAXAS FORAM APLICADAS COM SUCESSO!');
    } else {
      console.log('⚠️ Ainda há transações sem taxa. Verificar manualmente.');
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

if (require.main === module) {
  fixMissingFees()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { fixMissingFees };
