#!/usr/bin/env tsx

/**
 * Script para debug do processamento de taxas
 * Testa o cálculo de taxas passo a passo
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '../packages/database';
import { calculateTransactionFees } from '../packages/payments/src/taxes/calculator';

async function debugFeeProcessing() {
  console.log('🔍 DEBUG DO PROCESSAMENTO DE TAXAS');
  console.log('='.repeat(60));
  
  const organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  const amount = 99.00; // Ticket médio
  
  try {
    console.log(`\n📊 Testando cálculo de taxas para:`);
    console.log(`   Organização: ${organizationId}`);
    console.log(`   Valor: R$ ${amount}`);
    console.log(`   Tipo: CHARGE`);
    
    // Testar cálculo de taxas
    console.log('\n🔄 Calculando taxas...');
    const fees = await calculateTransactionFees(organizationId, amount, 'CHARGE');
    
    console.log('\n✅ Resultado do cálculo:');
    console.log(`   Taxa %: R$ ${fees.percentFee}`);
    console.log(`   Taxa Fixa: R$ ${fees.fixedFee}`);
    console.log(`   Taxa Total: R$ ${fees.totalFee}`);
    console.log(`   Fonte: ${fees.source}`);
    
    // Verificar se o cálculo está correto
    const expectedPercentFee = (amount * 1) / 100; // 1%
    const expectedFixedFee = 2.00; // R$ 2,00
    const expectedTotalFee = expectedPercentFee + expectedFixedFee;
    
    console.log('\n🧮 Verificação manual:');
    console.log(`   Taxa % esperada: R$ ${expectedPercentFee} (1% de R$ ${amount})`);
    console.log(`   Taxa Fixa esperada: R$ ${expectedFixedFee}`);
    console.log(`   Taxa Total esperada: R$ ${expectedTotalFee}`);
    
    const isCorrect = Math.abs(fees.totalFee - expectedTotalFee) < 0.01;
    console.log(`   Cálculo correto: ${isCorrect ? '✅ SIM' : '❌ NÃO'}`);
    
    if (!isCorrect) {
      console.log('\n❌ PROBLEMA DETECTADO:');
      console.log(`   Diferença: R$ ${Math.abs(fees.totalFee - expectedTotalFee)}`);
    }
    
    // Testar com uma transação real
    console.log('\n💳 Testando com transação real...');
    
    // Buscar uma transação recente
    const recentTransaction = await db.transaction.findFirst({
      where: { 
        organizationId,
        createdAt: { gte: new Date(Date.now() - 5 * 60 * 1000) }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    if (recentTransaction) {
      console.log(`   Transação encontrada: ${recentTransaction.id}`);
      console.log(`   Valor: R$ ${recentTransaction.amount}`);
      console.log(`   Status: ${recentTransaction.status}`);
      console.log(`   Taxa Total atual: ${recentTransaction.totalFee || 'N/A'}`);
      
      // Calcular taxas para esta transação
      const transactionFees = await calculateTransactionFees(
        recentTransaction.organizationId, 
        recentTransaction.amount, 
        'CHARGE'
      );
      
      console.log(`   Taxa Total calculada: R$ ${transactionFees.totalFee}`);
      
      if (recentTransaction.totalFee !== transactionFees.totalFee) {
        console.log('   ⚠️ Taxa não foi aplicada à transação!');
      } else {
        console.log('   ✅ Taxa foi aplicada corretamente');
      }
    } else {
      console.log('   Nenhuma transação recente encontrada');
    }
    
  } catch (error) {
    console.error('❌ Erro no debug:', error);
  }
}

if (require.main === module) {
  debugFeeProcessing()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { debugFeeProcessing };
