#!/usr/bin/env tsx

/**
 * Debug script para analisar o problema dos webhooks XDPAG
 * Verifica por que apenas 1 das 3 transferências foi aprovada
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class XdpagWebhookDebugger {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async debugWebhookIssue(): Promise<void> {
    console.log('🔍 DEBUG - PROBLEMA DOS WEBHOOKS XDPAG');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar as 3 transferências que criamos
    const transactions = await db.transaction.findMany({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        createdAt: {
          gte: new Date(Date.now() - 10 * 60 * 1000) // Últimos 10 minutos
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Transações encontradas: ${transactions.length}`);
    console.log('');

    if (transactions.length > 0) {
      console.log('📋 DETALHES DAS TRANSAÇÕES:');
      transactions.forEach((tx, index) => {
        console.log(`   ${index + 1}. ID: ${tx.id}`);
        console.log(`      External ID: ${tx.externalId || 'N/A'}`);
        console.log(`      Valor: R$ ${tx.amount.toFixed(2)}`);
        console.log(`      Status: ${tx.status}`);
        console.log(`      End-to-End ID: ${tx.endToEndId || 'N/A'}`);
        console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
        console.log(`      Atualizado em: ${tx.updatedAt.toISOString()}`);
        
        // Verificar metadata
        if (tx.metadata) {
          const metadata = tx.metadata as any;
          console.log(`      Metadata:`);
          console.log(`        - Provider: ${metadata.provider || 'N/A'}`);
          console.log(`        - Provider Type: ${metadata.providerType || 'N/A'}`);
          console.log(`        - Last Webhook: ${metadata.lastWebhookEvent || 'N/A'}`);
          console.log(`        - XDPAG Data: ${metadata.xdpag ? JSON.stringify(metadata.xdpag) : 'N/A'}`);
        }
        console.log('');
      });

      // Simular a busca que o webhook XDPAG faz
      console.log('🔍 SIMULANDO BUSCA DO WEBHOOK XDPAG:');
      console.log('-'.repeat(40));

      // Simular os dados dos webhooks que recebemos
      const webhookData = [
        {
          externalId: 'cmfrtnn4k02izyo2d3gad9fct',
          transactionId: '925c2d8b-051e-4879-a02b-70f0cea7f8d2',
          endToEndId: 'E33053580202509200522273045692e2',
          amount: 0.01,
          status: 'FINISHED'
        },
        {
          externalId: 'cmfrtnn6p02j1yo2dxb4rful9',
          transactionId: '1ff2b7f7-7abf-487e-abcc-dab5d3a23f48',
          endToEndId: 'E33053580202509200522261151a8009',
          amount: 0.04,
          status: 'FINISHED'
        },
        {
          externalId: 'cmfrtnn2n02ixyo2d01wx3uzo',
          transactionId: '3ff3c8c8-8cbf-487e-abcc-dab5d3a23f49', // Simulado
          endToEndId: 'E33053580202509200522261151a8010', // Simulado
          amount: 0.01,
          status: 'FINISHED'
        }
      ];

      for (let i = 0; i < webhookData.length; i++) {
        const webhook = webhookData[i];
        console.log(`\n🔄 Simulando webhook ${i + 1}:`);
        console.log(`   External ID: ${webhook.externalId}`);
        console.log(`   Transaction ID: ${webhook.transactionId}`);
        console.log(`   End-to-End ID: ${webhook.endToEndId}`);
        console.log(`   Amount: R$ ${webhook.amount.toFixed(2)}`);

        // Buscar por externalId (primeira tentativa)
        let foundTransaction = await db.transaction.findFirst({
          where: { externalId: webhook.externalId }
        });

        console.log(`   Busca por externalId: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
        if (foundTransaction) {
          console.log(`     ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
        }

        // Buscar por transactionId (segunda tentativa)
        if (!foundTransaction && webhook.transactionId) {
          foundTransaction = await db.transaction.findFirst({
            where: {
              OR: [
                { id: webhook.transactionId },
                { externalId: webhook.transactionId }
              ]
            }
          });
          console.log(`   Busca por transactionId: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
          if (foundTransaction) {
            console.log(`     ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
          }
        }

        // Buscar por múltiplos critérios (terceira tentativa)
        if (!foundTransaction) {
          const criteria = [];
          
          if (webhook.externalId) {
            criteria.push({ externalId: webhook.externalId });
          }
          
          if (webhook.transactionId) {
            criteria.push({ id: webhook.transactionId });
          }
          
          if (webhook.endToEndId) {
            criteria.push({
              metadata: {
                path: ['endToEndId'],
                equals: webhook.endToEndId
              }
            });
          }
          
          if (webhook.amount) {
            criteria.push({ amount: webhook.amount });
          }

          if (criteria.length > 0) {
            foundTransaction = await db.transaction.findFirst({
              where: {
                OR: criteria
              }
            });
            console.log(`   Busca por múltiplos critérios: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
            if (foundTransaction) {
              console.log(`     ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
              console.log(`     Critérios usados: ${criteria.length}`);
            }
          }
        }

        console.log(`   RESULTADO FINAL: ${foundTransaction ? '✅ ENCONTRADA' : '❌ NÃO ENCONTRADA'}`);
      }

      // Verificar se há transações duplicadas ou com IDs similares
      console.log('\n🔍 VERIFICAÇÃO DE DUPLICATAS:');
      console.log('-'.repeat(40));

      const allExternalIds = transactions.map(tx => tx.externalId).filter(Boolean);
      const allEndToEndIds = transactions.map(tx => tx.endToEndId).filter(Boolean);

      const uniqueExternalIds = new Set(allExternalIds);
      const uniqueEndToEndIds = new Set(allEndToEndIds);

      console.log(`External IDs únicos: ${uniqueExternalIds.size}/${allExternalIds.length}`);
      console.log(`End-to-End IDs únicos: ${uniqueEndToEndIds.size}/${allEndToEndIds.length}`);

      if (uniqueExternalIds.size !== allExternalIds.length) {
        console.log('⚠️ DUPLICATAS DE EXTERNAL ID DETECTADAS!');
        const duplicates = allExternalIds.filter((id, index) => allExternalIds.indexOf(id) !== index);
        console.log(`Duplicatas: ${duplicates.join(', ')}`);
      }

      if (uniqueEndToEndIds.size !== allEndToEndIds.length) {
        console.log('⚠️ DUPLICATAS DE END-TO-END ID DETECTADAS!');
        const duplicates = allEndToEndIds.filter((id, index) => allEndToEndIds.indexOf(id) !== index);
        console.log(`Duplicatas: ${duplicates.join(', ')}`);
      }

    } else {
      console.log('❌ Nenhuma transação encontrada');
    }
  }
}

// Executar debug se chamado diretamente
if (require.main === module) {
  const webhookDebugger = new XdpagWebhookDebugger();
  webhookDebugger.debugWebhookIssue()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { XdpagWebhookDebugger };
