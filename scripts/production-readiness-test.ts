#!/usr/bin/env tsx

/**
 * Production readiness test for PIX transaction duplication fixes
 * Comprehensive test suite to validate production deployment safety
 */

import { db } from '@repo/database';
import { logger } from '@repo/logs';
import { createTransactionSafely } from '@repo/utils/src/transaction-service-wrapper';
import { circuitBreakers, CircuitState } from '@repo/utils/src/circuit-breaker';
import { getFeatureFlags, isFeatureEnabled } from '@repo/utils/src/feature-flags';

interface ProductionTest {
  name: string;
  critical: boolean;
  passed: boolean;
  duration: number;
  details: any;
  error?: string;
}

class ProductionReadinessValidator {
  private tests: ProductionTest[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 Running Production Readiness Tests for PIX Transaction Fixes\n');

    await this.testDatabaseConnectivity();
    await this.testFeatureFlagConfiguration();
    await this.testCircuitBreakerConfiguration();
    await this.testBackwardCompatibility();
    await this.testTransactionCreationPerformance();
    await this.testConcurrentTransactionHandling();
    await this.testErrorHandlingAndRecovery();
    await this.testMonitoringAndLogging();
    await this.testRollbackCapability();

    this.printResults();
    this.generateReport();
  }

  private async testDatabaseConnectivity(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test basic database connectivity
      await db.$queryRaw`SELECT 1 as test`;
      
      // Test transaction table access
      const count = await db.transaction.count();
      
      this.tests.push({
        name: 'Database Connectivity',
        critical: true,
        passed: true,
        duration: Date.now() - startTime,
        details: { transactionCount: count }
      });
    } catch (error) {
      this.tests.push({
        name: 'Database Connectivity',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testFeatureFlagConfiguration(): Promise<void> {
    const startTime = Date.now();
    try {
      const flags = getFeatureFlags();
      
      // Validate critical feature flags are properly configured
      const criticalFlags = [
        'enableCircuitBreaker',
        'enableDetailedLogging',
        'enablePerformanceMonitoring'
      ];
      
      const missingFlags = criticalFlags.filter(flag => 
        !isFeatureEnabled(flag as any)
      );
      
      this.tests.push({
        name: 'Feature Flag Configuration',
        critical: true,
        passed: missingFlags.length === 0,
        duration: Date.now() - startTime,
        details: { 
          flags,
          missingCriticalFlags: missingFlags,
          rolloutPercentage: flags.rolloutPercentage
        }
      });
    } catch (error) {
      this.tests.push({
        name: 'Feature Flag Configuration',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testCircuitBreakerConfiguration(): Promise<void> {
    const startTime = Date.now();
    try {
      const metrics = {
        transactionService: circuitBreakers.transactionService.getMetrics(),
        databaseOperations: circuitBreakers.databaseOperations.getMetrics(),
        externalApi: circuitBreakers.externalApi.getMetrics()
      };
      
      // All circuit breakers should be in CLOSED state initially
      const allClosed = Object.values(metrics).every(m => m.state === CircuitState.CLOSED);
      
      this.tests.push({
        name: 'Circuit Breaker Configuration',
        critical: true,
        passed: allClosed,
        duration: Date.now() - startTime,
        details: { metrics }
      });
    } catch (error) {
      this.tests.push({
        name: 'Circuit Breaker Configuration',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testBackwardCompatibility(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test that legacy transaction creation still works
      const testOrgId = 'test-backward-compat';
      
      const result = await createTransactionSafely({
        customerEmail: '<EMAIL>',
        customerName: 'Backward Compatibility Test',
        amount: 100,
        organizationId: testOrgId,
        description: 'Backward compatibility test',
        type: 'CHARGE'
      });
      
      // Should work regardless of which service is used
      const passed = result.id && result.referenceCode && result.status;
      
      this.tests.push({
        name: 'Backward Compatibility',
        critical: true,
        passed: !!passed,
        duration: Date.now() - startTime,
        details: {
          transactionId: result.id,
          usedNewService: result.usedNewService,
          fallbackReason: result.fallbackReason
        }
      });
    } catch (error) {
      this.tests.push({
        name: 'Backward Compatibility',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testTransactionCreationPerformance(): Promise<void> {
    const startTime = Date.now();
    try {
      const testCount = 10;
      const durations: number[] = [];
      
      for (let i = 0; i < testCount; i++) {
        const txStart = Date.now();
        
        await createTransactionSafely({
          customerEmail: `perf-test-${i}@test.com`,
          customerName: `Performance Test ${i}`,
          amount: 50 + i,
          organizationId: 'test-performance',
          description: `Performance test transaction ${i}`,
          type: 'CHARGE'
        });
        
        durations.push(Date.now() - txStart);
      }
      
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDuration = Math.max(...durations);
      
      // Performance should be acceptable (under 1 second average, under 2 seconds max)
      const passed = avgDuration < 1000 && maxDuration < 2000;
      
      this.tests.push({
        name: 'Transaction Creation Performance',
        critical: false,
        passed,
        duration: Date.now() - startTime,
        details: {
          testCount,
          avgDuration,
          maxDuration,
          allDurations: durations
        }
      });
    } catch (error) {
      this.tests.push({
        name: 'Transaction Creation Performance',
        critical: false,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testConcurrentTransactionHandling(): Promise<void> {
    const startTime = Date.now();
    try {
      const concurrency = 5;
      const promises = Array.from({ length: concurrency }, async (_, i) => {
        return createTransactionSafely({
          customerEmail: `concurrent-${i}@test.com`,
          customerName: `Concurrent Test ${i}`,
          amount: 25 + i,
          organizationId: 'test-concurrent',
          description: `Concurrent test ${i}`,
          type: 'CHARGE'
        });
      });
      
      const results = await Promise.all(promises);
      const allSuccessful = results.every(r => r.id && r.status);
      
      this.tests.push({
        name: 'Concurrent Transaction Handling',
        critical: true,
        passed: allSuccessful,
        duration: Date.now() - startTime,
        details: {
          concurrency,
          successCount: results.filter(r => r.id).length,
          results: results.map(r => ({
            id: r.id,
            usedNewService: r.usedNewService,
            fallbackReason: r.fallbackReason
          }))
        }
      });
    } catch (error) {
      this.tests.push({
        name: 'Concurrent Transaction Handling',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testErrorHandlingAndRecovery(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test with invalid data to ensure proper error handling
      let errorHandled = false;
      
      try {
        await createTransactionSafely({
          customerEmail: 'invalid-email',
          customerName: '',
          amount: -100, // Invalid amount
          organizationId: 'test-error',
          description: 'Error test',
          type: 'CHARGE'
        });
      } catch (error) {
        errorHandled = true;
      }
      
      this.tests.push({
        name: 'Error Handling and Recovery',
        critical: true,
        passed: errorHandled,
        duration: Date.now() - startTime,
        details: { errorHandled }
      });
    } catch (error) {
      this.tests.push({
        name: 'Error Handling and Recovery',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testMonitoringAndLogging(): Promise<void> {
    const startTime = Date.now();
    try {
      // Test that logging is working
      logger.info('Production readiness test - monitoring check');
      
      // Test circuit breaker metrics collection
      const metrics = circuitBreakers.transactionService.getMetrics();
      
      this.tests.push({
        name: 'Monitoring and Logging',
        critical: false,
        passed: true,
        duration: Date.now() - startTime,
        details: { circuitBreakerMetrics: metrics }
      });
    } catch (error) {
      this.tests.push({
        name: 'Monitoring and Logging',
        critical: false,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async testRollbackCapability(): Promise<void> {
    const startTime = Date.now();
    try {
      // Verify rollback scripts exist
      const fs = await import('fs');
      const rollbackExists = fs.existsSync('packages/database/migrations/rollback_transaction_constraints.sql');
      
      this.tests.push({
        name: 'Rollback Capability',
        critical: true,
        passed: rollbackExists,
        duration: Date.now() - startTime,
        details: { rollbackScriptExists: rollbackExists }
      });
    } catch (error) {
      this.tests.push({
        name: 'Rollback Capability',
        critical: true,
        passed: false,
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private printResults(): void {
    console.log('\n📊 PRODUCTION READINESS TEST RESULTS');
    console.log('='.repeat(60));

    const totalTests = this.tests.length;
    const passedTests = this.tests.filter(t => t.passed).length;
    const criticalTests = this.tests.filter(t => t.critical).length;
    const criticalPassed = this.tests.filter(t => t.critical && t.passed).length;

    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Critical tests: ${criticalTests}`);
    console.log(`Critical passed: ${criticalPassed} (${Math.round(criticalPassed / criticalTests * 100)}%)`);

    console.log('\n📋 DETAILED RESULTS');
    console.log('-'.repeat(60));

    this.tests.forEach(test => {
      const status = test.passed ? '✅' : (test.critical ? '🚨' : '⚠️');
      const critical = test.critical ? ' [CRITICAL]' : '';
      console.log(`${status} ${test.name}${critical} (${test.duration}ms)`);
      
      if (test.error) {
        console.log(`   Error: ${test.error}`);
      }
      
      if (Object.keys(test.details).length > 0) {
        console.log(`   Details: ${JSON.stringify(test.details, null, 2)}`);
      }
      console.log('');
    });

    if (criticalPassed === criticalTests) {
      console.log('🎉 ALL CRITICAL TESTS PASSED! Ready for production deployment.');
    } else {
      console.log('❌ CRITICAL TESTS FAILED! Do not deploy to production.');
    }
  }

  private generateReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.tests.length,
        passedTests: this.tests.filter(t => t.passed).length,
        criticalTests: this.tests.filter(t => t.critical).length,
        criticalPassed: this.tests.filter(t => t.critical && t.passed).length
      },
      tests: this.tests,
      recommendation: this.tests.filter(t => t.critical && !t.passed).length === 0 
        ? 'APPROVED_FOR_PRODUCTION' 
        : 'NOT_READY_FOR_PRODUCTION'
    };

    console.log('\n📄 Generating production readiness report...');
    
    // In a real implementation, you might save this to a file or send to monitoring
    logger.info('Production readiness test completed', report);
  }
}

// Run tests if script is called directly
if (require.main === module) {
  const validator = new ProductionReadinessValidator();
  validator.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ProductionReadinessValidator };
