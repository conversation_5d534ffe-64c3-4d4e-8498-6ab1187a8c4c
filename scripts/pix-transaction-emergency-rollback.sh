#!/bin/bash

# Emergency Rollback Script for PIX Transaction Duplication Fix
# This script provides automated rollback capabilities for production emergencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HEALTH_ENDPOINT="${HEALTH_ENDPOINT:-http://localhost:3000/health/transactions}"
DB_HOST="${DB_HOST:-localhost}"
DB_USER="${DB_USER:-postgres}"
DB_NAME="${DB_NAME:-pluggou}"
LOG_FILE="/tmp/pix-emergency-rollback-$(date +%Y%m%d-%H%M%S).log"

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if system is healthy
check_system_health() {
    log "Checking system health..."
    
    if curl -s -f "$HEALTH_ENDPOINT" > /dev/null; then
        local status=$(curl -s "$HEALTH_ENDPOINT" | jq -r '.status' 2>/dev/null || echo "UNKNOWN")
        log "System status: $status"
        return 0
    else
        error "Cannot reach health endpoint: $HEALTH_ENDPOINT"
        return 1
    fi
}

# Level 1: Feature Flag Rollback
rollback_feature_flags() {
    log "=== LEVEL 1: Feature Flag Rollback ==="
    
    # Disable new transaction service
    export ENABLE_NEW_TRANSACTION_SERVICE=false
    export ROLLOUT_PERCENTAGE=0
    export ENABLE_CIRCUIT_BREAKER=false
    
    log "Feature flags disabled in environment"
    
    # Reset circuit breakers
    if curl -s -X POST "$HEALTH_ENDPOINT/reset-circuit-breakers" > /dev/null; then
        success "Circuit breakers reset successfully"
    else
        warning "Failed to reset circuit breakers via API"
    fi
    
    # If using PM2
    if command -v pm2 &> /dev/null; then
        log "Restarting PM2 processes..."
        pm2 restart all --silent
        success "PM2 processes restarted"
    fi
    
    # If using Kubernetes
    if command -v kubectl &> /dev/null; then
        log "Restarting Kubernetes deployment..."
        kubectl rollout restart deployment/api-server 2>/dev/null || warning "Failed to restart Kubernetes deployment"
    fi
    
    # Wait for restart
    sleep 10
    
    # Verify rollback
    if check_system_health; then
        success "Level 1 rollback completed successfully"
        return 0
    else
        error "Level 1 rollback failed"
        return 1
    fi
}

# Level 2: Database Constraint Rollback
rollback_database_constraints() {
    log "=== LEVEL 2: Database Constraint Rollback ==="
    
    # Check if rollback script exists
    if [[ ! -f "packages/database/migrations/rollback_transaction_constraints.sql" ]]; then
        error "Rollback script not found!"
        return 1
    fi
    
    log "Executing database rollback script..."
    
    # Execute rollback script
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -f packages/database/migrations/rollback_transaction_constraints.sql >> "$LOG_FILE" 2>&1; then
        success "Database rollback script executed successfully"
    else
        error "Database rollback script failed"
        return 1
    fi
    
    # Verify constraints are removed
    local constraint_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM pg_indexes 
        WHERE tablename = 'transaction' 
        AND indexname IN ('unique_send_transaction', 'unique_reference_code', 'unique_external_id');
    " 2>/dev/null | tr -d ' ')
    
    if [[ "$constraint_count" == "0" ]]; then
        success "Database constraints successfully removed"
        return 0
    else
        error "Some constraints may still exist (count: $constraint_count)"
        return 1
    fi
}

# Verify rollback success
verify_rollback() {
    log "=== Verifying Rollback ==="
    
    # Check system health
    if ! check_system_health; then
        error "System health check failed after rollback"
        return 1
    fi
    
    # Test basic transaction creation
    log "Testing basic transaction creation..."
    if npm run test:basic-transaction-creation >> "$LOG_FILE" 2>&1; then
        success "Basic transaction creation test passed"
    else
        warning "Basic transaction creation test failed - check logs"
    fi
    
    success "Rollback verification completed"
}

# Main rollback function
execute_rollback() {
    local level="$1"
    local success=false
    
    log "Starting PIX transaction emergency rollback - Level $level"
    log "Log file: $LOG_FILE"
    
    case "$level" in
        "1")
            if rollback_feature_flags; then
                success=true
            fi
            ;;
        "2")
            if rollback_feature_flags && rollback_database_constraints; then
                success=true
            fi
            ;;
        *)
            error "Invalid rollback level: $level"
            exit 1
            ;;
    esac
    
    if $success; then
        verify_rollback
        success "PIX transaction emergency rollback Level $level completed successfully!"
    else
        error "PIX transaction emergency rollback Level $level failed"
    fi
}

# Show usage
show_usage() {
    echo "PIX Transaction Emergency Rollback Script"
    echo ""
    echo "Usage: $0 [LEVEL] [OPTIONS]"
    echo ""
    echo "Rollback Levels:"
    echo "  1    Feature flag rollback (recommended first step)"
    echo "  2    Feature flags + database constraint rollback"
    echo ""
    echo "Options:"
    echo "  --check-health    Check system health only"
    echo "  --verify          Verify system after rollback"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 1                    # Quick feature flag rollback"
    echo "  $0 2                    # Feature flags + database rollback"
    echo "  $0 --check-health       # Check system health"
}

# Main script logic
case "${1:-}" in
    "1"|"2")
        execute_rollback "$1"
        ;;
    "--check-health")
        check_system_health
        ;;
    "--verify")
        verify_rollback
        ;;
    "--help"|"-h"|"")
        show_usage
        ;;
    *)
        error "Invalid option: $1"
        show_usage
        exit 1
        ;;
esac
