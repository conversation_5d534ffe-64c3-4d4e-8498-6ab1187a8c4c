#!/usr/bin/env tsx

/**
 * Teste real da nova lógica de busca do webhook XDPAG
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class RealWebhookLogicTester {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async testRealWebhookLogic(): Promise<void> {
    console.log('🔧 TESTE REAL - NOVA LÓGICA DO WEBHOOK XDPAG');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar a transação mais recente que tem externalId
    const transaction = await db.transaction.findFirst({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        externalId: { not: null }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!transaction) {
      console.log('❌ Nenhuma transação com externalId encontrada');
      return;
    }

    console.log('📊 TRANSAÇÃO DE TESTE:');
    console.log(`   ID: ${transaction.id}`);
    console.log(`   External ID: ${transaction.externalId}`);
    console.log(`   Status: ${transaction.status}`);
    console.log(`   Valor: R$ ${transaction.amount.toFixed(2)}`);
    console.log('');

    // Simular o webhook XDPAG com os dados reais
    const webhookData = {
      externalId: transaction.id, // ID interno da nossa transação
      transactionId: transaction.externalId, // ID do XDPAG
      amount: transaction.amount,
      status: 'FINISHED'
    };

    console.log('🔄 SIMULANDO WEBHOOK XDPAG:');
    console.log(`   External ID (nosso): ${webhookData.externalId}`);
    console.log(`   Transaction ID (XDPAG): ${webhookData.transactionId}`);
    console.log(`   Amount: R$ ${webhookData.amount.toFixed(2)}`);
    console.log('');

    // Testar a nova lógica de busca
    console.log('🔍 TESTANDO NOVA LÓGICA DE BUSCA:');
    console.log('-'.repeat(40));

    // 1. Busca por externalId (nosso ID interno) - PRIORIDADE MÁXIMA
    console.log('1️⃣ BUSCA POR EXTERNAL ID (PRIORIDADE MÁXIMA):');
    let foundTransaction = await db.transaction.findFirst({
      where: { externalId: webhookData.externalId }
    });

    console.log(`   Critério: externalId = "${webhookData.externalId}"`);
    console.log(`   Resultado: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

    if (foundTransaction) {
      console.log(`   ✅ ID: ${foundTransaction.id}`);
      console.log(`   ✅ External ID: ${foundTransaction.externalId}`);
      console.log(`   ✅ Status: ${foundTransaction.status}`);
      console.log(`   ✅ Amount: R$ ${foundTransaction.amount.toFixed(2)}`);
      console.log(`   ✅ É a transação correta: ${foundTransaction.id === transaction.id ? 'SIM' : 'NÃO'}`);
    }
    console.log('');

    // 2. Se não encontrou, busca por transactionId (ID do XDPAG)
    if (!foundTransaction) {
      console.log('2️⃣ BUSCA POR TRANSACTION ID (ID DO XDPAG):');
      foundTransaction = await db.transaction.findFirst({
        where: {
          OR: [
            { id: webhookData.transactionId },
            { externalId: webhookData.transactionId }
          ]
        }
      });

      console.log(`   Critério: id = "${webhookData.transactionId}" OR externalId = "${webhookData.transactionId}"`);
      console.log(`   Resultado: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

      if (foundTransaction) {
        console.log(`   ✅ ID: ${foundTransaction.id}`);
        console.log(`   ✅ External ID: ${foundTransaction.externalId}`);
        console.log(`   ✅ Status: ${foundTransaction.status}`);
        console.log(`   ✅ Amount: R$ ${foundTransaction.amount.toFixed(2)}`);
        console.log(`   ✅ É a transação correta: ${foundTransaction.id === transaction.id ? 'SIM' : 'NÃO'}`);
      }
      console.log('');
    }

    // 3. Resultado final
    console.log('🎯 RESULTADO FINAL:');
    if (foundTransaction) {
      if (foundTransaction.id === transaction.id) {
        console.log('✅ SUCESSO: Transação correta encontrada!');
        console.log('✅ A nova lógica está funcionando perfeitamente!');
        console.log('✅ O webhook XDPAG vai funcionar corretamente!');
      } else {
        console.log('⚠️ ATENÇÃO: Transação diferente encontrada');
        console.log(`   Esperada: ${transaction.id}`);
        console.log(`   Encontrada: ${foundTransaction.id}`);
        console.log('❌ Ainda há problemas na lógica de busca');
      }
    } else {
      console.log('❌ FALHA: Nenhuma transação encontrada');
      console.log('❌ A lógica de busca não está funcionando');
    }
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new RealWebhookLogicTester();
  tester.testRealWebhookLogic()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { RealWebhookLogicTester };
