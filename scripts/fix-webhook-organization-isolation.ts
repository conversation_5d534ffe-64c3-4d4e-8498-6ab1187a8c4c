#!/usr/bin/env tsx

/**
 * Script para identificar e corrigir webhooks sem isolamento por organização
 *
 * Este script:
 * 1. Lista todos os endpoints do Svix
 * 2. Identifica endpoints sem canais de organização
 * 3. Tenta corrigir ou deletar endpoints problemáticos
 * 4. Relata o status final
 */

import { svixService } from "../packages/utils/src/svix/index.js";
// @ts-ignore
import { db } from "@repo/database";
// @ts-ignore
import { logger } from "@repo/logs";

const SVIX_APP_ID = process.env.SVIX_APP_ID || "app_2xJGtEh9B3sOptpW4thRObvtlh9";

interface EndpointInfo {
  id: string;
  url: string;
  channels?: string[];
  description?: string;
  disabled: boolean;
  filterTypes?: string[];
}

interface WebhookInfo {
  id: string;
  url: string;
  organizationId: string;
  svixEndpointId?: string;
  isActive: boolean;
  events: string[];
}

async function listAllEndpoints(): Promise<EndpointInfo[]> {
  try {
    const svix = svixService.getClient();
    const response = await svix.endpoint.list(SVIX_APP_ID);
    return response.data;
  } catch (error) {
    logger.error("Failed to list endpoints", { error });
    throw error;
  }
}

async function listAllWebhooks(): Promise<WebhookInfo[]> {
  try {
    const webhooks = await db.webhook.findMany({
      select: {
        id: true,
        url: true,
        organizationId: true,
        svixEndpointId: true,
        isActive: true,
        events: true,
      },
    });
    return webhooks;
  } catch (error) {
    logger.error("Failed to list webhooks from database", { error });
    throw error;
  }
}

function identifyProblematicEndpoints(endpoints: EndpointInfo[]): {
  noChannels: EndpointInfo[];
  wrongChannels: EndpointInfo[];
  safe: EndpointInfo[];
} {
  const noChannels: EndpointInfo[] = [];
  const wrongChannels: EndpointInfo[] = [];
  const safe: EndpointInfo[] = [];

  for (const endpoint of endpoints) {
    if (!endpoint.channels || endpoint.channels.length === 0) {
      noChannels.push(endpoint);
    } else if (!endpoint.channels.some(channel => channel.startsWith('org-'))) {
      wrongChannels.push(endpoint);
    } else {
      safe.push(endpoint);
    }
  }

  return { noChannels, wrongChannels, safe };
}

async function fixEndpointChannels(problematicEndpoints: EndpointInfo[], webhooks: WebhookInfo[]): Promise<{
  fixed: number;
  deleted: number;
  failed: number;
}> {
  let fixed = 0;
  let deleted = 0;
  let failed = 0;

  const svix = svixService.getClient();

  for (const endpoint of problematicEndpoints) {
    try {
      // Find corresponding webhook in database
      const webhook = webhooks.find(w => w.svixEndpointId === endpoint.id);

      if (!webhook) {
        logger.warn("No webhook found for endpoint, deleting endpoint", {
          endpointId: endpoint.id,
          url: endpoint.url
        });

        await svix.endpoint.delete(SVIX_APP_ID, endpoint.id);
        deleted++;
        continue;
      }

      // Try to fix the endpoint by adding organization channel
      const organizationChannel = `org-${webhook.organizationId}`;

      logger.info("Attempting to fix endpoint with organization channel", {
        endpointId: endpoint.id,
        organizationId: webhook.organizationId,
        channel: organizationChannel
      });

      await svix.endpoint.update(SVIX_APP_ID, endpoint.id, {
        url: endpoint.url,
        description: `Webhook for organization ${webhook.organizationId}`,
        channels: [organizationChannel],
        disabled: !webhook.isActive,
        filterTypes: webhook.events.length > 0 ? webhook.events : undefined
      });

      // Verify the fix
      const updatedEndpoint = await svix.endpoint.get(SVIX_APP_ID, endpoint.id);
      if (updatedEndpoint.channels?.includes(organizationChannel)) {
        logger.info("Successfully fixed endpoint", {
          endpointId: endpoint.id,
          organizationId: webhook.organizationId,
          channels: updatedEndpoint.channels
        });
        fixed++;
      } else {
        throw new Error("Channel not properly set after update");
      }

    } catch (error) {
      logger.error("Failed to fix endpoint, deleting it", {
        endpointId: endpoint.id,
        error: error instanceof Error ? error.message : String(error)
      });

      try {
        await svix.endpoint.delete(SVIX_APP_ID, endpoint.id);
        deleted++;
      } catch (deleteError) {
        logger.error("Failed to delete problematic endpoint", {
          endpointId: endpoint.id,
          error: deleteError instanceof Error ? deleteError.message : String(deleteError)
        });
        failed++;
      }
    }
  }

  return { fixed, deleted, failed };
}

async function main() {
  try {
    console.log("🔍 Analyzing webhook organization isolation...\n");

    // List all endpoints and webhooks
    console.log("📋 Fetching endpoints from Svix...");
    const endpoints = await listAllEndpoints();
    console.log(`   Found ${endpoints.length} endpoints\n`);

    console.log("📋 Fetching webhooks from database...");
    const webhooks = await listAllWebhooks();
    console.log(`   Found ${webhooks.length} webhooks\n`);

    // Identify problematic endpoints
    console.log("🔍 Analyzing endpoint isolation...");
    const { noChannels, wrongChannels, safe } = identifyProblematicEndpoints(endpoints);

    console.log("📊 Analysis Results:");
    console.log(`   ✅ Safe endpoints (with org channels): ${safe.length}`);
    console.log(`   ❌ Endpoints without channels: ${noChannels.length}`);
    console.log(`   ⚠️  Endpoints with wrong channels: ${wrongChannels.length}\n`);

    if (noChannels.length === 0 && wrongChannels.length === 0) {
      console.log("🎉 All endpoints are properly isolated by organization!");
      return;
    }

    // Show problematic endpoints
    if (noChannels.length > 0) {
      console.log("❌ Endpoints WITHOUT organization channels:");
      noChannels.forEach(ep => {
        console.log(`   - ${ep.id}: ${ep.url} (${ep.description || 'No description'})`);
      });
      console.log();
    }

    if (wrongChannels.length > 0) {
      console.log("⚠️  Endpoints with WRONG channels:");
      wrongChannels.forEach(ep => {
        console.log(`   - ${ep.id}: ${ep.url} (channels: ${ep.channels?.join(', ') || 'none'})`);
      });
      console.log();
    }

    // Fix problematic endpoints
    const problematicEndpoints = [...noChannels, ...wrongChannels];
    console.log(`🔧 Attempting to fix ${problematicEndpoints.length} problematic endpoints...\n`);

    const results = await fixEndpointChannels(problematicEndpoints, webhooks);

    console.log("📊 Fix Results:");
    console.log(`   ✅ Fixed: ${results.fixed}`);
    console.log(`   🗑️  Deleted: ${results.deleted}`);
    console.log(`   ❌ Failed: ${results.failed}\n`);

    if (results.failed === 0) {
      console.log("🎉 All problematic endpoints have been resolved!");
    } else {
      console.log("⚠️  Some endpoints could not be fixed. Manual intervention may be required.");
    }

    // Final verification
    console.log("\n🔍 Final verification...");
    const finalEndpoints = await listAllEndpoints();
    const finalAnalysis = identifyProblematicEndpoints(finalEndpoints);

    console.log("📊 Final Status:");
    console.log(`   ✅ Safe endpoints: ${finalAnalysis.safe.length}`);
    console.log(`   ❌ Problematic endpoints: ${finalAnalysis.noChannels.length + finalAnalysis.wrongChannels.length}`);

  } catch (error) {
    logger.error("Script failed", { error });
    console.error("❌ Script failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { main as fixWebhookOrganizationIsolation };
