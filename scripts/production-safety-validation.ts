#!/usr/bin/env tsx

/**
 * Production Safety Validation Script
 * Validates feature flags, monitoring, rollback procedures under concurrent load
 */

import { getFeatureFlag, updateFeatureFlag } from '@repo/utils/src/feature-flags';
import { circuitBreakers } from '@repo/utils/src/circuit-breaker';
import { transactionMonitor } from '@repo/utils/src/transaction-monitoring';
import { createTransactionSafely } from '@repo/utils/src/transaction-service-wrapper';
import { db } from '@repo/database';
import { logger } from '@repo/logs';

interface ProductionSafetyResult {
  testName: string;
  passed: boolean;
  details: string[];
  errors: string[];
  metrics: Record<string, any>;
}

class ProductionSafetyValidator {
  private results: ProductionSafetyResult[] = [];
  private testOrganizationId = 'prod-safety-test-org';
  private originalFeatureFlags: Record<string, any> = {};

  async runAllValidations(): Promise<void> {
    console.log('🛡️ Starting Production Safety Validation\n');

    // Store original feature flag state
    await this.storeOriginalFeatureFlags();

    try {
      // Test 1: Feature flag system validation
      await this.validateFeatureFlagSystem();

      // Test 2: Circuit breaker functionality
      await this.validateCircuitBreakerFunctionality();

      // Test 3: Monitoring system validation
      await this.validateMonitoringSystem();

      // Test 4: Rollback procedures validation
      await this.validateRollbackProcedures();

      // Test 5: Concurrent load with feature flags
      await this.validateConcurrentLoadWithFeatureFlags();

      // Test 6: Health check endpoints
      await this.validateHealthCheckEndpoints();

      // Test 7: Emergency rollback simulation
      await this.validateEmergencyRollbackSimulation();

    } finally {
      // Restore original feature flags
      await this.restoreOriginalFeatureFlags();
    }

    this.printResults();
    this.generateProductionReadinessReport();
  }

  /**
   * Store original feature flag state for restoration
   */
  private async storeOriginalFeatureFlags(): Promise<void> {
    try {
      this.originalFeatureFlags = {
        enableNewTransactionService: await getFeatureFlag('enableNewTransactionService', this.testOrganizationId),
        rolloutPercentage: await getFeatureFlag('rolloutPercentage', this.testOrganizationId),
        enableCircuitBreaker: await getFeatureFlag('enableCircuitBreaker', this.testOrganizationId),
        enableDetailedLogging: await getFeatureFlag('enableDetailedLogging', this.testOrganizationId)
      };
      console.log('📋 Original feature flags stored for restoration');
    } catch (error) {
      console.log(`⚠️ Could not store original feature flags: ${error}`);
    }
  }

  /**
   * Restore original feature flag state
   */
  private async restoreOriginalFeatureFlags(): Promise<void> {
    try {
      for (const [flag, value] of Object.entries(this.originalFeatureFlags)) {
        await updateFeatureFlag(flag, value, this.testOrganizationId);
      }
      console.log('🔄 Original feature flags restored');
    } catch (error) {
      console.log(`⚠️ Could not restore original feature flags: ${error}`);
    }
  }

  /**
   * Test 1: Feature flag system validation
   */
  private async validateFeatureFlagSystem(): Promise<void> {
    const testName = 'Feature Flag System Validation';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Test flag reading
      const initialFlag = await getFeatureFlag('enableNewTransactionService', this.testOrganizationId);
      details.push(`Initial enableNewTransactionService: ${initialFlag}`);

      // Test flag updating
      await updateFeatureFlag('enableNewTransactionService', true, this.testOrganizationId);
      const updatedFlag = await getFeatureFlag('enableNewTransactionService', this.testOrganizationId);
      
      if (updatedFlag !== true) {
        errors.push('Feature flag update failed');
        passed = false;
      } else {
        details.push('Feature flag update successful');
      }

      // Test rollout percentage
      await updateFeatureFlag('rolloutPercentage', 25, this.testOrganizationId);
      const rolloutPercentage = await getFeatureFlag('rolloutPercentage', this.testOrganizationId);
      
      if (rolloutPercentage !== 25) {
        errors.push('Rollout percentage update failed');
        passed = false;
      } else {
        details.push('Rollout percentage update successful');
      }

      // Test organization-specific overrides
      await updateFeatureFlag('enableNewTransactionService', false, 'different-org');
      const differentOrgFlag = await getFeatureFlag('enableNewTransactionService', 'different-org');
      const testOrgFlag = await getFeatureFlag('enableNewTransactionService', this.testOrganizationId);

      if (differentOrgFlag === testOrgFlag) {
        errors.push('Organization-specific feature flags not working');
        passed = false;
      } else {
        details.push('Organization-specific feature flags working correctly');
      }

      // Reset to safe state
      await updateFeatureFlag('enableNewTransactionService', false, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 0, this.testOrganizationId);

    } catch (error) {
      errors.push(`Feature flag system error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: {}
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 2: Circuit breaker functionality
   */
  private async validateCircuitBreakerFunctionality(): Promise<void> {
    const testName = 'Circuit Breaker Functionality';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Get initial circuit breaker state
      const initialState = circuitBreakers.transactionService.getState();
      details.push(`Initial circuit breaker state: ${initialState}`);

      // Test circuit breaker with successful operations
      let successCount = 0;
      for (let i = 0; i < 5; i++) {
        try {
          await circuitBreakers.transactionService.execute(async () => {
            return { success: true, data: `test-${i}` };
          });
          successCount++;
        } catch (error) {
          errors.push(`Circuit breaker success test failed: ${error}`);
        }
      }

      if (successCount === 5) {
        details.push('Circuit breaker allows successful operations');
      } else {
        errors.push(`Circuit breaker blocked successful operations: ${successCount}/5`);
        passed = false;
      }

      // Test circuit breaker metrics
      const metrics = circuitBreakers.transactionService.getMetrics();
      details.push(`Circuit breaker metrics: ${JSON.stringify(metrics)}`);

      if (metrics.totalRequests >= 5) {
        details.push('Circuit breaker metrics tracking correctly');
      } else {
        errors.push('Circuit breaker metrics not tracking correctly');
        passed = false;
      }

    } catch (error) {
      errors.push(`Circuit breaker test error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: circuitBreakers.transactionService.getMetrics()
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 3: Monitoring system validation
   */
  private async validateMonitoringSystem(): Promise<void> {
    const testName = 'Monitoring System Validation';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Test metrics collection
      const initialMetrics = await transactionMonitor.getCurrentMetrics();
      details.push(`Initial metrics collected: ${Object.keys(initialMetrics).length} metrics`);

      // Create some test transactions to generate metrics
      for (let i = 0; i < 3; i++) {
        try {
          await createTransactionSafely({
            customerEmail: `monitoring-test-${i}@test.com`,
            customerName: `Monitoring Test ${i}`,
            amount: 10 + i,
            organizationId: this.testOrganizationId,
            description: `Monitoring test ${i}`,
            type: 'CHARGE'
          });
        } catch (error) {
          // Expected to potentially fail, we're testing monitoring
        }
      }

      // Check if metrics updated
      const updatedMetrics = await transactionMonitor.getCurrentMetrics();
      details.push(`Updated metrics: ${JSON.stringify(updatedMetrics)}`);

      if (updatedMetrics.totalTransactions >= initialMetrics.totalTransactions) {
        details.push('Monitoring system tracking transactions');
      } else {
        errors.push('Monitoring system not tracking transactions correctly');
        passed = false;
      }

      // Test health reporting
      const healthReport = await transactionMonitor.getHealthReport();
      details.push(`Health report generated: ${Object.keys(healthReport).length} health indicators`);

      if (healthReport.status) {
        details.push('Health reporting functional');
      } else {
        errors.push('Health reporting not functional');
        passed = false;
      }

    } catch (error) {
      errors.push(`Monitoring system error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: await transactionMonitor.getCurrentMetrics().catch(() => ({}))
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 4: Rollback procedures validation
   */
  private async validateRollbackProcedures(): Promise<void> {
    const testName = 'Rollback Procedures Validation';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Test feature flag rollback
      await updateFeatureFlag('enableNewTransactionService', true, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 50, this.testOrganizationId);
      
      details.push('Feature flags set to active state');

      // Simulate rollback
      await updateFeatureFlag('enableNewTransactionService', false, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 0, this.testOrganizationId);

      const rolledBackService = await getFeatureFlag('enableNewTransactionService', this.testOrganizationId);
      const rolledBackPercentage = await getFeatureFlag('rolloutPercentage', this.testOrganizationId);

      if (rolledBackService === false && rolledBackPercentage === 0) {
        details.push('Feature flag rollback successful');
      } else {
        errors.push('Feature flag rollback failed');
        passed = false;
      }

      // Test transaction creation after rollback (should use legacy)
      const testTransaction = await createTransactionSafely({
        customerEmail: '<EMAIL>',
        customerName: 'Rollback Test',
        amount: 99.99,
        organizationId: this.testOrganizationId,
        description: 'Rollback test transaction',
        type: 'CHARGE'
      });

      if (testTransaction.usedNewService === false) {
        details.push('Transaction creation uses legacy service after rollback');
      } else {
        errors.push('Transaction creation still using new service after rollback');
        passed = false;
      }

    } catch (error) {
      errors.push(`Rollback procedure error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: {}
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 5: Concurrent load with feature flags
   */
  private async validateConcurrentLoadWithFeatureFlags(): Promise<void> {
    const testName = 'Concurrent Load with Feature Flags';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;
    const concurrentRequests = 20;

    console.log(`🔄 ${testName} - ${concurrentRequests} concurrent requests...`);

    try {
      // Set feature flags to 50% rollout
      await updateFeatureFlag('enableNewTransactionService', true, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 50, this.testOrganizationId);

      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        try {
          const result = await createTransactionSafely({
            customerEmail: `concurrent-flag-${index}@test.com`,
            customerName: `Concurrent Flag Test ${index}`,
            amount: 15 + index,
            organizationId: this.testOrganizationId,
            description: `Concurrent flag test ${index}`,
            type: 'CHARGE'
          });
          return result;
        } catch (error) {
          return { error: error instanceof Error ? error.message : error };
        }
      });

      const results = await Promise.all(promises);
      const successful = results.filter(r => !r.error).length;
      const usedNewService = results.filter(r => !r.error && r.usedNewService).length;
      const usedLegacyService = results.filter(r => !r.error && !r.usedNewService).length;

      details.push(`Successful transactions: ${successful}/${concurrentRequests}`);
      details.push(`Used new service: ${usedNewService}`);
      details.push(`Used legacy service: ${usedLegacyService}`);

      // With 50% rollout, we expect roughly half to use each service
      const newServicePercentage = (usedNewService / successful) * 100;
      if (newServicePercentage >= 30 && newServicePercentage <= 70) {
        details.push(`Feature flag rollout working correctly: ${newServicePercentage.toFixed(1)}%`);
      } else {
        errors.push(`Feature flag rollout not working correctly: ${newServicePercentage.toFixed(1)}%`);
        passed = false;
      }

      if (successful === concurrentRequests) {
        details.push('All concurrent requests successful');
      } else {
        errors.push(`Some concurrent requests failed: ${concurrentRequests - successful} failures`);
        passed = false;
      }

    } catch (error) {
      errors.push(`Concurrent load test error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: {}
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 6: Health check endpoints
   */
  private async validateHealthCheckEndpoints(): Promise<void> {
    const testName = 'Health Check Endpoints';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Test transaction health endpoint
      const healthReport = await transactionMonitor.getHealthReport();
      details.push(`Health report keys: ${Object.keys(healthReport).join(', ')}`);

      if (healthReport.status !== undefined) {
        details.push('Health check endpoint responding');
      } else {
        errors.push('Health check endpoint not responding correctly');
        passed = false;
      }

      // Test circuit breaker status
      const circuitBreakerStatus = circuitBreakers.transactionService.getState();
      details.push(`Circuit breaker status: ${circuitBreakerStatus}`);

      if (['CLOSED', 'OPEN', 'HALF_OPEN'].includes(circuitBreakerStatus)) {
        details.push('Circuit breaker status endpoint working');
      } else {
        errors.push('Circuit breaker status endpoint not working');
        passed = false;
      }

    } catch (error) {
      errors.push(`Health check validation error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: {}
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Test 7: Emergency rollback simulation
   */
  private async validateEmergencyRollbackSimulation(): Promise<void> {
    const testName = 'Emergency Rollback Simulation';
    const details: string[] = [];
    const errors: string[] = [];
    let passed = true;

    console.log(`🔄 ${testName}...`);

    try {
      // Simulate emergency: enable new service
      await updateFeatureFlag('enableNewTransactionService', true, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 100, this.testOrganizationId);
      
      details.push('Emergency state: New service enabled at 100%');

      // Create transaction to verify new service is active
      const beforeRollback = await createTransactionSafely({
        customerEmail: '<EMAIL>',
        customerName: 'Emergency Before',
        amount: 50,
        organizationId: this.testOrganizationId,
        description: 'Before emergency rollback',
        type: 'CHARGE'
      });

      if (beforeRollback.usedNewService) {
        details.push('New service active before emergency rollback');
      }

      // EMERGENCY ROLLBACK: Disable new service immediately
      await updateFeatureFlag('enableNewTransactionService', false, this.testOrganizationId);
      await updateFeatureFlag('rolloutPercentage', 0, this.testOrganizationId);
      
      details.push('Emergency rollback executed: Service disabled');

      // Verify rollback worked
      const afterRollback = await createTransactionSafely({
        customerEmail: '<EMAIL>',
        customerName: 'Emergency After',
        amount: 50,
        organizationId: this.testOrganizationId,
        description: 'After emergency rollback',
        type: 'CHARGE'
      });

      if (!afterRollback.usedNewService) {
        details.push('Emergency rollback successful: Using legacy service');
      } else {
        errors.push('Emergency rollback failed: Still using new service');
        passed = false;
      }

      // Test multiple concurrent requests after rollback
      const concurrentAfterRollback = await Promise.all(
        Array.from({ length: 5 }, async (_, i) => {
          return createTransactionSafely({
            customerEmail: `emergency-concurrent-${i}@test.com`,
            customerName: `Emergency Concurrent ${i}`,
            amount: 25,
            organizationId: this.testOrganizationId,
            description: `Emergency concurrent test ${i}`,
            type: 'CHARGE'
          });
        })
      );

      const allUseLegacy = concurrentAfterRollback.every(r => !r.usedNewService);
      if (allUseLegacy) {
        details.push('All concurrent requests use legacy service after rollback');
      } else {
        errors.push('Some concurrent requests still use new service after rollback');
        passed = false;
      }

    } catch (error) {
      errors.push(`Emergency rollback simulation error: ${error instanceof Error ? error.message : error}`);
      passed = false;
    }

    this.results.push({
      testName,
      passed,
      details,
      errors,
      metrics: {}
    });

    console.log(`${passed ? '✅' : '❌'} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
  }

  /**
   * Print comprehensive validation results
   */
  private printResults(): void {
    console.log('\n🛡️ PRODUCTION SAFETY VALIDATION RESULTS');
    console.log('='.repeat(70));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log(`Total Validations: ${totalTests}`);
    console.log(`Passed: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Failed: ${failedTests}`);

    console.log('\n📋 DETAILED VALIDATION RESULTS');
    console.log('-'.repeat(70));

    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      
      if (result.details.length > 0) {
        result.details.forEach(detail => {
          console.log(`   ℹ️  ${detail}`);
        });
      }
      
      if (result.errors.length > 0) {
        result.errors.forEach(error => {
          console.log(`   ❌ ${error}`);
        });
      }
      
      console.log('');
    });
  }

  /**
   * Generate production readiness report
   */
  private generateProductionReadinessReport(): void {
    console.log('\n📊 PRODUCTION READINESS REPORT');
    console.log('='.repeat(50));

    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    const readinessScore = (passedTests / totalTests) * 100;

    console.log(`Production Readiness Score: ${readinessScore.toFixed(1)}%`);

    if (readinessScore === 100) {
      console.log('🎉 SYSTEM IS PRODUCTION READY!');
      console.log('✅ All safety validations passed');
      console.log('✅ Feature flags working correctly');
      console.log('✅ Circuit breakers functional');
      console.log('✅ Monitoring system operational');
      console.log('✅ Rollback procedures validated');
      console.log('✅ Emergency procedures tested');
      console.log('\n🚀 RECOMMENDATION: PROCEED WITH DEPLOYMENT');
    } else if (readinessScore >= 80) {
      console.log('⚠️ SYSTEM IS MOSTLY READY - Minor issues detected');
      console.log('🔍 Review failed validations before deployment');
      console.log('\n🚀 RECOMMENDATION: FIX ISSUES THEN DEPLOY');
    } else {
      console.log('❌ SYSTEM IS NOT PRODUCTION READY');
      console.log('🛑 Critical safety issues detected');
      console.log('\n🚫 RECOMMENDATION: DO NOT DEPLOY - FIX CRITICAL ISSUES');
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Review any failed validations');
    console.log('2. Fix identified issues');
    console.log('3. Re-run validation tests');
    console.log('4. Deploy with 0% feature flag rollout');
    console.log('5. Gradually increase rollout percentage');
    console.log('6. Monitor system metrics continuously');
  }
}

// Run validations if script is called directly
if (require.main === module) {
  const validator = new ProductionSafetyValidator();
  validator.runAllValidations()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ProductionSafetyValidator };
