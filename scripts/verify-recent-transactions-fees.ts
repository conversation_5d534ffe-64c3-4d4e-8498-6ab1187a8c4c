#!/usr/bin/env tsx

/**
 * Script para verificar se as transações recentes têm taxas aplicadas
 */

import { config } from 'dotenv';
import { resolve } from 'path';

config({ path: resolve(process.cwd(), '.env') });

import { db } from '@repo/database';

const ORGANIZATION_ID = 'fC99w8SdDGbNJM_q0b2s5';

async function verifyRecentTransactionsFees() {
  console.log('🔍 VERIFICANDO TAXAS EM TRANSAÇÕES RECENTES');
  console.log('='.repeat(60));
  console.log(`Organization ID: ${ORGANIZATION_ID}`);
  console.log('');

  try {
    // Buscar as 20 transações mais recentes da organização
    const recentTransactions = await db.transaction.findMany({
      where: {
        organizationId: ORGANIZATION_ID,
        type: 'CHARGE'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20,
      select: {
        id: true,
        amount: true,
        percentFee: true,
        fixedFee: true,
        totalFee: true,
        netAmount: true,
        createdAt: true,
        status: true
      }
    });

    if (recentTransactions.length === 0) {
      console.log('❌ Nenhuma transação encontrada para a organização');
      return;
    }

    console.log(`✅ ENCONTRADAS ${recentTransactions.length} TRANSAÇÕES RECENTES:`);
    console.log('');

    let transactionsWithFees = 0;
    let transactionsWithoutFees = 0;

    console.log('ID                        | Valor  | Taxa % | Taxa Fixa | Taxa Total | Líquido | Status  | Criada em');
    console.log('-'.repeat(110));

    for (const transaction of recentTransactions) {
      const hasValidFees = transaction.totalFee > 0;
      
      if (hasValidFees) {
        transactionsWithFees++;
      } else {
        transactionsWithoutFees++;
      }

      const status = hasValidFees ? '✅' : '❌';
      const id = transaction.id.substring(0, 25);
      const amount = `R$ ${transaction.amount.toFixed(2)}`.padEnd(6);
      const percentFee = `R$ ${transaction.percentFee.toFixed(2)}`.padEnd(6);
      const fixedFee = `R$ ${transaction.fixedFee.toFixed(2)}`.padEnd(9);
      const totalFee = `R$ ${transaction.totalFee.toFixed(2)}`.padEnd(10);
      const netAmount = transaction.netAmount ? `R$ ${transaction.netAmount.toFixed(2)}`.padEnd(7) : 'null   ';
      const transactionStatus = transaction.status.padEnd(7);
      const createdAt = transaction.createdAt.toISOString().substring(0, 19).replace('T', ' ');

      console.log(`${id} | ${amount} | ${percentFee} | ${fixedFee} | ${totalFee} | ${netAmount} | ${transactionStatus} | ${createdAt} ${status}`);
    }

    console.log('-'.repeat(110));
    console.log('');

    const feeApplicationRate = (transactionsWithFees / recentTransactions.length) * 100;

    console.log('📊 RESUMO:');
    console.log(`   Total de Transações: ${recentTransactions.length}`);
    console.log(`   Com Taxas Aplicadas: ${transactionsWithFees} (${feeApplicationRate.toFixed(1)}%)`);
    console.log(`   Sem Taxas Aplicadas: ${transactionsWithoutFees} (${(100 - feeApplicationRate).toFixed(1)}%)`);
    console.log('');

    if (feeApplicationRate >= 95) {
      console.log('🎉 EXCELENTE! Sistema aplicando taxas corretamente (≥95%)');
      console.log('✅ Sistema aprovado para produção em relação às taxas');
    } else if (feeApplicationRate >= 80) {
      console.log('⚠️ BOM: Sistema aplicando taxas na maioria das transações (≥80%)');
      console.log('🔧 Recomenda-se investigar as transações sem taxas');
    } else if (feeApplicationRate >= 50) {
      console.log('❌ PROBLEMA: Sistema aplicando taxas em menos de 80% das transações');
      console.log('🚨 Necessário corrigir antes de ir para produção');
    } else {
      console.log('🚨 CRÍTICO: Sistema aplicando taxas em menos de 50% das transações');
      console.log('❌ Sistema NÃO aprovado para produção');
    }

    // Calcular perda financeira estimada
    if (transactionsWithoutFees > 0) {
      const avgAmount = recentTransactions.reduce((sum, t) => sum + t.amount, 0) / recentTransactions.length;
      const avgExpectedFee = (avgAmount * 0.01) + 2; // 1% + R$ 2,00
      const dailyLoss = (transactionsWithoutFees / recentTransactions.length) * 20202 * avgExpectedFee; // 20.202 transações/dia
      const monthlyLoss = dailyLoss * 30;
      const yearlyLoss = dailyLoss * 365;

      console.log('');
      console.log('💰 IMPACTO FINANCEIRO ESTIMADO:');
      console.log(`   Taxa média esperada por transação: R$ ${avgExpectedFee.toFixed(2)}`);
      console.log(`   Perda diária estimada: R$ ${dailyLoss.toFixed(2)}`);
      console.log(`   Perda mensal estimada: R$ ${monthlyLoss.toFixed(2)}`);
      console.log(`   Perda anual estimada: R$ ${yearlyLoss.toFixed(2)}`);
    }

  } catch (error) {
    console.error('❌ ERRO:', error);
  } finally {
    await db.$disconnect();
  }
}

// Executar se script for chamado diretamente
if (require.main === module) {
  verifyRecentTransactionsFees()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { verifyRecentTransactionsFees };
