#!/usr/bin/env tsx

/**
 * Teste detalhado para transações SEND (criação de cobrança PIX)
 * Mostra IDs, verifica banco de dados e usa gateway padrão da organização
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { SendTransactionService } from '@repo/utils/src/send-transaction-service';
import { db } from '@repo/database';
import { logger } from '@repo/logs';

interface DetailedTestResult {
  testName: string;
  successful: boolean;
  transactionId?: string;
  referenceCode?: string;
  amount: number;
  pixKey: string;
  duration: number;
  error?: string;
  databaseVerified?: boolean;
  gatewayUsed?: string;
}

class DetailedSendPixTest {
  private testOrganizationId = 'fC99w8SdDGbNJM_q0b2s5'; // Sua organização de homologação
  private results: DetailedTestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🔍 TESTE DETALHADO - TRANSAÇÕES SEND (CRIAÇÃO DE COBRANÇA PIX)');
    console.log('='.repeat(80));
    console.log(`Organização: ${this.testOrganizationId}`);
    console.log(`Banco: ${process.env.DATABASE_URL ? '✅ Configurado' : '❌ Não configurado'}`);
    console.log('');

    // Verificar gateway padrão da organização
    await this.checkDefaultGateway();

    // Teste 1: Criação de cobrança PIX simples
    await this.testSimplePixCharge();

    // Teste 2: Verificar se transação está no banco
    await this.verifyTransactionInDatabase();

    // Teste 3: Teste de duplicação (deve prevenir)
    await this.testDuplicatePrevention();

    // Teste 4: Listar todas as transações da organização
    await this.listAllOrganizationTransactions();

    this.printResults();
  }

  /**
   * Verificar gateway padrão da organização
   */
  private async checkDefaultGateway(): Promise<void> {
    console.log('🔍 Verificando gateway padrão da organização...');

    try {
      // Buscar configurações da organização
      const organization = await db.organization.findUnique({
        where: {
          id: this.testOrganizationId
        },
        include: {
          gatewayConfigs: true
        }
      });

      if (organization) {
        console.log(`✅ Organização encontrada: ${organization.name}`);
        console.log(`   ID: ${organization.id}`);
        console.log(`   Gateway Configs: ${organization.gatewayConfigs?.length || 0}`);

        if (organization.gatewayConfigs && organization.gatewayConfigs.length > 0) {
          organization.gatewayConfigs.forEach(config => {
            console.log(`   - Gateway: ${config.gatewayName} (${config.isActive ? 'Ativo' : 'Inativo'})`);
          });
        }
      } else {
        console.log('❌ Organização não encontrada no banco de dados');
      }

    } catch (error) {
      console.log(`❌ Erro ao verificar organização: ${error instanceof Error ? error.message : error}`);
    }

    console.log('');
  }

  /**
   * Teste 1: Criação de cobrança PIX simples
   */
  private async testSimplePixCharge(): Promise<void> {
    const testName = 'Criação de Cobrança PIX Simples';
    const amount = 100.00;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      const result = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Detailed PIX',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste detalhado de criação de cobrança PIX',
        pixKey,
        pixKeyType: 'PHONE'
      });

      const duration = Date.now() - startTime;

      this.results.push({
        testName,
        successful: true,
        transactionId: result.id,
        referenceCode: result.referenceCode,
        amount,
        pixKey,
        duration
      });

      console.log(`✅ ${testName}: SUCESSO`);
      console.log(`   ID: ${result.id}`);
      console.log(`   Reference Code: ${result.referenceCode}`);
      console.log(`   Amount: R$ ${amount}`);
      console.log(`   PIX Key: ${pixKey}`);
      console.log(`   Duration: ${duration}ms`);

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 2: Verificar se transação está no banco
   */
  private async verifyTransactionInDatabase(): Promise<void> {
    const testName = 'Verificação no Banco de Dados';
    const startTime = Date.now();

    console.log(`🔄 ${testName}...`);

    try {
      // Buscar transações da organização
      const transactions = await db.transaction.findMany({
        where: {
          organizationId: this.testOrganizationId,
          type: 'SEND'
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 5
      });

      const duration = Date.now() - startTime;

      if (transactions.length > 0) {
        console.log(`✅ ${testName}: SUCESSO - ${transactions.length} transações encontradas`);

        transactions.forEach((tx, index) => {
          console.log(`   ${index + 1}. ID: ${tx.id}`);
          console.log(`      Reference Code: ${tx.referenceCode}`);
          console.log(`      Amount: R$ ${tx.amount}`);
          console.log(`      Status: ${tx.status}`);
          console.log(`      Created: ${tx.createdAt.toISOString()}`);
          console.log(`      Customer: ${tx.customerEmail}`);
          console.log('');
        });

        this.results.push({
          testName,
          successful: true,
          amount: transactions.reduce((sum, tx) => sum + tx.amount, 0),
          pixKey: 'N/A',
          duration,
          databaseVerified: true
        });

      } else {
        console.log(`❌ ${testName}: FALHOU - Nenhuma transação encontrada`);

        this.results.push({
          testName,
          successful: false,
          amount: 0,
          pixKey: 'N/A',
          duration,
          error: 'Nenhuma transação encontrada no banco'
        });
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey: 'N/A',
        duration,
        error: errorMessage
      });
    }
  }

  /**
   * Teste 3: Teste de duplicação (deve prevenir)
   */
  private async testDuplicatePrevention(): Promise<void> {
    const testName = 'Prevenção de Duplicação';
    const amount = 150.00;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      // Primeira tentativa
      const result1 = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Duplicação Detailed 1',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste de duplicação detalhado - primeira tentativa',
        pixKey,
        pixKeyType: 'PHONE'
      });

      console.log(`   Primeira tentativa: ID ${result1.id}`);

      // Segunda tentativa (deve retornar a mesma transação)
      const result2 = await SendTransactionService.createSendTransaction({
        customerEmail,
        customerName: 'Teste Duplicação Detailed 2',
        amount,
        organizationId: this.testOrganizationId,
        description: 'Teste de duplicação detalhado - segunda tentativa',
        pixKey,
        pixKeyType: 'PHONE'
      });

      const duration = Date.now() - startTime;

      // Verificar se são a mesma transação (prevenção de duplicação)
      const isDuplicate = result1.id === result2.id;

      this.results.push({
        testName,
        successful: isDuplicate,
        transactionId: result1.id,
        referenceCode: result1.referenceCode,
        amount,
        pixKey,
        duration,
        error: isDuplicate ? undefined : 'Duplicação não foi prevenida'
      });

      if (isDuplicate) {
        console.log(`✅ ${testName}: SUCESSO - Duplicação prevenida`);
        console.log(`   ID: ${result1.id}`);
        console.log(`   Reference Code: ${result1.referenceCode}`);
        console.log(`   Duration: ${duration}ms`);
      } else {
        console.log(`❌ ${testName}: FALHOU - Duplicação não foi prevenida`);
        console.log(`   ID 1: ${result1.id}`);
        console.log(`   ID 2: ${result2.id}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 4: Listar todas as transações da organização
   */
  private async listAllOrganizationTransactions(): Promise<void> {
    const testName = 'Listar Todas as Transações';
    const startTime = Date.now();

    console.log(`🔄 ${testName}...`);

    try {
      // Buscar todas as transações da organização
      const allTransactions = await db.transaction.findMany({
        where: {
          organizationId: this.testOrganizationId
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 20
      });

      const duration = Date.now() - startTime;

      console.log(`✅ ${testName}: SUCESSO - ${allTransactions.length} transações encontradas`);
      console.log('');

      if (allTransactions.length > 0) {
        console.log('📋 LISTA COMPLETA DE TRANSAÇÕES:');
        console.log('-'.repeat(80));

        allTransactions.forEach((tx, index) => {
          console.log(`${index + 1}. ID: ${tx.id}`);
          console.log(`   Reference Code: ${tx.referenceCode}`);
          console.log(`   Type: ${tx.type}`);
          console.log(`   Amount: R$ ${tx.amount}`);
          console.log(`   Status: ${tx.status}`);
          console.log(`   Customer: ${tx.customerEmail}`);
          console.log(`   Created: ${tx.createdAt.toISOString()}`);
          console.log(`   Updated: ${tx.updatedAt.toISOString()}`);
          console.log('');
        });

        // Estatísticas
        const sendTransactions = allTransactions.filter(tx => tx.type === 'SEND');
        const chargeTransactions = allTransactions.filter(tx => tx.type === 'CHARGE');
        const totalAmount = allTransactions.reduce((sum, tx) => sum + tx.amount, 0);

        console.log('📊 ESTATÍSTICAS:');
        console.log(`   Total de Transações: ${allTransactions.length}`);
        console.log(`   SEND: ${sendTransactions.length}`);
        console.log(`   CHARGE: ${chargeTransactions.length}`);
        console.log(`   Valor Total: R$ ${totalAmount.toFixed(2)}`);
        console.log('');

        this.results.push({
          testName,
          successful: true,
          amount: totalAmount,
          pixKey: 'N/A',
          duration,
          databaseVerified: true
        });

      } else {
        console.log('❌ Nenhuma transação encontrada para esta organização');

        this.results.push({
          testName,
          successful: false,
          amount: 0,
          pixKey: 'N/A',
          duration,
          error: 'Nenhuma transação encontrada'
        });
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey: 'N/A',
        duration,
        error: errorMessage
      });
    }
  }

  /**
   * Imprimir resultados
   */
  private printResults(): void {
    console.log('\n📊 RESULTADOS DOS TESTES DETALHADOS');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.successful).length;
    const totalAmount = this.results.reduce((sum, r) => sum + r.amount, 0);
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Passou: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Valor Total Testado: R$ ${totalAmount.toFixed(2)}`);
    console.log(`Tempo Total: ${totalDuration}ms`);

    console.log('\n📋 DETALHES DOS TESTES');
    console.log('-'.repeat(80));

    this.results.forEach(result => {
      const status = result.successful ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      console.log(`   Valor: R$ ${result.amount.toFixed(2)}`);
      console.log(`   PIX Key: ${result.pixKey}`);
      console.log(`   Tempo: ${result.duration}ms`);

      if (result.transactionId) {
        console.log(`   ID: ${result.transactionId}`);
        console.log(`   Reference Code: ${result.referenceCode}`);
      }

      if (result.databaseVerified !== undefined) {
        console.log(`   Banco Verificado: ${result.databaseVerified ? 'Sim' : 'Não'}`);
      }

      if (result.error) {
        console.log(`   Erro: ${result.error}`);
      }
      console.log('');
    });

    if (passedTests === totalTests) {
      console.log('🎉 TODOS OS TESTES PASSARAM!');
      console.log('✅ Sistema de criação de cobrança PIX funcionando perfeitamente');
      console.log('✅ Prevenção de duplicação funcionando');
      console.log('✅ Banco de dados conectado e funcionando');
    } else {
      console.log('⚠️ ALGUNS TESTES FALHARAM');
      console.log('🔍 Revise os erros antes de prosseguir');
    }
  }
}

// Executar testes se script for chamado diretamente
if (require.main === module) {
  const tester = new DetailedSendPixTest();
  tester.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { DetailedSendPixTest };
