#!/usr/bin/env tsx

/**
 * Teste final de integração - Verifica se os webhooks XDPAG funcionam corretamente
 * para todas as transferências PIX criadas
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class FinalWebhookIntegrationTester {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async testFinalIntegration(): Promise<void> {
    console.log('🎯 TESTE FINAL - INTEGRAÇÃO WEBHOOK XDPAG');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar todas as transferências PIX recentes
    const transactions = await db.transaction.findMany({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        createdAt: {
          gte: new Date(Date.now() - 2 * 60 * 60 * 1000) // Últimas 2 horas
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Transferências PIX encontradas: ${transactions.length}`);
    console.log('');

    if (transactions.length === 0) {
      console.log('❌ Nenhuma transferência encontrada');
      return;
    }

    console.log('📋 DETALHES DAS TRANSAÇÕES:');
    transactions.forEach((tx, index) => {
      console.log(`   ${index + 1}. ID: ${tx.id}`);
      console.log(`      External ID: ${tx.externalId || 'N/A'}`);
      console.log(`      Status: ${tx.status}`);
      console.log(`      Valor: R$ ${tx.amount.toFixed(2)}`);
      console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
      console.log('');
    });

    // Simular webhooks para cada transação
    console.log('🔄 SIMULANDO WEBHOOKS XDPAG:');
    console.log('-'.repeat(40));

    let successCount = 0;
    let failureCount = 0;

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      console.log(`\n${i + 1}. Simulando webhook para transação: ${transaction.id}`);

      // Simular dados do webhook XDPAG
      const webhookData = {
        externalId: transaction.id, // Nosso ID interno
        transactionId: transaction.externalId, // ID do XDPAG
        amount: transaction.amount,
        status: 'FINISHED'
      };

      console.log(`   External ID: ${webhookData.externalId}`);
      console.log(`   Transaction ID: ${webhookData.transactionId || 'N/A'}`);
      console.log(`   Amount: R$ ${webhookData.amount.toFixed(2)}`);

      // Testar busca usando a nova lógica
      let foundTransaction = null;

      // 1. Busca por externalId (nosso ID interno)
      if (webhookData.externalId) {
        foundTransaction = await db.transaction.findFirst({
          where: { externalId: webhookData.externalId }
        });
      }

      // 2. Se não encontrou, busca por transactionId (ID do XDPAG)
      if (!foundTransaction && webhookData.transactionId) {
        foundTransaction = await db.transaction.findFirst({
          where: {
            OR: [
              { id: webhookData.transactionId },
              { externalId: webhookData.transactionId }
            ]
          }
        });
      }

      if (foundTransaction) {
        if (foundTransaction.id === transaction.id) {
          console.log(`   ✅ SUCESSO: Transação correta encontrada!`);
          successCount++;
        } else {
          console.log(`   ⚠️ ATENÇÃO: Transação diferente encontrada`);
          console.log(`      Esperada: ${transaction.id}`);
          console.log(`      Encontrada: ${foundTransaction.id}`);
          failureCount++;
        }
      } else {
        console.log(`   ❌ FALHA: Nenhuma transação encontrada`);
        failureCount++;
      }
    }

    console.log('\n📊 RESULTADO FINAL:');
    console.log('='.repeat(40));
    console.log(`✅ Sucessos: ${successCount}/${transactions.length}`);
    console.log(`❌ Falhas: ${failureCount}/${transactions.length}`);
    console.log(`📈 Taxa de sucesso: ${((successCount / transactions.length) * 100).toFixed(1)}%`);

    if (successCount === transactions.length) {
      console.log('\n🎉 PERFEITO! Todos os webhooks funcionam corretamente!');
      console.log('✅ O sistema está pronto para produção!');
      console.log('✅ As transferências PIX serão processadas corretamente!');
    } else if (successCount > 0) {
      console.log('\n⚠️ PARCIALMENTE FUNCIONANDO');
      console.log('Alguns webhooks funcionam, outros não');
      console.log('Recomendação: Investigar as falhas antes de ir para produção');
    } else {
      console.log('\n❌ SISTEMA NÃO FUNCIONANDO');
      console.log('Nenhum webhook está funcionando corretamente');
      console.log('Recomendação: Corrigir os problemas antes de ir para produção');
    }
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new FinalWebhookIntegrationTester();
  tester.testFinalIntegration()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { FinalWebhookIntegrationTester };
