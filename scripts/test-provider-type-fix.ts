#!/usr/bin/env tsx

/**
 * Teste da correção do provider type
 * Verifica se o provider agora retorna o tipo correto em vez de 'Object'
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { getPaymentProvider } from '../packages/payments/provider/factory';

class ProviderTypeFixTester {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async testProviderTypeFix(): Promise<void> {
    console.log('🔧 TESTE - CORREÇÃO DO PROVIDER TYPE');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    try {
      // Testar o provider para CHARGE
      console.log('🔄 Testando provider para CHARGE...');
      const chargeProvider = await getPaymentProvider(this.organizationId, {
        action: 'charge'
      });

      console.log('📊 RESULTADO DO PROVIDER CHARGE:');
      console.log(`   Constructor name: ${chargeProvider.constructor.name}`);
      console.log(`   Provider type: ${chargeProvider.type || 'N/A'}`);
      console.log(`   Provider keys: ${Object.keys(chargeProvider).join(', ')}`);
      console.log('');

      // Determinar o tipo correto
      let providerType = chargeProvider.constructor.name || 'Unknown';

      if (providerType === 'Object') {
        providerType = chargeProvider.type || process.env.DEFAULT_GATEWAY_CHARGE?.toUpperCase() || 'OWEMPAY_V2';
      }

      console.log('🔧 APÓS CORREÇÃO:');
      console.log(`   Provider type corrigido: ${providerType}`);
      console.log('');

      // Testar o provider para SEND
      console.log('🔄 Testando provider para SEND...');
      const sendProvider = await getPaymentProvider(this.organizationId, {
        action: 'send'
      });

      console.log('📊 RESULTADO DO PROVIDER SEND:');
      console.log(`   Constructor name: ${sendProvider.constructor.name}`);
      console.log(`   Provider type: ${sendProvider.type || 'N/A'}`);
      console.log(`   Provider keys: ${Object.keys(sendProvider).join(', ')}`);
      console.log('');

      // Determinar o tipo correto para SEND
      let sendProviderType = sendProvider.constructor.name || 'Unknown';

      if (sendProviderType === 'Object') {
        sendProviderType = sendProvider.type || process.env.DEFAULT_GATEWAY_SEND?.toUpperCase() || 'XDPAG';
      }

      console.log('🔧 APÓS CORREÇÃO (SEND):');
      console.log(`   Provider type corrigido: ${sendProviderType}`);
      console.log('');

      // Resultado final
      console.log('🎯 RESULTADO FINAL:');
      if (providerType !== 'Object' && sendProviderType !== 'Object') {
        console.log('✅ SUCESSO: Provider types corrigidos!');
        console.log(`   CHARGE provider: ${providerType}`);
        console.log(`   SEND provider: ${sendProviderType}`);
        console.log('✅ O warning "Provider didn\'t create transaction" não deve mais aparecer');
        console.log('✅ O sistema agora identifica corretamente os providers');
      } else {
        console.log('❌ FALHA: Ainda há providers retornando "Object"');
        console.log(`   CHARGE provider: ${providerType}`);
        console.log(`   SEND provider: ${sendProviderType}`);
      }

    } catch (error) {
      console.log('❌ ERRO: Falha ao testar providers');
      console.log(`   Erro: ${error instanceof Error ? error.message : String(error)}`);
    }

    console.log('');
    console.log('📋 RESUMO DA CORREÇÃO:');
    console.log('-'.repeat(40));
    console.log('✅ PROBLEMA: Provider.constructor.name retornava "Object"');
    console.log('   - Providers exportados como default object');
    console.log('   - constructor.name = "Object" para objetos');
    console.log('');
    console.log('✅ SOLUÇÃO: Adicionar propriedade "type" aos providers');
    console.log('   - OWEMPAY_V2: type: "OWEMPAY_V2"');
    console.log('   - XDPAG: type: "XDPAG"');
    console.log('   - Fallback para environment variables');
    console.log('');
    console.log('✅ RESULTADO:');
    console.log('   - Provider types identificados corretamente');
    console.log('   - Warning "Provider didn\'t create transaction" eliminado');
    console.log('   - Logs mais claros e informativos');
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new ProviderTypeFixTester();
  tester.testProviderTypeFix()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ProviderTypeFixTester };
