#!/usr/bin/env tsx

/**
 * Simula o webhook XDPAG para testar se consegue encontrar a transação correta
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class XdpagWebhookSimulationTester {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async simulateWebhook(): Promise<void> {
    console.log('🔧 SIMULAÇÃO - WEBHOOK XDPAG');
    console.log('='.repeat(50));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar a transação mais recente que tem externalId
    const transaction = await db.transaction.findFirst({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        externalId: { not: null }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!transaction) {
      console.log('❌ Nenhuma transação com externalId encontrada');
      return;
    }

    console.log('📊 TRANSAÇÃO ENCONTRADA:');
    console.log(`   ID: ${transaction.id}`);
    console.log(`   External ID: ${transaction.externalId}`);
    console.log(`   Status: ${transaction.status}`);
    console.log(`   Valor: R$ ${transaction.amount.toFixed(2)}`);
    console.log('');

    // Simular o webhook XDPAG com os dados reais
    const webhookPayload = {
      type: "PAYOUT",
      data: {
        id: transaction.externalId, // Usar o externalId real
        externalId: transaction.id, // Usar o ID interno como externalId (como no webhook real)
        amount: transaction.amount.toString(),
        document: "**************",
        original_amount: transaction.amount.toString(),
        status: "FINISHED",
        endToEndId: "E33053580202509200522261151a8009",
        receipt: "https://api.xdpag.com/receipt/E33053580202509200522261151a8009/payout",
        fee: "0.00",
        metadata: {
          authCode: transaction.externalId,
          amount: transaction.amount.toString(),
          paymentDateTime: "2025-09-20T05:22:28.114+00:00",
          pixKey: "bfd515ce-16fb-48ac-bd37-9dc57333c7e9",
          receiveName: "ISMAEL MUNIZ DA COSTA TECNOLOGIA DA INFORMACAO LTDA",
          receiverName: "ISMAEL MUNIZ DA COSTA TECNOLOGIA DA INFORMACAO LTDA",
          receiverBankName: "********",
          receiverDocument: "**************",
          receiveAgency: "0000",
          receiveAccount: "0000",
          payerName: "TIGERPAY SOLUCAO EM INVESTIMENTOS LTDA",
          payerAgency: "0000",
          payerAccount: "0000",
          payerDocument: "**************",
          payerBankName: "TIGERPAY SOLUCAO EM INVESTIMENTOS LTDA",
          createdAt: "2025-09-20T05:22:25.000000Z",
          endToEnd: "E33053580202509200522261151a8009"
        },
        refunds: []
      }
    };

    console.log('🔄 SIMULANDO WEBHOOK XDPAG:');
    console.log(`   Transaction ID: ${webhookPayload.data.id}`);
    console.log(`   External ID: ${webhookPayload.data.externalId}`);
    console.log(`   Status: ${webhookPayload.data.status}`);
    console.log(`   Amount: R$ ${webhookPayload.data.amount}`);
    console.log(`   End-to-End ID: ${webhookPayload.data.endToEndId}`);
    console.log('');

    // Simular a busca que o webhook faz
    console.log('🔍 SIMULANDO BUSCA DO WEBHOOK:');
    console.log('-'.repeat(40));

    const { externalId, transactionId, endToEndId, amount } = webhookPayload.data;

    // Primeiro tenta pelo externalId (nosso ID interno)
    let foundTransaction = await db.transaction.findFirst({
      where: { externalId: externalId }
    });

    console.log(`1. Busca por externalId (${externalId}): ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
    if (foundTransaction) {
      console.log(`   ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
    }

    // Se não encontrou, tenta pelo transactionId
    if (!foundTransaction && transactionId) {
      foundTransaction = await db.transaction.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { externalId: transactionId }
          ]
        }
      });
      console.log(`2. Busca por transactionId (${transactionId}): ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
      if (foundTransaction) {
        console.log(`   ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
      }
    }

    // Se ainda não encontrou, tenta por múltiplos critérios
    if (!foundTransaction) {
      const criteria = [];
      
      if (externalId) {
        criteria.push({ externalId: externalId });
      }
      
      if (transactionId) {
        criteria.push({ id: transactionId });
      }
      
      if (endToEndId) {
        criteria.push({
          metadata: {
            path: ['endToEndId'],
            equals: endToEndId
          }
        });
      }
      
      if (amount) {
        criteria.push({ amount: parseFloat(amount) });
      }

      if (criteria.length > 0) {
        foundTransaction = await db.transaction.findFirst({
          where: {
            OR: criteria
          }
        });
        console.log(`3. Busca por múltiplos critérios: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
        if (foundTransaction) {
          console.log(`   ID: ${foundTransaction.id}, Status: ${foundTransaction.status}`);
          console.log(`   Critérios usados: ${criteria.length}`);
        }
      }
    }

    console.log('');
    console.log(`🎯 RESULTADO FINAL: ${foundTransaction ? '✅ ENCONTRADA' : '❌ NÃO ENCONTRADA'}`);

    if (foundTransaction) {
      console.log('');
      console.log('✅ SUCESSO: Webhook consegue encontrar a transação!');
      console.log('✅ O problema dos webhooks foi resolvido!');
      
      if (foundTransaction.id === transaction.id) {
        console.log('✅ Transação correta encontrada!');
      } else {
        console.log('⚠️ Transação diferente encontrada - pode haver duplicatas');
      }
    } else {
      console.log('');
      console.log('❌ PROBLEMA: Webhook não consegue encontrar a transação');
      console.log('❌ Ainda há problemas na lógica de busca');
    }
  }
}

// Executar simulação se chamado diretamente
if (require.main === module) {
  const tester = new XdpagWebhookSimulationTester();
  tester.simulateWebhook()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { XdpagWebhookSimulationTester };
