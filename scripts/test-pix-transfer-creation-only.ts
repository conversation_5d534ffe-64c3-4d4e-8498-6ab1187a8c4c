#!/usr/bin/env tsx

/**
 * Teste de Criação de Transferências PIX (SEND) - Apenas Criação
 * Testa 3 transferências simultâneas para validar idempotência na criação
 * Não processa o pagamento, apenas testa a criação das transações
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

interface TransferCreationResult {
  success: boolean;
  transactionId?: string;
  referenceCode?: string;
  endToEndId?: string;
  amount: number;
  pixKey: string;
  error?: string;
  responseTime: number;
}

class PixTransferCreationTester {
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private pixKey = 'bfd515ce-16fb-48ac-bd37-9dc57333c7e9';
  private baseUrl = 'http://localhost:3000';
  private testResults: TransferCreationResult[] = [];

  async runCreationTest(): Promise<void> {
    console.log('🚀 TESTE DE CRIAÇÃO - TRANSFERÊNCIAS PIX (SEND)');
    console.log('='.repeat(70));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log(`🔑 Chave PIX: ${this.pixKey}`);
    console.log(`💰 Valores: R$ 0,01 a R$ 0,05 (1-5 centavos)`);
    console.log(`⚡ Concorrência: 3 transferências simultâneas`);
    console.log(`🎯 Foco: Apenas criação de transações (não processamento)`);
    console.log('');

    // Limpar dados de teste anteriores
    await this.cleanupTestData();

    // Gerar 3 valores aleatórios entre 1-5 centavos (valores muito pequenos)
    const amounts = this.generateRandomAmounts(3, 1, 5);
    console.log(`💰 Valores gerados: ${amounts.map(a => `R$ ${a.toFixed(2)}`).join(', ')}`);
    console.log('');

    // Executar transferências simultâneas
    const startTime = Date.now();
    const promises = amounts.map((amount, index) => 
      this.createPixTransfer(amount, index + 1)
    );

    try {
      const results = await Promise.allSettled(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Processar resultados
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          this.testResults.push(result.value);
        } else {
          this.testResults.push({
            success: false,
            amount: amounts[index],
            pixKey: this.pixKey,
            error: result.reason?.message || 'Unknown error',
            responseTime: 0
          });
        }
      });

      // Analisar resultados
      this.analyzeResults(totalTime);

    } catch (error) {
      console.error('❌ Erro crítico no teste:', error);
    } finally {
      // Verificar dados no banco
      await this.verifyDatabaseData();
    }
  }

  /**
   * Gera valores aleatórios entre min e max centavos (retorna em reais decimais)
   */
  private generateRandomAmounts(count: number, minCents: number, maxCents: number): number[] {
    const amounts: number[] = [];
    for (let i = 0; i < count; i++) {
      const cents = Math.floor(Math.random() * (maxCents - minCents + 1)) + minCents;
      // Converter centavos para reais decimais (ex: 5 centavos = 0.05 reais)
      amounts.push(cents / 100);
    }
    return amounts;
  }

  /**
   * Cria uma transferência PIX via API
   */
  private async createPixTransfer(amount: number, testNumber: number): Promise<TransferCreationResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Teste ${testNumber}: Criando transferência de R$ ${amount.toFixed(2)}...`);

      const response = await fetch(`${this.baseUrl}/api/payments/transfers/pix`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify({
          amount: amount,
          organizationId: this.organizationId,
          pixKey: this.pixKey,
          pixKeyType: 'RANDOM',
          description: `Teste de criação ${testNumber} - Transferência PIX`
        })
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      
      console.log(`✅ Teste ${testNumber}: Transferência criada com sucesso`);
      console.log(`   ID: ${data.id}`);
      console.log(`   Código de Referência: ${data.referenceCode || 'N/A'}`);
      console.log(`   End-to-End ID: ${data.endToEndId || 'N/A'}`);
      console.log(`   Status: ${data.status}`);
      console.log(`   Tempo de resposta: ${responseTime}ms`);
      console.log('');

      return {
        success: true,
        transactionId: data.id,
        referenceCode: data.referenceCode,
        endToEndId: data.endToEndId,
        amount: amount,
        pixKey: this.pixKey,
        responseTime: responseTime
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.log(`❌ Teste ${testNumber}: FALHOU - ${error instanceof Error ? error.message : String(error)}`);
      console.log(`   Tempo de resposta: ${responseTime}ms`);
      console.log('');

      return {
        success: false,
        amount: amount,
        pixKey: this.pixKey,
        error: error instanceof Error ? error.message : String(error),
        responseTime: responseTime
      };
    }
  }

  /**
   * Analisa os resultados do teste
   */
  private analyzeResults(totalTime: number): void {
    console.log('📊 ANÁLISE DOS RESULTADOS');
    console.log('='.repeat(50));

    const successful = this.testResults.filter(r => r.success);
    const failed = this.testResults.filter(r => !r.success);
    const successRate = (successful.length / this.testResults.length) * 100;

    console.log(`✅ Transferências criadas com sucesso: ${successful.length}/${this.testResults.length} (${successRate.toFixed(1)}%)`);
    console.log(`❌ Transferências falharam: ${failed.length}`);
    console.log(`⏱️ Tempo total: ${totalTime}ms`);
    console.log(`⚡ Tempo médio por transferência: ${(totalTime / this.testResults.length).toFixed(0)}ms`);
    console.log('');

    if (successful.length > 0) {
      console.log('🔍 ANÁLISE DE UNICIDADE:');
      
      // Verificar IDs únicos
      const transactionIds = successful.map(r => r.transactionId).filter(Boolean);
      const referenceCodes = successful.map(r => r.referenceCode).filter(Boolean);
      const endToEndIds = successful.map(r => r.endToEndId).filter(Boolean);

      const uniqueTransactionIds = new Set(transactionIds);
      const uniqueReferenceCodes = new Set(referenceCodes);
      const uniqueEndToEndIds = new Set(endToEndIds);

      console.log(`   📋 IDs de Transação únicos: ${uniqueTransactionIds.size}/${transactionIds.length}`);
      console.log(`   🔗 Códigos de Referência únicos: ${uniqueReferenceCodes.size}/${referenceCodes.length}`);
      console.log(`   🎯 End-to-End IDs únicos: ${uniqueEndToEndIds.size}/${endToEndIds.length}`);

      // Verificar duplicatas
      const hasDuplicateTransactionIds = uniqueTransactionIds.size !== transactionIds.length;
      const hasDuplicateReferenceCodes = uniqueReferenceCodes.size !== referenceCodes.length;
      const hasDuplicateEndToEndIds = uniqueEndToEndIds.size !== endToEndIds.length;

      if (hasDuplicateTransactionIds || hasDuplicateReferenceCodes || hasDuplicateEndToEndIds) {
        console.log('⚠️ DUPLICATAS DETECTADAS:');
        if (hasDuplicateTransactionIds) console.log('   - IDs de Transação duplicados');
        if (hasDuplicateReferenceCodes) console.log('   - Códigos de Referência duplicados');
        if (hasDuplicateEndToEndIds) console.log('   - End-to-End IDs duplicados');
      } else {
        console.log('✅ NENHUMA DUPLICATA DETECTADA - Sistema seguro!');
      }

      console.log('');
      console.log('💰 VALORES DAS TRANSAÇÕES:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. R$ ${result.amount.toFixed(2)} - ${result.responseTime}ms`);
      });
    }

    if (failed.length > 0) {
      console.log('❌ FALHAS DETECTADAS:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. R$ ${result.amount.toFixed(2)} - ${result.error}`);
      });
    }

    console.log('');
    console.log('🛡️ AVALIAÇÃO DE SEGURANÇA:');
    if (successRate === 100 && successful.length > 0) {
      // Recriar as variáveis de unicidade para esta análise
      const transactionIds = successful.map(r => r.transactionId).filter(Boolean);
      const referenceCodes = successful.map(r => r.referenceCode).filter(Boolean);
      const endToEndIds = successful.map(r => r.endToEndId).filter(Boolean);
      
      const uniqueTransactionIds = new Set(transactionIds);
      const uniqueReferenceCodes = new Set(referenceCodes);
      const uniqueEndToEndIds = new Set(endToEndIds);
      
      const hasDuplicates = uniqueTransactionIds.size !== transactionIds.length || 
                           uniqueReferenceCodes.size !== referenceCodes.length || 
                           uniqueEndToEndIds.size !== endToEndIds.length;
      
      if (!hasDuplicates) {
        console.log('✅ SISTEMA SEGURO - Nenhuma duplicidade detectada');
        console.log('✅ Idempotência funcionando corretamente');
        console.log('✅ Criação de Transferências PIX (SEND) funcionando');
        console.log('✅ Sistema pronto para produção (criação de transações)');
      } else {
        console.log('⚠️ RISCO DETECTADO - Duplicatas encontradas');
        console.log('🚫 NÃO RECOMENDADO para produção');
      }
    } else {
      console.log('❌ SISTEMA INSTÁVEL - Falhas detectadas');
      console.log('🚫 NÃO RECOMENDADO para produção');
    }
  }

  /**
   * Verifica dados no banco de dados
   */
  private async verifyDatabaseData(): Promise<void> {
    console.log('🔍 VERIFICAÇÃO NO BANCO DE DADOS');
    console.log('-'.repeat(40));

    try {
      // Buscar transações de teste criadas
      const transactions = await db.transaction.findMany({
        where: {
          organizationId: this.organizationId,
          type: 'SEND',
          createdAt: {
            gte: new Date(Date.now() - 5 * 60 * 1000) // Últimos 5 minutos
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      });

      console.log(`📊 Transações encontradas: ${transactions.length}`);
      
      if (transactions.length > 0) {
        console.log('📋 DETALHES DAS TRANSAÇÕES:');
        transactions.forEach((tx, index) => {
          console.log(`   ${index + 1}. ID: ${tx.id}`);
          console.log(`      Valor: R$ ${tx.amount.toFixed(2)}`);
          console.log(`      Status: ${tx.status}`);
          console.log(`      Código Ref: ${tx.referenceCode || 'N/A'}`);
          console.log(`      End-to-End: ${tx.endToEndId || 'N/A'}`);
          console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
          console.log('');
        });

        // Verificar duplicatas no banco
        const referenceCodes = transactions.map(tx => tx.referenceCode).filter(Boolean);
        const endToEndIds = transactions.map(tx => tx.endToEndId).filter(Boolean);

        const uniqueRefCodes = new Set(referenceCodes);
        const uniqueEndToEndIds = new Set(endToEndIds);

        const hasDuplicateRefCodes = uniqueRefCodes.size !== referenceCodes.length;
        const hasDuplicateEndToEndIds = uniqueEndToEndIds.size !== endToEndIds.length;

        if (hasDuplicateRefCodes || hasDuplicateEndToEndIds) {
          console.log('⚠️ DUPLICATAS NO BANCO DE DADOS:');
          if (hasDuplicateRefCodes) console.log('   - Códigos de Referência duplicados');
          if (hasDuplicateEndToEndIds) console.log('   - End-to-End IDs duplicados');
        } else {
          console.log('✅ NENHUMA DUPLICATA NO BANCO - Dados consistentes');
        }
      }

    } catch (error) {
      console.log(`❌ Erro ao verificar banco de dados: ${error}`);
    }
  }

  /**
   * Limpa dados de teste anteriores
   */
  private async cleanupTestData(): Promise<void> {
    try {
      console.log('🧹 Limpando dados de teste anteriores...');
      
      // Deletar transações de teste antigas
      await db.transaction.deleteMany({
        where: {
          organizationId: this.organizationId,
          type: 'SEND',
          description: {
            contains: 'Teste de criação'
          }
        }
      });

      console.log('✅ Limpeza concluída');
    } catch (error) {
      console.log(`⚠️ Aviso na limpeza: ${error}`);
    }
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new PixTransferCreationTester();
  tester.runCreationTest()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { PixTransferCreationTester };
