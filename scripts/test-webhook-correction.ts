#!/usr/bin/env tsx

/**
 * Teste da correção do webhook XDPAG
 * Simula o webhook com os campos invertidos e verifica se encontra a transação
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class WebhookCorrectionTester {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async testWebhookCorrection(): Promise<void> {
    console.log('🔧 TESTE - CORREÇÃO DO WEBHOOK XDPAG');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar uma transação recente que tem externalId
    const transaction = await db.transaction.findFirst({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        externalId: { not: null }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!transaction) {
      console.log('❌ Nenhuma transação com externalId encontrada');
      return;
    }

    console.log('📊 TRANSAÇÃO DE TESTE:');
    console.log(`   ID: ${transaction.id}`);
    console.log(`   External ID: ${transaction.externalId}`);
    console.log(`   Status: ${transaction.status}`);
    console.log(`   Amount: R$ ${transaction.amount.toFixed(2)}`);
    console.log('');

    // Simular o webhook XDPAG com campos invertidos (como está chegando)
    const webhookData = {
      // Campos como chegam no webhook (INVERTIDOS)
      originalTransactionId: transaction.externalId, // ID do XDPAG
      originalExternalId: transaction.id, // Nosso ID interno

      // Campos corrigidos (como deveria ser)
      correctedTransactionId: transaction.id, // Nosso ID interno
      correctedExternalId: transaction.externalId, // ID do XDPAG

      amount: transaction.amount,
      status: 'FINISHED'
    };

    console.log('🔄 SIMULANDO WEBHOOK XDPAG (CAMPOS INVERTIDOS):');
    console.log(`   Original Transaction ID: ${webhookData.originalTransactionId} (ID do XDPAG)`);
    console.log(`   Original External ID: ${webhookData.originalExternalId} (Nosso ID interno)`);
    console.log('');
    console.log('🔧 APÓS CORREÇÃO:');
    console.log(`   Corrected Transaction ID: ${webhookData.correctedTransactionId} (Nosso ID interno)`);
    console.log(`   Corrected External ID: ${webhookData.correctedExternalId} (ID do XDPAG)`);
    console.log('');

    // Testar a lógica corrigida
    console.log('🔍 TESTANDO LÓGICA CORRIGIDA:');
    console.log('-'.repeat(40));

    // 1. Busca por ID interno (correctedTransactionId) - PRIORIDADE MÁXIMA
    console.log('1️⃣ BUSCA POR ID INTERNO (PRIORIDADE MÁXIMA):');
    let foundTransaction = await db.transaction.findFirst({
      where: { id: webhookData.correctedTransactionId }
    });

    console.log(`   Critério: id = "${webhookData.correctedTransactionId}"`);
    console.log(`   Resultado: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

    if (foundTransaction) {
      console.log(`   ✅ ID: ${foundTransaction.id}`);
      console.log(`   ✅ External ID: ${foundTransaction.externalId}`);
      console.log(`   ✅ Status: ${foundTransaction.status}`);
      console.log(`   ✅ Amount: R$ ${foundTransaction.amount.toFixed(2)}`);
      console.log(`   ✅ É a transação correta: ${foundTransaction.id === transaction.id ? 'SIM' : 'NÃO'}`);
    }
    console.log('');

    // 2. Se não encontrou, busca por externalId (ID do XDPAG)
    if (!foundTransaction) {
      console.log('2️⃣ BUSCA POR EXTERNAL ID (ID DO XDPAG):');
      foundTransaction = await db.transaction.findFirst({
        where: { externalId: webhookData.correctedExternalId }
      });

      console.log(`   Critério: externalId = "${webhookData.correctedExternalId}"`);
      console.log(`   Resultado: ${foundTransaction ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);

      if (foundTransaction) {
        console.log(`   ✅ ID: ${foundTransaction.id}`);
        console.log(`   ✅ External ID: ${foundTransaction.externalId}`);
        console.log(`   ✅ Status: ${foundTransaction.status}`);
        console.log(`   ✅ Amount: R$ ${foundTransaction.amount.toFixed(2)}`);
        console.log(`   ✅ É a transação correta: ${foundTransaction.id === transaction.id ? 'SIM' : 'NÃO'}`);
      }
      console.log('');
    }

    // 3. Resultado final
    console.log('🎯 RESULTADO FINAL:');
    if (foundTransaction) {
      if (foundTransaction.id === transaction.id) {
        console.log('✅ SUCESSO: Transação correta encontrada!');
        console.log('✅ A correção do webhook está funcionando!');
        console.log('✅ O webhook XDPAG vai funcionar corretamente!');
      } else {
        console.log('⚠️ ATENÇÃO: Transação diferente encontrada');
        console.log(`   Esperada: ${transaction.id}`);
        console.log(`   Encontrada: ${foundTransaction.id}`);
        console.log('❌ Ainda há problemas na lógica de busca');
      }
    } else {
      console.log('❌ FALHA: Nenhuma transação encontrada');
      console.log('❌ A lógica de busca não está funcionando');
    }

    console.log('');
    console.log('📋 RESUMO DA CORREÇÃO:');
    console.log('-'.repeat(40));
    console.log('❌ PROBLEMA: Webhook XDPAG envia campos invertidos');
    console.log('   - externalId no webhook = nosso ID interno');
    console.log('   - id no webhook = ID do XDPAG');
    console.log('');
    console.log('✅ SOLUÇÃO: Corrigir campos antes de buscar');
    console.log('   - correctedTransactionId = externalId do webhook');
    console.log('   - correctedExternalId = id do webhook');
    console.log('   - Buscar por ID interno primeiro (mais confiável)');
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new WebhookCorrectionTester();
  tester.testWebhookCorrection()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { WebhookCorrectionTester };
