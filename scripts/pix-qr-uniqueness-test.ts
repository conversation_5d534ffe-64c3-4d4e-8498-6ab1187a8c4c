#!/usr/bin/env tsx

/**
 * PIX QR Code Uniqueness and Financial Safety Test
 * Validates that PIX QR codes are unique under concurrent load and no financial losses occur
 */

import { ChargeTransactionService } from '@repo/utils/src/charge-transaction-service';
import { createTransactionSafely } from '@repo/utils/src/transaction-service-wrapper';
import { db } from '@repo/database';
import { logger } from '@repo/logs';

interface PixQRTestResult {
  testName: string;
  concurrentRequests: number;
  successfulTransactions: number;
  uniquePixCodes: number;
  uniqueQRImages: number;
  uniqueReferenceCodes: number;
  duplicatePixCodes: number;
  averageResponseTime: number;
  errors: string[];
  financialSafety: {
    totalAmountRequested: number;
    totalAmountCreated: number;
    duplicateCharges: number;
  };
  passed: boolean;
}

class PixQRUniquenessTest {
  private results: PixQRTestResult[] = [];
  private testOrganizationId = 'test-pix-qr-org';

  async runAllTests(): Promise<void> {
    console.log('🎯 Starting PIX QR Code Uniqueness and Financial Safety Tests\n');

    // Test 1: Concurrent identical CHARGE requests (should create unique PIX codes)
    await this.testConcurrentIdenticalChargeRequests();

    // Test 2: Concurrent different amount requests
    await this.testConcurrentDifferentAmountRequests();

    // Test 3: High-volume concurrent requests
    await this.testHighVolumeConcurrentRequests();

    // Test 4: Transaction wrapper PIX generation
    await this.testTransactionWrapperPixGeneration();

    this.printResults();
    await this.validateDatabaseConsistency();
  }

  /**
   * Test 1: Concurrent identical CHARGE requests should create unique PIX codes
   */
  private async testConcurrentIdenticalChargeRequests(): Promise<void> {
    const testName = 'Concurrent Identical CHARGE Requests';
    const concurrentRequests = 25;
    const amount = 150.75;
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName} - ${concurrentRequests} concurrent requests...`);

    const errors: string[] = [];
    const responseTimes: number[] = [];
    const transactions: any[] = [];
    const pixCodes: string[] = [];
    const qrImages: string[] = [];
    const referenceCodes: string[] = [];

    try {
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const requestStart = Date.now();
        
        try {
          // Create transaction
          const result = await ChargeTransactionService.createChargeTransaction({
            customerEmail: `${customerEmail.split('@')[0]}+${index}@${customerEmail.split('@')[1]}`,
            customerName: `PIX Test ${index}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `PIX QR test ${index}`,
            allowMultiple: true
          });

          // Simulate PIX QR code generation (like real providers do)
          const pixData = {
            pixCode: `00020126580014br.gov.bcb.pix0136${result.id}${Date.now()}520400005303986540${amount.toFixed(2)}5802BR5925PIX Test ${index}6009SAO PAULO62070503***6304`,
            pixQrCode: `data:image/png;base64,${Buffer.from(`qr_${result.id}_${Date.now()}_${index}`).toString('base64')}`,
            pixExpiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          };

          // Update transaction with PIX data (simulating real flow)
          await ChargeTransactionService.updateChargeWithPixData(result.id, pixData);

          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          transactions.push({ ...result, ...pixData });
          pixCodes.push(pixData.pixCode);
          qrImages.push(pixData.pixQrCode);
          referenceCodes.push(result.referenceCode);

          return { ...result, ...pixData };
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          errors.push(`Request ${index}: ${error instanceof Error ? error.message : error}`);
          return null;
        }
      });

      const results = await Promise.all(promises);
      const successfulTransactions = results.filter(r => r !== null).length;
      const uniquePixCodes = new Set(pixCodes).size;
      const uniqueQRImages = new Set(qrImages).size;
      const uniqueReferenceCodes = new Set(referenceCodes).size;
      const duplicatePixCodes = pixCodes.length - uniquePixCodes;

      // Financial safety calculations
      const totalAmountRequested = concurrentRequests * amount;
      const totalAmountCreated = transactions.reduce((sum, t) => sum + (t?.amount || 0), 0);
      const duplicateCharges = Math.max(0, transactions.length - concurrentRequests);

      this.results.push({
        testName,
        concurrentRequests,
        successfulTransactions,
        uniquePixCodes,
        uniqueQRImages,
        uniqueReferenceCodes,
        duplicatePixCodes,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        errors,
        financialSafety: {
          totalAmountRequested,
          totalAmountCreated,
          duplicateCharges
        },
        passed: successfulTransactions === concurrentRequests && 
                uniquePixCodes === concurrentRequests && 
                uniqueQRImages === concurrentRequests &&
                duplicatePixCodes === 0
      });

      console.log(`✅ ${testName}: ${successfulTransactions}/${concurrentRequests} successful`);
      console.log(`   Unique PIX codes: ${uniquePixCodes}, Unique QR images: ${uniqueQRImages}`);
      console.log(`   Duplicate PIX codes: ${duplicatePixCodes}`);

    } catch (error) {
      console.log(`❌ ${testName}: Failed - ${error}`);
      this.results.push({
        testName,
        concurrentRequests,
        successfulTransactions: 0,
        uniquePixCodes: 0,
        uniqueQRImages: 0,
        uniqueReferenceCodes: 0,
        duplicatePixCodes: 0,
        averageResponseTime: 0,
        errors: [error instanceof Error ? error.message : String(error)],
        financialSafety: {
          totalAmountRequested: 0,
          totalAmountCreated: 0,
          duplicateCharges: 0
        },
        passed: false
      });
    }
  }

  /**
   * Test 2: Concurrent different amount requests
   */
  private async testConcurrentDifferentAmountRequests(): Promise<void> {
    const testName = 'Concurrent Different Amount Requests';
    const concurrentRequests = 30;

    console.log(`🔄 ${testName} - ${concurrentRequests} concurrent requests...`);

    const errors: string[] = [];
    const responseTimes: number[] = [];
    const transactions: any[] = [];
    const pixCodes: string[] = [];
    const qrImages: string[] = [];
    let totalAmountRequested = 0;

    try {
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const requestStart = Date.now();
        const amount = 50 + (index * 5.25); // Different amounts: 50, 55.25, 60.50, etc.
        totalAmountRequested += amount;
        
        try {
          const result = await ChargeTransactionService.createChargeTransaction({
            customerEmail: `pix-different-${index}@test.com`,
            customerName: `PIX Different Test ${index}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `PIX different amount test ${index}`,
            allowMultiple: true
          });

          // Generate unique PIX code based on amount and transaction ID
          const pixData = {
            pixCode: `00020126580014br.gov.bcb.pix0136${result.id}${Date.now()}520400005303986540${amount.toFixed(2)}5802BR5925PIX Different Test ${index}6009SAO PAULO62070503***6304`,
            pixQrCode: `data:image/png;base64,${Buffer.from(`qr_${result.id}_${amount}_${Date.now()}`).toString('base64')}`,
            pixExpiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          };

          await ChargeTransactionService.updateChargeWithPixData(result.id, pixData);

          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          transactions.push({ ...result, ...pixData });
          pixCodes.push(pixData.pixCode);
          qrImages.push(pixData.pixQrCode);

          return { ...result, ...pixData };
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          errors.push(`Request ${index}: ${error instanceof Error ? error.message : error}`);
          return null;
        }
      });

      const results = await Promise.all(promises);
      const successfulTransactions = results.filter(r => r !== null).length;
      const uniquePixCodes = new Set(pixCodes).size;
      const uniqueQRImages = new Set(qrImages).size;
      const totalAmountCreated = transactions.reduce((sum, t) => sum + (t?.amount || 0), 0);

      this.results.push({
        testName,
        concurrentRequests,
        successfulTransactions,
        uniquePixCodes,
        uniqueQRImages,
        uniqueReferenceCodes: new Set(transactions.map(t => t.referenceCode)).size,
        duplicatePixCodes: pixCodes.length - uniquePixCodes,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        errors,
        financialSafety: {
          totalAmountRequested,
          totalAmountCreated,
          duplicateCharges: 0
        },
        passed: successfulTransactions === concurrentRequests && 
                uniquePixCodes === concurrentRequests && 
                Math.abs(totalAmountCreated - totalAmountRequested) < 0.01
      });

      console.log(`✅ ${testName}: ${successfulTransactions}/${concurrentRequests} successful`);
      console.log(`   Amount accuracy: $${Math.abs(totalAmountCreated - totalAmountRequested).toFixed(2)} discrepancy`);

    } catch (error) {
      console.log(`❌ ${testName}: Failed - ${error}`);
    }
  }

  /**
   * Test 3: High-volume concurrent requests
   */
  private async testHighVolumeConcurrentRequests(): Promise<void> {
    const testName = 'High-Volume Concurrent Requests';
    const concurrentRequests = 100;
    const amount = 25.99;

    console.log(`🔄 ${testName} - ${concurrentRequests} concurrent requests...`);

    const errors: string[] = [];
    const responseTimes: number[] = [];
    const pixCodes: string[] = [];
    const referenceCodes: string[] = [];

    try {
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const requestStart = Date.now();
        
        try {
          const result = await createTransactionSafely({
            customerEmail: `high-volume-${index}@test.com`,
            customerName: `High Volume Test ${index}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `High volume PIX test ${index}`,
            type: 'CHARGE'
          });

          // Simulate PIX generation
          const pixCode = `00020126580014br.gov.bcb.pix0136${result.id}${Date.now()}${index}520400005303986540${amount.toFixed(2)}5802BR6304`;
          
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          pixCodes.push(pixCode);
          referenceCodes.push(result.referenceCode);

          return result;
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          errors.push(`Request ${index}: ${error instanceof Error ? error.message : error}`);
          return null;
        }
      });

      const results = await Promise.all(promises);
      const successfulTransactions = results.filter(r => r !== null).length;
      const uniquePixCodes = new Set(pixCodes).size;
      const uniqueReferenceCodes = new Set(referenceCodes).size;

      this.results.push({
        testName,
        concurrentRequests,
        successfulTransactions,
        uniquePixCodes,
        uniqueQRImages: uniquePixCodes, // Assume 1:1 mapping
        uniqueReferenceCodes,
        duplicatePixCodes: pixCodes.length - uniquePixCodes,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        errors,
        financialSafety: {
          totalAmountRequested: concurrentRequests * amount,
          totalAmountCreated: successfulTransactions * amount,
          duplicateCharges: 0
        },
        passed: successfulTransactions === concurrentRequests && 
                uniquePixCodes === concurrentRequests &&
                uniqueReferenceCodes === concurrentRequests
      });

      console.log(`✅ ${testName}: ${successfulTransactions}/${concurrentRequests} successful`);
      console.log(`   Performance: ${responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length}ms avg`);

    } catch (error) {
      console.log(`❌ ${testName}: Failed - ${error}`);
    }
  }

  /**
   * Test 4: Transaction wrapper PIX generation
   */
  private async testTransactionWrapperPixGeneration(): Promise<void> {
    const testName = 'Transaction Wrapper PIX Generation';
    const concurrentRequests = 20;
    const amount = 99.99;

    console.log(`🔄 ${testName} - ${concurrentRequests} concurrent requests...`);

    const errors: string[] = [];
    const responseTimes: number[] = [];
    const usedNewService: boolean[] = [];
    const referenceCodes: string[] = [];

    try {
      const promises = Array.from({ length: concurrentRequests }, async (_, index) => {
        const requestStart = Date.now();
        
        try {
          const result = await createTransactionSafely({
            customerEmail: `wrapper-pix-${index}@test.com`,
            customerName: `Wrapper PIX Test ${index}`,
            amount,
            organizationId: this.testOrganizationId,
            description: `Wrapper PIX test ${index}`,
            type: 'CHARGE'
          });

          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          usedNewService.push(result.usedNewService);
          referenceCodes.push(result.referenceCode);

          return result;
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          errors.push(`Request ${index}: ${error instanceof Error ? error.message : error}`);
          return null;
        }
      });

      const results = await Promise.all(promises);
      const successfulTransactions = results.filter(r => r !== null).length;
      const uniqueReferenceCodes = new Set(referenceCodes).size;
      const newServiceUsage = usedNewService.filter(Boolean).length;

      this.results.push({
        testName,
        concurrentRequests,
        successfulTransactions,
        uniquePixCodes: uniqueReferenceCodes, // Proxy for PIX uniqueness
        uniqueQRImages: uniqueReferenceCodes,
        uniqueReferenceCodes,
        duplicatePixCodes: 0,
        averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
        errors,
        financialSafety: {
          totalAmountRequested: concurrentRequests * amount,
          totalAmountCreated: successfulTransactions * amount,
          duplicateCharges: 0
        },
        passed: successfulTransactions === concurrentRequests && uniqueReferenceCodes === concurrentRequests
      });

      console.log(`✅ ${testName}: ${successfulTransactions}/${concurrentRequests} successful`);
      console.log(`   New service usage: ${newServiceUsage}/${concurrentRequests}`);

    } catch (error) {
      console.log(`❌ ${testName}: Failed - ${error}`);
    }
  }

  /**
   * Print comprehensive test results
   */
  private printResults(): void {
    console.log('\n🎯 PIX QR CODE UNIQUENESS TEST RESULTS');
    console.log('='.repeat(70));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const totalRequests = this.results.reduce((sum, r) => sum + r.concurrentRequests, 0);
    const totalSuccessful = this.results.reduce((sum, r) => sum + r.successfulTransactions, 0);
    const totalDuplicatePixCodes = this.results.reduce((sum, r) => sum + r.duplicatePixCodes, 0);

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Total Requests: ${totalRequests}`);
    console.log(`Successful: ${totalSuccessful}`);
    console.log(`Duplicate PIX Codes: ${totalDuplicatePixCodes}`);

    console.log('\n📋 DETAILED RESULTS');
    console.log('-'.repeat(70));

    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      console.log(`   Requests: ${result.concurrentRequests}, Success: ${result.successfulTransactions}`);
      console.log(`   Unique PIX: ${result.uniquePixCodes}, Unique QR: ${result.uniqueQRImages}`);
      console.log(`   Duplicate PIX: ${result.duplicatePixCodes}`);
      console.log(`   Avg Response: ${result.averageResponseTime.toFixed(0)}ms`);
      console.log(`   Financial: $${result.financialSafety.totalAmountRequested.toFixed(2)} → $${result.financialSafety.totalAmountCreated.toFixed(2)}`);
      
      if (result.errors.length > 0) {
        console.log(`   Errors: ${result.errors.length}`);
      }
      console.log('');
    });

    if (passedTests === totalTests && totalDuplicatePixCodes === 0) {
      console.log('🎉 ALL PIX QR CODE UNIQUENESS TESTS PASSED!');
      console.log('✅ PIX codes are unique under concurrent load');
      console.log('✅ No financial safety issues detected');
    } else {
      console.log('❌ SOME TESTS FAILED - Review PIX generation logic');
    }
  }

  /**
   * Validate database consistency after tests
   */
  private async validateDatabaseConsistency(): Promise<void> {
    console.log('\n🔍 DATABASE CONSISTENCY VALIDATION');
    console.log('='.repeat(50));

    try {
      // Check for duplicate reference codes
      const duplicateRefCodes = await db.$queryRaw<Array<{ referenceCode: string, count: number }>>`
        SELECT "referenceCode", COUNT(*) as count
        FROM "transaction"
        WHERE "organizationId" = ${this.testOrganizationId}
        GROUP BY "referenceCode"
        HAVING COUNT(*) > 1
      `;

      // Check for null reference codes
      const nullRefCodes = await db.transaction.count({
        where: {
          organizationId: this.testOrganizationId,
          referenceCode: null
        }
      });

      // Check total test transactions
      const totalTestTransactions = await db.transaction.count({
        where: {
          organizationId: this.testOrganizationId
        }
      });

      console.log(`Total test transactions: ${totalTestTransactions}`);
      console.log(`Duplicate reference codes: ${duplicateRefCodes.length}`);
      console.log(`Null reference codes: ${nullRefCodes}`);

      if (duplicateRefCodes.length === 0 && nullRefCodes === 0) {
        console.log('✅ Database consistency validated - No issues found');
      } else {
        console.log('❌ Database consistency issues detected');
        if (duplicateRefCodes.length > 0) {
          console.log(`   Duplicate reference codes found: ${duplicateRefCodes.map(d => d.referenceCode).join(', ')}`);
        }
      }

    } catch (error) {
      console.log(`❌ Database validation failed: ${error}`);
    }
  }
}

// Run tests if script is called directly
if (require.main === module) {
  const tester = new PixQRUniquenessTest();
  tester.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { PixQRUniquenessTest };
