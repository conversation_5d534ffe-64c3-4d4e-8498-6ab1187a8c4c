#!/usr/bin/env tsx

/**
 * Script para verificar configuração de taxas
 * Identifica por que as taxas não estão sendo aplicadas
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '../packages/database';

async function checkFeeConfiguration() {
  console.log('🔍 VERIFICANDO CONFIGURAÇÃO DE TAXAS');
  console.log('='.repeat(60));
  
  const organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  
  try {
    // 1. Verificar taxas da organização
    console.log('\n📊 1. VERIFICANDO TAXAS DA ORGANIZAÇÃO...');
    const orgTaxes = await db.organization_taxes.findUnique({
      where: { organizationId }
    });
    
    if (orgTaxes) {
      console.log('✅ Taxas da organização encontradas:');
      console.log(`   PIX Charge %: ${orgTaxes.pixChargePercentFee}%`);
      console.log(`   PIX Charge Fixa: R$ ${orgTaxes.pixChargeFixedFee}`);
      console.log(`   PIX Transfer %: ${orgTaxes.pixTransferPercentFee}%`);
      console.log(`   PIX Transfer Fixa: R$ ${orgTaxes.pixTransferFixedFee}`);
    } else {
      console.log('❌ Nenhuma configuração de taxas encontrada para a organização');
    }
    
    // 2. Verificar gateways ativos
    console.log('\n🏦 2. VERIFICANDO GATEWAYS ATIVOS...');
    const gateways = await db.payment_gateway.findMany({
      where: { isActive: true },
      select: {
        id: true,
        type: true,
        name: true,
        pixChargePercentFee: true,
        pixChargeFixedFee: true,
        isGlobal: true,
        isDefault: true,
        isActive: true
      }
    });
    
    console.log(`Total de gateways ativos: ${gateways.length}`);
    gateways.forEach((gateway, index) => {
      console.log(`   ${index + 1}. ${gateway.name} (${gateway.type})`);
      console.log(`      PIX Charge %: ${gateway.pixChargePercentFee}%`);
      console.log(`      PIX Charge Fixa: R$ ${gateway.pixChargeFixedFee}`);
      console.log(`      Global: ${gateway.isGlobal}, Padrão: ${gateway.isDefault}`);
    });
    
    // 3. Verificar gateway padrão
    console.log('\n⭐ 3. VERIFICANDO GATEWAY PADRÃO...');
    const defaultGateway = await db.payment_gateway.findFirst({
      where: { isDefault: true, isActive: true }
    });
    
    if (defaultGateway) {
      console.log('✅ Gateway padrão encontrado:');
      console.log(`   Nome: ${defaultGateway.name}`);
      console.log(`   Tipo: ${defaultGateway.type}`);
      console.log(`   PIX Charge %: ${defaultGateway.pixChargePercentFee}%`);
      console.log(`   PIX Charge Fixa: R$ ${defaultGateway.pixChargeFixedFee}`);
    } else {
      console.log('❌ Nenhum gateway padrão encontrado');
    }
    
    // 4. Verificar transações recentes
    console.log('\n💳 4. VERIFICANDO TRANSAÇÕES RECENTES...');
    const recentTransactions = await db.transaction.findMany({
      where: { 
        organizationId,
        createdAt: { gte: new Date(Date.now() - 10 * 60 * 1000) } // últimos 10 minutos
      },
      select: {
        id: true,
        amount: true,
        percentFee: true,
        fixedFee: true,
        totalFee: true,
        netAmount: true,
        status: true,
        createdAt: true,
        gatewayId: true,
        gatewayName: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    console.log(`Transações recentes (últimos 10 min): ${recentTransactions.length}`);
    recentTransactions.forEach((tx, index) => {
      console.log(`   ${index + 1}. ID: ${tx.id}`);
      console.log(`      Valor: R$ ${tx.amount}`);
      console.log(`      Taxa %: ${tx.percentFee || 'N/A'}`);
      console.log(`      Taxa Fixa: R$ ${tx.fixedFee || 'N/A'}`);
      console.log(`      Taxa Total: R$ ${tx.totalFee || 'N/A'}`);
      console.log(`      Valor Líquido: R$ ${tx.netAmount || 'N/A'}`);
      console.log(`      Status: ${tx.status}`);
      console.log(`      Gateway: ${tx.gatewayName || 'N/A'}`);
      console.log(`      Criado: ${tx.createdAt.toISOString()}`);
      console.log('');
    });
    
    // 5. Análise do problema
    console.log('\n🔍 5. ANÁLISE DO PROBLEMA...');
    
    if (!orgTaxes && gateways.length === 0) {
      console.log('❌ PROBLEMA IDENTIFICADO: Nenhuma configuração de taxas encontrada');
      console.log('   Solução: Configurar taxas para a organização ou criar gateway padrão');
    } else if (!orgTaxes && gateways.length > 0) {
      console.log('⚠️ PROBLEMA IDENTIFICADO: Taxas da organização não configuradas');
      console.log('   Solução: Sistema deve usar taxas do gateway, mas pode não estar funcionando');
    } else if (orgTaxes && recentTransactions.length > 0 && recentTransactions.every(tx => !tx.totalFee)) {
      console.log('❌ PROBLEMA IDENTIFICADO: Taxas configuradas mas não aplicadas');
      console.log('   Solução: Verificar código de processamento de taxas');
    } else {
      console.log('✅ Configuração parece correta, investigar logs de erro');
    }
    
    // 6. Recomendações
    console.log('\n💡 6. RECOMENDAÇÕES...');
    
    if (!orgTaxes) {
      console.log('1. Configurar taxas para a organização:');
      console.log('   INSERT INTO organization_taxes (organizationId, pixChargePercentFee, pixChargeFixedFee)');
      console.log(`   VALUES ('${organizationId}', 2.0, 0.50);`);
    }
    
    if (gateways.length === 0) {
      console.log('2. Criar gateway padrão:');
      console.log('   INSERT INTO payment_gateway (name, type, isDefault, isActive, pixChargePercentFee, pixChargeFixedFee)');
      console.log('   VALUES (\'Gateway Padrão\', \'MOCKSIM\', true, true, 2.0, 0.50);');
    }
    
    console.log('3. Verificar logs da aplicação para erros no processamento de taxas');
    console.log('4. Testar processamento de taxas manualmente');
    
  } catch (error) {
    console.error('❌ Erro ao verificar configuração:', error);
  }
}

if (require.main === module) {
  checkFeeConfiguration()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { checkFeeConfiguration };
