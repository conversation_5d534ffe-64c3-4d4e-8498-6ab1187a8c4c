#!/usr/bin/env tsx

/**
 * Teste usando API real do projeto (porta 3000)
 * Cria transações CHARGE (cobranças PIX) via HTTP requests
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

interface ApiTestResult {
  testName: string;
  successful: boolean;
  transactionId?: string;
  referenceCode?: string;
  amount: number;
  pixKey: string;
  duration: number;
  error?: string;
  responseData?: any;
  statusCode?: number;
}

class ApiPixChargeTest {
  private baseUrl = 'http://localhost:3000';
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private results: ApiTestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🌐 TESTE VIA API REAL - TRANSAÇÕES CHARGE (CRIAÇÃO DE COBRANÇA PIX)');
    console.log('='.repeat(80));
    console.log(`API URL: ${this.baseUrl}`);
    console.log(`Organization ID: ${this.organizationId}`);
    console.log(`API Key: ${this.apiKey.substring(0, 10)}...`);
    console.log('');

    // Verificar se API está rodando
    await this.checkApiHealth();

    // Teste 1: Criação de cobrança PIX via API
    await this.testCreatePixChargeViaApi();

    // Teste 2: Teste de duplicação via API
    await this.testDuplicatePreventionViaApi();

    // Teste 3: Listar transações via API
    await this.testListTransactionsViaApi();

    // Teste 4: Teste com diferentes valores via API
    await this.testDifferentAmountsViaApi();

    this.printResults();
  }

  /**
   * Verificar se API está rodando
   */
  private async checkApiHealth(): Promise<void> {
    console.log('🔍 Verificando se API está rodando...');

    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API está rodando: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data)}`);
      } else {
        console.log(`⚠️ API respondeu com status: ${response.status}`);
      }

    } catch (error) {
      console.log(`❌ API não está rodando: ${error instanceof Error ? error.message : error}`);
      console.log('   Certifique-se de que o projeto está rodando na porta 3000');
    }

    console.log('');
  }

  /**
   * Teste 1: Criação de cobrança PIX via API
   */
  private async testCreatePixChargeViaApi(): Promise<void> {
    const testName = 'Criação de Cobrança PIX via API';
    const amount = 75.50;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      const requestBody = {
        amount,
        customerName: 'Teste API PIX',
        customerEmail: '<EMAIL>',
        customerPhone: '11999887766',
        customerDocument: '12345678901',
        customerDocumentType: 'cpf',
        description: 'Teste de criação de cobrança PIX via API',
        organizationId: this.organizationId,
        metadata: {
          testType: 'api_charge_test'
        }
      };

      const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (response.ok) {
        this.results.push({
          testName,
          successful: true,
          transactionId: responseData.id,
          referenceCode: responseData.referenceCode,
          amount,
          pixKey,
          duration,
          responseData,
          statusCode: response.status
        });

        console.log(`✅ ${testName}: SUCESSO`);
        console.log(`   Status: ${response.status}`);
        console.log(`   ID: ${responseData.id}`);
        console.log(`   Reference Code: ${responseData.referenceCode}`);
        console.log(`   Amount: R$ ${amount}`);
        console.log(`   PIX Key: ${pixKey}`);
        console.log(`   Duration: ${duration}ms`);
        console.log(`   Response: ${JSON.stringify(responseData, null, 2)}`);

      } else {
        this.results.push({
          testName,
          successful: false,
          amount,
          pixKey,
          duration,
          error: `API Error ${response.status}: ${responseData.message || 'Unknown error'}`,
          responseData,
          statusCode: response.status
        });

        console.log(`❌ ${testName}: FALHOU`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${responseData.message || 'Unknown error'}`);
        console.log(`   Response: ${JSON.stringify(responseData, null, 2)}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 2: Teste de duplicação via API
   */
  private async testDuplicatePreventionViaApi(): Promise<void> {
    const testName = 'Prevenção de Duplicação via API';
    const amount = 125.00;
    const pixKey = '11999887766';
    const customerEmail = '<EMAIL>';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();

    try {
      const requestBody = {
        amount,
        customerName: 'Teste Duplicação API',
        customerEmail: '<EMAIL>',
        customerPhone: '11999887766',
        customerDocument: '12345678901',
        customerDocumentType: 'cpf',
        description: 'Teste de duplicação via API',
        organizationId: this.organizationId,
        metadata: {
          testType: 'duplicate_test'
        }
      };

      // Primeira tentativa
      const response1 = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const responseData1 = await response1.json();
      console.log(`   Primeira tentativa: Status ${response1.status}, ID ${responseData1.id}`);

      // Segunda tentativa (deve retornar a mesma transação)
      const response2 = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const responseData2 = await response2.json();
      const duration = Date.now() - startTime;

      // Verificar se são a mesma transação (prevenção de duplicação)
      const isDuplicate = response1.ok && response2.ok && responseData1.id === responseData2.id;

      this.results.push({
        testName,
        successful: isDuplicate,
        transactionId: responseData1.id,
        referenceCode: responseData1.referenceCode,
        amount,
        pixKey,
        duration,
        error: isDuplicate ? undefined : 'Duplicação não foi prevenida via API',
        responseData: { first: responseData1, second: responseData2 },
        statusCode: response2.status
      });

      if (isDuplicate) {
        console.log(`✅ ${testName}: SUCESSO - Duplicação prevenida via API`);
        console.log(`   ID: ${responseData1.id}`);
        console.log(`   Reference Code: ${responseData1.referenceCode}`);
        console.log(`   Duration: ${duration}ms`);
      } else {
        console.log(`❌ ${testName}: FALHOU - Duplicação não foi prevenida via API`);
        console.log(`   ID 1: ${responseData1.id}`);
        console.log(`   ID 2: ${responseData2.id}`);
        console.log(`   Status 1: ${response1.status}`);
        console.log(`   Status 2: ${response2.status}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 3: Listar transações via API
   */
  private async testListTransactionsViaApi(): Promise<void> {
    const testName = 'Listar Transações via API';
    const startTime = Date.now();

    console.log(`🔄 ${testName}...`);

    try {
      const response = await fetch(`${this.baseUrl}/api/payments/transactions?organizationId=${this.organizationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        }
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (response.ok) {
        const transactions = responseData.transactions || responseData;
        const transactionCount = Array.isArray(transactions) ? transactions.length : 0;

        console.log(`✅ ${testName}: SUCESSO - ${transactionCount} transações encontradas`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Duration: ${duration}ms`);

        if (Array.isArray(transactions) && transactions.length > 0) {
          console.log('   Primeiras 5 transações:');
          transactions.slice(0, 5).forEach((tx: any, index: number) => {
            console.log(`   ${index + 1}. ID: ${tx.id}`);
            console.log(`      Type: ${tx.type}`);
            console.log(`      Amount: R$ ${tx.amount}`);
            console.log(`      Status: ${tx.status}`);
            console.log(`      Customer: ${tx.customerEmail}`);
            console.log('');
          });
        }

        this.results.push({
          testName,
          successful: true,
          amount: 0,
          pixKey: 'N/A',
          duration,
          responseData,
          statusCode: response.status
        });

      } else {
        this.results.push({
          testName,
          successful: false,
          amount: 0,
          pixKey: 'N/A',
          duration,
          error: `API Error ${response.status}: ${responseData.message || 'Unknown error'}`,
          responseData,
          statusCode: response.status
        });

        console.log(`❌ ${testName}: FALHOU`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${responseData.message || 'Unknown error'}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey: 'N/A',
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Teste 4: Teste com diferentes valores via API
   */
  private async testDifferentAmountsViaApi(): Promise<void> {
    const testName = 'Diferentes Valores via API';
    const amounts = [25.00, 50.75, 100.25];
    const pixKey = '11999887766';

    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();
    let successCount = 0;

    try {
      for (let i = 0; i < amounts.length; i++) {
        const amount = amounts[i];
        const customerEmail = `test-amount-${i}@pluggou.com`;

        try {
          const requestBody = {
            amount,
            customerName: `Teste Valor ${i + 1}`,
            customerEmail: `test-amount-${i}@pluggou.com`,
            customerPhone: '11999887766',
            customerDocument: '12345678901',
            customerDocumentType: 'cpf',
            description: `Teste com valor R$ ${amount} via API`,
            organizationId: this.organizationId,
            metadata: {
              testType: 'amount_test',
              testIndex: i
            }
          };

          const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-Key': this.apiKey
            },
            body: JSON.stringify(requestBody)
          });

          const responseData = await response.json();

          if (response.ok) {
            successCount++;
            console.log(`   ✅ Valor R$ ${amount}: ID ${responseData.id}`);
          } else {
            console.log(`   ❌ Valor R$ ${amount}: ${response.status} - ${responseData.message}`);
          }

        } catch (error) {
          console.log(`   ❌ Valor R$ ${amount}: ${error instanceof Error ? error.message : error}`);
        }
      }

      const duration = Date.now() - startTime;
      const successful = successCount === amounts.length;

      this.results.push({
        testName,
        successful,
        amount: amounts.reduce((sum, amount) => sum + amount, 0),
        pixKey,
        duration,
        error: successful ? undefined : `${successCount}/${amounts.length} sucessos`
      });

      if (successful) {
        console.log(`✅ ${testName}: SUCESSO - Todos os valores processados via API`);
      } else {
        console.log(`⚠️ ${testName}: PARCIAL - ${successCount}/${amounts.length} valores processados via API`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        successful: false,
        amount: 0,
        pixKey,
        duration,
        error: errorMessage
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }
  }

  /**
   * Imprimir resultados
   */
  private printResults(): void {
    console.log('\n📊 RESULTADOS DOS TESTES VIA API');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.successful).length;
    const totalAmount = this.results.reduce((sum, r) => sum + r.amount, 0);
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Passou: ${passedTests} (${Math.round(passedTests / totalTests * 100)}%)`);
    console.log(`Valor Total Testado: R$ ${totalAmount.toFixed(2)}`);
    console.log(`Tempo Total: ${totalDuration}ms`);

    console.log('\n📋 DETALHES DOS TESTES');
    console.log('-'.repeat(80));

    this.results.forEach(result => {
      const status = result.successful ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      console.log(`   Valor: R$ ${result.amount.toFixed(2)}`);
      console.log(`   PIX Key: ${result.pixKey}`);
      console.log(`   Tempo: ${result.duration}ms`);

      if (result.transactionId) {
        console.log(`   ID: ${result.transactionId}`);
        console.log(`   Reference Code: ${result.referenceCode}`);
      }

      if (result.statusCode) {
        console.log(`   Status Code: ${result.statusCode}`);
      }

      if (result.error) {
        console.log(`   Erro: ${result.error}`);
      }
      console.log('');
    });

    if (passedTests === totalTests) {
      console.log('🎉 TODOS OS TESTES PASSARAM!');
      console.log('✅ API de criação de cobrança PIX funcionando perfeitamente');
      console.log('✅ Prevenção de duplicação funcionando via API');
      console.log('✅ Sistema suporta diferentes valores via API');
    } else {
      console.log('⚠️ ALGUNS TESTES FALHARAM');
      console.log('🔍 Revise os erros antes de prosseguir');
    }
  }
}

// Executar testes se script for chamado diretamente
if (require.main === module) {
  const tester = new ApiPixChargeTest();
  tester.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ApiPixChargeTest };
