#!/usr/bin/env tsx

/**
 * Debug detalhado da busca do webhook
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class WebhookSearchDebugger {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async debugWebhookSearch(): Promise<void> {
    console.log('🔍 DEBUG DETALHADO - BUSCA DO WEBHOOK');
    console.log('='.repeat(60));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Dados do webhook simulado
    const webhookData = {
      externalId: 'cmfrtvw9i02j3yo2dm9x9i0mu', // ID interno da transação mais recente
      transactionId: '81dfbb6a-345e-4abe-8e95-00da00673968', // External ID da transação mais recente
      endToEndId: 'E33053580202509200522261151a8009',
      amount: 0.01
    };

    console.log('🔄 DADOS DO WEBHOOK:');
    console.log(`   External ID: ${webhookData.externalId}`);
    console.log(`   Transaction ID: ${webhookData.transactionId}`);
    console.log(`   End-to-End ID: ${webhookData.endToEndId}`);
    console.log(`   Amount: R$ ${webhookData.amount.toFixed(2)}`);
    console.log('');

    // 1. Busca por externalId
    console.log('1️⃣ BUSCA POR EXTERNAL ID:');
    const byExternalId = await db.transaction.findFirst({
      where: { externalId: webhookData.externalId }
    });
    console.log(`   Resultado: ${byExternalId ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
    if (byExternalId) {
      console.log(`   ID: ${byExternalId.id}, Status: ${byExternalId.status}, Amount: R$ ${byExternalId.amount.toFixed(2)}`);
    }
    console.log('');

    // 2. Busca por transactionId
    console.log('2️⃣ BUSCA POR TRANSACTION ID:');
    const byTransactionId = await db.transaction.findFirst({
      where: {
        OR: [
          { id: webhookData.transactionId },
          { externalId: webhookData.transactionId }
        ]
      }
    });
    console.log(`   Resultado: ${byTransactionId ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
    if (byTransactionId) {
      console.log(`   ID: ${byTransactionId.id}, Status: ${byTransactionId.status}, Amount: R$ ${byTransactionId.amount.toFixed(2)}`);
    }
    console.log('');

    // 3. Busca por múltiplos critérios (versão atual)
    console.log('3️⃣ BUSCA POR MÚLTIPLOS CRITÉRIOS (ATUAL):');
    const criteria = [];
    
    if (webhookData.externalId) {
      criteria.push({ externalId: webhookData.externalId });
    }
    
    if (webhookData.transactionId) {
      criteria.push({ id: webhookData.transactionId });
    }
    
    if (webhookData.endToEndId) {
      criteria.push({
        metadata: {
          path: ['endToEndId'],
          equals: webhookData.endToEndId
        }
      });
    }
    
    if (webhookData.amount) {
      criteria.push({ amount: webhookData.amount });
    }

    console.log(`   Critérios: ${criteria.length}`);
    criteria.forEach((c, i) => {
      console.log(`     ${i + 1}. ${JSON.stringify(c)}`);
    });

    const byMultipleCriteria = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: criteria
          },
          {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
            }
          }
        ]
      },
      orderBy: {
        createdAt: 'desc' // Mais recente primeiro
      }
    });

    console.log(`   Resultado: ${byMultipleCriteria ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
    if (byMultipleCriteria) {
      console.log(`   ID: ${byMultipleCriteria.id}, Status: ${byMultipleCriteria.status}, Amount: R$ ${byMultipleCriteria.amount.toFixed(2)}`);
      console.log(`   External ID: ${byMultipleCriteria.externalId || 'N/A'}`);
      console.log(`   Criado em: ${byMultipleCriteria.createdAt.toISOString()}`);
    }
    console.log('');

    // 4. Busca por múltiplos critérios SEM filtro de tempo
    console.log('4️⃣ BUSCA POR MÚLTIPLOS CRITÉRIOS (SEM FILTRO DE TEMPO):');
    const byMultipleCriteriaNoTime = await db.transaction.findFirst({
      where: {
        OR: criteria
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`   Resultado: ${byMultipleCriteriaNoTime ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
    if (byMultipleCriteriaNoTime) {
      console.log(`   ID: ${byMultipleCriteriaNoTime.id}, Status: ${byMultipleCriteriaNoTime.status}, Amount: R$ ${byMultipleCriteriaNoTime.amount.toFixed(2)}`);
      console.log(`   External ID: ${byMultipleCriteriaNoTime.externalId || 'N/A'}`);
      console.log(`   Criado em: ${byMultipleCriteriaNoTime.createdAt.toISOString()}`);
    }
    console.log('');

    // 5. Verificar todas as transações que atendem aos critérios
    console.log('5️⃣ TODAS AS TRANSAÇÕES QUE ATENDEM AOS CRITÉRIOS:');
    const allMatching = await db.transaction.findMany({
      where: {
        OR: criteria
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`   Total encontradas: ${allMatching.length}`);
    allMatching.forEach((tx, index) => {
      const ageInHours = (Date.now() - tx.createdAt.getTime()) / (1000 * 60 * 60);
      console.log(`   ${index + 1}. ID: ${tx.id}`);
      console.log(`      External ID: ${tx.externalId || 'N/A'}`);
      console.log(`      Status: ${tx.status}`);
      console.log(`      Amount: R$ ${tx.amount.toFixed(2)}`);
      console.log(`      Idade: ${ageInHours.toFixed(1)} horas`);
      console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
      console.log('');
    });

    // 6. Verificar qual critério está causando o match
    console.log('6️⃣ VERIFICAÇÃO DE CRITÉRIOS INDIVIDUAIS:');
    
    for (let i = 0; i < criteria.length; i++) {
      const singleCriteria = criteria[i];
      const result = await db.transaction.findFirst({
        where: singleCriteria,
        orderBy: { createdAt: 'desc' }
      });
      
      console.log(`   Critério ${i + 1}: ${JSON.stringify(singleCriteria)}`);
      console.log(`   Resultado: ${result ? 'ENCONTRADA' : 'NÃO ENCONTRADA'}`);
      if (result) {
        console.log(`   ID: ${result.id}, Status: ${result.status}, Amount: R$ ${result.amount.toFixed(2)}`);
      }
      console.log('');
    }
  }
}

// Executar debug se chamado diretamente
if (require.main === module) {
  const webhookDebugger = new WebhookSearchDebugger();
  webhookDebugger.debugWebhookSearch()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { WebhookSearchDebugger };
