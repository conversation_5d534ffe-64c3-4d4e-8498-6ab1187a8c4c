#!/usr/bin/env tsx

/**
 * Script para testar o processamento de taxas diretamente
 * Este script busca uma transação recente e testa o processamento de taxas
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '@repo/database';

const ORGANIZATION_ID = 'fC99w8SdDGbNJM_q0b2s5';

async function testFeeProcessingDirectly() {
  console.log('🧪 TESTANDO PROCESSAMENTO DE TAXAS DIRETAMENTE');
  console.log('='.repeat(60));
  console.log(`Organization ID: ${ORGANIZATION_ID}`);
  console.log('');

  try {
    // Buscar uma transação recente da organização
    const recentTransaction = await db.transaction.findFirst({
      where: {
        organizationId: ORGANIZATION_ID,
        type: 'CHARGE'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!recentTransaction) {
      console.log('❌ Nenhuma transação encontrada para a organização');
      return;
    }

    console.log('✅ TRANSAÇÃO ENCONTRADA:');
    console.log(`   ID: ${recentTransaction.id}`);
    console.log(`   Valor: R$ ${recentTransaction.amount}`);
    console.log(`   Status: ${recentTransaction.status}`);
    console.log(`   Tipo: ${recentTransaction.type}`);
    console.log(`   Taxa %: ${recentTransaction.percentFee}`);
    console.log(`   Taxa Fixa: R$ ${recentTransaction.fixedFee}`);
    console.log(`   Taxa Total: R$ ${recentTransaction.totalFee}`);
    console.log(`   Valor Líquido: R$ ${recentTransaction.netAmount || 'null'}`);
    console.log(`   Criada em: ${recentTransaction.createdAt}`);
    console.log('');

    // Verificar se já tem taxas aplicadas
    if (recentTransaction.totalFee > 0) {
      console.log('✅ TRANSAÇÃO JÁ TEM TAXAS APLICADAS');
      console.log('   Não é necessário reprocessar');
      return;
    }

    console.log('🔄 PROCESSANDO TAXAS PARA A TRANSAÇÃO...');
    console.log('');

    // Importar e executar o processamento de taxas
    const { processTransactionFees } = await import('../packages/payments/src/taxes/fee-service');

    console.log('✅ Serviço de taxas importado com sucesso');

    const feeResult = await processTransactionFees(recentTransaction, 'CHARGE');

    console.log('✅ RESULTADO DO PROCESSAMENTO:');
    console.log(`   Sucesso: ${feeResult.success}`);
    console.log(`   Taxa %: R$ ${feeResult.fees.percentFee}`);
    console.log(`   Taxa Fixa: R$ ${feeResult.fees.fixedFee}`);
    console.log(`   Taxa Total: R$ ${feeResult.fees.totalFee}`);
    console.log(`   Valor Líquido: R$ ${feeResult.netAmount}`);
    console.log(`   Fonte: ${feeResult.fees.source || 'unknown'}`);
    console.log('');

    if (feeResult.success) {
      console.log('💾 ATUALIZANDO TRANSAÇÃO NO BANCO...');

      const updatedTransaction = await db.transaction.update({
        where: { id: recentTransaction.id },
        data: {
          percentFee: feeResult.fees.percentFee,
          fixedFee: feeResult.fees.fixedFee,
          totalFee: feeResult.fees.totalFee,
          netAmount: feeResult.netAmount,
          metadata: {
            ...(recentTransaction.metadata as Record<string, any> || {}),
            fees: {
              percentFee: feeResult.fees.percentFee,
              fixedFee: feeResult.fees.fixedFee,
              totalFee: feeResult.fees.totalFee,
              source: feeResult.fees.source || 'organization',
              calculatedAt: new Date().toISOString()
            },
            netAmount: feeResult.netAmount,
            feeProcessed: true,
            feeProcessedAt: new Date().toISOString()
          }
        }
      });

      console.log('✅ TRANSAÇÃO ATUALIZADA COM SUCESSO:');
      console.log(`   ID: ${updatedTransaction.id}`);
      console.log(`   Taxa %: ${updatedTransaction.percentFee}`);
      console.log(`   Taxa Fixa: R$ ${updatedTransaction.fixedFee}`);
      console.log(`   Taxa Total: R$ ${updatedTransaction.totalFee}`);
      console.log(`   Valor Líquido: R$ ${updatedTransaction.netAmount}`);
      console.log('');

      console.log('🎉 PROCESSAMENTO DE TAXAS FUNCIONANDO PERFEITAMENTE!');
      console.log('   O problema deve estar no router da API não executando este código');
    } else {
      console.log('❌ PROCESSAMENTO DE TAXAS FALHOU');
      console.log('   Verifique os logs para mais detalhes');
    }

  } catch (error) {
    console.error('❌ ERRO:', error);
  } finally {
    await db.$disconnect();
  }
}

// Executar se script for chamado diretamente
if (require.main === module) {
  testFeeProcessingDirectly()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { testFeeProcessingDirectly };
