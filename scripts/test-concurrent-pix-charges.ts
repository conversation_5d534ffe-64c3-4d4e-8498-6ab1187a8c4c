#!/usr/bin/env tsx

/**
 * Teste de Concorrência Massiva - Cobranças PIX (CHARGE)
 * Testa criação simultânea de múltiplas cobranças PIX para garantir unicidade de IDs
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

interface ConcurrentTestResult {
  testName: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  duplicateIds: number;
  uniqueIds: Set<string>;
  duration: number;
  averageResponseTime: number;
  errors: string[];
  transactionIds: string[];
  referenceCodes: string[];
}

class ConcurrentPixChargeTest {
  private baseUrl = 'http://localhost:3000';
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private results: ConcurrentTestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 TESTE DE CONCORRÊNCIA MASSIVA - COBRANÇAS PIX (CHARGE)');
    console.log('='.repeat(80));
    console.log(`API URL: ${this.baseUrl}`);
    console.log(`Organization ID: ${this.organizationId}`);
    console.log(`API Key: ${this.apiKey.substring(0, 10)}...`);
    console.log('');

    // Verificar se API está rodando
    await this.checkApiHealth();

    // Teste 1: 10 requisições simultâneas
    await this.testConcurrentCharges(10, '10 Requisições Simultâneas');

    // Teste 2: 25 requisições simultâneas
    await this.testConcurrentCharges(25, '25 Requisições Simultâneas');

    // Teste 3: 50 requisições simultâneas
    await this.testConcurrentCharges(50, '50 Requisições Simultâneas');

    // Teste 4: 100 requisições simultâneas (teste de stress)
    await this.testConcurrentCharges(100, '100 Requisições Simultâneas (Stress Test)');

    this.printResults();
  }

  /**
   * Verificar se API está rodando
   */
  private async checkApiHealth(): Promise<void> {
    console.log('🔍 Verificando se API está rodando...');

    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log(`✅ API está rodando: ${response.status}`);
      } else {
        console.log(`⚠️ API respondeu com status: ${response.status}`);
      }

    } catch (error) {
      console.log(`❌ API não está rodando: ${error instanceof Error ? error.message : error}`);
      console.log('   Certifique-se de que o projeto está rodando na porta 3000');
    }

    console.log('');
  }

  /**
   * Teste de concorrência com N requisições simultâneas
   */
  private async testConcurrentCharges(concurrency: number, testName: string): Promise<void> {
    console.log(`🔄 ${testName}...`);

    const startTime = Date.now();
    const uniqueIds = new Set<string>();
    const transactionIds: string[] = [];
    const referenceCodes: string[] = [];
    const errors: string[] = [];
    let successfulRequests = 0;
    let failedRequests = 0;

    // Criar array de promessas para execução simultânea
    const promises = Array.from({ length: concurrency }, (_, index) =>
      this.createPixCharge(index, concurrency)
    );

    try {
      // Executar todas as requisições simultaneamente
      const results = await Promise.allSettled(promises);

      // Processar resultados
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulRequests++;
          const id = result.value.transactionId;
          const refCode = result.value.referenceCode;

          if (id) {
            transactionIds.push(id);
            if (uniqueIds.has(id)) {
              console.log(`⚠️ ID DUPLICADO DETECTADO: ${id} (índice ${index})`);
            } else {
              uniqueIds.add(id);
            }
          }

          if (refCode) {
            referenceCodes.push(refCode);
          }
        } else {
          failedRequests++;
          const error = result.status === 'rejected'
            ? result.reason
            : result.value.error || 'Unknown error';
          errors.push(`Request ${index}: ${error}`);
        }
      });

      const duration = Date.now() - startTime;
      const averageResponseTime = duration / concurrency;
      const duplicateIds = concurrency - uniqueIds.size;

      this.results.push({
        testName,
        totalRequests: concurrency,
        successfulRequests,
        failedRequests,
        duplicateIds,
        uniqueIds,
        duration,
        averageResponseTime,
        errors,
        transactionIds,
        referenceCodes
      });

      // Log do resultado
      const successRate = (successfulRequests / concurrency) * 100;
      const duplicateRate = (duplicateIds / concurrency) * 100;

      console.log(`✅ ${testName}: CONCLUÍDO`);
      console.log(`   Total: ${concurrency} | Sucesso: ${successfulRequests} | Falhas: ${failedRequests}`);
      console.log(`   Taxa de Sucesso: ${successRate.toFixed(1)}%`);
      console.log(`   IDs Únicos: ${uniqueIds.size} | Duplicados: ${duplicateIds}`);
      console.log(`   Taxa de Duplicação: ${duplicateRate.toFixed(1)}%`);
      console.log(`   Duração Total: ${duration}ms | Tempo Médio: ${averageResponseTime.toFixed(0)}ms`);

      if (duplicateIds > 0) {
        console.log(`   ⚠️ ATENÇÃO: ${duplicateIds} IDs duplicados detectados!`);
      }

      if (errors.length > 0) {
        console.log(`   ❌ Erros: ${errors.length}`);
        errors.slice(0, 3).forEach(error => {
          console.log(`     - ${error}`);
        });
        if (errors.length > 3) {
          console.log(`     ... e mais ${errors.length - 3} erros`);
        }
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        totalRequests: concurrency,
        successfulRequests: 0,
        failedRequests: concurrency,
        duplicateIds: 0,
        uniqueIds: new Set(),
        duration,
        averageResponseTime: 0,
        errors: [errorMessage],
        transactionIds: [],
        referenceCodes: []
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }

    console.log('');
  }

  /**
   * Criar uma cobrança PIX individual
   */
  private async createPixCharge(index: number, total: number): Promise<{
    success: boolean;
    transactionId?: string;
    referenceCode?: string;
    error?: string;
    duration?: number;
  }> {
    const startTime = Date.now();
    const amount = 10 + (index % 90); // Valores entre R$ 10 e R$ 100
    const customerEmail = `test-concurrent-${index}-${Date.now()}@pluggou.com`;

    try {
      const requestBody = {
        amount,
        customerName: `Teste Concorrência ${index + 1}/${total}`,
        customerEmail,
        customerPhone: `1199988${String(index).padStart(4, '0')}`,
        customerDocument: `1234567890${String(index).padStart(2, '0')}`,
        customerDocumentType: 'cpf' as const,
        description: `Teste de concorrência ${index + 1}/${total} - R$ ${amount}`,
        organizationId: this.organizationId,
        metadata: {
          testType: 'concurrent_test',
          testIndex: index,
          totalTests: total,
          timestamp: Date.now()
        }
      };

      const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (response.ok) {
        return {
          success: true,
          transactionId: responseData.id,
          referenceCode: responseData.referenceCode,
          duration
        };
      } else {
        return {
          success: false,
          error: `API Error ${response.status}: ${responseData.message || 'Unknown error'}`,
          duration
        };
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration
      };
    }
  }

  /**
   * Imprimir resultados finais
   */
  private printResults(): void {
    console.log('\n📊 RESULTADOS FINAIS DOS TESTES DE CONCORRÊNCIA');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const totalRequests = this.results.reduce((sum, r) => sum + r.totalRequests, 0);
    const totalSuccessful = this.results.reduce((sum, r) => sum + r.successfulRequests, 0);
    const totalFailed = this.results.reduce((sum, r) => sum + r.failedRequests, 0);
    const totalDuplicates = this.results.reduce((sum, r) => sum + r.duplicateIds, 0);
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Total de Requisições: ${totalRequests}`);
    console.log(`Sucessos: ${totalSuccessful} (${Math.round(totalSuccessful / totalRequests * 100)}%)`);
    console.log(`Falhas: ${totalFailed} (${Math.round(totalFailed / totalRequests * 100)}%)`);
    console.log(`IDs Duplicados: ${totalDuplicates} (${Math.round(totalDuplicates / totalRequests * 100)}%)`);
    console.log(`Duração Total: ${totalDuration}ms`);

    console.log('\n📋 DETALHES POR TESTE');
    console.log('-'.repeat(80));

    this.results.forEach(result => {
      const successRate = (result.successfulRequests / result.totalRequests) * 100;
      const duplicateRate = (result.duplicateIds / result.totalRequests) * 100;

      console.log(`\n🔍 ${result.testName}`);
      console.log(`   Requisições: ${result.totalRequests}`);
      console.log(`   Sucessos: ${result.successfulRequests} (${successRate.toFixed(1)}%)`);
      console.log(`   Falhas: ${result.failedRequests} (${(100 - successRate).toFixed(1)}%)`);
      console.log(`   IDs Únicos: ${result.uniqueIds.size}`);
      console.log(`   Duplicados: ${result.duplicateIds} (${duplicateRate.toFixed(1)}%)`);
      console.log(`   Duração: ${result.duration}ms`);
      console.log(`   Tempo Médio: ${result.averageResponseTime.toFixed(0)}ms`);

      if (result.duplicateIds > 0) {
        console.log(`   ⚠️ PROBLEMA: ${result.duplicateIds} IDs duplicados!`);
      }

      if (result.errors.length > 0) {
        console.log(`   ❌ Erros: ${result.errors.length}`);
      }
    });

    // Análise de segurança
    console.log('\n🔒 ANÁLISE DE SEGURANÇA');
    console.log('-'.repeat(80));

    if (totalDuplicates === 0) {
      console.log('✅ EXCELENTE: Nenhum ID duplicado detectado!');
      console.log('✅ Sistema garante unicidade de IDs mesmo sob alta concorrência');
    } else {
      console.log(`❌ PROBLEMA: ${totalDuplicates} IDs duplicados detectados!`);
      console.log('❌ Sistema NÃO garante unicidade de IDs sob concorrência');
    }

    if (totalSuccessful / totalRequests >= 0.95) {
      console.log('✅ EXCELENTE: Taxa de sucesso alta (>95%)');
    } else if (totalSuccessful / totalRequests >= 0.90) {
      console.log('⚠️ BOM: Taxa de sucesso boa (>90%)');
    } else {
      console.log('❌ PROBLEMA: Taxa de sucesso baixa (<90%)');
    }

    // Recomendação final
    console.log('\n🎯 RECOMENDAÇÃO FINAL');
    console.log('-'.repeat(80));

    if (totalDuplicates === 0 && totalSuccessful / totalRequests >= 0.95) {
      console.log('🎉 SISTEMA APROVADO PARA PRODUÇÃO!');
      console.log('✅ Unicidade de IDs garantida');
      console.log('✅ Alta taxa de sucesso');
      console.log('✅ Sistema robusto sob concorrência');
    } else if (totalDuplicates === 0) {
      console.log('⚠️ SISTEMA PARCIALMENTE APROVADO');
      console.log('✅ Unicidade de IDs garantida');
      console.log('⚠️ Melhorar taxa de sucesso');
    } else {
      console.log('❌ SISTEMA NÃO APROVADO');
      console.log('❌ Corrigir problema de IDs duplicados');
      console.log('❌ Sistema não é seguro para produção');
    }
  }
}

// Executar testes se script for chamado diretamente
if (require.main === module) {
  const tester = new ConcurrentPixChargeTest();
  tester.runAllTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ConcurrentPixChargeTest };
