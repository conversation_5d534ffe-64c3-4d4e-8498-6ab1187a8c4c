#!/usr/bin/env tsx

/**
 * Script para testar o isolamento de webhooks entre organizações
 *
 * Este script:
 * 1. Cria webhooks de teste para duas organizações diferentes
 * 2. Envia eventos para cada organização
 * 3. Verifica se os eventos chegam apenas na organização correta
 * 4. Limpa os dados de teste
 */

import { svixService } from "../packages/utils/src/svix/index.js";
// @ts-ignore
import { db } from "@repo/database";
// @ts-ignore
import { logger } from "@repo/logs";
import { createId } from "@paralleldrive/cuid2";

const SVIX_APP_ID = process.env.SVIX_APP_ID || "app_2xJGtEh9B3sOptpW4thRObvtlh9";

// URLs de teste (usar ngrok ou similar)
const TEST_WEBHOOK_URL_1 = process.env.TEST_WEBHOOK_URL_1 || "https://webhook.site/unique-id-1";
const TEST_WEBHOOK_URL_2 = process.env.TEST_WEBHOOK_URL_2 || "https://webhook.site/unique-id-2";

interface TestResult {
  organizationId: string;
  webhookId: string;
  endpointId: string;
  eventsReceived: string[];
  success: boolean;
  error?: string;
}

async function createTestWebhook(organizationId: string, webhookUrl: string): Promise<{
  webhookId: string;
  endpointId: string;
}> {
  // Create webhook in database
  const webhook = await db.webhook.create({
    data: {
      id: createId(),
      url: webhookUrl,
      events: ['transaction.created', 'transaction.updated'],
      organizationId,
      isActive: true,
      secret: `whsec_test_${Math.random().toString(36).substring(2, 15)}`,
      useSvix: true,
      updatedAt: new Date(),
    },
  });

  // Create Svix endpoint
  const endpointId = await svixService.createEndpoint(
    SVIX_APP_ID,
    webhookUrl,
    `Test webhook for organization ${organizationId}`,
    ['Transaction.Created', 'Transaction.Updated'],
    organizationId
  );

  // Update webhook with endpoint ID
  await db.webhook.update({
    where: { id: webhook.id },
    data: { svixEndpointId: endpointId },
  });

  return { webhookId: webhook.id, endpointId };
}

async function sendTestEvent(organizationId: string, eventType: string): Promise<string> {
  const testPayload = {
    id: createId(),
    type: eventType,
    created_at: new Date().toISOString(),
    data: {
      id: createId(),
      organizationId,
      amount: 1000,
      status: 'processing',
      testEvent: true,
      timestamp: new Date().toISOString(),
    },
  };

  return await svixService.sendEvent(
    SVIX_APP_ID,
    eventType,
    testPayload,
    organizationId
  );
}

async function verifyWebhookIsolation(): Promise<{
  org1Result: TestResult;
  org2Result: TestResult;
  crossContamination: boolean;
}> {
  const org1Id = createId();
  const org2Id = createId();

  console.log("🧪 Creating test organizations and webhooks...");
  console.log(`   Organization 1: ${org1Id}`);
  console.log(`   Organization 2: ${org2Id}\n`);

  // Create test organizations
  await db.organization.createMany({
    data: [
      {
        id: org1Id,
        name: 'Test Organization 1',
        slug: 'test-org-1',
        status: 'APPROVED',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: org2Id,
        name: 'Test Organization 2',
        slug: 'test-org-2',
        status: 'APPROVED',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
  });

  // Create test webhooks
  const org1Webhook = await createTestWebhook(org1Id, TEST_WEBHOOK_URL_1);
  const org2Webhook = await createTestWebhook(org2Id, TEST_WEBHOOK_URL_2);

  console.log("📡 Created test webhooks:");
  console.log(`   Org 1: ${org1Webhook.webhookId} -> ${org1Webhook.endpointId}`);
  console.log(`   Org 2: ${org2Webhook.webhookId} -> ${org2Webhook.endpointId}\n`);

  // Send test events
  console.log("📤 Sending test events...");

  const org1Event1 = await sendTestEvent(org1Id, 'transaction.created');
  const org1Event2 = await sendTestEvent(org1Id, 'transaction.updated');
  const org2Event1 = await sendTestEvent(org2Id, 'transaction.created');
  const org2Event2 = await sendTestEvent(org2Id, 'transaction.updated');

  console.log(`   Org 1 Events: ${org1Event1}, ${org1Event2}`);
  console.log(`   Org 2 Events: ${org2Event1}, ${org2Event2}\n`);

  // Wait a bit for webhook delivery
  console.log("⏳ Waiting for webhook delivery...");
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Check webhook delivery attempts
  const svix = svixService.getClient();

  const org1Attempts1 = await svix.messageAttempt.listByMsg(SVIX_APP_ID, org1Event1);
  const org1Attempts2 = await svix.messageAttempt.listByMsg(SVIX_APP_ID, org1Event2);
  const org2Attempts1 = await svix.messageAttempt.listByMsg(SVIX_APP_ID, org2Event1);
  const org2Attempts2 = await svix.messageAttempt.listByMsg(SVIX_APP_ID, org2Event2);

  // Analyze results
  const org1Result: TestResult = {
    organizationId: org1Id,
    webhookId: org1Webhook.webhookId,
    endpointId: org1Webhook.endpointId,
    eventsReceived: [],
    success: true,
  };

  const org2Result: TestResult = {
    organizationId: org2Id,
    webhookId: org2Webhook.webhookId,
    endpointId: org2Webhook.endpointId,
    eventsReceived: [],
    success: true,
  };

  // Check if org1 events went to org1 webhook only
  const org1Events = [...org1Attempts1.data, ...org1Attempts2.data];
  const org1CrossContamination = org1Events.some(attempt =>
    attempt.endpointId === org2Webhook.endpointId
  );

  // Check if org2 events went to org2 webhook only
  const org2Events = [...org2Attempts1.data, ...org2Attempts2.data];
  const org2CrossContamination = org2Events.some(attempt =>
    attempt.endpointId === org1Webhook.endpointId
  );

  const crossContamination = org1CrossContamination || org2CrossContamination;

  // Clean up test data
  console.log("🧹 Cleaning up test data...");

  try {
    await svix.endpoint.delete(SVIX_APP_ID, org1Webhook.endpointId);
    await svix.endpoint.delete(SVIX_APP_ID, org2Webhook.endpointId);

    await db.webhook.deleteMany({
      where: { id: { in: [org1Webhook.webhookId, org2Webhook.webhookId] } }
    });

    await db.organization.deleteMany({
      where: { id: { in: [org1Id, org2Id] } }
    });

    console.log("✅ Test data cleaned up\n");
  } catch (error) {
    console.log("⚠️  Warning: Failed to clean up some test data:", error);
  }

  return {
    org1Result,
    org2Result,
    crossContamination,
  };
}

async function main() {
  try {
    console.log("🧪 Webhook Organization Isolation Test\n");

    const result = await verifyWebhookIsolation();

    console.log("📊 Test Results:");
    console.log(`   Organization 1 Webhook: ${result.org1Result.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Organization 2 Webhook: ${result.org2Result.success ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Cross-Contamination: ${result.crossContamination ? '❌ DETECTED' : '✅ None'}\n`);

    if (result.crossContamination) {
      console.log("🚨 SECURITY ISSUE: Events are leaking between organizations!");
      console.log("   This indicates that webhook isolation is not working properly.");
      console.log("   Run the fix script to resolve this issue.\n");
    } else {
      console.log("✅ Webhook isolation is working correctly!");
      console.log("   Events are properly isolated by organization.\n");
    }

    // Security assessment
    if (result.crossContamination) {
      console.log("🛡️  Security Status: ❌ VULNERABLE - Data leakage detected");
      process.exit(1);
    } else {
      console.log("🛡️  Security Status: ✅ SECURE - No data leakage detected");
    }

  } catch (error) {
    logger.error("Isolation test failed", { error });
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { verifyWebhookIsolation };
