#!/usr/bin/env tsx

/**
 * Teste de Integração - API de Produção
 * Testa criação de PIX (CHARGE) e transferências PIX (SEND) usando a API de produção
 * Valores: PIX 1-5 reais, Transferências 1-5 centavos
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

interface TestResult {
  success: boolean;
  transactionId?: string;
  externalId?: string;
  referenceCode?: string;
  endToEndId?: string;
  amount: number;
  type: 'CHARGE' | 'SEND';
  error?: string;
  responseTime: number;
}

class ProductionApiIntegrationTester {
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private pixKey = 'bfd515ce-16fb-48ac-bd37-9dc57333c7e9';
  private baseUrl = 'https://app.pluggou.io';
  private testResults: TestResult[] = [];

  async runProductionTests(): Promise<void> {
    console.log('🚀 TESTE DE INTEGRAÇÃO - API DE PRODUÇÃO');
    console.log('='.repeat(70));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log(`🔑 Chave PIX: ${this.pixKey}`);
    console.log(`🌐 API: ${this.baseUrl}`);
    console.log(`💰 PIX (CHARGE): R$ 1,00 a R$ 5,00`);
    console.log(`💸 Transferências (SEND): R$ 0,01 a R$ 0,05`);
    console.log('');

    try {
      // Teste 1: Criar PIX (CHARGE) - 3 transações
      console.log('📱 TESTE 1: CRIAÇÃO DE PIX (CHARGE)');
      console.log('-'.repeat(50));
      await this.testPixCharges();

      // Aguardar um pouco entre os testes
      console.log('\n⏳ Aguardando 5 segundos entre testes...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Teste 2: Transferências PIX (SEND) - 3 transferências
      console.log('\n💸 TESTE 2: TRANSFERÊNCIAS PIX (SEND)');
      console.log('-'.repeat(50));
      await this.testPixTransfers();

      // Análise final
      this.analyzeResults();

    } catch (error) {
      console.error('❌ Erro crítico no teste:', error);
    }
  }

  /**
   * Testa criação de PIX (CHARGE) - valores de R$ 1,00 a R$ 5,00
   */
  private async testPixCharges(): Promise<void> {
    const amounts = this.generateRandomAmounts(3, 100, 500); // 1-5 reais em centavos

    console.log(`💰 Valores gerados: ${amounts.map(a => `R$ ${(a/100).toFixed(2)}`).join(', ')}`);
    console.log('');

    for (let i = 0; i < amounts.length; i++) {
      const amount = amounts[i];
      const testNumber = i + 1;

      try {
        console.log(`🔄 Teste ${testNumber}: Criando PIX de R$ ${(amount/100).toFixed(2)}...`);

        const startTime = Date.now();
        const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          },
          body: JSON.stringify({
            amount: amount / 100, // Converter para reais
            customerName: this.generateRandomName(),
            customerEmail: this.generateRandomEmail(),
            customerPhone: this.generateRandomPhone(),
            customerDocument: this.generateRandomCPF(),
            customerDocumentType: 'cpf',
            description: `Teste PIX Produção ${testNumber}`,
            organizationId: this.organizationId,
            metadata: {
              testType: 'production_integration',
              testNumber: testNumber,
              timestamp: new Date().toISOString()
            }
          })
        });

        const responseTime = Date.now() - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();

        console.log(`✅ Teste ${testNumber}: PIX criado com sucesso`);
        console.log(`   ID: ${data.id}`);
        console.log(`   External ID: ${data.externalId || 'N/A'}`);
        console.log(`   Código de Referência: ${data.referenceCode || 'N/A'}`);
        console.log(`   Status: ${data.status}`);
        console.log(`   Tempo: ${responseTime}ms`);
        console.log('');

        this.testResults.push({
          success: true,
          transactionId: data.id,
          externalId: data.externalId,
          referenceCode: data.referenceCode,
          endToEndId: data.endToEndId,
          amount: amount / 100,
          type: 'CHARGE',
          responseTime
        });

      } catch (error) {
        const responseTime = Date.now() - startTime;
        console.log(`❌ Teste ${testNumber}: FALHOU - ${error instanceof Error ? error.message : String(error)}`);
        console.log(`   Tempo: ${responseTime}ms`);
        console.log('');

        this.testResults.push({
          success: false,
          amount: amount / 100,
          type: 'CHARGE',
          error: error instanceof Error ? error.message : String(error),
          responseTime
        });
      }
    }
  }

  /**
   * Testa transferências PIX (SEND) - valores de R$ 0,01 a R$ 0,05
   */
  private async testPixTransfers(): Promise<void> {
    const amounts = this.generateRandomAmounts(3, 1, 5); // 1-5 centavos

    console.log(`💰 Valores gerados: ${amounts.map(a => `R$ ${(a/100).toFixed(2)}`).join(', ')}`);
    console.log('');

    for (let i = 0; i < amounts.length; i++) {
      const amount = amounts[i];
      const testNumber = i + 1;

      try {
        console.log(`🔄 Teste ${testNumber}: Criando transferência de R$ ${(amount/100).toFixed(2)}...`);

        const startTime = Date.now();
        const response = await fetch(`${this.baseUrl}/api/payments/transfers/pix`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          },
          body: JSON.stringify({
            amount: amount / 100, // Converter para reais
            organizationId: this.organizationId,
            pixKey: this.pixKey,
            pixKeyType: 'RANDOM',
            description: `Teste Transferência Produção ${testNumber}`
          })
        });

        const responseTime = Date.now() - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();

        console.log(`✅ Teste ${testNumber}: Transferência criada com sucesso`);
        console.log(`   ID: ${data.id}`);
        console.log(`   External ID: ${data.externalId || 'N/A'}`);
        console.log(`   Status: ${data.status}`);
        console.log(`   Tempo: ${responseTime}ms`);
        console.log('');

        this.testResults.push({
          success: true,
          transactionId: data.id,
          externalId: data.externalId,
          referenceCode: data.referenceCode,
          endToEndId: data.endToEndId,
          amount: amount / 100,
          type: 'SEND',
          responseTime
        });

      } catch (error) {
        const responseTime = Date.now() - startTime;
        console.log(`❌ Teste ${testNumber}: FALHOU - ${error instanceof Error ? error.message : String(error)}`);
        console.log(`   Tempo: ${responseTime}ms`);
        console.log('');

        this.testResults.push({
          success: false,
          amount: amount / 100,
          type: 'SEND',
          error: error instanceof Error ? error.message : String(error),
          responseTime
        });
      }
    }
  }

  /**
   * Gera valores aleatórios entre min e max centavos
   */
  private generateRandomAmounts(count: number, minCents: number, maxCents: number): number[] {
    const amounts: number[] = [];
    for (let i = 0; i < count; i++) {
      const cents = Math.floor(Math.random() * (maxCents - minCents + 1)) + minCents;
      amounts.push(cents);
    }
    return amounts;
  }

  /**
   * Gera nome aleatório
   */
  private generateRandomName(): string {
    const names = [
      'João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa', 'Carlos Lima',
      'Fernanda Alves', 'Rafael Pereira', 'Juliana Rodrigues', 'Lucas Ferreira', 'Camila Souza'
    ];
    return names[Math.floor(Math.random() * names.length)];
  }

  /**
   * Gera email aleatório
   */
  private generateRandomEmail(): string {
    const domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com'];
    const names = ['joao', 'maria', 'pedro', 'ana', 'carlos', 'fernanda', 'rafael', 'juliana', 'lucas', 'camila'];
    const name = names[Math.floor(Math.random() * names.length)];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    const random = Math.floor(Math.random() * 1000);
    return `${name}${random}@${domain}`;
  }

  /**
   * Gera telefone aleatório
   */
  private generateRandomPhone(): string {
    const ddd = Math.floor(Math.random() * 90) + 10; // 10-99
    const number = Math.floor(Math.random() * 900000000) + 100000000; // 9 dígitos
    return `+55${ddd}${number}`;
  }

  /**
   * Gera CPF aleatório
   */
  private generateRandomCPF(): string {
    const cpf = Math.floor(Math.random() * 900000000) + 100000000; // 9 dígitos
    return cpf.toString().padStart(11, '0');
  }

  /**
   * Analisa os resultados dos testes
   */
  private analyzeResults(): void {
    console.log('📊 ANÁLISE FINAL DOS RESULTADOS');
    console.log('='.repeat(60));

    const successful = this.testResults.filter(r => r.success);
    const failed = this.testResults.filter(r => !r.success);
    const charges = this.testResults.filter(r => r.type === 'CHARGE');
    const transfers = this.testResults.filter(r => r.type === 'SEND');

    const successRate = (successful.length / this.testResults.length) * 100;
    const avgResponseTime = this.testResults.reduce((sum, r) => sum + r.responseTime, 0) / this.testResults.length;

    console.log(`📈 ESTATÍSTICAS GERAIS:`);
    console.log(`   Total de testes: ${this.testResults.length}`);
    console.log(`   Sucessos: ${successful.length} (${successRate.toFixed(1)}%)`);
    console.log(`   Falhas: ${failed.length}`);
    console.log(`   Tempo médio: ${avgResponseTime.toFixed(0)}ms`);
    console.log('');

    console.log(`📱 PIX (CHARGE):`);
    console.log(`   Testes: ${charges.length}`);
    console.log(`   Sucessos: ${charges.filter(r => r.success).length}`);
    console.log(`   Falhas: ${charges.filter(r => !r.success).length}`);
    console.log('');

    console.log(`💸 TRANSFERÊNCIAS (SEND):`);
    console.log(`   Testes: ${transfers.length}`);
    console.log(`   Sucessos: ${transfers.filter(r => r.success).length}`);
    console.log(`   Falhas: ${transfers.filter(r => !r.success).length}`);
    console.log('');

    if (successful.length > 0) {
      console.log(`✅ TRANSAÇÕES BEM-SUCEDIDAS:`);
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.type} - R$ ${result.amount.toFixed(2)} - ${result.responseTime}ms`);
        if (result.transactionId) {
          console.log(`      ID: ${result.transactionId}`);
        }
        if (result.externalId) {
          console.log(`      External ID: ${result.externalId}`);
        }
      });
      console.log('');
    }

    if (failed.length > 0) {
      console.log(`❌ FALHAS DETECTADAS:`);
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.type} - R$ ${result.amount.toFixed(2)} - ${result.error}`);
      });
      console.log('');
    }

    console.log(`🎯 AVALIAÇÃO FINAL:`);
    if (successRate === 100) {
      console.log(`✅ PERFEITO! Todos os testes passaram!`);
      console.log(`✅ API de produção funcionando perfeitamente!`);
      console.log(`✅ Sistema pronto para uso em produção!`);
    } else if (successRate >= 80) {
      console.log(`⚠️ BOM! Maioria dos testes passou (${successRate.toFixed(1)}%)`);
      console.log(`⚠️ Investigar falhas antes de usar em produção`);
    } else {
      console.log(`❌ PROBLEMAS DETECTADOS! Apenas ${successRate.toFixed(1)}% dos testes passaram`);
      console.log(`❌ NÃO RECOMENDADO para produção`);
    }
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  const tester = new ProductionApiIntegrationTester();
  tester.runProductionTests()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { ProductionApiIntegrationTester };
