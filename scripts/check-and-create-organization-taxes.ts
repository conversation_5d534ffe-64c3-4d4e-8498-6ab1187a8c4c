#!/usr/bin/env tsx

/**
 * Script para verificar e criar taxas da organização
 * Este script verifica se a organização de teste tem taxas configuradas
 * e cria as taxas se não existirem
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

import { db } from '@repo/database';

const ORGANIZATION_ID = 'fC99w8SdDGbNJM_q0b2s5';

async function checkAndCreateOrganizationTaxes() {
  console.log('🔍 VERIFICANDO CONFIGURAÇÃO DE TAXAS DA ORGANIZAÇÃO');
  console.log('='.repeat(60));
  console.log(`Organization ID: ${ORGANIZATION_ID}`);
  console.log('');

  try {
    // Verificar se a organização existe
    const organization = await db.organization.findUnique({
      where: { id: ORGANIZATION_ID },
      select: {
        id: true,
        name: true,
        status: true
      }
    });

    if (!organization) {
      console.log('❌ ERRO: Organização não encontrada!');
      console.log(`   ID: ${ORGANIZATION_ID}`);
      return;
    }

    console.log('✅ Organização encontrada:');
    console.log(`   Nome: ${organization.name}`);
    console.log(`   Status: ${organization.status}`);
    console.log('');

    // Verificar se existem taxas configuradas
    const existingTaxes = await db.organization_taxes.findUnique({
      where: { organizationId: ORGANIZATION_ID }
    });

    if (existingTaxes) {
      console.log('✅ TAXAS JÁ CONFIGURADAS:');
      console.log(`   PIX Charge - Taxa %: ${existingTaxes.pixChargePercentFee}%`);
      console.log(`   PIX Charge - Taxa Fixa: R$ ${existingTaxes.pixChargeFixedFee}`);
      console.log(`   PIX Transfer - Taxa %: ${existingTaxes.pixTransferPercentFee}%`);
      console.log(`   PIX Transfer - Taxa Fixa: R$ ${existingTaxes.pixTransferFixedFee}`);
      console.log(`   Criado em: ${existingTaxes.createdAt}`);
      console.log(`   Atualizado em: ${existingTaxes.updatedAt}`);
      console.log('');

      // Verificar se as taxas estão corretas (1% + R$ 2,00)
      const expectedChargePercent = 1.0;
      const expectedChargeFixed = 2.0;

      if (existingTaxes.pixChargePercentFee === expectedChargePercent && 
          existingTaxes.pixChargeFixedFee === expectedChargeFixed) {
        console.log('✅ TAXAS ESTÃO CORRETAS (1% + R$ 2,00)');
        console.log('   Sistema deveria estar aplicando taxas automaticamente');
        console.log('');
        
        // Testar o cálculo de taxas
        await testTaxCalculation();
      } else {
        console.log('⚠️ TAXAS INCORRETAS - ATUALIZANDO...');
        await updateTaxes();
      }
    } else {
      console.log('❌ NENHUMA TAXA CONFIGURADA - CRIANDO...');
      await createTaxes();
    }

  } catch (error) {
    console.error('❌ ERRO:', error);
  } finally {
    await db.$disconnect();
  }
}

async function createTaxes() {
  try {
    const taxes = await db.organization_taxes.create({
      data: {
        organizationId: ORGANIZATION_ID,
        pixChargePercentFee: 1.0,      // 1%
        pixChargeFixedFee: 2.0,        // R$ 2,00
        pixTransferPercentFee: 0.5,    // 0.5%
        pixTransferFixedFee: 1.0       // R$ 1,00
      }
    });

    console.log('✅ TAXAS CRIADAS COM SUCESSO:');
    console.log(`   PIX Charge - Taxa %: ${taxes.pixChargePercentFee}%`);
    console.log(`   PIX Charge - Taxa Fixa: R$ ${taxes.pixChargeFixedFee}`);
    console.log(`   PIX Transfer - Taxa %: ${taxes.pixTransferPercentFee}%`);
    console.log(`   PIX Transfer - Taxa Fixa: R$ ${taxes.pixTransferFixedFee}`);
    console.log('');

    // Testar o cálculo de taxas
    await testTaxCalculation();
  } catch (error) {
    console.error('❌ ERRO AO CRIAR TAXAS:', error);
  }
}

async function updateTaxes() {
  try {
    const taxes = await db.organization_taxes.update({
      where: { organizationId: ORGANIZATION_ID },
      data: {
        pixChargePercentFee: 1.0,      // 1%
        pixChargeFixedFee: 2.0,        // R$ 2,00
        pixTransferPercentFee: 0.5,    // 0.5%
        pixTransferFixedFee: 1.0       // R$ 1,00
      }
    });

    console.log('✅ TAXAS ATUALIZADAS COM SUCESSO:');
    console.log(`   PIX Charge - Taxa %: ${taxes.pixChargePercentFee}%`);
    console.log(`   PIX Charge - Taxa Fixa: R$ ${taxes.pixChargeFixedFee}`);
    console.log(`   PIX Transfer - Taxa %: ${taxes.pixTransferPercentFee}%`);
    console.log(`   PIX Transfer - Taxa Fixa: R$ ${taxes.pixTransferFixedFee}`);
    console.log('');

    // Testar o cálculo de taxas
    await testTaxCalculation();
  } catch (error) {
    console.error('❌ ERRO AO ATUALIZAR TAXAS:', error);
  }
}

async function testTaxCalculation() {
  console.log('🧮 TESTANDO CÁLCULO DE TAXAS');
  console.log('-'.repeat(40));

  try {
    // Importar a função de cálculo de taxas
    const { calculateTransactionFees } = await import('../packages/payments/src/taxes/calculator');

    // Testar com diferentes valores
    const testValues = [1, 2, 3, 4, 5, 10, 50, 100];

    for (const amount of testValues) {
      const fees = await calculateTransactionFees(ORGANIZATION_ID, amount, 'CHARGE');
      
      console.log(`   R$ ${amount.toFixed(2)} -> Taxa %: R$ ${fees.percentFee.toFixed(2)} | Taxa Fixa: R$ ${fees.fixedFee.toFixed(2)} | Total: R$ ${fees.totalFee.toFixed(2)} | Líquido: R$ ${(amount - fees.totalFee).toFixed(2)}`);
    }

    console.log('');
    console.log('✅ CÁLCULO DE TAXAS FUNCIONANDO CORRETAMENTE');
    console.log('   Se as transações ainda não estão aplicando taxas,');
    console.log('   o problema está no processamento automático no router.');
  } catch (error) {
    console.error('❌ ERRO NO CÁLCULO DE TAXAS:', error);
  }
}

// Executar se script for chamado diretamente
if (require.main === module) {
  checkAndCreateOrganizationTaxes()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { checkAndCreateOrganizationTaxes };
