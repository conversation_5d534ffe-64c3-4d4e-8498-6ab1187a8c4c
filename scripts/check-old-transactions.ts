#!/usr/bin/env tsx

/**
 * Verifica transações antigas que podem estar causando conflito
 */

// Carregar variáveis de ambiente
import { config } from 'dotenv';
config();

import { db } from '../packages/database';

class OldTransactionsChecker {
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';

  async checkOldTransactions(): Promise<void> {
    console.log('🔍 VERIFICAÇÃO DE TRANSAÇÕES ANTIGAS');
    console.log('='.repeat(50));
    console.log(`📊 Organização: ${this.organizationId}`);
    console.log('');

    // Buscar todas as transações SEND com valor 0.01
    const transactions = await db.transaction.findMany({
      where: {
        organizationId: this.organizationId,
        type: 'SEND',
        amount: 0.01
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Transações SEND com valor R$ 0,01: ${transactions.length}`);
    console.log('');

    if (transactions.length > 0) {
      console.log('📋 DETALHES DAS TRANSAÇÕES:');
      transactions.forEach((tx, index) => {
        const ageInHours = (Date.now() - tx.createdAt.getTime()) / (1000 * 60 * 60);
        console.log(`   ${index + 1}. ID: ${tx.id}`);
        console.log(`      External ID: ${tx.externalId || 'N/A'}`);
        console.log(`      Status: ${tx.status}`);
        console.log(`      Criado em: ${tx.createdAt.toISOString()}`);
        console.log(`      Idade: ${ageInHours.toFixed(1)} horas`);
        console.log('');
      });

      // Verificar se há transações muito antigas
      const oldTransactions = transactions.filter(tx => {
        const ageInHours = (Date.now() - tx.createdAt.getTime()) / (1000 * 60 * 60);
        return ageInHours > 24; // Mais de 24 horas
      });

      if (oldTransactions.length > 0) {
        console.log(`⚠️ TRANSAÇÕES ANTIGAS DETECTADAS: ${oldTransactions.length}`);
        console.log('Essas transações podem estar causando conflito na busca do webhook');
        console.log('');
        
        // Sugerir limpeza
        console.log('🧹 SUGESTÃO: Limpar transações antigas de teste');
        console.log('Execute: DELETE FROM transaction WHERE organization_id = ? AND type = ? AND amount = ? AND created_at < ?');
        console.log(`   organizationId: ${this.organizationId}`);
        console.log(`   type: SEND`);
        console.log(`   amount: 0.01`);
        console.log(`   created_at < ${new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()}`);
      } else {
        console.log('✅ Nenhuma transação muito antiga encontrada');
      }
    } else {
      console.log('❌ Nenhuma transação encontrada');
    }
  }
}

// Executar verificação se chamado diretamente
if (require.main === module) {
  const checker = new OldTransactionsChecker();
  checker.checkOldTransactions()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { OldTransactionsChecker };
