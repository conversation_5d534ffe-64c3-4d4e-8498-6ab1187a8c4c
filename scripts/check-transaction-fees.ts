#!/usr/bin/env tsx

import { config } from 'dotenv';
import { resolve } from 'path';

config({ path: resolve(process.cwd(), '.env') });

import { db } from '@repo/database';

async function checkTransaction() {
  const transaction = await db.transaction.findUnique({
    where: { id: 'cmfrs9q03029pyo2dardqj0sj' }
  });
  
  console.log('Transação:', {
    id: transaction?.id,
    amount: transaction?.amount,
    percentFee: transaction?.percentFee,
    fixedFee: transaction?.fixedFee,
    totalFee: transaction?.totalFee,
    netAmount: transaction?.netAmount,
    metadata: transaction?.metadata
  });
  
  await db.$disconnect();
}

checkTransaction().catch(console.error);
