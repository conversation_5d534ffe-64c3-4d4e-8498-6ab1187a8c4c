#!/usr/bin/env tsx

/**
 * Teste Realista de Processador de Pagamento - Valores Pequenos
 * Simula transações reais com nomes, CPFs e emails reais
 * Valores entre R$ 1 e R$ 5 para testes mais seguros
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

interface TestResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  amount: number;
  customerName: string;
  customerEmail: string;
  customerDocument: string;
  pixCode?: string;
  qrCode?: string;
  referenceCode?: string;
  totalFee?: number;
  percentFee?: number;
  fixedFee?: number;
}

class RealisticPaymentProcessorTest {
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private baseUrl = 'http://localhost:3000';

  // Nomes e CPFs realistas para não parecer teste
  private realNames = [
    '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>ro Silva',
    'Mariana Almeida', 'Gustavo Rodrigues', 'Tatiana Lima',
    'Vinícius Santos', 'Renata Costa', 'Fábio Oliveira'
  ];

  private realEmails = [
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>'
  ];

  private realPhones = [
    '11999887766', '11988776655', '11977665544', '11966554433',
    '11955443322', '11944332211', '11933221100', '11922110099',
    '11911009988', '11900998877', '11899887766', '11888776655',
    '11877665544', '11866554433', '11855443322', '11844332211',
    '11833221100', '11822110099', '11811009988', '11800998877'
  ];

  async runRealisticTest(): Promise<void> {
    console.log('🏦 TESTE REALISTA DE PROCESSADOR DE PAGAMENTO - VALORES PEQUENOS');
    console.log('='.repeat(80));
    console.log('Simulando transações reais com nomes, CPFs e emails reais');
    console.log('Valores entre R$ 1 e R$ 5 para testes mais seguros');
    console.log('Volume: R$ 2.000.000/dia | Ticket médio: R$ 3 | ~666.667 transações/dia\n');

    // Verificar se API está rodando
    if (!(await this.checkApiHealth())) {
      console.log('❌ API não está rodando. Inicie o servidor primeiro.');
      return;
    }

    // Executar testes com diferentes volumes
    await this.runVolumeTest('Volume Normal (1 minuto de pico)', 21, 1000);
    await this.runVolumeTest('Volume de Pico (2 minutos de pico)', 42, 2000);
    await this.runVolumeTest('Volume Crítico (3 minutos de pico)', 63, 3000);
    await this.runVolumeTest('Volume Extremo (5 minutos de pico)', 105, 5000);

    // Análise final
    await this.analyzeResults();
  }

  private async checkApiHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`);
      return response.ok;
    } catch {
      return false;
    }
  }

  private async runVolumeTest(testName: string, concurrentTransactions: number, delayMs: number): Promise<void> {
    console.log(`\n🔄 ${testName} (${concurrentTransactions} transações simultâneas)...`);

    const startTime = Date.now();
    const promises: Promise<TestResult>[] = [];

    // Criar transações simultâneas
    for (let i = 0; i < concurrentTransactions; i++) {
      promises.push(this.createRealisticTransaction(i));

      // Pequena pausa entre requisições para simular carga real
      if (i % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    const results = await Promise.all(promises);
    const duration = Date.now() - startTime;

    // Análise dos resultados
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const successRate = (successful.length / results.length) * 100;

    // Verificar unicidade
    const transactionIds = successful.map(r => r.transactionId).filter(Boolean);
    const uniqueIds = new Set(transactionIds);
    const duplicateIds = transactionIds.length - uniqueIds.size;

    const pixCodes = successful.map(r => r.pixCode).filter(Boolean);
    const uniquePixCodes = new Set(pixCodes);
    const duplicatePixCodes = pixCodes.length - uniquePixCodes.size;

    const referenceCodes = successful.map(r => r.referenceCode).filter(Boolean);
    const uniqueRefCodes = new Set(referenceCodes);
    const duplicateRefCodes = referenceCodes.length - uniqueRefCodes.size;

    // Verificar taxas aplicadas
    const withFees = successful.filter(r => r.totalFee && r.totalFee > 0);
    const withoutFees = successful.filter(r => !r.totalFee || r.totalFee === 0);
    const feeApplicationRate = (withFees.length / successful.length) * 100;

    console.log(`✅ ${testName}: CONCLUÍDO`);
    console.log(`   Total: ${results.length} | Sucesso: ${successful.length} | Falhas: ${failed.length}`);
    console.log(`   Taxa de Sucesso: ${successRate.toFixed(1)}%`);
    console.log(`   IDs Únicos: ${uniqueIds.size} | Duplicados: ${duplicateIds}`);
    console.log(`   QR Codes Únicos: ${uniquePixCodes.size} | Duplicados: ${duplicatePixCodes}`);
    console.log(`   Reference Codes Únicos: ${uniqueRefCodes.size} | Duplicados: ${duplicateRefCodes}`);
    console.log(`   Transações com Taxa: ${withFees.length} | Sem Taxa: ${withoutFees.length}`);
    console.log(`   Taxa de Aplicação de Taxas: ${feeApplicationRate.toFixed(1)}%`);
    console.log(`   Duração: ${duration}ms | Tempo Médio: ${Math.round(duration / results.length)}ms`);

    if (withoutFees.length > 0) {
      console.log(`   ⚠️ PROBLEMA: ${withoutFees.length} transações sem taxa aplicada!`);
    }

    // Pausa entre testes
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }

  private async createRealisticTransaction(index: number): Promise<TestResult> {
    try {
      // Gerar dados realistas
      const name = this.realNames[index % this.realNames.length];
      const email = this.realEmails[index % this.realEmails.length];
      const phone = this.realPhones[index % this.realPhones.length];
      const document = this.generateRealisticCPF();
      const amount = this.generateSmallAmount(); // R$ 1 a R$ 5

      const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify({
          amount,
          customerName: name,
          customerEmail: email,
          customerPhone: phone,
          customerDocument: document,
          customerDocumentType: 'cpf',
          description: `Compra online - ${name}`,
          organizationId: this.organizationId
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `Status: ${response.status} Error: ${errorText}`,
          amount,
          customerName: name,
          customerEmail: email,
          customerDocument: document
        };
      }

      const data = await response.json();

      return {
        success: true,
        transactionId: data.id,
        amount,
        customerName: name,
        customerEmail: email,
        customerDocument: document,
        pixCode: data.pix?.qrCode?.emv,
        qrCode: data.pix?.qrCode?.imagem,
        referenceCode: data.referenceCode,
        totalFee: data.totalFee,
        percentFee: data.percentFee,
        fixedFee: data.fixedFee
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        amount: this.generateSmallAmount(),
        customerName: this.realNames[index % this.realNames.length],
        customerEmail: this.realEmails[index % this.realEmails.length],
        customerDocument: this.generateRealisticCPF()
      };
    }
  }

  private generateRealisticCPF(): string {
    // Gerar CPF válido (apenas para teste)
    const digits = Array.from({ length: 9 }, () => Math.floor(Math.random() * 10));

    // Calcular primeiro dígito verificador
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += digits[i] * (10 - i);
    }
    const firstDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);

    // Calcular segundo dígito verificador
    sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += digits[i] * (11 - i);
    }
    sum += firstDigit * 2;
    const secondDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);

    return [...digits, firstDigit, secondDigit].join('');
  }

  private generateSmallAmount(): number {
    // Valores entre R$ 1 e R$ 5 (em centavos)
    const amounts = [100, 150, 200, 250, 300, 350, 400, 450, 500];
    return amounts[Math.floor(Math.random() * amounts.length)];
  }

  private async analyzeResults(): Promise<void> {
    console.log('\n💰 ANÁLISE DE PROBLEMAS DE TAXAS');
    console.log('-'.repeat(60));

    // Buscar transações recentes para análise
    try {
      const response = await fetch(`${this.baseUrl}/api/payments/transactions?organizationId=${this.organizationId}`);
      if (response.ok) {
        const data = await response.json();
        const transactions = data.transactions || [];

        const withFees = transactions.filter((t: any) => t.totalFee && t.totalFee > 0);
        const withoutFees = transactions.filter((t: any) => !t.totalFee || t.totalFee === 0);

        console.log(`Total de Transações Analisadas: ${transactions.length}`);
        console.log(`Transações sem Taxa: ${withoutFees.length}`);
        console.log(`Taxa de Aplicação de Taxas: ${((withFees.length / transactions.length) * 100).toFixed(1)}%`);

        if (withoutFees.length > 0) {
          console.log('\n🔍 PROBLEMAS DETECTADOS:');
          withoutFees.slice(0, 10).forEach((tx: any, i: number) => {
            console.log(`   ${i + 1}. Transação ${tx.id}: totalFee = ${tx.totalFee}, fixedFee = ${tx.fixedFee}, percentFee = ${tx.percentFee}`);
          });

          console.log('\n🔧 POSSÍVEIS CAUSAS:');
          console.log('   1. Taxas não configuradas para a organização');
          console.log('   2. Gateway não aplicando taxas automaticamente');
          console.log('   3. Código de processamento de taxas não executando');
          console.log('   4. Configuração de taxas incorreta no banco de dados');
          console.log('   5. Provider não retornando informações de taxa');

          console.log('\n💡 RECOMENDAÇÕES:');
          console.log('   1. Verificar configuração de taxas da organização');
          console.log('   2. Implementar processamento automático de taxas');
          console.log('   3. Adicionar logs de debug para processamento de taxas');
          console.log('   4. Validar se gateway está aplicando taxas corretamente');
          console.log('   5. Implementar fallback para aplicação manual de taxas');
        }
      }
    } catch (error) {
      console.log('❌ Erro ao analisar transações:', error);
    }
  }
}

// Executar teste se script for chamado diretamente
if (require.main === module) {
  const tester = new RealisticPaymentProcessorTest();
  tester.runRealisticTest()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { RealisticPaymentProcessorTest };
